﻿/*
 * (c) Copyright Ascensio System SIA 2010-2019
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-12 Ernesta Birznieka-Upisha
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */

"use strict";

(function(window, undefined){

    window["AscCommon"] = window.AscCommon = (window["AscCommon"] || {});

    var charA = "A".charCodeAt(0);
    var charZ = "Z".charCodeAt(0);
    var chara = "a".charCodeAt(0);
    var charz = "z".charCodeAt(0);
    var char0 = "0".charCodeAt(0);
    var char9 = "9".charCodeAt(0);
    var charp = "+".charCodeAt(0);
    var chars = "/".charCodeAt(0);
    var char_break = ";".charCodeAt(0);

    function decodeBase64Char(ch)
    {
        if (ch >= charA && ch <= charZ)
            return ch - charA + 0;
        if (ch >= chara && ch <= charz)
            return ch - chara + 26;
        if (ch >= char0 && ch <= char9)
            return ch - char0 + 52;
        if (ch == charp)
            return 62;
        if (ch == chars)
            return 63;
        return -1;
    }

    var stringBase64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    var arrayBase64  = [];
    for (var index64 = 0; index64 < stringBase64.length; index64++)
    {
        arrayBase64.push(stringBase64.charAt(index64));
    }

    window.AscCommon["Base64"] = window.AscCommon.Base64 = {};

    /**
     * Decode input base64 data to output array
     * @memberof AscCommon.Base64
     * @alias decodeData
     * @param {string|Array|TypedArray} input input data
     * @param {number} [input_offset = undefined] offset in input data. 0 by default
     * @param {number} [input_len = undefined] length input data (not length of needed data, this value does not depend on the offset. input.length by default
     * @param {Array|TypedArray} output output data
     * @param {number} [output_offset = undefined] output data offset. 0 by default
     * @return {number} offset in output data (output_offset + count_write_bytes)
     */
    window.AscCommon.Base64.decodeData = window.AscCommon.Base64["decodeData"] = function(input, input_offset, input_len, output, output_offset)
    {
        var isBase64 = typeof input === "string";
        if (undefined === input_len) input_len = input.length;
        var writeIndex = (undefined === output_offset) ? 0 : output_offset;
        var index = (undefined === input_offset) ? 0 : input_offset;

        while (index < input_len)
        {
            var dwCurr = 0;
            var i;
            var nBits = 0;
            for (i=0; i<4; i++)
            {
                if (index >= input_len)
                    break;
                var nCh = decodeBase64Char(isBase64 ? input.charCodeAt(index) : input[index]);
                index++;
                if (nCh == -1)
                {
                    i--;
                    continue;
                }
                dwCurr <<= 6;
                dwCurr |= nCh;
                nBits += 6;
            }

            dwCurr <<= 24-nBits;
            for (i=0; i<(nBits>>3); i++)
            {
                output[writeIndex++] = ((dwCurr & 0x00ff0000) >>> 16);
                dwCurr <<= 8;
            }
        }
        return writeIndex;
    };

    /**
     * Decode input base64 data to returned Uint8Array
     * @memberof AscCommon.Base64
     * @alias decode
     * @param {string|Array|TypedArray} input input data
     * @param {boolean} [isUsePrefix = undefined] is detect destination size by prefix. false by default
     * @param {number} [dstlen = undefined] destination length
     * @param {number} [offset] offset of input data
     * @return {Uint8Array} decoded data
     */
    window.AscCommon.Base64.decode = window.AscCommon.Base64["decode"] = function(input, isUsePrefix, dstlen, offset)
    {
        var srcLen = input.length;
        var index = (undefined === offset) ? 0 : offset;
        var dstLen = (undefined === dstlen) ? srcLen : dstlen;

        var isBase64 = typeof input === "string";

        if (isUsePrefix && isBase64)
        {
            // ищем длину
            dstLen = 0;
            var maxLen = Math.max(11, srcLen); // > 4 Gb
            while (index < maxLen)
            {
                var c = input.charCodeAt(index++);
                if (c == char_break)
                    break;

                dstLen *= 10;
                dstLen += (c - char0);
            }

            if (index == maxLen)
            {
                // длины нет
                index = 0;
                dstLen = srcLen;
            }
        }

        var dst = new Uint8Array(dstLen);
        var writeIndex = window.AscCommon.Base64.decodeData(input, index, srcLen, dst, 0);

        if (writeIndex == dstLen)
            return dst;

        return new Uint8Array(dst.buffer, 0, writeIndex);
    };

    /**
     * Encode input data to base64 string
     * @memberof AscCommon.Base64
     * @alias encode
     * @param {Array|TypedArray} input input data
     * @param {number} [offset = undefined] offset of input data. 0 by default
     * @param {number} [length = undefined] length input data (last index: offset + length). input.length by default
     * @param {boolean} [isUsePrefix = undefined] is add destination size by prefix. false by default
     * @return {string} encoded data
     */
    window.AscCommon.Base64.encode = window.AscCommon.Base64["encode"] = function(input, offset, length, isUsePrefix)
    {
        var srcLen = (undefined === length) ? input.length : length;
        var index = (undefined === offset) ? 0 : offset;

        var len1 = (((srcLen / 3) >> 0) * 4);
        var len2 = (len1 / 76) >> 0;
        var len3 = 19;
        var dstArray = [];

        var sTemp = "";
        var dwCurr = 0;
        for (var i = 0; i <= len2; i++)
        {
            if (i == len2)
                len3 = ((len1 % 76) / 4) >> 0;

            for (var j = 0; j < len3; j++)
            {
                dwCurr = 0;
                for (var n = 0; n < 3; n++)
                {
                    dwCurr |= input[index++];
                    dwCurr <<= 8;
                }

                sTemp = "";
                for (var k = 0; k < 4; k++)
                {
                    var b = (dwCurr >>> 26) & 0xFF;
                    sTemp += arrayBase64[b];
                    dwCurr <<= 6;
                    dwCurr &= 0xFFFFFFFF;
                }
                dstArray.push(sTemp);
            }
        }
        len2 = (srcLen % 3 != 0) ? (srcLen % 3 + 1) : 0;
        if (len2)
        {
            dwCurr = 0;
            for (var n = 0; n < 3; n++)
            {
                if (n < (srcLen % 3))
                    dwCurr |= input[index++];
                dwCurr <<= 8;
            }

            sTemp = "";
            for (var k = 0; k < len2; k++)
            {
                var b = (dwCurr >>> 26) & 0xFF;
                sTemp += arrayBase64[b];
                dwCurr <<= 6;
            }

            len3 = (len2 != 0) ? 4 - len2 : 0;
            for (var j = 0; j < len3; j++)
            {
                sTemp += '=';
            }
            dstArray.push(sTemp);
        }

        return isUsePrefix ? (("" + srcLen + ";") + dstArray.join("")) : dstArray.join("");
    };

    window.AscCommon["Hex"] = window.AscCommon.Hex = {};

    /**
     * Decode input hex data to Uint8Array
     * @memberof AscCommon.Hex
     * @alias decode
     * @param {string} input input data
     * @return {Uint8Array} decoded data
     */
    window.AscCommon.Hex.decode = window.AscCommon.Hex["decode"] = function(input)
    {
        var hexToByte = function(c) {
            if (c >= 48 && c <= 57) return c - 48; // 0..9
            if (c >= 97 && c <= 102) return c - 87;
            if (c >= 65 && c <= 70) return c - 55;
            return 0;
        };

        var len = input.length;
        if (len & 0x01) len -= 1;
        var result = new Uint8Array(len >> 1);
        var resIndex = 0;
        for (var i = 0; i < len; i += 2)
        {
            result[resIndex++] = hexToByte(input.charCodeAt(i)) << 4 | hexToByte(input.charCodeAt(i + 1));
        }
        return result;
    };

    /**
     * Encode Uint8Array to hex string
     * @memberof AscCommon.Hex
     * @alias encode
     * @param {Array|TypedArray} input input data
     * @param {boolean} [isUpperCase = false] is use upper case
     * @return {string} encoded data
     */
    window.AscCommon.Hex.encode = window.AscCommon.Hex["encode"] = function(input, isUpperCase)
    {
        var byteToHex = new Array(256);
        for (var i = 0; i < 16; i++)
            byteToHex[i] = "0" + (isUpperCase ? i.toString(16).toUpperCase() : i.toString(16));
        for (var i = 16; i < 256; i++)
            byteToHex[i] = isUpperCase ? i.toString(16).toUpperCase() : i.toString(16);

        var result = "";
        for (var i = 0, len = input.length; i < len; i++)
            result += byteToHex[input[i]];

        return result;
    };


})(self);


(function(window, undefined) {

	window.messageData = null;
	window.messagePort = null;
	function onMessageEvent(data, port)
	{
	    if (data.type == "hash")
	    {
	        window.messageData = data.value;
	        window.messagePort = port;
	        if (!window.engineInit)
	        	return;
	        checkMessage();
	    }
	}

	window.onconnect = function(e)
	{
	    var port = e.ports[0];
	    port.onmessage = function(e) {
	        onMessageEvent(e.data, port);
	    }    
	};
	window.onmessage = function(e)
	{
	    onMessageEvent(e.data);
	};
	window.engineInit = false;
	window.onEngineInit = function()
	{
		window.engineInit = true;
		if (window.messageData)
			checkMessage();
	};

	function checkMessage()
	{
		var data = window.messageData;
		var res = [];

		for (var i = 0, len = data.length; i < len; i++)
        {
            res.push(AscCommon.Hash.hashOffice(data[i].password, data[i].salt, data[i].spinCount, data[i].alg).base64());
        }

		var sender = window.messagePort || window;
		sender.postMessage({ hashValue : res });
	}

	var printErr = undefined;
    var FS = undefined;
    var print = undefined;

    var getBinaryPromise = null;
    function isLocal()
    {
        if (window.navigator && window.navigator.userAgent.toLowerCase().indexOf("ascdesktopeditor") < 0)
            return false;
        if (window.location && window.location.protocol == "file:")
            return true;
        if (window.document && window.document.currentScript && 0 == window.document.currentScript.src.indexOf("file:///"))
            return true;
        return false;
    }

    if (isLocal())
    {
        // fetch not support file:/// scheme
        window.fetch = undefined;

        getBinaryPromise = function() {

            var wasmPath = "ascdesktop://fonts/" + wasmBinaryFile.substr(8);
            return new Promise(function (resolve, reject) {

                var xhr = new XMLHttpRequest();
                xhr.open('GET', wasmPath, true);
                xhr.responseType = 'arraybuffer';

                if (xhr.overrideMimeType)
                    xhr.overrideMimeType('text/plain; charset=x-user-defined');
                else
                    xhr.setRequestHeader('Accept-Charset', 'x-user-defined');

                xhr.onload = function () {
                    if (this.status == 200) {
                        resolve(new Uint8Array(this.response));
                    }
                };

                xhr.send(null);

            });
        }
    }
    else
    {
        getBinaryPromise = function() {
            return getBinaryPromise2();
        }
    }
    
    var ob;function pb(h){var f=0;return function(){return f<h.length?{done:!1,value:h[f++]}:{done:!0}}}function qb(h){var f="undefined"!=typeof Symbol&&Symbol.iterator&&h[Symbol.iterator];return f?f.call(h):{next:pb(h)}}var dd="undefined"!=typeof window&&window===this?this:"undefined"!=typeof global&&null!=global?global:this,Fd="function"==typeof Object.defineProperties?Object.defineProperty:function(h,f,Ka){h!=Array.prototype&&h!=Object.prototype&&(h[f]=Ka.value)};if(!dd)dd=self;
function Gd(h,f){if(f){var Ka=dd;h=h.split(".");for(var Za=0;Za<h.length-1;Za++){var bb=h[Za];bb in Ka||(Ka[bb]={});Ka=Ka[bb]}h=h[h.length-1];Za=Ka[h];f=f(Za);f!=Za&&null!=f&&Fd(Ka,h,{configurable:!0,writable:!0,value:f})}}
Gd("Promise",function(h){function f(f){this.MQf=0;this.Cug=void 0;this.Qie=[];var h=this.slg();try{f(h.resolve,h.reject)}catch(Tb){h.reject(Tb)}}function Ka(){this.FAd=null}function Za(h){return h instanceof f?h:new f(function(f){f(h)})}if(h)return h;Ka.prototype.nJg=function(f){if(null==this.FAd){this.FAd=[];var h=this;this.oJg(function(){h.Ihh()})}this.FAd.push(f)};var bb=dd.setTimeout;Ka.prototype.oJg=function(f){bb(f,0)};Ka.prototype.Ihh=function(){for(;this.FAd&&this.FAd.length;){var f=this.FAd;
this.FAd=[];for(var h=0;h<f.length;++h){var Ka=f[h];f[h]=null;try{Ka()}catch(jb){this.Heh(jb)}}}this.FAd=null};Ka.prototype.Heh=function(f){this.oJg(function(){throw f;})};f.prototype.slg=function(){function f(f){return function(z){Ka||(Ka=!0,f.call(h,z))}}var h=this,Ka=!1;return{resolve:f(this.Cph),reject:f(this.oug)}};f.prototype.Cph=function(h){if(h===this)this.oug(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof f)this.Aqh(h);else{a:switch(typeof h){case "object":var z=
null!=h;break a;case "function":z=!0;break a;default:z=!1}z?this.Bph(h):this.dNg(h)}};f.prototype.Bph=function(f){var h=void 0;try{h=f.then}catch(Tb){this.oug(Tb);return}"function"==typeof h?this.Bqh(h,f):this.dNg(f)};f.prototype.oug=function(f){this.vWg(2,f)};f.prototype.dNg=function(f){this.vWg(1,f)};f.prototype.vWg=function(f,h){if(0!=this.MQf)throw Error("Cannot settle("+f+", "+h+"): Promise already settled in state"+this.MQf);this.MQf=f;this.Cug=h;this.Jhh()};f.prototype.Jhh=function(){if(null!=
this.Qie){for(var f=0;f<this.Qie.length;++f)gb.nJg(this.Qie[f]);this.Qie=null}};var gb=new Ka;f.prototype.Aqh=function(f){var h=this.slg();f.cZf(h.resolve,h.reject)};f.prototype.Bqh=function(f,h){var z=this.slg();try{f.call(h,z.resolve,z.reject)}catch(jb){z.reject(jb)}};f.prototype.then=function(h,Ka){function z(f,h){return"function"==typeof f?function(h){try{gb(f(h))}catch(hc){Ma(hc)}}:h}var gb,Ma,bb=new f(function(f,h){gb=f;Ma=h});this.cZf(z(h,gb),z(Ka,Ma));return bb};f.prototype.catch=function(f){return this.then(void 0,
f)};f.prototype.cZf=function(f,h){function z(){switch(Ka.MQf){case 1:f(Ka.Cug);break;case 2:h(Ka.Cug);break;default:throw Error("Unexpected state: "+Ka.MQf);}}var Ka=this;null==this.Qie?gb.nJg(z):this.Qie.push(z)};f.resolve=Za;f.reject=function(h){return new f(function(f,z){z(h)})};f.race=function(h){return new f(function(f,z){for(var Ka=qb(h),Ma=Ka.next();!Ma.done;Ma=Ka.next())Za(Ma.value).cZf(f,z)})};f.all=function(h){var z=qb(h),Ka=z.next();return Ka.done?Za([]):new f(function(f,h){function Ma(h){return function(z){Ta[h]=
z;gb--;0==gb&&f(Ta)}}var Ta=[],gb=0;do Ta.push(void 0),gb++,Za(Ka.value).cZf(Ma(Ta.length-1),h),Ka=z.next();while(!Ka.done)})};return f});Gd("Array.prototype.fill",function(h){return h?h:function(f,h,Za){var Ka=this.length||0;0>h&&(h=Math.max(0,Ka+h));if(null==Za||Za>Ka)Za=Ka;Za=Number(Za);0>Za&&(Za=Math.max(0,Ka+Za));for(h=Number(h||0);h<Za;h++)this[h]=f;return this}});
function Hd(h,f,Ka){if(null==h)throw new TypeError("The 'this' value for String.prototype."+Ka+" must not be null or undefined");if(f instanceof RegExp)throw new TypeError("First argument to String.prototype."+Ka+" must not be a regular expression");return h+""}Gd("String.prototype.repeat",function(h){return h?h:function(f){var h=Hd(this,null,"repeat");if(0>f||1342177279<f)throw new RangeError("Invalid count value");f|=0;for(var Za="";f;)if(f&1&&(Za+=h),f>>>=1)h+=h;return Za}});
Gd("Number.isFinite",function(h){return h?h:function(f){return"number"!==typeof f?!1:!isNaN(f)&&Infinity!==f&&-Infinity!==f}});Gd("Number.isInteger",function(h){return h?h:function(f){return Number.isFinite(f)?f===Math.floor(f):!1}});Gd("String.prototype.endsWith",function(h){return h?h:function(f,h){var Ka=Hd(this,f,"endsWith");f+="";void 0===h&&(h=Ka.length);h=Math.max(0,Math.min(h|0,Ka.length));for(var bb=f.length;0<bb&&0<h;)if(Ka[--h]!=f[--bb])return!1;return 0>=bb}});
Gd("String.prototype.padStart",function(h){return h?h:function(f,h){var Ka=Hd(this,null,"padStart");f-=Ka.length;h=void 0!==h?String(h):" ";return(0<f&&h?h.repeat(Math.ceil(f/h.length)).substring(0,f):"")+Ka}});function Be(){Be=function(){};dd.Symbol||(dd.Symbol=De)}function Ee(h,f){this.kYg=h;Fd(this,"description",{configurable:!0,writable:!0,value:f})}Ee.prototype.toString=function(){return this.kYg};
var De=function(){function h(Ka){if(this instanceof h)throw new TypeError("Symbol is not a constructor");return new Ee("jscomp_symbol_"+(Ka||"")+"_"+f++,Ka)}var f=0;return h}();function Ng(){Be();var h=dd.Symbol.iterator;h||(h=dd.Symbol.iterator=dd.Symbol("Symbol.iterator"));"function"!=typeof Array.prototype[h]&&Fd(Array.prototype,h,{configurable:!0,writable:!0,value:function(){return Kh(pb(this))}});Ng=function(){}}
function Kh(h){Ng();h={next:h};h[dd.Symbol.iterator]=function(){return this};return h}function qm(h,f){Ng();h instanceof String&&(h+="");var Ka=0,Za={next:function(){if(Ka<h.length){var bb=Ka++;return{value:f(bb,h[bb]),done:!1}}Za.next=function(){return{done:!0,value:void 0}};return Za.next()}};Za[Symbol.iterator]=function(){return Za};return Za}Gd("Array.prototype.values",function(h){return h?h:function(){return qm(this,function(f,h){return h})}});
Gd("Math.sign",function(h){return h?h:function(f){f=Number(f);return 0===f||isNaN(f)?f:0<f?1:-1}});Gd("Array.prototype.keys",function(h){return h?h:function(){return qm(this,function(f){return f})}});function Sm(h,f){return Object.prototype.hasOwnProperty.call(h,f)}
Gd("WeakMap",function(h){function f(f){this.aCf=(z+=Math.random()+1).toString();if(f){f=qb(f);for(var h;!(h=f.next()).done;)h=h.value,this.set(h[0],h[1])}}function Ka(){}function Za(f){Sm(f,gb)||Fd(f,gb,{value:new Ka})}function bb(f){var h=Object[f];h&&(Object[f]=function(f){if(f instanceof Ka)return f;Za(f);return h(f)})}if(function(){if(!h||!Object.seal)return!1;try{var f=Object.seal({}),z=Object.seal({}),Ka=new h([[f,2],[z,3]]);if(2!=Ka.get(f)||3!=Ka.get(z))return!1;Ka.delete(f);Ka.set(z,4);return!Ka.has(f)&&
4==Ka.get(z)}catch(Ma){return!1}}())return h;var gb="$jscomp_hidden_"+Math.random();bb("freeze");bb("preventExtensions");bb("seal");var z=0;f.prototype.set=function(f,h){Za(f);if(!Sm(f,gb))throw Error("WeakMap key fail: "+f);f[gb][this.aCf]=h;return this};f.prototype.get=function(f){return Sm(f,gb)?f[gb][this.aCf]:void 0};f.prototype.has=function(f){return Sm(f,gb)&&Sm(f[gb],this.aCf)};f.prototype.delete=function(f){return Sm(f,gb)&&Sm(f[gb],this.aCf)?delete f[gb][this.aCf]:!1};return f});
Gd("Map",function(h){function f(){var f={};return f.previous=f.next=f.head=f}function Ka(f,h){var z=f.b4c;return Kh(function(){if(z){for(;z.head!=f.b4c;)z=z.previous;for(;z.next!=z.head;)return z=z.next,{done:!1,value:h(z)};z=null}return{done:!0,value:void 0}})}function Za(f,h){var Ka=h&&typeof h;"object"==Ka||"function"==Ka?gb.has(h)?Ka=gb.get(h):(Ka=""+ ++z,gb.set(h,Ka)):Ka="p_"+h;var Ma=f.rsf[Ka];if(Ma&&Sm(f.rsf,Ka))for(f=0;f<Ma.length;f++){var bb=Ma[f];if(h!==h&&bb.key!==bb.key||h===bb.key)return{id:Ka,
list:Ma,index:f,SNb:bb}}return{id:Ka,list:Ma,index:-1,SNb:void 0}}function bb(h){this.rsf={};this.b4c=f();this.size=0;if(h){h=qb(h);for(var z;!(z=h.next()).done;)z=z.value,this.set(z[0],z[1])}}if(function(){if(!h||"function"!=typeof h||!h.prototype.entries||"function"!=typeof Object.seal)return!1;try{var f=Object.seal({x:4}),z=new h(qb([[f,"s"]]));if("s"!=z.get(f)||1!=z.size||z.get({x:4})||z.set({x:4},"t")!=z||2!=z.size)return!1;var Ka=z.entries(),Ma=Ka.next();if(Ma.done||Ma.value[0]!=f||"s"!=Ma.value[1])return!1;
Ma=Ka.next();return Ma.done||4!=Ma.value[0].x||"t"!=Ma.value[1]||!Ka.next().done?!1:!0}catch(Kb){return!1}}())return h;Ng();var gb=new WeakMap;bb.prototype.set=function(f,h){f=0===f?0:f;var z=Za(this,f);z.list||(z.list=this.rsf[z.id]=[]);z.SNb?z.SNb.value=h:(z.SNb={next:this.b4c,previous:this.b4c.previous,head:this.b4c,key:f,value:h},z.list.push(z.SNb),this.b4c.previous.next=z.SNb,this.b4c.previous=z.SNb,this.size++);return this};bb.prototype.delete=function(f){f=Za(this,f);return f.SNb&&f.list?(f.list.splice(f.index,
1),f.list.length||delete this.rsf[f.id],f.SNb.previous.next=f.SNb.next,f.SNb.next.previous=f.SNb.previous,f.SNb.head=null,this.size--,!0):!1};bb.prototype.clear=function(){this.rsf={};this.b4c=this.b4c.previous=f();this.size=0};bb.prototype.has=function(f){return!!Za(this,f).SNb};bb.prototype.get=function(f){return(f=Za(this,f).SNb)&&f.value};bb.prototype.entries=function(){return Ka(this,function(f){return[f.key,f.value]})};bb.prototype.keys=function(){return Ka(this,function(f){return f.key})};
bb.prototype.values=function(){return Ka(this,function(f){return f.value})};bb.prototype.forEach=function(f,h){for(var z=this.entries(),Ma;!(Ma=z.next()).done;)Ma=Ma.value,f.call(h,Ma[1],Ma[0],this)};bb.prototype[Symbol.iterator]=bb.prototype.entries;var z=0;return bb});function Fw(h,f,Ka){h instanceof String&&(h=String(h));for(var Za=h.length,bb=0;bb<Za;bb++){var gb=h[bb];if(f.call(Ka,gb,bb,h))return{dn:bb,Ju:gb}}return{dn:-1,Ju:void 0}}
Gd("Array.prototype.find",function(h){return h?h:function(f,h){return Fw(this,f,h).Ju}});Gd("String.prototype.startsWith",function(h){return h?h:function(f,h){var Ka=Hd(this,f,"startsWith");f+="";var bb=Ka.length,gb=f.length;h=Math.max(0,Math.min(h|0,Ka.length));for(var z=0;z<gb&&h<bb;)if(Ka[h++]!=f[z++])return!1;return z>=gb}});Gd("Object.is",function(h){return h?h:function(f,h){return f===h?0!==f||1/f===1/h:f!==f&&h!==h}});
Gd("Array.prototype.includes",function(h){return h?h:function(f,h){var Ka=this;Ka instanceof String&&(Ka=String(Ka));var bb=Ka.length;h=h||0;for(0>h&&(h=Math.max(h+bb,0));h<bb;h++){var gb=Ka[h];if(gb===f||Object.is(gb,f))return!0}return!1}});Gd("String.prototype.includes",function(h){return h?h:function(f,h){return-1!==Hd(this,f,"includes").indexOf(f,h||0)}});
Gd("Math.tanh",function(h){return h?h:function(f){f=Number(f);if(0===f)return f;var h=Math.exp(-2*Math.abs(f));h=(1-h)/(1+h);return 0>f?-h:h}});Gd("Math.log1p",function(h){return h?h:function(f){f=Number(f);if(.25>f&&-.25<f){for(var h=f,Za=1,bb=f,gb=0,z=1;gb!=bb;)h*=f,z*=-1,bb=(gb=bb)+z*h/++Za;return bb}return Math.log(1+f)}});Gd("Math.expm1",function(h){return h?h:function(f){f=Number(f);if(.25>f&&-.25<f){for(var h=f,Za=1,bb=f,gb=0;gb!=bb;)h*=f/++Za,bb=(gb=bb)+h;return bb}return Math.exp(f)-1}});
Gd("Math.trunc",function(h){return h?h:function(f){f=Number(f);if(isNaN(f)||Infinity===f||-Infinity===f||0===f)return f;var h=Math.floor(Math.abs(f));return 0>f?-h:h}});Gd("Math.log10",function(h){return h?h:function(f){return Math.log(f)/Math.LN10}});Gd("Math.cosh",function(h){if(h)return h;var f=Math.exp;return function(h){h=Number(h);return(f(h)+f(-h))/2}});Gd("Math.sinh",function(h){if(h)return h;var f=Math.exp;return function(h){h=Number(h);return 0===h?h:(f(h)-f(-h))/2}});
Gd("Math.acosh",function(h){return h?h:function(f){f=Number(f);return Math.log(f+Math.sqrt(f*f-1))}});Gd("Math.atanh",function(h){if(h)return h;var f=Math.log1p;return function(h){h=Number(h);return(f(h)-f(-h))/2}});Gd("Math.asinh",function(h){return h?h:function(f){f=Number(f);if(0===f)return f;var h=Math.log(Math.abs(f)+Math.sqrt(f*f+1));return 0>f?-h:h}});Gd("Array.prototype.findIndex",function(h){return h?h:function(f,h){return Fw(this,f,h).dn}});

Math.imul = Math.imul || function(a, b) {
  var ah = (a >>> 16) & 0xffff;
  var al = a & 0xffff;
  var bh = (b >>> 16) & 0xffff;
  var bl = b & 0xffff;
  // сдвиг на 0 бит закрепляет знак в старшей части числа
  // окончательный |0 преобразует беззнаковое значение обратно в знаковое значение
  return ((al * bl) + (((ah * bl + al * bh) << 16) >>> 0)|0);
};

    (function(){

	if (undefined !== String.prototype.fromUtf8 &&
		undefined !== String.prototype.toUtf8)
		return;

	/**
	 * Read string from utf8
	 * @param {Uint8Array} buffer
	 * @param {number} [start=0]
	 * @param {number} [len]
	 * @returns {string}
	 */
	String.prototype.fromUtf8 = function(buffer, start, len) {
		if (undefined === start)
			start = 0;
		if (undefined === len)
			len = buffer.length;

		var result = "";
		var index  = start;
		var end = start + len;
		while (index < end)
		{
			var u0 = buffer[index++];
			if (!(u0 & 128))
			{
				result += String.fromCharCode(u0);
				continue;
			}
			var u1 = buffer[index++] & 63;
			if ((u0 & 224) == 192)
			{
				result += String.fromCharCode((u0 & 31) << 6 | u1);
				continue;
			}
			var u2 = buffer[index++] & 63;
			if ((u0 & 240) == 224)
				u0 = (u0 & 15) << 12 | u1 << 6 | u2;
			else
				u0 = (u0 & 7) << 18 | u1 << 12 | u2 << 6 | buffer[index++] & 63;
			if (u0 < 65536)
				result += String.fromCharCode(u0);
			else
			{
				var ch = u0 - 65536;
				result += String.fromCharCode(55296 | ch >> 10, 56320 | ch & 1023);
			}
		}
		return result;
	};

	/**
	 * Convert string to utf8 array
	 * @returns {Uint8Array}
	 */
	String.prototype.toUtf8 = function(isNoEndNull) {
		var inputLen = this.length;
		var testLen  = 6 * inputLen + 1;
		var tmpStrings = new ArrayBuffer(testLen);

		var code  = 0;
		var index = 0;

		var outputIndex = 0;
		var outputDataTmp = new Uint8Array(tmpStrings);
		var outputData = outputDataTmp;

		while (index < inputLen)
		{
			code = this.charCodeAt(index++);
			if (code >= 0xD800 && code <= 0xDFFF && index < inputLen)
				code = 0x10000 + (((code & 0x3FF) << 10) | (0x03FF & this.charCodeAt(index++)));

			if (code < 0x80)
				outputData[outputIndex++] = code;
			else if (code < 0x0800)
			{
				outputData[outputIndex++] = 0xC0 | (code >> 6);
				outputData[outputIndex++] = 0x80 | (code & 0x3F);
			}
			else if (code < 0x10000)
			{
				outputData[outputIndex++] = 0xE0 | (code >> 12);
				outputData[outputIndex++] = 0x80 | ((code >> 6) & 0x3F);
				outputData[outputIndex++] = 0x80 | (code & 0x3F);
			}
			else if (code < 0x1FFFFF)
			{
				outputData[outputIndex++] = 0xF0 | (code >> 18);
				outputData[outputIndex++] = 0x80 | ((code >> 12) & 0x3F);
				outputData[outputIndex++] = 0x80 | ((code >> 6) & 0x3F);
				outputData[outputIndex++] = 0x80 | (code & 0x3F);
			}
			else if (code < 0x3FFFFFF)
			{
				outputData[outputIndex++] = 0xF8 | (code >> 24);
				outputData[outputIndex++] = 0x80 | ((code >> 18) & 0x3F);
				outputData[outputIndex++] = 0x80 | ((code >> 12) & 0x3F);
				outputData[outputIndex++] = 0x80 | ((code >> 6) & 0x3F);
				outputData[outputIndex++] = 0x80 | (code & 0x3F);
			}
			else if (code < 0x7FFFFFFF)
			{
				outputData[outputIndex++] = 0xFC | (code >> 30);
				outputData[outputIndex++] = 0x80 | ((code >> 24) & 0x3F);
				outputData[outputIndex++] = 0x80 | ((code >> 18) & 0x3F);
				outputData[outputIndex++] = 0x80 | ((code >> 12) & 0x3F);
				outputData[outputIndex++] = 0x80 | ((code >> 6) & 0x3F);
				outputData[outputIndex++] = 0x80 | (code & 0x3F);
			}
		}

		if (isNoEndNull !== true)
			outputData[outputIndex++] = 0;

		return new Uint8Array(tmpStrings, 0, outputIndex);
	};

})();


    var Module=typeof Module!=="undefined"?Module:{};var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=false;var ENVIRONMENT_IS_WORKER=true;var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=function(url){try{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText}catch(err){var data=tryParseAsDataURI(url);if(data){return intArrayToString(data)}throw err}};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){try{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}catch(err){var data=tryParseAsDataURI(url);if(data){return data}throw err}}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}var data=tryParseAsDataURI(url);if(data){onload(data.buffer);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;var WebAssembly={Memory:function(opts){this.buffer=new ArrayBuffer(opts["initial"]*65536)},Module:function(binary){},Instance:function(module,info){this.exports=(
// EMSCRIPTEN_START_ASM
function instantiate(aa){function c(d){d.set=function(a,b){this[a]=b};d.get=function(a){return this[a]};return d}var e;var f=new Uint8Array(123);for(var a=25;a>=0;--a){f[48+a]=52+a;f[65+a]=a;f[97+a]=26+a}f[43]=62;f[47]=63;function l(m,n,o){var g,h,a=0,i=n,j=o.length,k=n+(j*3>>2)-(o[j-2]=="=")-(o[j-1]=="=");for(;a<j;a+=4){g=f[o.charCodeAt(a+1)];h=f[o.charCodeAt(a+2)];m[i++]=f[o.charCodeAt(a)]<<2|g>>4;if(i<k)m[i++]=g<<4|h>>2;if(i<k)m[i++]=h<<6|f[o.charCodeAt(a+3)]}}function p(q){l(e,1024,"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");l(e,10496,"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");l(e,18640,"AQAAACBMEA==")}function _($){var r=$.a;var s=r.buffer;r.grow=Y;var t=new Int8Array(s);var u=new Int16Array(s);var v=new Int32Array(s);var w=new Uint8Array(s);var x=new Uint16Array(s);var y=new Uint32Array(s);var z=new Float32Array(s);var A=new Float64Array(s);var B=Math.imul;var C=Math.fround;var D=Math.abs;var E=Math.clz32;var F=Math.min;var G=Math.max;var H=Math.floor;var I=Math.ceil;var J=Math.trunc;var K=Math.sqrt;var L=$.abort;var M=NaN;var N=Infinity;var O=$.b;var P=$.c;var Q=1068064;var R=0;
// EMSCRIPTEN_START_FUNCS
function ga(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0;if(c){d=a;za=v[d+56>>2];Ca=v[d+60>>2];ta=v[d+48>>2];Aa=v[d+52>>2];ra=v[d+40>>2];xa=v[d+44>>2];ma=v[d+32>>2];pa=v[d+36>>2];Ba=v[d+24>>2];Da=v[d+28>>2];ua=v[d+16>>2];ya=v[d+20>>2];sa=v[d+8>>2];va=v[d+12>>2];na=v[d>>2];qa=v[d+4>>2];while(1){p=w[b|0]|w[b+1|0]<<8|(w[b+2|0]<<16|w[b+3|0]<<24);i=p<<24|p<<8&16711680;e=w[b+4|0]|w[b+5|0]<<8|(w[b+6|0]<<16|w[b+7|0]<<24);d=e<<24|p>>>8;f=0;ga=d&65280;d=e<<8|p>>>24;i=d&255|ga|i;d=e;la=((d&255)<<24|p>>>8)&-16777216|((d&16777215)<<8|p>>>24)&16711680|(d>>>8&65280|d>>>24)|f;d=i;ja=d;e=d;d=Ga(ma,pa,50);i=R;d=Ga(ma,pa,46)^d;p=R^i;y=Ga(ma,pa,23)^d;i=y+(ma&ra)|0;d=(pa&xa)+(R^p)|0;d=i>>>0<y>>>0?d+1|0:d;f=i;i=za;p=f+i|0;d=d+Ca|0;d=i>>>0>p>>>0?d+1|0:d;f=p;p=(ma^-1)&ta;i=f+p|0;d=((pa^-1)&Aa)+d|0;d=i>>>0<p>>>0?d+1|0:d;p=i;i=i+la|0;d=d+e|0;e=i-685199838|0;d=(i>>>0<p>>>0?d+1|0:d)+1116352408|0;B=e;d=e>>>0<3609767458?d+1|0:d;i=d;e=d;d=Ga(na,qa,36);p=R;d=Ga(na,qa,30)^d;y=R^p;ga=Ga(na,qa,25)^d;f=na&sa;p=ga+(sa&ua^(f^na&ua))|0;M=qa&va;d=(va&ya^(M^qa&ya))+(R^y)|0;d=p>>>0<ga>>>0?d+1|0:d;y=p;p=p+B|0;d=d+e|0;ea=p;d=p>>>0<y>>>0?d+1|0:d;e=Ga(p,d,36);p=R;H=d;e=Ga(ea,d,30)^e;p=R^p;ha=ea&na;y=ha^(f^ea&sa);e=y+(Ga(ea,d,25)^e)|0;ia=d&qa;d=(R^p)+(ia^(M^d&va))|0;k=e;p=e>>>0<y>>>0?d+1|0:d;f=w[b+8|0]|w[b+9|0]<<8|(w[b+10|0]<<16|w[b+11|0]<<24);y=f<<24|f<<8&16711680;e=w[b+12|0]|w[b+13|0]<<8|(w[b+14|0]<<16|w[b+15|0]<<24);d=e<<24|f>>>8;M=0;K=d&65280;d=e<<8|f>>>24;y=d&255|K|y;d=e;ga=((d&255)<<24|f>>>8)&-16777216|((d&16777215)<<8|f>>>24)&16711680|(d>>>8&65280|d>>>24)|M;d=y;oa=d;K=Aa;X=ra;D=xa;f=Ba;B=f+B|0;d=i+Da|0;d=f>>>0>B>>>0?d+1|0:d;f=B;i=d;B=Ga(f,d,50);$=R;B=Ga(f,d,46)^B;$=R^$;M=(f^-1)&X;e=ta+M|0;d=((d^-1)&D)+K|0;d=e>>>0<M>>>0?d+1|0:d;M=f&ma;e=M+e|0;d=(i&pa)+d|0;d=e>>>0<M>>>0?d+1|0:d;M=Ga(f,i,23)^B;e=M+e|0;d=(R^$)+d|0;d=e>>>0<M>>>0?d+1|0:d;M=e;e=ga+e|0;d=d+y|0;d=e>>>0<M>>>0?d+1|0:d;e=e+602891725|0;d=d+1899447441|0;d=e>>>0<602891725?d+1|0:d;D=e;y=e;e=e+k|0;g=p;p=d;d=g+d|0;N=e;d=e>>>0<y>>>0?d+1|0:d;e=Ga(e,d,36);y=R;k=d;e=Ga(N,d,30)^e;y=R^y;P=N&ea;M=P^(ha^N&na);e=M+(Ga(N,d,25)^e)|0;J=d&H;d=(R^y)+(J^(ia^d&qa))|0;ba=e;y=e>>>0<M>>>0?d+1|0:d;K=w[b+16|0]|w[b+17|0]<<8|(w[b+18|0]<<16|w[b+19|0]<<24);M=K<<24|K<<8&16711680;e=w[b+20|0]|w[b+21|0]<<8|(w[b+22|0]<<16|w[b+23|0]<<24);d=e<<24|K>>>8;B=0;ha=d&65280;d=e<<8|K>>>24;X=B;B=d&255|ha|M;d=e;M=((d&255)<<24|K>>>8)&-16777216|((d&16777215)<<8|K>>>24)&16711680|(d>>>8&65280|d>>>24)|X;d=B;ha=d;K=d;B=xa;ia=ma;$=pa;d=D;D=ua;U=d+D|0;d=p+ya|0;d=D>>>0>U>>>0?d+1|0:d;D=U;p=d;U=Ga(D,d,50);G=R;U=Ga(D,d,46)^U;G=R^G;X=(D^-1)&ia;e=ra+X|0;d=((d^-1)&$)+B|0;d=e>>>0<X>>>0?d+1|0:d;X=f&D;e=X+e|0;d=(i&p)+d|0;d=e>>>0<X>>>0?d+1|0:d;X=Ga(D,p,23)^U;e=X+e|0;d=(R^G)+d|0;d=e>>>0<X>>>0?d+1|0:d;X=e;e=M+e|0;d=d+K|0;d=e>>>0<X>>>0?d+1|0:d;K=e;B=e-330482897|0;e=B+ba|0;Q=d-((K>>>0<330482897)+1245643824|0)|0;d=Q+y|0;Z=e;d=e>>>0<B>>>0?d+1|0:d;e=Ga(e,d,36);y=R;ba=d;e=Ga(Z,d,30)^e;y=R^y;Y=N&Z;K=Y^(P^Z&ea);e=K+(Ga(Z,d,25)^e)|0;I=d&k;d=(R^y)+(I^(J^d&H))|0;F=e;X=e>>>0<K>>>0?d+1|0:d;K=w[b+24|0]|w[b+25|0]<<8|(w[b+26|0]<<16|w[b+27|0]<<24);y=K<<24|K<<8&16711680;e=w[b+28|0]|w[b+29|0]<<8|(w[b+30|0]<<16|w[b+31|0]<<24);d=e<<24|K>>>8;$=0;U=d&65280;d=e<<8|K>>>24;y=d&255|U|y;d=e;K=((d&255)<<24|K>>>8)&-16777216|((d&16777215)<<8|K>>>24)&16711680|(d>>>8&65280|d>>>24)|$;d=y;ia=d;$=d;P=pa;y=sa;B=y+B|0;d=Q+va|0;d=y>>>0>B>>>0?d+1|0:d;y=d;Q=Ga(B,d,50);m=R;Q=Ga(B,d,46)^Q;m=R^m;g=F;U=(B^-1)&f;e=ma+U|0;d=((d^-1)&i)+P|0;d=e>>>0<U>>>0?d+1|0:d;U=B&D;e=U+e|0;d=(p&y)+d|0;d=e>>>0<U>>>0?d+1|0:d;U=Ga(B,y,23)^Q;e=U+e|0;d=(R^m)+d|0;d=e>>>0<U>>>0?d+1|0:d;U=e;e=K+e|0;d=d+$|0;d=e>>>0<U>>>0?d+1|0:d;$=e;F=e-2121671748|0;e=g+F|0;m=d-(($>>>0<2121671748)+373957722|0)|0;d=m+X|0;j=e;d=e>>>0<F>>>0?d+1|0:d;e=Ga(e,d,36);X=R;Q=d;e=Ga(j,d,30)^e;X=R^X;r=j&Z;$=r^(Y^j&N);e=$+(Ga(j,d,25)^e)|0;fa=d&ba;d=(R^X)+(fa^(I^d&k))|0;aa=e;U=e>>>0<$>>>0?d+1|0:d;$=w[b+32|0]|w[b+33|0]<<8|(w[b+34|0]<<16|w[b+35|0]<<24);X=$<<24|$<<8&16711680;e=w[b+36|0]|w[b+37|0]<<8|(w[b+38|0]<<16|w[b+39|0]<<24);d=e<<24|$>>>8;J=0;G=d&65280;d=e<<8|$>>>24;P=J;J=d&255|G|X;d=e;X=((d&255)<<24|$>>>8)&-16777216|((d&16777215)<<8|$>>>24)&16711680|(d>>>8&65280|d>>>24)|P;d=J;$=d;P=d;J=f;G=i;i=na;f=i+F|0;d=m+qa|0;d=f>>>0<i>>>0?d+1|0:d;i=d;F=Ga(f,d,50);m=R;F=Ga(f,d,46)^F;m=R^m;e=J;J=(f^-1)&D;e=e+J|0;d=((d^-1)&p)+G|0;d=e>>>0<J>>>0?d+1|0:d;J=f&B;e=J+e|0;d=(i&y)+d|0;d=e>>>0<J>>>0?d+1|0:d;J=Ga(f,i,23)^F;e=J+e|0;d=(R^m)+d|0;d=e>>>0<J>>>0?d+1|0:d;J=e;e=X+e|0;d=d+P|0;d=e>>>0<J>>>0?d+1|0:d;e=e-213338824|0;d=d+961987163|0;d=e>>>0<4081628472?d+1|0:d;P=e;G=e;e=e+aa|0;J=d;d=d+U|0;n=e;d=e>>>0<G>>>0?d+1|0:d;e=Ga(e,d,36);U=R;Y=d;e=Ga(n,d,30)^e;U=R^U;aa=j&n;G=aa^(r^n&Z);e=G+(Ga(n,d,25)^e)|0;O=d&Q;d=(R^U)+(O^(fa^d&ba))|0;da=e;G=e>>>0<G>>>0?d+1|0:d;I=w[b+40|0]|w[b+41|0]<<8|(w[b+42|0]<<16|w[b+43|0]<<24);U=I<<24|I<<8&16711680;e=w[b+44|0]|w[b+45|0]<<8|(w[b+46|0]<<16|w[b+47|0]<<24);d=e<<24|I>>>8;m=0;r=d&65280;d=e<<8|I>>>24;U=d&255|r|U;d=e;fa=((d&255)<<24|I>>>8)&-16777216|((d&16777215)<<8|I>>>24)&16711680|(d>>>8&65280|d>>>24)|m;d=U;U=d;I=d;F=p;m=B;r=y;d=H+J|0;p=P+ea|0;d=p>>>0<P>>>0?d+1|0:d;H=p;p=d;P=Ga(H,d,50);J=R;P=Ga(H,d,46)^P;J=R^J;e=D;D=(H^-1)&m;e=e+D|0;d=((d^-1)&r)+F|0;d=e>>>0<D>>>0?d+1|0:d;D=f&H;e=D+e|0;d=(i&p)+d|0;d=e>>>0<D>>>0?d+1|0:d;D=Ga(H,p,23)^P;e=D+e|0;d=(R^J)+d|0;d=e>>>0<D>>>0?d+1|0:d;D=e;e=fa+e|0;d=d+I|0;d=e>>>0<D>>>0?d+1|0:d;e=e-1241133031|0;d=d+1508970993|0;d=e>>>0<3053834265?d+1|0:d;D=e;e=e+da|0;J=d;d=d+G|0;s=e;d=e>>>0<D>>>0?d+1|0:d;e=Ga(e,d,36);P=R;I=d;e=Ga(s,d,30)^e;P=R^P;da=n&s;G=da^(aa^j&s);e=G+(Ga(s,d,25)^e)|0;g=d&Y;d=(R^P)+(g^(O^d&Q))|0;O=e;G=e>>>0<G>>>0?d+1|0:d;F=w[b+48|0]|w[b+49|0]<<8|(w[b+50|0]<<16|w[b+51|0]<<24);P=F<<24|F<<8&16711680;e=w[b+52|0]|w[b+53|0]<<8|(w[b+54|0]<<16|w[b+55|0]<<24);d=e<<24|F>>>8;r=0;ea=d&65280;d=e<<8|F>>>24;m=r;P=d&255|ea|P;d=e;ea=((d&255)<<24|F>>>8)&-16777216|((d&16777215)<<8|F>>>24)&16711680|(d>>>8&65280|d>>>24)|m;d=P;P=d;F=d;m=y;d=k+J|0;y=D+N|0;d=y>>>0<D>>>0?d+1|0:d;k=y;y=d;D=Ga(k,d,50);J=R;D=Ga(k,d,46)^D;J=R^J;e=B;B=(k^-1)&f;e=e+B|0;d=((d^-1)&i)+m|0;d=e>>>0<B>>>0?d+1|0:d;B=k&H;e=B+e|0;d=(p&y)+d|0;d=e>>>0<B>>>0?d+1|0:d;D=Ga(k,y,23)^D;e=D+e|0;d=(R^J)+d|0;d=e>>>0<D>>>0?d+1|0:d;D=e;e=ea+e|0;d=d+F|0;d=e>>>0<D>>>0?d+1|0:d;D=e;B=e-1357295717|0;e=B+O|0;m=G;G=d-((D>>>0<1357295717)+1841331547|0)|0;d=m+G|0;F=e;d=e>>>0<B>>>0?d+1|0:d;e=Ga(e,d,36);D=R;r=d;e=Ga(F,d,30)^e;D=R^D;u=s&F;J=u^(da^n&F);e=J+(Ga(F,d,25)^e)|0;E=d&I;d=(R^D)+(E^(g^d&Y))|0;g=e;m=e>>>0<J>>>0?d+1|0:d;J=w[b+56|0]|w[b+57|0]<<8|(w[b+58|0]<<16|w[b+59|0]<<24);D=J<<24|J<<8&16711680;e=w[b+60|0]|w[b+61|0]<<8|(w[b+62|0]<<16|w[b+63|0]<<24);d=e<<24|J>>>8;N=0;O=d&65280;d=e<<8|J>>>24;aa=N;N=d&255|O|D;d=e;D=((d&255)<<24|J>>>8)&-16777216|((d&16777215)<<8|J>>>24)&16711680|(d>>>8&65280|d>>>24)|aa;d=N;J=d;N=i;d=G+ba|0;i=B+Z|0;d=i>>>0<B>>>0?d+1|0:d;G=i;i=d;B=Ga(G,d,50);ba=R;B=Ga(G,d,46)^B;ba=R^ba;e=f;f=(G^-1)&H;e=e+f|0;d=((d^-1)&p)+N|0;d=e>>>0<f>>>0?d+1|0:d;f=k&G;e=f+e|0;d=(i&y)+d|0;d=e>>>0<f>>>0?d+1|0:d;f=Ga(G,i,23)^B;e=f+e|0;d=(R^ba)+d|0;d=e>>>0<f>>>0?d+1|0:d;f=e;e=D+e|0;d=d+J|0;d=e>>>0<f>>>0?d+1|0:d;B=e;f=e-630357736|0;e=f+g|0;g=d-((B>>>0<630357736)+1424204074|0)|0;d=g+m|0;m=e;d=e>>>0<f>>>0?d+1|0:d;e=Ga(e,d,36);B=R;Z=d;e=Ga(m,d,30)^e;B=R^B;A=m&F;ba=A^(u^m&s);e=ba+(Ga(m,d,25)^e)|0;u=d&r;d=(R^B)+(u^(E^d&I))|0;E=e;aa=e>>>0<ba>>>0?d+1|0:d;ba=w[b+64|0]|w[b+65|0]<<8|(w[b+66|0]<<16|w[b+67|0]<<24);B=ba<<24|ba<<8&16711680;e=w[b+68|0]|w[b+69|0]<<8|(w[b+70|0]<<16|w[b+71|0]<<24);d=e<<24|ba>>>8;O=0;da=d&65280;d=e<<8|ba>>>24;N=O;O=d&255|da|B;d=e;B=((d&255)<<24|ba>>>8)&-16777216|((d&16777215)<<8|ba>>>24)&16711680|(d>>>8&65280|d>>>24)|N;d=O;ba=d;N=d;d=g+Q|0;j=f+j|0;d=j>>>0<f>>>0?d+1|0:d;Q=j;f=d;j=Ga(j,d,50);g=R;j=Ga(Q,d,46)^j;g=R^g;e=H;H=(Q^-1)&k;e=e+H|0;d=((d^-1)&y)+p|0;d=e>>>0<H>>>0?d+1|0:d;p=G&Q;e=p+e|0;d=(f&i)+d|0;d=e>>>0<p>>>0?d+1|0:d;p=Ga(Q,f,23)^j;e=p+e|0;d=(R^g)+d|0;d=e>>>0<p>>>0?d+1|0:d;p=e;e=B+e|0;d=d+N|0;d=e>>>0<p>>>0?d+1|0:d;p=e;H=e-1560083902|0;e=H+E|0;C=d-((p>>>0<1560083902)+670586215|0)|0;d=C+aa|0;j=e;d=e>>>0<H>>>0?d+1|0:d;e=Ga(e,d,36);p=R;E=d;e=Ga(j,d,30)^e;p=R^p;x=j&m;aa=x^(A^j&F);e=aa+(Ga(j,d,25)^e)|0;h=d&Z;d=(R^p)+(h^(u^d&r))|0;u=e;N=e>>>0<aa>>>0?d+1|0:d;aa=w[b+72|0]|w[b+73|0]<<8|(w[b+74|0]<<16|w[b+75|0]<<24);p=aa<<24|aa<<8&16711680;e=w[b+76|0]|w[b+77|0]<<8|(w[b+78|0]<<16|w[b+79|0]<<24);d=e<<24|aa>>>8;da=0;g=d&65280;d=e<<8|aa>>>24;O=da;p=d&255|g|p;d=e;aa=((d&255)<<24|aa>>>8)&-16777216|((d&16777215)<<8|aa>>>24)&16711680|(d>>>8&65280|d>>>24)|O;d=p;p=d;g=i;d=C+Y|0;n=n+H|0;d=n>>>0<H>>>0?d+1|0:d;Y=n;H=d;n=Ga(n,d,50);A=R;n=Ga(Y,d,46)^n;A=R^A;e=k;k=(Y^-1)&G;e=e+k|0;d=((d^-1)&g)+y|0;d=e>>>0<k>>>0?d+1|0:d;y=Q&Y;e=y+e|0;d=(f&H)+d|0;d=e>>>0<y>>>0?d+1|0:d;y=Ga(Y,H,23)^n;e=y+e|0;d=(R^A)+d|0;d=e>>>0<y>>>0?d+1|0:d;y=e;e=aa+e|0;d=d+p|0;d=e>>>0<y>>>0?d+1|0:d;e=e+1164996542|0;d=d+310598401|0;d=e>>>0<1164996542?d+1|0:d;k=e;e=e+u|0;O=d;d=d+N|0;g=e;d=e>>>0<k>>>0?d+1|0:d;e=Ga(e,d,36);y=R;A=d;e=Ga(g,d,30)^e;y=R^y;l=g&j;N=l^(x^g&m);e=N+(Ga(g,d,25)^e)|0;z=d&E;d=(R^y)+(z^(h^d&Z))|0;h=e;da=e>>>0<N>>>0?d+1|0:d;N=w[b+80|0]|w[b+81|0]<<8|(w[b+82|0]<<16|w[b+83|0]<<24);y=N<<24|N<<8&16711680;e=w[b+84|0]|w[b+85|0]<<8|(w[b+86|0]<<16|w[b+87|0]<<24);d=e<<24|N>>>8;u=0;C=d&65280;d=e<<8|N>>>24;n=u;y=d&255|C|y;d=e;N=((d&255)<<24|N>>>8)&-16777216|((d&16777215)<<8|N>>>24)&16711680|(d>>>8&65280|d>>>24)|n;d=y;y=d;n=d;u=i;C=Q;d=I+O|0;i=k+s|0;d=i>>>0<k>>>0?d+1|0:d;I=i;i=d;k=Ga(I,d,50);O=R;k=Ga(I,d,46)^k;O=R^O;e=G;G=(I^-1)&C;e=e+G|0;d=((d^-1)&f)+u|0;d=e>>>0<G>>>0?d+1|0:d;G=I&Y;e=G+e|0;d=(i&H)+d|0;d=e>>>0<G>>>0?d+1|0:d;k=Ga(I,i,23)^k;e=k+e|0;d=(R^O)+d|0;d=e>>>0<k>>>0?d+1|0:d;k=e;e=N+e|0;d=d+n|0;d=e>>>0<k>>>0?d+1|0:d;e=e+1323610764|0;d=d+607225278|0;d=e>>>0<1323610764?d+1|0:d;s=e;e=e+h|0;k=d;d=d+da|0;ca=e;d=e>>>0<s>>>0?d+1|0:d;e=Ga(e,d,36);G=R;x=d;e=Ga(ca,d,30)^e;G=R^G;h=g&ca;O=h^(l^j&ca);e=O+(Ga(ca,d,25)^e)|0;l=d&A;d=(R^G)+(l^(z^d&E))|0;z=e;da=e>>>0<O>>>0?d+1|0:d;O=w[b+88|0]|w[b+89|0]<<8|(w[b+90|0]<<16|w[b+91|0]<<24);G=O<<24|O<<8&16711680;e=w[b+92|0]|w[b+93|0]<<8|(w[b+94|0]<<16|w[b+95|0]<<24);d=e<<24|O>>>8;u=0;C=d&65280;d=e<<8|O>>>24;n=u;G=d&255|C|G;d=e;O=((d&255)<<24|O>>>8)&-16777216|((d&16777215)<<8|O>>>24)&16711680|(d>>>8&65280|d>>>24)|n;d=G;G=d;n=d;u=Y;C=H;d=k+r|0;s=s+F|0;d=s>>>0<F>>>0?d+1|0:d;F=s;k=d;r=Ga(s,d,50);s=R;r=Ga(F,d,46)^r;s=R^s;o=z;e=Q;Q=(F^-1)&u;e=e+Q|0;d=((d^-1)&C)+f|0;d=e>>>0<Q>>>0?d+1|0:d;f=F&I;e=f+e|0;d=(i&k)+d|0;d=e>>>0<f>>>0?d+1|0:d;f=Ga(F,k,23)^r;e=f+e|0;d=(R^s)+d|0;d=e>>>0<f>>>0?d+1|0:d;f=e;e=O+e|0;d=d+n|0;d=e>>>0<f>>>0?d+1|0:d;e=e-704662302|0;d=d+1426881987|0;d=e>>>0<3590304994?d+1|0:d;z=e;e=o+e|0;f=d;d=d+da|0;n=e;d=e>>>0<z>>>0?d+1|0:d;e=Ga(e,d,36);Q=R;s=d;e=Ga(n,d,30)^e;Q=R^Q;q=n&ca;r=q^(h^g&n);e=r+(Ga(n,d,25)^e)|0;t=d&x;d=(R^Q)+(t^(l^d&A))|0;o=e;u=e>>>0<r>>>0?d+1|0:d;r=w[b+96|0]|w[b+97|0]<<8|(w[b+98|0]<<16|w[b+99|0]<<24);Q=r<<24|r<<8&16711680;e=w[b+100|0]|w[b+101|0]<<8|(w[b+102|0]<<16|w[b+103|0]<<24);d=e<<24|r>>>8;C=0;h=d&65280;d=e<<8|r>>>24;Q=d&255|h|Q;d=e;da=((d&255)<<24|r>>>8)&-16777216|((d&16777215)<<8|r>>>24)&16711680|(d>>>8&65280|d>>>24)|C;d=Q;Q=d;C=d;h=I;d=f+Z|0;r=m+z|0;d=r>>>0<m>>>0?d+1|0:d;m=d;f=Ga(r,d,50);Z=R;f=Ga(r,d,46)^f;Z=R^Z;l=o;e=Y;Y=(r^-1)&h;e=e+Y|0;d=((d^-1)&i)+H|0;d=e>>>0<Y>>>0?d+1|0:d;H=r&F;e=H+e|0;d=(k&m)+d|0;d=e>>>0<H>>>0?d+1|0:d;f=Ga(r,m,23)^f;e=f+e|0;d=(R^Z)+d|0;d=e>>>0<f>>>0?d+1|0:d;f=e;e=da+e|0;d=d+C|0;d=e>>>0<f>>>0?d+1|0:d;e=e-226784913|0;d=d+1925078388|0;d=e>>>0<4068182383?d+1|0:d;o=e;e=l+e|0;f=d;d=d+u|0;S=e;d=e>>>0<o>>>0?d+1|0:d;e=Ga(e,d,36);H=R;u=d;e=Ga(S,d,30)^e;H=R^H;V=n&S;Y=V^(q^S&ca);e=Y+(Ga(S,d,25)^e)|0;q=d&s;d=(R^H)+(q^(t^d&x))|0;t=e;H=e>>>0<Y>>>0?d+1|0:d;Z=w[b+104|0]|w[b+105|0]<<8|(w[b+106|0]<<16|w[b+107|0]<<24);Y=Z<<24|Z<<8&16711680;e=w[b+108|0]|w[b+109|0]<<8|(w[b+110|0]<<16|w[b+111|0]<<24);d=e<<24|Z>>>8;h=0;l=d&65280;d=e<<8|Z>>>24;Y=d&255|l|Y;d=e;Z=((d&255)<<24|Z>>>8)&-16777216|((d&16777215)<<8|Z>>>24)&16711680|(d>>>8&65280|d>>>24)|h;d=Y;Y=d;C=d;h=i;l=F;d=f+E|0;i=j+o|0;d=i>>>0<j>>>0?d+1|0:d;f=i;i=d;j=Ga(f,d,50);E=R;j=Ga(f,d,46)^j;E=R^E;e=I;I=(f^-1)&l;e=e+I|0;d=((d^-1)&k)+h|0;d=e>>>0<I>>>0?d+1|0:d;I=f&r;e=I+e|0;d=(i&m)+d|0;d=e>>>0<I>>>0?d+1|0:d;I=Ga(f,i,23)^j;e=I+e|0;d=(R^E)+d|0;d=e>>>0<I>>>0?d+1|0:d;I=e;e=Z+e|0;d=d+C|0;d=e>>>0<I>>>0?d+1|0:d;I=e;j=e+991336113|0;e=j+t|0;C=d-((I>>>0<3303631183)+2132889089|0)|0;d=C+H|0;_=e;d=e>>>0<j>>>0?d+1|0:d;e=Ga(e,d,36);H=R;E=d;e=Ga(_,d,30)^e;H=R^H;t=S&_;I=t^(V^n&_);e=I+(Ga(_,d,25)^e)|0;o=d&u;d=(R^H)+(o^(q^d&s))|0;V=e;h=e>>>0<I>>>0?d+1|0:d;I=w[b+112|0]|w[b+113|0]<<8|(w[b+114|0]<<16|w[b+115|0]<<24);H=I<<24|I<<8&16711680;e=w[b+116|0]|w[b+117|0]<<8|(w[b+118|0]<<16|w[b+119|0]<<24);d=e<<24|I>>>8;z=0;q=d&65280;d=e<<8|I>>>24;l=z;z=d&255|q|H;d=e;H=((d&255)<<24|I>>>8)&-16777216|((d&16777215)<<8|I>>>24)&16711680|(d>>>8&65280|d>>>24)|l;d=z;I=d;l=d;d=A+C|0;j=g+j|0;d=j>>>0<g>>>0?d+1|0:d;C=j;j=d;g=Ga(C,d,50);A=R;g=Ga(C,d,46)^g;A=R^A;e=F;F=(C^-1)&r;e=e+F|0;d=((d^-1)&m)+k|0;d=e>>>0<F>>>0?d+1|0:d;k=f&C;e=k+e|0;d=(j&i)+d|0;d=e>>>0<k>>>0?d+1|0:d;k=Ga(C,j,23)^g;e=k+e|0;d=(R^A)+d|0;d=e>>>0<k>>>0?d+1|0:d;k=e;e=H+e|0;d=d+l|0;d=e>>>0<k>>>0?d+1|0:d;F=e;k=e+633803317|0;g=k;e=g+V|0;T=d-((F>>>0<3661163979)+1680079192|0)|0;d=T+h|0;V=e;d=e>>>0<g>>>0?d+1|0:d;e=Ga(e,d,36);F=R;A=d;e=Ga(V,d,30)^e;F=R^F;g=t^(S^_)&V;e=g+(Ga(V,d,25)^e)|0;d=(R^F)+(o^(u^E)&d)|0;wa=e;g=e>>>0<g>>>0?d+1|0:d;h=w[b+120|0]|w[b+121|0]<<8|(w[b+122|0]<<16|w[b+123|0]<<24);F=h<<24|h<<8&16711680;e=w[b+124|0]|w[b+125|0]<<8|(w[b+126|0]<<16|w[b+127|0]<<24);d=e<<24|h>>>8;z=0;q=d&65280;d=e<<8|h>>>24;l=z;F=d&255|q|F;d=e;e=((d&255)<<24|h>>>8)&-16777216|((d&16777215)<<8|h>>>24)&16711680|(d>>>8&65280|d>>>24)|l;d=F;F=d;l=d;z=r;q=m;d=x+T|0;m=k+ca|0;d=m>>>0<k>>>0?d+1|0:d;r=m;m=d;k=Ga(r,d,50);x=R;ca=Ga(r,d,46)^k;x=R^x;h=(r^-1)&f;k=h+z|0;d=((d^-1)&i)+q|0;d=h>>>0>k>>>0?d+1|0:d;h=r&C;k=h+k|0;d=(j&m)+d|0;d=h>>>0>k>>>0?d+1|0:d;h=Ga(r,m,23)^ca;k=h+k|0;d=(R^x)+d|0;d=h>>>0>k>>>0?d+1|0:d;x=k;k=e+k|0;d=d+l|0;d=k>>>0<x>>>0?d+1|0:d;x=k;h=k-815192428|0;k=h+wa|0;x=d-((x>>>0<815192428)+1046744715|0)|0;d=x+g|0;q=k;g=k>>>0<h>>>0?d+1|0:d;d=x+s|0;k=h+n|0;d=k>>>0<n>>>0?d+1|0:d;n=k;k=d;wa=16;while(1){ca=wa<<3;d=ca+1280|0;h=v[d>>2];l=v[d+4>>2];d=Ga(H,I,45);x=R;d=Ga(H,I,3)^d;z=R^x;s=d;x=I;d=x>>>6|0;t=s^((x&63)<<26|H>>>6);x=t+aa|0;d=(d^z)+p|0;d=x>>>0<t>>>0?d+1|0:d;x=x+la|0;d=d+ja|0;ja=x;la=x>>>0<la>>>0?d+1|0:d;d=Ga(ga,oa,63);x=R;z=ja;s=Ga(ga,oa,56)^d;ja=oa;d=ja>>>7|0;s=s^((ja&127)<<25|ga>>>7);ja=z+s|0;d=(d^(R^x))+la|0;la=ja;d=s>>>0>ja>>>0?d+1|0:d;ja=d;x=d;d=Ga(n,k,50);s=R;d=Ga(n,k,46)^d;z=R^s;o=h;h=Ga(n,k,23)^d;s=h+(n&r)|0;d=(k&m)+(R^z)|0;d=h>>>0>s>>>0?d+1|0:d;s=f+s|0;d=d+i|0;d=f>>>0>s>>>0?d+1|0:d;f=(n^-1)&C;i=f+s|0;d=((k^-1)&j)+d|0;d=f>>>0>i>>>0?d+1|0:d;f=i;i=f+la|0;d=d+x|0;d=f>>>0>i>>>0?d+1|0:d;f=i;i=o+f|0;d=d+l|0;d=f>>>0>i>>>0?d+1|0:d;s=d;f=d;d=Ga(q,g,36);x=R;d=Ga(q,g,30)^d;h=R^x;z=Ga(q,g,25)^d;l=q&V;x=z+(V&_^(l^q&_))|0;o=g&A;d=(E&A^(o^g&E))+(R^h)|0;d=x>>>0<z>>>0?d+1|0:d;h=x;x=h+i|0;d=d+f|0;t=x;d=h>>>0>x>>>0?d+1|0:d;f=Ga(x,d,36);h=R;x=d;f=Ga(t,d,30)^f;h=R^h;z=q&t;l=z^(l^t&V);f=l+(Ga(t,d,25)^f)|0;T=d&g;d=(R^h)+(T^(o^d&A))|0;o=f;h=f>>>0<l>>>0?d+1|0:d;d=(ca|8)+1280|0;W=v[d>>2];L=v[d+4>>2];d=Ga(e,F,45);f=R;d=Ga(e,F,3)^d;ka=R^f;l=d;f=F;d=f>>>6|0;l=l^((f&63)<<26|e>>>6);f=l+N|0;d=(d^ka)+y|0;d=f>>>0<l>>>0?d+1|0:d;f=f+ga|0;d=d+oa|0;d=f>>>0<ga>>>0?d+1|0:d;ga=d;d=Ga(M,ha,63);oa=R;ka=f;l=Ga(M,ha,56)^d;f=ha;d=f>>>7|0;l=l^((f&127)<<25|M>>>7);f=ka+l|0;d=(d^(R^oa))+ga|0;d=f>>>0<l>>>0?d+1|0:d;ga=f;f=C+f|0;oa=d;d=j+d|0;d=f>>>0<C>>>0?d+1|0:d;j=f;f=f+W|0;d=d+L|0;W=f;j=f>>>0<j>>>0?d+1|0:d;d=s+u|0;f=i+S|0;d=f>>>0<i>>>0?d+1|0:d;i=d;u=Ga(f,d,50);s=R;S=Ga(f,d,46)^u;s=R^s;C=(f^-1)&r;u=C+W|0;d=((d^-1)&m)+j|0;d=u>>>0<C>>>0?d+1|0:d;j=u;u=f&n;j=j+u|0;d=(i&k)+d|0;d=j>>>0<u>>>0?d+1|0:d;u=Ga(f,i,23)^S;j=u+j|0;d=(R^s)+d|0;d=j>>>0<u>>>0?d+1|0:d;u=j+o|0;C=d;d=d+h|0;o=u;d=j>>>0>u>>>0?d+1|0:d;u=Ga(u,d,36);s=R;h=u;u=d;h=h^Ga(o,d,30);l=R^s;s=Ga(o,d,25)^h;W=t&o;h=W^(z^q&o);s=s+h|0;L=d&x;d=(R^l)+(L^(T^d&g))|0;S=s;s=h>>>0>s>>>0?d+1|0:d;d=(ca|16)+1280|0;l=v[d>>2];z=v[d+4>>2];d=G+ha|0;h=O+M|0;d=h>>>0<M>>>0?d+1|0:d;M=h;ha=d;d=Ga(K,ia,63);h=R;ka=M;T=Ga(K,ia,56)^d;M=ia;d=M>>>7|0;T=T^((M&127)<<25|K>>>7);M=ka+T|0;d=(d^(R^h))+ha|0;d=M>>>0<T>>>0?d+1|0:d;ha=d;d=Ga(la,ja,45);h=R;T=l;ka=M;l=Ga(la,ja,3)^d;M=ja;d=M>>>6|0;l=l^((M&63)<<26|la>>>6);M=ka+l|0;d=(d^(R^h))+ha|0;d=l>>>0>M>>>0?d+1|0:d;h=r+M|0;ha=d;d=m+d|0;d=h>>>0<r>>>0?d+1|0:d;m=T+h|0;d=d+z|0;T=m;h=m>>>0<h>>>0?d+1|0:d;d=E+C|0;m=j+_|0;d=m>>>0<j>>>0?d+1|0:d;r=d;j=Ga(m,d,50);E=R;C=Ga(m,d,46)^j;E=R^E;l=(m^-1)&n;j=l+T|0;d=((d^-1)&k)+h|0;d=j>>>0<l>>>0?d+1|0:d;h=f&m;j=h+j|0;d=(i&r)+d|0;d=j>>>0<h>>>0?d+1|0:d;C=Ga(m,r,23)^C;j=C+j|0;d=(R^E)+d|0;d=j>>>0<C>>>0?d+1|0:d;E=j+S|0;C=d;d=d+s|0;S=E;d=j>>>0>E>>>0?d+1|0:d;E=Ga(E,d,36);s=R;h=E;E=d;h=h^Ga(S,d,30);l=R^s;s=Ga(S,d,25)^h;z=o&S;h=z^(W^t&S);s=s+h|0;T=d&u;d=(R^l)+(T^(L^d&x))|0;_=s;s=h>>>0>s>>>0?d+1|0:d;d=(ca|24)+1280|0;l=v[d>>2];W=v[d+4>>2];d=Q+ia|0;h=K+da|0;d=h>>>0<K>>>0?d+1|0:d;K=h;ia=d;d=Ga(X,$,63);h=R;ka=K;L=Ga(X,$,56)^d;K=$;d=K>>>7|0;L=L^((K&127)<<25|X>>>7);K=ka+L|0;d=(d^(R^h))+ia|0;d=K>>>0<L>>>0?d+1|0:d;ia=d;d=Ga(ga,oa,45);h=R;L=l;ka=K;l=Ga(ga,oa,3)^d;K=oa;d=K>>>6|0;l=l^((K&63)<<26|ga>>>6);K=ka+l|0;d=(d^(R^h))+ia|0;d=l>>>0>K>>>0?d+1|0:d;h=n+K|0;ia=d;d=k+d|0;d=h>>>0<n>>>0?d+1|0:d;k=L+h|0;d=d+W|0;W=k;n=k>>>0<h>>>0?d+1|0:d;d=A+C|0;k=j+V|0;d=k>>>0<j>>>0?d+1|0:d;j=d;A=Ga(k,d,50);C=R;V=Ga(k,d,46)^A;C=R^C;h=(k^-1)&f;A=h+W|0;d=((d^-1)&i)+n|0;d=h>>>0>A>>>0?d+1|0:d;h=A;A=k&m;n=h+A|0;d=(j&r)+d|0;d=n>>>0<A>>>0?d+1|0:d;A=Ga(k,j,23)^V;n=A+n|0;d=(R^C)+d|0;d=n>>>0<A>>>0?d+1|0:d;h=n;A=h+_|0;C=d;d=d+s|0;_=A;d=h>>>0>A>>>0?d+1|0:d;A=Ga(A,d,36);s=R;h=A;A=d;h=h^Ga(_,d,30);l=R^s;s=Ga(_,d,25)^h;W=S&_;h=W^(z^o&_);s=s+h|0;z=d&E;d=(R^l)+(z^(T^d&u))|0;V=s;s=h>>>0>s>>>0?d+1|0:d;d=(ca|32)+1280|0;l=v[d>>2];T=v[d+4>>2];h=i;d=Y+$|0;i=X+Z|0;d=i>>>0<X>>>0?d+1|0:d;X=d;d=Ga(fa,U,63);$=R;ka=i;L=Ga(fa,U,56)^d;i=U;d=i>>>7|0;L=L^((i&127)<<25|fa>>>7);i=ka+L|0;d=(d^(R^$))+X|0;d=i>>>0<L>>>0?d+1|0:d;X=d;d=Ga(M,ha,45);$=R;L=f;ka=i;f=Ga(M,ha,3)^d;i=ha;d=i>>>6|0;f=f^((i&63)<<26|M>>>6);i=ka+f|0;d=(d^(R^$))+X|0;d=f>>>0>i>>>0?d+1|0:d;X=i;f=i;i=L+f|0;$=d;d=d+h|0;d=f>>>0>i>>>0?d+1|0:d;f=i;i=f+l|0;d=d+T|0;T=i;f=f>>>0>i>>>0?d+1|0:d;h=m;l=r;d=g+C|0;i=n+q|0;d=i>>>0<n>>>0?d+1|0:d;g=d;n=Ga(i,d,50);C=R;q=Ga(i,d,46)^n;C=R^C;h=(i^-1)&h;n=h+T|0;d=((d^-1)&l)+f|0;d=h>>>0>n>>>0?d+1|0:d;f=n;n=i&k;f=f+n|0;d=(g&j)+d|0;d=f>>>0<n>>>0?d+1|0:d;n=Ga(i,g,23)^q;f=n+f|0;d=(R^C)+d|0;d=f>>>0<n>>>0?d+1|0:d;n=f;f=f+V|0;h=s;s=d;d=h+d|0;V=f;d=f>>>0<n>>>0?d+1|0:d;f=Ga(f,d,36);h=R;C=d;f=Ga(V,d,30)^f;h=R^h;q=V&_;l=q^(W^S&V);f=l+(Ga(V,d,25)^f)|0;W=d&A;d=(R^h)+(W^(z^d&E))|0;T=f;h=f>>>0<l>>>0?d+1|0:d;f=H;fa=f+fa|0;d=I+U|0;d=f>>>0>fa>>>0?d+1|0:d;f=fa;U=d;d=Ga(ea,P,63);fa=R;z=f;l=Ga(ea,P,56)^d;f=P;d=f>>>7|0;l=l^((f&127)<<25|ea>>>7);f=z+l|0;d=(d^(R^fa))+U|0;d=f>>>0<l>>>0?d+1|0:d;U=d;d=Ga(K,ia,45);fa=R;z=f;l=Ga(K,ia,3)^d;f=ia;d=f>>>6|0;l=l^((f&63)<<26|K>>>6);f=z+l|0;d=(d^(R^fa))+U|0;d=f>>>0<l>>>0?d+1|0:d;fa=f;l=(ca|40)+1280|0;z=v[l>>2];f=f+z|0;U=d;d=d+v[l+4>>2]|0;d=f>>>0<z>>>0?d+1|0:d;f=f+m|0;d=d+r|0;L=f;r=f>>>0<m>>>0?d+1|0:d;d=x+s|0;f=n+t|0;d=f>>>0<n>>>0?d+1|0:d;m=d;n=Ga(f,d,50);x=R;s=Ga(f,d,46)^n;x=R^x;l=(f^-1)&k;n=l+L|0;d=((d^-1)&j)+r|0;d=n>>>0<l>>>0?d+1|0:d;l=n;n=f&i;r=l+n|0;d=(g&m)+d|0;d=n>>>0>r>>>0?d+1|0:d;n=Ga(f,m,23)^s;r=n+r|0;d=(R^x)+d|0;d=n>>>0>r>>>0?d+1|0:d;x=r+T|0;n=d;d=d+h|0;T=x;d=x>>>0<r>>>0?d+1|0:d;x=Ga(x,d,36);s=R;h=x;x=d;h=h^Ga(T,d,30);l=R^s;s=Ga(T,d,25)^h;z=V&T;h=z^(q^_&T);s=s+h|0;q=d&C;d=(R^l)+(q^(W^d&A))|0;t=s;s=h>>>0>t>>>0?d+1|0:d;d=F+P|0;h=e+ea|0;P=h;ea=e>>>0>h>>>0?d+1|0:d;d=Ga(D,J,63);h=R;l=Ga(D,J,56)^d;d=J>>>7|0;l=l^((J&127)<<25|D>>>7);P=l+P|0;d=(d^(R^h))+ea|0;d=l>>>0>P>>>0?d+1|0:d;ea=d;d=Ga(X,$,45);h=R;L=P;l=Ga(X,$,3)^d;P=$;d=P>>>6|0;l=l^((P&63)<<26|X>>>6);P=L+l|0;d=(d^(R^h))+ea|0;d=l>>>0>P>>>0?d+1|0:d;ea=P;l=(ca|48)+1280|0;W=v[l>>2];h=P+W|0;P=d;d=d+v[l+4>>2]|0;d=h>>>0<W>>>0?d+1|0:d;h=h+k|0;d=d+j|0;W=h;j=h>>>0<k>>>0?d+1|0:d;d=n+u|0;k=r+o|0;d=k>>>0<r>>>0?d+1|0:d;r=d;n=Ga(k,d,50);u=R;o=Ga(k,d,46)^n;u=R^u;h=(k^-1)&i;n=h+W|0;d=((d^-1)&g)+j|0;d=h>>>0>n>>>0?d+1|0:d;j=n;n=f&k;j=j+n|0;d=(m&r)+d|0;d=j>>>0<n>>>0?d+1|0:d;n=Ga(k,r,23)^o;j=n+j|0;d=(R^u)+d|0;d=j>>>0<n>>>0?d+1|0:d;u=j;n=j;j=j+t|0;h=d;d=d+s|0;d=j>>>0<n>>>0?d+1|0:d;n=j;j=Ga(j,d,36);l=R;s=d;j=Ga(n,d,30)^j;l=R^l;t=n&T;z=t^(z^n&V);j=z+(Ga(n,d,25)^j)|0;o=d&x;d=(R^l)+(o^(q^d&C))|0;W=j;l=j>>>0<z>>>0?d+1|0:d;d=Ga(B,ba,63);j=R;d=Ga(B,ba,56)^d;z=R^j;q=d;j=ba;d=j>>>7|0;j=D+(q^((j&127)<<25|B>>>7))|0;d=J+(d^z)|0;d=j>>>0<D>>>0?d+1|0:d;D=la;J=D+j|0;d=d+ja|0;d=D>>>0>J>>>0?d+1|0:d;D=J;J=d;d=Ga(fa,U,45);j=R;q=D;z=Ga(fa,U,3)^d;D=U;d=D>>>6|0;z=z^((D&63)<<26|fa>>>6);D=q+z|0;d=(d^(R^j))+J|0;d=z>>>0>D>>>0?d+1|0:d;z=(ca|56)+1280|0;q=v[z>>2];j=D+q|0;J=d;d=d+v[z+4>>2]|0;d=j>>>0<q>>>0?d+1|0:d;j=j+i|0;d=d+g|0;L=j;g=j>>>0<i>>>0?d+1|0:d;d=h+E|0;i=u+S|0;d=i>>>0<u>>>0?d+1|0:d;j=d;u=Ga(i,d,50);E=R;h=Ga(i,d,46)^u;E=R^E;z=(i^-1)&f;u=z+L|0;d=((d^-1)&m)+g|0;d=u>>>0<z>>>0?d+1|0:d;g=u;u=i&k;g=g+u|0;d=(j&r)+d|0;d=g>>>0<u>>>0?d+1|0:d;u=Ga(i,j,23)^h;g=u+g|0;d=(R^E)+d|0;d=g>>>0<u>>>0?d+1|0:d;E=g;u=g;g=g+W|0;h=l;l=d;d=h+d|0;d=g>>>0<u>>>0?d+1|0:d;u=g;g=Ga(g,d,36);z=R;h=d;g=Ga(u,d,30)^g;z=R^z;S=n&u;q=S^(t^u&T);g=q+(Ga(u,d,25)^g)|0;W=d&s;d=(R^z)+(W^(o^d&x))|0;o=g;z=g>>>0<q>>>0?d+1|0:d;d=Ga(aa,p,63);g=R;d=Ga(aa,p,56)^d;q=R^g;t=d;g=p;d=g>>>7|0;g=B+(t^((g&127)<<25|aa>>>7))|0;d=ba+(d^q)|0;d=g>>>0<B>>>0?d+1|0:d;B=ga;ba=B+g|0;d=d+oa|0;d=B>>>0>ba>>>0?d+1|0:d;B=ba;ba=d;d=Ga(ea,P,45);g=R;t=B;q=Ga(ea,P,3)^d;B=P;d=B>>>6|0;q=q^((B&63)<<26|ea>>>6);B=t+q|0;d=(d^(R^g))+ba|0;d=q>>>0>B>>>0?d+1|0:d;q=(ca|64)+1280|0;t=v[q>>2];g=B+t|0;ba=d;d=d+v[q+4>>2]|0;d=g>>>0<t>>>0?d+1|0:d;g=f+g|0;d=d+m|0;L=g;g=f>>>0>g>>>0?d+1|0:d;d=l+A|0;f=E+_|0;d=f>>>0<E>>>0?d+1|0:d;m=d;E=Ga(f,d,50);A=R;l=Ga(f,d,46)^E;A=R^A;q=(f^-1)&k;E=q+L|0;d=((d^-1)&r)+g|0;d=q>>>0>E>>>0?d+1|0:d;g=E;E=f&i;g=g+E|0;d=(j&m)+d|0;d=g>>>0<E>>>0?d+1|0:d;E=Ga(f,m,23)^l;g=E+g|0;d=(R^A)+d|0;d=g>>>0<E>>>0?d+1|0:d;A=g;g=g+o|0;l=z;z=d;d=l+d|0;d=g>>>0<A>>>0?d+1|0:d;E=g;g=Ga(g,d,36);q=R;l=d;g=Ga(E,d,30)^g;q=R^q;_=u&E;t=_^(S^n&E);g=t+(Ga(E,d,25)^g)|0;S=d&h;d=(R^q)+(S^(W^d&s))|0;W=g;q=g>>>0<t>>>0?d+1|0:d;d=Ga(N,y,63);g=R;d=Ga(N,y,56)^d;t=R^g;o=d;g=y;d=g>>>7|0;g=aa+(o^((g&127)<<25|N>>>7))|0;d=p+(d^t)|0;d=g>>>0<aa>>>0?d+1|0:d;p=M;aa=p+g|0;d=d+ha|0;d=p>>>0>aa>>>0?d+1|0:d;p=aa;aa=d;d=Ga(D,J,45);g=R;o=p;t=Ga(D,J,3)^d;p=J;d=p>>>6|0;t=t^((p&63)<<26|D>>>6);p=o+t|0;d=(d^(R^g))+aa|0;d=p>>>0<t>>>0?d+1|0:d;aa=p;t=(ca|72)+1280|0;o=v[t>>2];g=p+o|0;p=d;d=d+v[t+4>>2]|0;d=g>>>0<o>>>0?d+1|0:d;g=g+k|0;d=d+r|0;L=g;g=g>>>0<k>>>0?d+1|0:d;d=z+C|0;k=A+V|0;d=k>>>0<A>>>0?d+1|0:d;r=d;A=Ga(k,d,50);C=R;z=Ga(k,d,46)^A;C=R^C;t=(k^-1)&i;A=t+L|0;d=((d^-1)&j)+g|0;d=t>>>0>A>>>0?d+1|0:d;g=A;A=f&k;g=g+A|0;d=(m&r)+d|0;d=g>>>0<A>>>0?d+1|0:d;A=Ga(k,r,23)^z;g=A+g|0;d=(R^C)+d|0;d=g>>>0<A>>>0?d+1|0:d;z=g;A=g;g=g+W|0;C=q;q=d;d=C+d|0;d=g>>>0<A>>>0?d+1|0:d;A=g;g=Ga(g,d,36);t=R;C=d;g=Ga(A,d,30)^g;t=R^t;V=E&A;o=V^(_^u&A);g=o+(Ga(A,d,25)^g)|0;_=d&l;d=(R^t)+(_^(S^d&h))|0;W=g;t=g>>>0<o>>>0?d+1|0:d;d=Ga(O,G,63);g=R;d=Ga(O,G,56)^d;o=R^g;L=d;g=G;d=g>>>7|0;g=N+(L^((g&127)<<25|O>>>7))|0;d=y+(d^o)|0;d=g>>>0<N>>>0?d+1|0:d;y=K;N=y+g|0;d=d+ia|0;d=y>>>0>N>>>0?d+1|0:d;y=N;N=d;d=Ga(B,ba,45);g=R;L=y;o=Ga(B,ba,3)^d;y=ba;d=y>>>6|0;o=o^((y&63)<<26|B>>>6);y=L+o|0;d=(d^(R^g))+N|0;d=y>>>0<o>>>0?d+1|0:d;N=y;o=(ca|80)+1280|0;S=v[o>>2];g=y+S|0;y=d;d=d+v[o+4>>2]|0;d=g>>>0<S>>>0?d+1|0:d;g=g+i|0;d=d+j|0;L=g;g=g>>>0<i>>>0?d+1|0:d;d=x+q|0;i=z+T|0;d=i>>>0<z>>>0?d+1|0:d;j=d;x=Ga(i,d,50);z=R;q=Ga(i,d,46)^x;z=R^z;o=(i^-1)&f;x=o+L|0;d=((d^-1)&m)+g|0;d=x>>>0<o>>>0?d+1|0:d;g=x;x=i&k;g=g+x|0;d=(j&r)+d|0;d=g>>>0<x>>>0?d+1|0:d;x=Ga(i,j,23)^q;g=x+g|0;d=(R^z)+d|0;d=g>>>0<x>>>0?d+1|0:d;T=g;x=g;g=g+W|0;q=d;d=d+t|0;d=g>>>0<x>>>0?d+1|0:d;x=g;g=Ga(g,d,36);t=R;z=d;g=Ga(x,d,30)^g;t=R^t;W=x&A;o=W^(V^x&E);g=o+(Ga(x,d,25)^g)|0;V=d&C;d=(R^t)+(V^(_^d&l))|0;_=g;t=g>>>0<o>>>0?d+1|0:d;d=Ga(da,Q,63);g=R;d=Ga(da,Q,56)^d;o=R^g;L=d;g=Q;d=g>>>7|0;g=O+(L^((g&127)<<25|da>>>7))|0;d=G+(d^o)|0;d=g>>>0<O>>>0?d+1|0:d;G=X;O=G+g|0;d=d+$|0;d=G>>>0>O>>>0?d+1|0:d;G=O;O=d;d=Ga(aa,p,45);g=R;o=Ga(aa,p,3)^d;d=p>>>6|0;o=o^((p&63)<<26|aa>>>6);G=o+G|0;d=(d^(R^g))+O|0;d=o>>>0>G>>>0?d+1|0:d;O=G;o=(ca|88)+1280|0;S=v[o>>2];g=G+S|0;G=d;d=d+v[o+4>>2]|0;d=g>>>0<S>>>0?d+1|0:d;g=f+g|0;d=d+m|0;L=g;f=f>>>0>g>>>0?d+1|0:d;d=s+q|0;m=n+T|0;d=m>>>0<n>>>0?d+1|0:d;g=d;n=Ga(m,d,50);s=R;q=Ga(m,d,46)^n;s=R^s;S=_;o=(m^-1)&k;n=o+L|0;d=((d^-1)&r)+f|0;d=n>>>0<o>>>0?d+1|0:d;f=n;n=i&m;f=f+n|0;d=(g&j)+d|0;d=f>>>0<n>>>0?d+1|0:d;n=Ga(m,g,23)^q;f=n+f|0;d=(R^s)+d|0;d=f>>>0<n>>>0?d+1|0:d;_=f;n=f;f=S+f|0;q=d;d=d+t|0;d=f>>>0<n>>>0?d+1|0:d;n=f;f=Ga(f,d,36);t=R;s=d;f=Ga(n,d,30)^f;t=R^t;T=n&x;o=T^(W^n&A);f=o+(Ga(n,d,25)^f)|0;W=d&z;d=(R^t)+(W^(V^d&C))|0;V=f;t=f>>>0<o>>>0?d+1|0:d;d=Ga(Z,Y,63);f=R;d=Ga(Z,Y,56)^d;o=R^f;L=d;f=Y;d=f>>>7|0;f=da+(L^((f&127)<<25|Z>>>7))|0;d=Q+(d^o)|0;d=f>>>0<da>>>0?d+1|0:d;o=f;f=fa;Q=o+f|0;d=d+U|0;d=f>>>0>Q>>>0?d+1|0:d;f=Q;Q=d;d=Ga(N,y,45);da=R;L=f;o=Ga(N,y,3)^d;f=y;d=f>>>6|0;o=o^((f&63)<<26|N>>>6);f=L+o|0;d=(d^(R^da))+Q|0;d=f>>>0<o>>>0?d+1|0:d;da=f;o=(ca|96)+1280|0;S=v[o>>2];f=f+S|0;Q=d;d=d+v[o+4>>2]|0;d=f>>>0<S>>>0?d+1|0:d;f=f+k|0;d=d+r|0;L=f;f=f>>>0<k>>>0?d+1|0:d;d=h+q|0;k=u+_|0;d=k>>>0<u>>>0?d+1|0:d;r=d;u=Ga(k,d,50);h=R;q=Ga(k,d,46)^u;h=R^h;o=(k^-1)&i;u=o+L|0;d=((d^-1)&j)+f|0;d=u>>>0<o>>>0?d+1|0:d;f=u;u=k&m;f=f+u|0;d=(g&r)+d|0;d=f>>>0<u>>>0?d+1|0:d;u=Ga(k,r,23)^q;f=u+f|0;d=(R^h)+d|0;d=f>>>0<u>>>0?d+1|0:d;_=f;u=f;f=f+V|0;h=d;d=d+t|0;S=f;d=f>>>0<u>>>0?d+1|0:d;f=Ga(f,d,36);q=R;u=d;f=Ga(S,d,30)^f;q=R^q;V=n&S;t=V^(T^x&S);f=t+(Ga(S,d,25)^f)|0;T=d&s;d=(R^q)+(T^(W^d&z))|0;W=f;q=f>>>0<t>>>0?d+1|0:d;d=Ga(H,I,63);f=R;d=Ga(H,I,56)^d;t=R^f;o=d;f=I;d=f>>>7|0;f=Z+(o^((f&127)<<25|H>>>7))|0;d=Y+(d^t)|0;d=f>>>0<Z>>>0?d+1|0:d;o=f;f=ea;Y=o+f|0;d=d+P|0;d=f>>>0>Y>>>0?d+1|0:d;f=Y;Y=d;d=Ga(O,G,45);Z=R;o=f;t=Ga(O,G,3)^d;f=G;d=f>>>6|0;t=t^((f&63)<<26|O>>>6);f=o+t|0;d=(d^(R^Z))+Y|0;d=f>>>0<t>>>0?d+1|0:d;Z=f;t=(ca|104)+1280|0;o=v[t>>2];f=f+o|0;Y=d;d=d+v[t+4>>2]|0;d=f>>>0<o>>>0?d+1|0:d;f=f+i|0;d=d+j|0;L=f;j=f>>>0<i>>>0?d+1|0:d;d=h+l|0;i=E+_|0;d=i>>>0<E>>>0?d+1|0:d;f=i;i=d;E=Ga(f,d,50);h=R;l=Ga(f,d,46)^E;h=R^h;t=(f^-1)&m;E=t+L|0;d=((d^-1)&g)+j|0;d=t>>>0>E>>>0?d+1|0:d;j=E;E=f&k;j=j+E|0;d=(i&r)+d|0;d=j>>>0<E>>>0?d+1|0:d;E=Ga(f,i,23)^l;j=E+j|0;d=(R^h)+d|0;d=j>>>0<E>>>0?d+1|0:d;t=j;j=j+W|0;h=d;d=d+q|0;_=j;d=j>>>0<t>>>0?d+1|0:d;j=Ga(j,d,36);l=R;E=d;j=Ga(_,d,30)^j;l=R^l;o=S&_;q=o^(V^n&_);j=q+(Ga(_,d,25)^j)|0;W=d&u;d=(R^l)+(W^(T^d&s))|0;V=j;l=j>>>0<q>>>0?d+1|0:d;d=Ga(e,F,63);j=R;d=Ga(e,F,56)^d;q=R^j;L=d;j=F;d=j>>>7|0;j=H+(L^((j&127)<<25|e>>>7))|0;d=I+(d^q)|0;d=j>>>0<H>>>0?d+1|0:d;I=D+j|0;d=d+J|0;d=D>>>0>I>>>0?d+1|0:d;H=I;I=d;d=Ga(da,Q,45);j=R;L=H;q=Ga(da,Q,3)^d;H=Q;d=H>>>6|0;q=q^((H&63)<<26|da>>>6);H=L+q|0;d=(d^(R^j))+I|0;d=q>>>0>H>>>0?d+1|0:d;q=(ca|112)+1280|0;T=v[q>>2];j=H+T|0;I=d;d=d+v[q+4>>2]|0;d=j>>>0<T>>>0?d+1|0:d;j=j+m|0;d=d+g|0;T=j;m=j>>>0<m>>>0?d+1|0:d;g=k;d=h+C|0;j=t+A|0;d=j>>>0<A>>>0?d+1|0:d;C=j;j=d;A=Ga(C,d,50);h=R;A=Ga(C,d,46)^A;h=R^h;t=(C^-1)&g;g=t+T|0;d=((d^-1)&r)+m|0;d=g>>>0<t>>>0?d+1|0:d;m=g;g=f&C;m=m+g|0;d=(j&i)+d|0;d=g>>>0>m>>>0?d+1|0:d;g=Ga(C,j,23)^A;m=g+m|0;d=(R^h)+d|0;d=g>>>0>m>>>0?d+1|0:d;t=m;A=m;m=m+V|0;g=d;d=d+l|0;V=m;d=m>>>0<A>>>0?d+1|0:d;m=Ga(m,d,36);h=R;A=d;m=Ga(V,d,30)^m;h=R^h;l=o^(S^_)&V;m=l+(Ga(V,d,25)^m)|0;d=(R^h)+(W^(u^E)&d)|0;o=m;h=m>>>0<l>>>0?d+1|0:d;d=Ga(la,ja,63);m=R;d=Ga(la,ja,56)^d;l=R^m;q=d;m=ja;d=m>>>7|0;m=e+(q^((m&127)<<25|la>>>7))|0;d=F+(d^l)|0;d=e>>>0>m>>>0?d+1|0:d;e=B;F=e+m|0;d=d+ba|0;d=e>>>0>F>>>0?d+1|0:d;e=F;F=d;d=Ga(Z,Y,45);m=R;q=e;l=Ga(Z,Y,3)^d;e=Y;d=e>>>6|0;l=l^((e&63)<<26|Z>>>6);e=q+l|0;d=(d^(R^m))+F|0;d=e>>>0<l>>>0?d+1|0:d;l=(ca|120)+1280|0;q=v[l>>2];m=e+q|0;F=d;d=d+v[l+4>>2]|0;d=m>>>0<q>>>0?d+1|0:d;m=k+m|0;d=d+r|0;ca=m;k=k>>>0>m>>>0?d+1|0:d;d=g+z|0;m=x+t|0;d=m>>>0<x>>>0?d+1|0:d;r=m;m=d;g=Ga(r,d,50);x=R;z=Ga(r,d,46)^g;x=R^x;l=(r^-1)&f;g=l+ca|0;d=((d^-1)&i)+k|0;d=g>>>0<l>>>0?d+1|0:d;k=g;g=r&C;k=k+g|0;d=(j&m)+d|0;d=g>>>0>k>>>0?d+1|0:d;g=Ga(r,m,23)^z;k=g+k|0;d=(R^x)+d|0;d=g>>>0>k>>>0?d+1|0:d;l=k;g=k+o|0;k=d;d=d+h|0;q=g;g=g>>>0<l>>>0?d+1|0:d;d=k+s|0;x=n+l|0;d=x>>>0<n>>>0?d+1|0:d;n=x;k=d;d=wa>>>0<64;wa=wa+16|0;if(d){continue}break}e=a;d=i+Ca|0;p=f+za|0;d=p>>>0<za>>>0?d+1|0:d;za=p;Ca=d;v[e+56>>2]=p;v[e+60>>2]=d;d=j+Aa|0;i=C+ta|0;d=i>>>0<ta>>>0?d+1|0:d;ta=i;Aa=d;v[e+48>>2]=i;v[e+52>>2]=d;d=m+xa|0;i=r+ra|0;d=i>>>0<ra>>>0?d+1|0:d;ra=i;xa=d;v[e+40>>2]=i;v[e+44>>2]=d;d=k+pa|0;i=n+ma|0;d=i>>>0<ma>>>0?d+1|0:d;ma=i;pa=d;v[e+32>>2]=i;v[e+36>>2]=d;d=u+Da|0;i=S+Ba|0;d=i>>>0<Ba>>>0?d+1|0:d;Ba=i;Da=d;v[e+24>>2]=i;v[e+28>>2]=d;d=E+ya|0;i=_+ua|0;d=i>>>0<ua>>>0?d+1|0:d;ua=i;ya=d;v[e+16>>2]=i;v[e+20>>2]=d;d=A+va|0;i=V+sa|0;d=i>>>0<sa>>>0?d+1|0:d;sa=i;va=d;v[e+8>>2]=i;v[e+12>>2]=d;d=g+qa|0;i=q+na|0;d=i>>>0<na>>>0?d+1|0:d;na=i;qa=d;v[e>>2]=i;v[e+4>>2]=d;b=b+128|0;c=c-1|0;if(c){continue}break}}}function fa(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,u=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=0,Ia=0,Ja=0,Ka=0,La=0,Ma=0,Na=0,Oa=0,Pa=0,Qa=0,Ra=0,Sa=0,Ta=0,Ua=0,Va=0,Wa=0,Xa=0,Ya=0,Za=0,_a=0,$a=0,ab=0,bb=0,cb=0,db=0;d=Q+-64|0;Za=d+56|0;while(1){Ya=b&7;a:{if(Ya){e=b;k=w[e+60|0]|w[e+61|0]<<8|(w[e+62|0]<<16|w[e+63|0]<<24);v[d+56>>2]=w[e+56|0]|w[e+57|0]<<8|(w[e+58|0]<<16|w[e+59|0]<<24);v[d+60>>2]=k;k=w[e+52|0]|w[e+53|0]<<8|(w[e+54|0]<<16|w[e+55|0]<<24);v[d+48>>2]=w[e+48|0]|w[e+49|0]<<8|(w[e+50|0]<<16|w[e+51|0]<<24);v[d+52>>2]=k;k=w[e+44|0]|w[e+45|0]<<8|(w[e+46|0]<<16|w[e+47|0]<<24);v[d+40>>2]=w[e+40|0]|w[e+41|0]<<8|(w[e+42|0]<<16|w[e+43|0]<<24);v[d+44>>2]=k;k=w[e+36|0]|w[e+37|0]<<8|(w[e+38|0]<<16|w[e+39|0]<<24);v[d+32>>2]=w[e+32|0]|w[e+33|0]<<8|(w[e+34|0]<<16|w[e+35|0]<<24);v[d+36>>2]=k;k=w[e+28|0]|w[e+29|0]<<8|(w[e+30|0]<<16|w[e+31|0]<<24);v[d+24>>2]=w[e+24|0]|w[e+25|0]<<8|(w[e+26|0]<<16|w[e+27|0]<<24);v[d+28>>2]=k;k=w[e+20|0]|w[e+21|0]<<8|(w[e+22|0]<<16|w[e+23|0]<<24);v[d+16>>2]=w[e+16|0]|w[e+17|0]<<8|(w[e+18|0]<<16|w[e+19|0]<<24);v[d+20>>2]=k;k=w[e+12|0]|w[e+13|0]<<8|(w[e+14|0]<<16|w[e+15|0]<<24);v[d+8>>2]=w[e+8|0]|w[e+9|0]<<8|(w[e+10|0]<<16|w[e+11|0]<<24);v[d+12>>2]=k;k=w[e+4|0]|w[e+5|0]<<8|(w[e+6|0]<<16|w[e+7|0]<<24);e=w[e|0]|w[e+1|0]<<8|(w[e+2|0]<<16|w[e+3|0]<<24);v[d>>2]=e;v[d+4>>2]=k;y=v[a+4>>2];m=v[a>>2];v[d>>2]=m^e;v[d+4>>2]=k^y;e=v[a+12>>2];k=e^v[d+12>>2];C=v[a+8>>2];v[d+8>>2]=C^v[d+8>>2];v[d+12>>2]=k;k=v[a+20>>2];z=k^v[d+20>>2];o=v[a+16>>2];v[d+16>>2]=o^v[d+16>>2];v[d+20>>2]=z;z=v[a+28>>2];A=z^v[d+28>>2];r=v[a+24>>2];v[d+24>>2]=r^v[d+24>>2];v[d+28>>2]=A;A=v[a+36>>2];s=A^v[d+36>>2];ga=v[a+32>>2];v[d+32>>2]=ga^v[d+32>>2];v[d+36>>2]=s;s=v[a+44>>2];p=s^v[d+44>>2];J=v[a+40>>2];v[d+40>>2]=J^v[d+40>>2];v[d+44>>2]=p;p=v[a+52>>2];q=p^v[d+52>>2];u=v[a+48>>2];v[d+48>>2]=u^v[d+48>>2];v[d+52>>2]=q;g=Za;break a}y=v[a+4>>2];e=y^v[b+4>>2];m=v[a>>2];v[d>>2]=m^v[b>>2];v[d+4>>2]=e;e=v[a+12>>2];k=e^v[b+12>>2];C=v[a+8>>2];v[d+8>>2]=C^v[b+8>>2];v[d+12>>2]=k;k=v[a+20>>2];z=k^v[b+20>>2];o=v[a+16>>2];v[d+16>>2]=o^v[b+16>>2];v[d+20>>2]=z;z=v[a+28>>2];A=z^v[b+28>>2];r=v[a+24>>2];v[d+24>>2]=r^v[b+24>>2];v[d+28>>2]=A;A=v[a+36>>2];s=A^v[b+36>>2];ga=v[a+32>>2];v[d+32>>2]=ga^v[b+32>>2];v[d+36>>2]=s;s=v[a+44>>2];p=s^v[b+44>>2];J=v[a+40>>2];v[d+40>>2]=J^v[b+40>>2];v[d+44>>2]=p;p=v[a+52>>2];q=p^v[b+52>>2];u=v[a+48>>2];v[d+48>>2]=u^v[b+48>>2];v[d+52>>2]=q;g=b+56|0}q=v[a+60>>2];K=q^v[g+4>>2];L=v[a+56>>2];v[d+56>>2]=L^v[g>>2];v[d+60>>2]=K;M=0;while(1){Ka=w[d+57|0];La=w[d+50|0];Ma=w[d+43|0];Na=w[d+36|0];Oa=w[d+29|0];Pa=w[d+22|0];Qa=w[d+15|0];_a=w[d|0];na=w[d+61|0];oa=w[d+54|0];pa=w[d+47|0];Ea=w[d+32|0];Fa=w[d+25|0];Ga=w[d+18|0];Ha=w[d+11|0];$a=w[d+4|0];h=w[d+62|0];X=w[d+55|0];qa=w[d+40|0];P=w[d+33|0];za=w[d+26|0];Ra=w[d+19|0];Sa=w[d+12|0];ab=w[d+5|0];i=w[d+63|0];Y=w[d+48|0];ra=w[d+41|0];R=w[d+34|0];Aa=w[d+27|0];Ba=w[d+20|0];Ca=w[d+13|0];Ta=w[d+6|0];x=w[d+56|0];Z=w[d+49|0];sa=w[d+42|0];B=w[d+35|0];Da=w[d+28|0];_=w[d+21|0];$=w[d+14|0];ka=w[d+7|0];l=w[d+58|0];ha=w[d+51|0];ta=w[d+44|0];aa=w[d+37|0];ua=w[d+30|0];ia=w[d+23|0];ba=w[d+8|0];la=w[d+1|0];f=w[d+59|0];va=w[d+52|0];ca=w[d+45|0];wa=w[d+38|0];ja=w[d+31|0];D=w[d+16|0];E=w[d+9|0];da=w[d+2|0];g=(w[d+60|0]<<6)+2208|0;K=v[g>>2];j=v[g+4>>2];g=(w[d+53|0]<<6)+2216|0;N=v[g>>2];Ia=v[g+4>>2];g=(w[d+46|0]<<6)+2224|0;xa=v[g>>2];Ua=v[g+4>>2];g=(w[d+39|0]<<6)+2232|0;ya=v[g>>2];S=v[g+4>>2];g=(w[d+24|0]<<6)+2176|0;Ja=v[g>>2];T=v[g+4>>2];g=(w[d+17|0]<<6)+2184|0;F=v[g>>2];U=v[g+4>>2];g=(w[d+10|0]<<6)+2192|0;G=v[g>>2];V=v[g+4>>2];g=((((q&536870911)<<3|L>>>29)&2040)<<3)+2208|0;H=v[g>>2];W=v[g+4>>2];g=((p>>>5&2040)<<3)+2216|0;I=v[g>>2];ea=v[g+4>>2];g=((s>>>13&2040)<<3)+2224|0;O=v[g>>2];fa=v[g+4>>2];bb=K;cb=N;db=xa;Va=ya;Wa=F;Xa=G;ma=H;n=I;ya=O;F=(A>>>21<<3|56)+2176|0;g=r;G=(g<<6&16320)+2176|0;K=o;H=(o>>>2&16320)+2184|0;N=C;I=(N>>>10&16320)+2192|0;xa=m;O=(m>>>18&16320)+2200|0;ya=ma^(n^(ya^(v[F>>2]^(v[G>>2]^(v[H>>2]^(v[I>>2]^v[O>>2]))))));o=(w[d+3|0]<<6)+2200|0;Ja=bb^(cb^(db^(Va^(Wa^(Xa^(ya^v[o>>2]))^Ja))));r=Ia;Ia=v[F+4>>2]^(v[G+4>>2]^(v[H+4>>2]^(v[I+4>>2]^v[O+4>>2])))^fa^ea^W;j=r^(Ia^v[o+4>>2]^V^U^T^S^Ua)^j;Ua=j;v[d+24>>2]=Ja;v[d+28>>2]=j;n=(f<<6)+2200|0;S=v[n>>2];T=v[n+4>>2];n=(va<<6)+2208|0;va=v[n>>2];F=v[n+4>>2];n=(ca<<6)+2216|0;ca=v[n>>2];U=v[n+4>>2];n=(wa<<6)+2224|0;wa=v[n>>2];G=v[n+4>>2];n=(ja<<6)+2232|0;ja=v[n>>2];V=v[n+4>>2];n=(D<<6)+2176|0;D=v[n>>2];H=v[n+4>>2];n=(E<<6)+2184|0;E=v[n>>2];W=v[n+4>>2];n=L;f=(n>>>18&16320)+2200|0;I=v[f>>2];ea=v[f+4>>2];f=((((p&536870911)<<3|u>>>29)&2040)<<3)+2208|0;O=v[f>>2];fa=v[f+4>>2];f=((s>>>5&2040)<<3)+2216|0;o=v[f>>2];L=v[f+4>>2];f=((A>>>13&2040)<<3)+2224|0;C=v[f>>2];r=v[f+4>>2];Va=va;Wa=ca;Xa=ja;ma=D;j=E;f=(z>>>21<<3|56)+2176|0;ca=(K<<6&16320)+2176|0;ja=(N>>>2&16320)+2184|0;D=(m>>>10&16320)+2192|0;va=v[f>>2]^(v[ca>>2]^(v[ja>>2]^v[D>>2]))^C^o^O^I;E=(da<<6)+2192|0;wa=Va^(Wa^(Xa^(ma^(j^(va^v[E>>2])))^wa))^S;ca=v[f+4>>2]^(v[ca+4>>2]^(v[ja+4>>2]^v[D+4>>2]))^r^L^fa^ea;f=ca^v[E+4>>2]^W^H^V^G^U^F^T;ja=f;v[d+16>>2]=wa;v[d+20>>2]=f;j=(l<<6)+2192|0;D=v[j>>2];E=v[j+4>>2];j=(ha<<6)+2200|0;ha=v[j>>2];da=v[j+4>>2];j=(ta<<6)+2208|0;ta=v[j>>2];S=v[j+4>>2];j=(aa<<6)+2216|0;aa=v[j>>2];T=v[j+4>>2];j=(ua<<6)+2224|0;ua=v[j>>2];F=v[j+4>>2];j=(ia<<6)+2232|0;ia=v[j>>2];U=v[j+4>>2];j=(ba<<6)+2176|0;ba=v[j>>2];G=v[j+4>>2];j=(n>>>10&16320)+2192|0;V=v[j>>2];H=v[j+4>>2];j=u;l=(j>>>18&16320)+2200|0;W=v[l>>2];I=v[l+4>>2];l=((((s&536870911)<<3|J>>>29)&2040)<<3)+2208|0;ea=v[l>>2];O=v[l+4>>2];l=((A>>>5&2040)<<3)+2216|0;fa=v[l>>2];o=v[l+4>>2];l=((z>>>13&2040)<<3)+2224|0;L=v[l>>2];C=v[l+4>>2];ma=ha;f=ta;r=aa;u=ia;l=(k>>>21<<3|56)+2176|0;ha=(N<<6&16320)+2176|0;aa=(m>>>2&16320)+2184|0;ta=v[l>>2]^(v[ha>>2]^v[aa>>2])^L^fa^ea^W^V;ia=(la<<6)+2184|0;ua=ma^(f^(r^(u^(ta^v[ia>>2]^ba)^ua)))^D;aa=v[l+4>>2]^(v[ha+4>>2]^v[aa+4>>2])^C^o^O^I^H;l=aa^v[ia+4>>2]^G^U^F^T^S^da^E;ia=l;v[d+8>>2]=ua;v[d+12>>2]=l;f=(x<<6)+2176|0;ba=v[f>>2];la=v[f+4>>2];f=(Z<<6)+2184|0;Z=v[f>>2];D=v[f+4>>2];f=(sa<<6)+2192|0;sa=v[f>>2];E=v[f+4>>2];f=(B<<6)+2200|0;B=v[f>>2];da=v[f+4>>2];f=(Da<<6)+2208|0;Da=v[f>>2];S=v[f+4>>2];f=(_<<6)+2216|0;_=v[f>>2];T=v[f+4>>2];f=($<<6)+2224|0;$=v[f>>2];F=v[f+4>>2];f=(n<<6&16320)+2176|0;U=v[f>>2];G=v[f+4>>2];f=(j>>>2&16320)+2184|0;V=v[f>>2];H=v[f+4>>2];f=J;l=(f>>>10&16320)+2192|0;W=v[l>>2];I=v[l+4>>2];l=ga;x=(l>>>18&16320)+2200|0;ea=v[x>>2];O=v[x+4>>2];x=((((z&536870911)<<3|g>>>29)&2040)<<3)+2208|0;fa=v[x>>2];o=v[x+4>>2];x=((k>>>5&2040)<<3)+2216|0;L=v[x>>2];C=v[x+4>>2];x=((e>>>13&2040)<<3)+2224|0;r=v[x>>2];J=v[x+4>>2];ma=Z;u=B;Z=(y>>>21<<3|56)+2176|0;x=v[Z>>2]^r^L^fa^ea^W^V^U;B=(ka<<6)+2232|0;sa=ma^(u^(x^v[B>>2]^$^_^Da)^sa)^ba;Z=v[Z+4>>2]^J^C^o^O^I^H^G;B=Z^v[B+4>>2]^F^T^S^da^E^D^la;Da=B;v[d+56>>2]=sa;v[d+60>>2]=B;i=(i<<6)+2232|0;B=v[i>>2];_=v[i+4>>2];i=(Y<<6)+2176|0;Y=v[i>>2];$=v[i+4>>2];i=(ra<<6)+2184|0;ra=v[i>>2];ka=v[i+4>>2];i=(R<<6)+2192|0;R=v[i>>2];ba=v[i+4>>2];i=(Aa<<6)+2200|0;Aa=v[i>>2];la=v[i+4>>2];i=(Ba<<6)+2208|0;Ba=v[i>>2];D=v[i+4>>2];i=(Ca<<6)+2216|0;Ca=v[i>>2];E=v[i+4>>2];i=(q>>>21<<3|56)+2176|0;da=v[i>>2];S=v[i+4>>2];i=(j<<6&16320)+2176|0;T=v[i>>2];F=v[i+4>>2];i=(f>>>2&16320)+2184|0;U=v[i>>2];G=v[i+4>>2];i=(l>>>10&16320)+2192|0;V=v[i>>2];H=v[i+4>>2];i=(g>>>18&16320)+2200|0;W=v[i>>2];I=v[i+4>>2];i=((((k&536870911)<<3|K>>>29)&2040)<<3)+2208|0;ea=v[i>>2];O=v[i+4>>2];i=((e>>>5&2040)<<3)+2216|0;fa=v[i>>2];o=v[i+4>>2];r=Y;u=R;Y=((y>>>13&2040)<<3)+2224|0;i=v[Y>>2]^fa^ea^W^V^U^T^da;R=(Ta<<6)+2224|0;ra=r^(u^(i^v[R>>2]^Ca^Ba^Aa)^ra)^B;Y=v[Y+4>>2]^o^O^I^H^G^F^S;R=Y^v[R+4>>2]^E^D^la^ba^ka^$^_;Aa=R;v[d+48>>2]=ra;v[d+52>>2]=R;h=(h<<6)+2224|0;R=v[h>>2];Ba=v[h+4>>2];h=(X<<6)+2232|0;X=v[h>>2];Ca=v[h+4>>2];h=(qa<<6)+2176|0;qa=v[h>>2];Ta=v[h+4>>2];h=(P<<6)+2184|0;P=v[h>>2];B=v[h+4>>2];h=(za<<6)+2192|0;za=v[h>>2];_=v[h+4>>2];h=(Ra<<6)+2200|0;Ra=v[h>>2];$=v[h+4>>2];h=(Sa<<6)+2208|0;Sa=v[h>>2];ka=v[h+4>>2];h=((q>>>13&2040)<<3)+2224|0;ba=v[h>>2];la=v[h+4>>2];h=(p>>>21<<3|56)+2176|0;D=v[h>>2];E=v[h+4>>2];h=(f<<6&16320)+2176|0;da=v[h>>2];S=v[h+4>>2];h=(l>>>2&16320)+2184|0;T=v[h>>2];F=v[h+4>>2];h=(g>>>10&16320)+2192|0;U=v[h>>2];G=v[h+4>>2];h=(K>>>18&16320)+2200|0;V=v[h>>2];H=v[h+4>>2];h=((((e&536870911)<<3|N>>>29)&2040)<<3)+2208|0;W=v[h>>2];I=v[h+4>>2];r=X;u=P;X=((y>>>5&2040)<<3)+2216|0;h=v[X>>2]^W^V^U^T^da^D^ba;P=(ab<<6)+2216|0;qa=r^(u^(h^v[P>>2]^Sa^Ra^za)^qa)^R;X=v[X+4>>2]^I^H^G^F^S^E^la;P=X^v[P+4>>2]^ka^$^_^B^Ta^Ca^Ba;za=P;v[d+40>>2]=qa;v[d+44>>2]=P;g=(g>>>2&16320)+2184|0;B=v[g>>2];_=v[g+4>>2];g=(K>>>10&16320)+2192|0;K=v[g>>2];$=v[g+4>>2];g=(N>>>18&16320)+2200|0;N=v[g>>2];ka=v[g+4>>2];na=(na<<6)+2216|0;oa=(oa<<6)+2224|0;pa=(pa<<6)+2232|0;Ea=(Ea<<6)+2176|0;Fa=(Fa<<6)+2184|0;Ga=(Ga<<6)+2192|0;Ha=(Ha<<6)+2200|0;q=((q>>>5&2040)<<3)+2216|0;p=((p>>>13&2040)<<3)+2224|0;s=(s>>>21<<3|56)+2176|0;l=(l<<6&16320)+2176|0;y=((((y&536870911)<<3|m>>>29)&2040)<<3)+2208|0;g=v[q>>2]^(v[p>>2]^(v[s>>2]^(v[l>>2]^(v[y>>2]^N^K^B))));J=($a<<6)+2208|0;N=v[na>>2]^(v[oa>>2]^(v[pa>>2]^(v[Ea>>2]^(v[Fa>>2]^(v[Ga>>2]^(v[Ha>>2]^(g^v[J>>2])))))));K=v[q+4>>2]^(v[p+4>>2]^(v[s+4>>2]^(v[l+4>>2]^(v[y+4>>2]^ka^$^_))));s=v[na+4>>2]^(v[oa+4>>2]^(v[pa+4>>2]^(v[Ea+4>>2]^(v[Fa+4>>2]^(v[Ga+4>>2]^(v[Ha+4>>2]^(K^v[J+4>>2])))))));l=s;v[d+32>>2]=N;v[d+36>>2]=l;p=(Ka<<6)+2184|0;q=v[p>>2];y=(La<<6)+2192|0;J=v[y>>2];u=(Ma<<6)+2200|0;L=v[u>>2];m=(Na<<6)+2208|0;ha=v[m>>2];Ka=v[m+4>>2];m=(Oa<<6)+2216|0;La=v[m>>2];Ma=v[m+4>>2];m=(Pa<<6)+2224|0;Na=v[m>>2];Oa=v[m+4>>2];m=(Qa<<6)+2232|0;Pa=v[m>>2];Qa=v[m+4>>2];m=(n>>>2&16320)+2184|0;n=v[m>>2];na=v[m+4>>2];m=(j>>>10&16320)+2192|0;j=v[m>>2];oa=v[m+4>>2];m=(f>>>18&16320)+2200|0;f=v[m>>2];pa=v[m+4>>2];P=q;A=((((A&536870911)<<3|ga>>>29)&2040)<<3)+2208|0;z=((z>>>5&2040)<<3)+2216|0;k=((k>>>13&2040)<<3)+2224|0;e=(e>>>21<<3|56)+2176|0;q=(M<<3)+18560|0;C=(xa<<6&16320)+2176|0;m=v[A>>2]^(v[z>>2]^(v[k>>2]^(v[e>>2]^(v[q>>2]^v[C>>2]))))^f^j^n;o=(_a<<6)+2176|0;xa=P^(m^v[o>>2]^Pa^Na^La^ha^L^J);ga=v[y+4>>2];y=v[A+4>>2]^(v[z+4>>2]^(v[k+4>>2]^(v[e+4>>2]^(v[q+4>>2]^v[C+4>>2]))))^pa^oa^na;e=v[p+4>>2]^(ga^(v[u+4>>2]^(y^v[o+4>>2]^Qa^Oa^Ma^Ka)));n=e;v[d>>2]=xa;v[d+4>>2]=e;L=x;q=Z;u=i;p=Y;J=h;s=X;ga=g;A=K;r=ya;z=Ia;o=va;k=ca;C=ta;e=aa;M=M+1|0;if((M|0)!=10){continue}break}M=0;b:{if(Ya){while(1){e=a+M|0;t[e|0]=w[e|0]^(w[b+M|0]^w[d+M|0]);e=M|1;k=e+a|0;t[k|0]=w[k|0]^(w[b+e|0]^w[d+e|0]);M=M+2|0;if((M|0)!=64){continue}break b}}e=v[a+4>>2]^(n^v[b+4>>2]);v[a>>2]=v[a>>2]^(xa^v[b>>2]);v[a+4>>2]=e;e=v[a+12>>2]^(ia^v[b+12>>2]);v[a+8>>2]=v[a+8>>2]^(ua^v[b+8>>2]);v[a+12>>2]=e;e=v[a+20>>2]^(ja^v[b+20>>2]);v[a+16>>2]=v[a+16>>2]^(wa^v[b+16>>2]);v[a+20>>2]=e;e=v[a+28>>2]^(Ua^v[b+28>>2]);v[a+24>>2]=v[a+24>>2]^(Ja^v[b+24>>2]);v[a+28>>2]=e;e=v[a+36>>2]^(l^v[b+36>>2]);v[a+32>>2]=v[a+32>>2]^(N^v[b+32>>2]);v[a+36>>2]=e;e=v[a+44>>2]^(za^v[b+44>>2]);v[a+40>>2]=v[a+40>>2]^(qa^v[b+40>>2]);v[a+44>>2]=e;e=v[a+52>>2]^(Aa^v[b+52>>2]);v[a+48>>2]=v[a+48>>2]^(ra^v[b+48>>2]);v[a+52>>2]=e;e=v[a+60>>2]^(Da^v[b+60>>2]);v[a+56>>2]=v[a+56>>2]^(sa^v[b+56>>2]);v[a+60>>2]=e}b=b- -64|0;c=c-1|0;if(c){continue}break}}function ka(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0;if(c){L=v[a+16>>2];H=v[a+12>>2];I=v[a+8>>2];y=v[a+4>>2];M=v[a>>2];while(1){E=w[b+44|0]|w[b+45|0]<<8|(w[b+46|0]<<16|w[b+47|0]<<24);x=w[b+40|0]|w[b+41|0]<<8|(w[b+42|0]<<16|w[b+43|0]<<24);s=w[b+20|0]|w[b+21|0]<<8|(w[b+22|0]<<16|w[b+23|0]<<24);F=w[b+52|0]|w[b+53|0]<<8|(w[b+54|0]<<16|w[b+55|0]<<24);t=w[b+28|0]|w[b+29|0]<<8|(w[b+30|0]<<16|w[b+31|0]<<24);C=w[b+36|0]|w[b+37|0]<<8|(w[b+38|0]<<16|w[b+39|0]<<24);p=w[b+32|0]|w[b+33|0]<<8|(w[b+34|0]<<16|w[b+35|0]<<24);z=w[b+24|0]|w[b+25|0]<<8|(w[b+26|0]<<16|w[b+27|0]<<24);q=w[b+4|0]|w[b+5|0]<<8|(w[b+6|0]<<16|w[b+7|0]<<24);A=w[b|0]|w[b+1|0]<<8|(w[b+2|0]<<16|w[b+3|0]<<24);r=Fa(A+((y^I^H)+M|0)|0,11)+L|0;i=Fa(I,10);n=Fa((q+L|0)+(r^(i^y))|0,14)+H|0;o=Fa(n,10);g=o+z|0;u=w[b+16|0]|w[b+17|0]<<8|(w[b+18|0]<<16|w[b+19|0]<<24);h=Fa(y,10);B=w[b+8|0]|w[b+9|0]<<8|(w[b+10|0]<<16|w[b+11|0]<<24);e=Fa((B+H|0)+(n^(h^r))|0,15)+i|0;G=w[b+12|0]|w[b+13|0]<<8|(w[b+14|0]<<16|w[b+15|0]<<24);r=Fa(r,10);n=Fa((i+G|0)+(e^(r^n))|0,12)+h|0;d=Fa((u+h|0)+(e^o^n)|0,5)+r|0;k=Fa(n,10);l=n;n=Fa(e,10);o=Fa((r+s|0)+(d^(l^n))|0,8)+o|0;e=Fa(g+(d^k^o)|0,7)+n|0;r=Fa(e,10);d=Fa(d,10);n=Fa((n+t|0)+(e^(d^o))|0,9)+k|0;o=Fa(o,10);e=Fa((k+p|0)+(n^(o^e))|0,11)+d|0;d=Fa((d+C|0)+(e^(n^r))|0,13)+o|0;k=Fa(e,10);n=Fa(n,10);o=Fa((o+x|0)+(d^(n^e))|0,14)+r|0;e=Fa((r+E|0)+(d^k^o)|0,15)+n|0;j=Fa(e,10);f=Fa(o,10);r=w[b+60|0]|w[b+61|0]<<8|(w[b+62|0]<<16|w[b+63|0]<<24);m=f+r|0;J=e^f;l=n;n=w[b+48|0]|w[b+49|0]<<8|(w[b+50|0]<<16|w[b+51|0]<<24);g=o;o=Fa(d,10);e=Fa((l+n|0)+(e^(g^o))|0,6)+k|0;k=Fa((k+F|0)+(J^e)|0,7)+o|0;g=Fa(e,10);d=o;o=w[b+56|0]|w[b+57|0]<<8|(w[b+58|0]<<16|w[b+59|0]<<24);f=Fa((d+o|0)+(k^(e^j))|0,9)+f|0;d=j+Fa(m+(k^g^f)|0,8)|0;k=Fa(k,10);m=Fa(((j+t|0)+(d&(k^f)^k)|0)+1518500249|0,7)+g|0;e=Fa(m,10);j=Fa(d,10);l=d;d=Fa(f,10);f=Fa(((g+u|0)+(m&(l^d)^d)|0)+1518500249|0,6)+k|0;k=Fa(((k+F|0)+((j^m)&f^j)|0)+1518500249|0,8)+d|0;d=Fa(((d+q|0)+(e^k&(e^f))|0)+1518500249|0,13)+j|0;l=j+x|0;j=Fa(f,10);f=Fa((l+(d&(j^k)^j)|0)+1518500249|0,11)+e|0;k=Fa(k,10);m=Fa(((e+z|0)+(f&(k^d)^k)|0)+1518500249|0,9)+j|0;e=Fa(m,10);g=Fa(f,10);d=Fa(d,10);j=Fa(((j+r|0)+(m&(d^f)^d)|0)+1518500249|0,7)+k|0;k=Fa(((k+G|0)+((g^m)&j^g)|0)+1518500249|0,15)+d|0;d=Fa(((d+n|0)+(e^k&(e^j))|0)+1518500249|0,7)+g|0;j=Fa(j,10);f=Fa(((g+A|0)+(d&(j^k)^j)|0)+1518500249|0,12)+e|0;k=Fa(k,10);m=Fa(((e+C|0)+(f&(k^d)^k)|0)+1518500249|0,15)+j|0;e=Fa(m,10);g=Fa(f,10);d=Fa(d,10);j=Fa(((j+s|0)+(m&(d^f)^d)|0)+1518500249|0,9)+k|0;k=Fa(((k+B|0)+((g^m)&j^g)|0)+1518500249|0,11)+d|0;d=Fa(((d+o|0)+(e^k&(e^j))|0)+1518500249|0,7)+g|0;j=Fa(j,10);f=Fa(((g+E|0)+(d&(j^k)^j)|0)+1518500249|0,13)+e|0;l=e+p|0;e=Fa(k,10);g=Fa((l+(f&(e^d)^e)|0)+1518500249|0,12)+j|0;k=Fa(g,10);m=Fa(f,10);l=e+x|0;d=Fa(d,10);e=Fa(((j+G|0)+(d^(g|f^-1))|0)+1859775393|0,11)+e|0;j=Fa((l+(m^(e|g^-1))|0)+1859775393|0,13)+d|0;f=Fa(((d+o|0)+(k^(j|e^-1))|0)+1859775393|0,6)+m|0;g=Fa(e,10);e=Fa(((m+u|0)+((j^-1|f)^g)|0)+1859775393|0,7)+k|0;d=k+C|0;k=Fa(j,10);j=Fa((d+(k^(e|f^-1))|0)+1859775393|0,14)+g|0;d=Fa(j,10);m=Fa(e,10);l=k+p|0;J=j|e^-1;e=Fa(f,10);k=Fa(((g+r|0)+(J^e)|0)+1859775393|0,9)+k|0;j=Fa((l+(m^(k|j^-1))|0)+1859775393|0,13)+e|0;f=Fa(((e+q|0)+(d^(j|k^-1))|0)+1859775393|0,15)+m|0;k=Fa(k,10);e=Fa(((m+B|0)+((j^-1|f)^k)|0)+1859775393|0,14)+d|0;j=Fa(j,10);g=Fa(((d+t|0)+(j^(e|f^-1))|0)+1859775393|0,8)+k|0;d=Fa(g,10);m=Fa(e,10);l=g|e^-1;e=Fa(f,10);k=Fa(((k+A|0)+(l^e)|0)+1859775393|0,13)+j|0;j=Fa(((j+z|0)+(m^(k|g^-1))|0)+1859775393|0,6)+e|0;f=Fa(((e+F|0)+(d^(j|k^-1))|0)+1859775393|0,5)+m|0;g=Fa(k,10);e=Fa(((m+E|0)+((j^-1|f)^g)|0)+1859775393|0,12)+d|0;j=Fa(j,10);d=Fa(((d+s|0)+(j^(e|f^-1))|0)+1859775393|0,7)+g|0;k=Fa(d,10);m=Fa(e,10);f=Fa(f,10);e=Fa(((g+n|0)+(f^(d|e^-1))|0)+1859775393|0,5)+j|0;d=Fa(((j+q|0)+(m&(e^d)^d)|0)-1894007588|0,11)+f|0;j=Fa(((f+C|0)+(e^k&(d^e))|0)-1894007588|0,12)+m|0;f=Fa(e,10);e=Fa(((m+E|0)+((d^j)&f^d)|0)-1894007588|0,14)+k|0;g=Fa(d,10);d=Fa(((k+x|0)+(j^g&(e^j))|0)-1894007588|0,15)+f|0;k=Fa(d,10);m=Fa(e,10);j=Fa(j,10);e=Fa(((f+A|0)+(e^j&(d^e))|0)-1894007588|0,14)+g|0;d=Fa(((g+p|0)+(d^m&(e^d))|0)-1894007588|0,15)+j|0;j=Fa(((j+n|0)+(e^k&(d^e))|0)-1894007588|0,9)+m|0;f=Fa(e,10);e=Fa(((m+u|0)+((d^j)&f^d)|0)-1894007588|0,8)+k|0;g=Fa(d,10);d=Fa(((k+F|0)+(j^g&(e^j))|0)-1894007588|0,9)+f|0;k=Fa(d,10);m=Fa(e,10);j=Fa(j,10);e=Fa(((f+G|0)+(e^j&(d^e))|0)-1894007588|0,14)+g|0;d=Fa(((g+t|0)+(d^m&(e^d))|0)-1894007588|0,5)+j|0;j=Fa(((j+r|0)+(e^k&(d^e))|0)-1894007588|0,6)+m|0;g=Fa(e,10);e=Fa(((m+o|0)+((d^j)&g^d)|0)-1894007588|0,8)+k|0;m=Fa(d,10);d=Fa(((k+s|0)+(j^m&(e^j))|0)-1894007588|0,6)+g|0;k=Fa(d,10);f=Fa(e,10);j=Fa(j,10);g=Fa(((g+z|0)+(e^j&(d^e))|0)-1894007588|0,5)+m|0;e=Fa(((m+B|0)+(d^f&(g^d))|0)-1894007588|0,12)+j|0;d=Fa(((j+u|0)+(e^(g|k^-1))|0)-1454113458|0,9)+f|0;l=f+A|0;f=Fa(g,10);j=Fa((l+(d^(e|f^-1))|0)-1454113458|0,15)+k|0;l=k+s|0;k=Fa(e,10);g=Fa((l+(j^(d|k^-1))|0)-1454113458|0,5)+f|0;e=Fa(g,10);m=Fa(j,10);l=f+C|0;f=Fa(d,10);j=Fa((l+(g^(j|f^-1))|0)-1454113458|0,11)+k|0;d=Fa(((k+t|0)+((g|m^-1)^j)|0)-1454113458|0,6)+f|0;k=Fa(((f+n|0)+(d^(j|e^-1))|0)-1454113458|0,8)+m|0;f=Fa(j,10);j=Fa(((m+B|0)+(k^(d|f^-1))|0)-1454113458|0,13)+e|0;d=Fa(d,10);g=Fa(((e+x|0)+(j^(k|d^-1))|0)-1454113458|0,12)+f|0;e=Fa(g,10);m=Fa(j,10);k=Fa(k,10);j=Fa(((f+o|0)+(g^(j|k^-1))|0)-1454113458|0,5)+d|0;d=Fa(((d+q|0)+((g|m^-1)^j)|0)-1454113458|0,12)+k|0;k=Fa(((k+G|0)+(d^(j|e^-1))|0)-1454113458|0,13)+m|0;l=m+p|0;m=Fa(j,10);j=Fa((l+(k^(d|m^-1))|0)-1454113458|0,14)+e|0;J=Fa(d,10);N=Fa(((e+E|0)+(j^(k|J^-1))|0)-1454113458|0,11)+m|0;O=Fa(N,10);d=Fa((s+(((H^-1|I)^y)+M|0)|0)+1352829926|0,8)+L|0;e=Fa(d,10);f=Fa((o+((d^(i^-1|y))+L|0)|0)+1352829926|0,9)+H|0;d=Fa(((t+H|0)+(f^(d|h^-1))|0)+1352829926|0,9)+i|0;i=Fa(((i+A|0)+(d^(f|e^-1))|0)+1352829926|0,11)+h|0;f=Fa(f,10);h=Fa(((h+C|0)+(i^(d|f^-1))|0)+1352829926|0,13)+e|0;d=Fa(d,10);g=Fa(((e+B|0)+(h^(i|d^-1))|0)+1352829926|0,15)+f|0;e=Fa(g,10);D=d+u|0;l=Fa(h,10);K=f+E|0;f=Fa(i,10);d=Fa((K+(g^(h|f^-1))|0)+1352829926|0,15)+d|0;i=Fa((D+((g|l^-1)^d)|0)+1352829926|0,5)+f|0;h=Fa(((f+F|0)+(i^(d|e^-1))|0)+1352829926|0,7)+l|0;f=Fa(d,10);d=Fa(((l+z|0)+(h^(i|f^-1))|0)+1352829926|0,7)+e|0;i=Fa(i,10);g=Fa(((e+r|0)+(d^(h|i^-1))|0)+1352829926|0,8)+f|0;e=Fa(g,10);D=i+q|0;l=Fa(d,10);K=f+p|0;f=Fa(h,10);i=Fa((K+(g^(d|f^-1))|0)+1352829926|0,11)+i|0;h=Fa((D+((g|l^-1)^i)|0)+1352829926|0,14)+f|0;d=Fa(((f+x|0)+(h^(i|e^-1))|0)+1352829926|0,14)+l|0;f=Fa(i,10);i=Fa(((l+G|0)+(d^(h|f^-1))|0)+1352829926|0,12)+e|0;g=Fa(h,10);h=Fa(((e+n|0)+(i^(d|g^-1))|0)+1352829926|0,6)+f|0;e=Fa(h,10);l=Fa(i,10);d=Fa(d,10);i=Fa(((f+z|0)+(i^d&(i^h))|0)+1548603684|0,9)+g|0;h=Fa(((g+E|0)+(h^l&(i^h))|0)+1548603684|0,13)+d|0;d=Fa(((d+G|0)+(i^e&(i^h))|0)+1548603684|0,15)+l|0;f=Fa(i,10);i=Fa(((l+t|0)+((h^d)&f^h)|0)+1548603684|0,7)+e|0;g=Fa(h,10);h=Fa(((e+A|0)+(d^g&(d^i))|0)+1548603684|0,12)+f|0;e=Fa(h,10);l=Fa(i,10);d=Fa(d,10);i=Fa(((f+F|0)+(i^d&(i^h))|0)+1548603684|0,8)+g|0;h=Fa(((g+s|0)+(h^l&(i^h))|0)+1548603684|0,9)+d|0;d=Fa(((d+x|0)+(i^e&(i^h))|0)+1548603684|0,11)+l|0;f=Fa(i,10);i=Fa(((l+o|0)+((h^d)&f^h)|0)+1548603684|0,7)+e|0;g=Fa(h,10);h=Fa(((e+r|0)+(d^g&(d^i))|0)+1548603684|0,7)+f|0;e=Fa(h,10);l=Fa(i,10);d=Fa(d,10);i=Fa(((f+p|0)+(i^d&(i^h))|0)+1548603684|0,12)+g|0;h=Fa(((g+n|0)+(h^l&(i^h))|0)+1548603684|0,7)+d|0;d=Fa(((d+u|0)+(i^e&(i^h))|0)+1548603684|0,6)+l|0;f=Fa(i,10);i=Fa(((l+C|0)+((h^d)&f^h)|0)+1548603684|0,15)+e|0;l=e+q|0;e=Fa(h,10);g=Fa((l+(d^e&(d^i))|0)+1548603684|0,13)+f|0;h=Fa(g,10);l=Fa(i,10);d=Fa(d,10);i=Fa(((f+B|0)+(i^d&(i^g))|0)+1548603684|0,11)+e|0;e=Fa(((e+r|0)+(l^(i|g^-1))|0)+1836072691|0,9)+d|0;d=Fa(((d+s|0)+(h^(e|i^-1))|0)+1836072691|0,7)+l|0;f=Fa(i,10);i=Fa(((l+q|0)+((e^-1|d)^f)|0)+1836072691|0,15)+h|0;e=Fa(e,10);g=Fa(((h+G|0)+(e^(i|d^-1))|0)+1836072691|0,11)+f|0;h=Fa(g,10);l=Fa(i,10);D=e+o|0;K=g|i^-1;i=Fa(d,10);e=Fa(((f+t|0)+(K^i)|0)+1836072691|0,8)+e|0;d=Fa((D+(l^(e|g^-1))|0)+1836072691|0,6)+i|0;f=Fa(((i+z|0)+(h^(d|e^-1))|0)+1836072691|0,6)+l|0;e=Fa(e,10);i=Fa(((l+C|0)+((d^-1|f)^e)|0)+1836072691|0,14)+h|0;d=Fa(d,10);g=Fa(((h+E|0)+(d^(i|f^-1))|0)+1836072691|0,12)+e|0;h=Fa(g,10);l=Fa(i,10);D=g|i^-1;i=Fa(f,10);e=Fa(((e+p|0)+(D^i)|0)+1836072691|0,13)+d|0;d=Fa(((d+n|0)+(l^(e|g^-1))|0)+1836072691|0,5)+i|0;f=Fa(((i+B|0)+(h^(d|e^-1))|0)+1836072691|0,14)+l|0;g=Fa(e,10);e=Fa(((l+x|0)+((d^-1|f)^g)|0)+1836072691|0,13)+h|0;d=Fa(d,10);l=Fa(((h+A|0)+(d^(e|f^-1))|0)+1836072691|0,13)+g|0;i=Fa(l,10);h=Fa(e,10);f=Fa(f,10);g=Fa(((g+u|0)+(f^(l|e^-1))|0)+1836072691|0,7)+d|0;e=Fa(((d+F|0)+(h^(g|l^-1))|0)+1836072691|0,5)+f|0;d=Fa(((f+p|0)+(i^e&(i^g))|0)+2053994217|0,15)+h|0;l=h+z|0;h=Fa(g,10);f=Fa((l+(d&(h^e)^h)|0)+2053994217|0,5)+i|0;e=Fa(e,10);l=Fa(((i+u|0)+(f&(e^d)^e)|0)+2053994217|0,8)+h|0;i=Fa(l,10);g=Fa(f,10);D=h+q|0;h=Fa(d,10);d=Fa((D+(l&(h^f)^h)|0)+2053994217|0,11)+e|0;e=Fa(((e+G|0)+((g^l)&d^g)|0)+2053994217|0,14)+h|0;h=Fa(((h+E|0)+(i^e&(d^i))|0)+2053994217|0,14)+g|0;d=Fa(d,10);f=Fa(((g+r|0)+(h&(d^e)^d)|0)+2053994217|0,6)+i|0;e=Fa(e,10);l=Fa(((i+A|0)+(f&(e^h)^e)|0)+2053994217|0,14)+d|0;i=Fa(l,10);g=Fa(f,10);h=Fa(h,10);d=Fa(((d+s|0)+(l&(h^f)^h)|0)+2053994217|0,6)+e|0;e=Fa(((e+n|0)+((g^l)&d^g)|0)+2053994217|0,9)+h|0;h=Fa(((h+B|0)+(i^e&(d^i))|0)+2053994217|0,12)+g|0;d=Fa(d,10);f=Fa(((g+F|0)+(h&(d^e)^d)|0)+2053994217|0,9)+i|0;e=Fa(e,10);g=Fa(((i+C|0)+(f&(e^h)^e)|0)+2053994217|0,12)+d|0;i=Fa(g,10);h=Fa(h,10);d=Fa(((d+t|0)+(g&(h^f)^h)|0)+2053994217|0,5)+e|0;D=i+r|0;l=h+o|0;K=e+x|0;e=Fa(f,10);h=Fa((K+(d&(e^g)^e)|0)+2053994217|0,15)+h|0;f=Fa((l+(i^h&(d^i))|0)+2053994217|0,8)+e|0;g=Fa(h,10);l=h;h=Fa(d,10);i=Fa((e+n|0)+(f^(l^h))|0,8)+i|0;e=Fa(D+(f^g^i)|0,5)+h|0;n=Fa(e,10);d=h+x|0;h=Fa(f,10);x=Fa(d+(e^(h^i))|0,12)+g|0;d=n+p|0;l=h+q|0;p=Fa(i,10);q=Fa((g+u|0)+(x^(p^e))|0,9)+h|0;u=Fa(l+(q^(n^x))|0,12)+p|0;i=Fa(q,10);h=p+s|0;s=Fa(x,10);p=Fa(h+(u^(s^q))|0,5)+n|0;q=Fa(d+(u^i^p)|0,14)+s|0;x=Fa(q,10);n=s+t|0;t=Fa(u,10);s=Fa(n+(q^(t^p))|0,6)+i|0;n=t+B|0;p=Fa(p,10);t=Fa((i+z|0)+(s^(p^q))|0,8)+t|0;q=Fa(n+(t^(s^x))|0,13)+p|0;u=Fa(t,10);s=Fa(s,10);t=Fa((p+F|0)+(q^(s^t))|0,6)+x|0;p=Fa((o+x|0)+(q^u^t)|0,5)+s|0;B=Fa(p,10);x=B+(H+O|0)|0;v[a+8>>2]=x;q=Fa(q,10);A=Fa((s+A|0)+(p^(q^t))|0,15)+u|0;n=Fa(A,10);o=I;I=Fa(k,10);z=Fa(((m+z|0)+(N^(j|I^-1))|0)-1454113458|0,8)+J|0;s=n+(o+Fa(z,10)|0)|0;v[a+4>>2]=s;o=y;y=r+J|0;r=Fa(j,10);y=Fa((y+(z^(N|r^-1))|0)-1454113458|0,5)+I|0;d=p;p=Fa(t,10);u=Fa((u+G|0)+(A^(d^p))|0,13)+q|0;t=(o+y|0)+Fa(u,10)|0;v[a>>2]=t;C=Fa((q+C|0)+(u^(A^B))|0,11)+p|0;y=C+(Fa(((F+I|0)+(y^(z|O^-1))|0)-1454113458|0,6)+(r+M|0)|0)|0;v[a+16>>2]=y;H=Fa((p+E|0)+(C^(n^u))|0,11)+(B+(r+L|0)|0)|0;v[a+12>>2]=H;b=b- -64|0;L=y;I=x;y=s;M=t;c=c-1|0;if(c){continue}break}}}function da(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;l=Q-16|0;Q=l;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{k:{if(a>>>0<=244){e=v[4745];h=a>>>0<11?16:a+11&-8;c=h>>>3|0;b=e>>>c|0;if(b&3){d=c+((b^-1)&1)|0;b=d<<3;f=v[b+19028>>2];a=f+8|0;c=v[f+8>>2];b=b+19020|0;l:{if((c|0)==(b|0)){m=18980,n=Fa(-2,d)&e,v[m>>2]=n;break l}v[c+12>>2]=b;v[b+8>>2]=c}b=d<<3;v[f+4>>2]=b|3;b=b+f|0;v[b+4>>2]=v[b+4>>2]|1;break a}k=v[4747];if(k>>>0>=h>>>0){break k}if(b){a=2<<c;a=(0-a|a)&b<<c;b=(0-a&a)-1|0;a=b>>>12&16;c=a;b=b>>>a|0;a=b>>>5&8;c=c|a;b=b>>>a|0;a=b>>>2&4;c=c|a;b=b>>>a|0;a=b>>>1&2;c=c|a;b=b>>>a|0;a=b>>>1&1;c=(c|a)+(b>>>a|0)|0;a=c<<3;g=v[a+19028>>2];b=v[g+8>>2];a=a+19020|0;m:{if((b|0)==(a|0)){e=Fa(-2,c)&e;v[4745]=e;break m}v[b+12>>2]=a;v[a+8>>2]=b}a=g+8|0;v[g+4>>2]=h|3;d=g+h|0;b=c<<3;f=b-h|0;v[d+4>>2]=f|1;v[b+g>>2]=f;if(k){b=k>>>3|0;c=(b<<3)+19020|0;g=v[4750];b=1<<b;n:{if(!(b&e)){v[4745]=b|e;b=c;break n}b=v[c+8>>2]}v[c+8>>2]=g;v[b+12>>2]=g;v[g+12>>2]=c;v[g+8>>2]=b}v[4750]=d;v[4747]=f;break a}j=v[4746];if(!j){break k}b=(j&0-j)-1|0;a=b>>>12&16;c=a;b=b>>>a|0;a=b>>>5&8;c=c|a;b=b>>>a|0;a=b>>>2&4;c=c|a;b=b>>>a|0;a=b>>>1&2;c=c|a;b=b>>>a|0;a=b>>>1&1;b=v[((c|a)+(b>>>a|0)<<2)+19284>>2];d=(v[b+4>>2]&-8)-h|0;c=b;while(1){o:{a=v[c+16>>2];if(!a){a=v[c+20>>2];if(!a){break o}}c=(v[a+4>>2]&-8)-h|0;f=c>>>0<d>>>0;d=f?c:d;b=f?a:b;c=a;continue}break}i=v[b+24>>2];f=v[b+12>>2];if((f|0)!=(b|0)){a=v[b+8>>2];v[a+12>>2]=f;v[f+8>>2]=a;break b}c=b+20|0;a=v[c>>2];if(!a){a=v[b+16>>2];if(!a){break j}c=b+16|0}while(1){g=c;f=a;c=a+20|0;a=v[c>>2];if(a){continue}c=f+16|0;a=v[f+16>>2];if(a){continue}break}v[g>>2]=0;break b}h=-1;if(a>>>0>4294967231){break k}a=a+11|0;h=a&-8;j=v[4746];if(!j){break k}d=0-h|0;e=0;p:{if(h>>>0<256){break p}e=31;if(h>>>0>16777215){break p}a=a>>>8|0;g=a+1048320>>>16&8;a=a<<g;c=a+520192>>>16&4;a=a<<c;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(c|g))|0;e=(a<<1|h>>>a+21&1)+28|0}c=v[(e<<2)+19284>>2];q:{r:{s:{if(!c){a=0;break s}a=0;b=h<<((e|0)==31?0:25-(e>>>1|0)|0);while(1){t:{g=(v[c+4>>2]&-8)-h|0;if(g>>>0>=d>>>0){break t}f=c;d=g;if(d){break t}d=0;a=c;break r}g=v[c+20>>2];c=v[((b>>>29&4)+c|0)+16>>2];a=g?(g|0)==(c|0)?a:g:a;b=b<<1;if(c){continue}break}}if(!(a|f)){f=0;a=2<<e;a=(0-a|a)&j;if(!a){break k}b=(a&0-a)-1|0;a=b>>>12&16;c=a;b=b>>>a|0;a=b>>>5&8;c=c|a;b=b>>>a|0;a=b>>>2&4;c=c|a;b=b>>>a|0;a=b>>>1&2;c=c|a;b=b>>>a|0;a=b>>>1&1;a=v[((c|a)+(b>>>a|0)<<2)+19284>>2]}if(!a){break q}}while(1){b=(v[a+4>>2]&-8)-h|0;c=b>>>0<d>>>0;d=c?b:d;f=c?a:f;b=v[a+16>>2];if(b){a=b}else{a=v[a+20>>2]}if(a){continue}break}}if(!f|v[4747]-h>>>0<=d>>>0){break k}e=v[f+24>>2];b=v[f+12>>2];if((f|0)!=(b|0)){a=v[f+8>>2];v[a+12>>2]=b;v[b+8>>2]=a;break c}c=f+20|0;a=v[c>>2];if(!a){a=v[f+16>>2];if(!a){break i}c=f+16|0}while(1){g=c;b=a;c=a+20|0;a=v[c>>2];if(a){continue}c=b+16|0;a=v[b+16>>2];if(a){continue}break}v[g>>2]=0;break c}c=v[4747];if(c>>>0>=h>>>0){d=v[4750];b=c-h|0;u:{if(b>>>0>=16){v[4747]=b;a=d+h|0;v[4750]=a;v[a+4>>2]=b|1;v[c+d>>2]=b;v[d+4>>2]=h|3;break u}v[4750]=0;v[4747]=0;v[d+4>>2]=c|3;a=c+d|0;v[a+4>>2]=v[a+4>>2]|1}a=d+8|0;break a}i=v[4748];if(i>>>0>h>>>0){b=i-h|0;v[4748]=b;c=v[4751];a=c+h|0;v[4751]=a;v[a+4>>2]=b|1;v[c+4>>2]=h|3;a=c+8|0;break a}a=0;j=h+47|0;b=j;if(v[4863]){c=v[4865]}else{v[4866]=-1;v[4867]=-1;v[4864]=4096;v[4865]=4096;v[4863]=l+12&-16^1431655768;v[4868]=0;v[4856]=0;c=4096}g=b+c|0;f=0-c|0;c=g&f;if(c>>>0<=h>>>0){break a}d=v[4855];if(d){b=v[4853];e=b+c|0;if(d>>>0<e>>>0|b>>>0>=e>>>0){break a}}if(w[19424]&4){break f}v:{w:{d=v[4751];if(d){a=19428;while(1){b=v[a>>2];if(b>>>0<=d>>>0&d>>>0<b+v[a+4>>2]>>>0){break w}a=v[a+8>>2];if(a){continue}break}}b=ea(0);if((b|0)==-1){break g}e=c;d=v[4864];a=d-1|0;if(a&b){e=(c-b|0)+(a+b&0-d)|0}if(e>>>0<=h>>>0|e>>>0>2147483646){break g}d=v[4855];if(d){a=v[4853];f=a+e|0;if(d>>>0<f>>>0|a>>>0>=f>>>0){break g}}a=ea(e);if((b|0)!=(a|0)){break v}break e}e=f&g-i;if(e>>>0>2147483646){break g}b=ea(e);if((b|0)==(v[a>>2]+v[a+4>>2]|0)){break h}a=b}if(!((a|0)==-1|h+48>>>0<=e>>>0)){b=v[4865];b=b+(j-e|0)&0-b;if(b>>>0>2147483646){b=a;break e}if((ea(b)|0)!=-1){e=b+e|0;b=a;break e}ea(0-e|0);break g}b=a;if((a|0)!=-1){break e}break g}f=0;break b}b=0;break c}if((b|0)!=-1){break e}}v[4856]=v[4856]|4}if(c>>>0>2147483646){break d}b=ea(c);a=ea(0);if((b|0)==-1|(a|0)==-1|a>>>0<=b>>>0){break d}e=a-b|0;if(e>>>0<=h+40>>>0){break d}}a=v[4853]+e|0;v[4853]=a;if(a>>>0>y[4854]){v[4854]=a}x:{y:{z:{g=v[4751];if(g){a=19428;while(1){d=v[a>>2];c=v[a+4>>2];if((d+c|0)==(b|0)){break z}a=v[a+8>>2];if(a){continue}break}break y}a=v[4749];if(!(a>>>0<=b>>>0?a:0)){v[4749]=b}a=0;v[4858]=e;v[4857]=b;v[4753]=-1;v[4754]=v[4863];v[4860]=0;while(1){d=a<<3;c=d+19020|0;v[d+19028>>2]=c;v[d+19032>>2]=c;a=a+1|0;if((a|0)!=32){continue}break}d=e-40|0;a=b+8&7?-8-b&7:0;c=d-a|0;v[4748]=c;a=a+b|0;v[4751]=a;v[a+4>>2]=c|1;v[(b+d|0)+4>>2]=40;v[4752]=v[4867];break x}if(w[a+12|0]&8|d>>>0>g>>>0|b>>>0<=g>>>0){break y}v[a+4>>2]=c+e;a=g+8&7?-8-g&7:0;c=a+g|0;v[4751]=c;b=v[4748]+e|0;a=b-a|0;v[4748]=a;v[c+4>>2]=a|1;v[(b+g|0)+4>>2]=40;v[4752]=v[4867];break x}if(y[4749]>b>>>0){v[4749]=b}c=b+e|0;a=19428;A:{B:{C:{D:{E:{F:{while(1){if((c|0)!=v[a>>2]){a=v[a+8>>2];if(a){continue}break F}break}if(!(w[a+12|0]&8)){break E}}a=19428;while(1){c=v[a>>2];if(c>>>0<=g>>>0){f=c+v[a+4>>2]|0;if(f>>>0>g>>>0){break D}}a=v[a+8>>2];continue}}v[a>>2]=b;v[a+4>>2]=v[a+4>>2]+e;j=(b+8&7?-8-b&7:0)+b|0;v[j+4>>2]=h|3;e=c+(c+8&7?-8-c&7:0)|0;i=h+j|0;c=e-i|0;if((e|0)==(g|0)){v[4751]=i;a=v[4748]+c|0;v[4748]=a;v[i+4>>2]=a|1;break B}if(v[4750]==(e|0)){v[4750]=i;a=v[4747]+c|0;v[4747]=a;v[i+4>>2]=a|1;v[a+i>>2]=a;break B}a=v[e+4>>2];if((a&3)==1){g=a&-8;G:{if(a>>>0<=255){d=v[e+8>>2];a=a>>>3|0;b=v[e+12>>2];if((b|0)==(d|0)){m=18980,n=v[4745]&Fa(-2,a),v[m>>2]=n;break G}v[d+12>>2]=b;v[b+8>>2]=d;break G}h=v[e+24>>2];b=v[e+12>>2];H:{if((e|0)!=(b|0)){a=v[e+8>>2];v[a+12>>2]=b;v[b+8>>2]=a;break H}I:{a=e+20|0;d=v[a>>2];if(d){break I}a=e+16|0;d=v[a>>2];if(d){break I}b=0;break H}while(1){f=a;b=d;a=b+20|0;d=v[a>>2];if(d){continue}a=b+16|0;d=v[b+16>>2];if(d){continue}break}v[f>>2]=0}if(!h){break G}d=v[e+28>>2];a=(d<<2)+19284|0;J:{if(v[a>>2]==(e|0)){v[a>>2]=b;if(b){break J}m=18984,n=v[4746]&Fa(-2,d),v[m>>2]=n;break G}v[h+(v[h+16>>2]==(e|0)?16:20)>>2]=b;if(!b){break G}}v[b+24>>2]=h;a=v[e+16>>2];if(a){v[b+16>>2]=a;v[a+24>>2]=b}a=v[e+20>>2];if(!a){break G}v[b+20>>2]=a;v[a+24>>2]=b}e=e+g|0;c=c+g|0}v[e+4>>2]=v[e+4>>2]&-2;v[i+4>>2]=c|1;v[c+i>>2]=c;if(c>>>0<=255){a=c>>>3|0;b=(a<<3)+19020|0;c=v[4745];a=1<<a;K:{if(!(c&a)){v[4745]=a|c;a=b;break K}a=v[b+8>>2]}v[b+8>>2]=i;v[a+12>>2]=i;v[i+12>>2]=b;v[i+8>>2]=a;break B}a=31;if(c>>>0<=16777215){a=c>>>8|0;f=a+1048320>>>16&8;a=a<<f;d=a+520192>>>16&4;a=a<<d;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(d|f))|0;a=(a<<1|c>>>a+21&1)+28|0}v[i+28>>2]=a;v[i+16>>2]=0;v[i+20>>2]=0;f=(a<<2)+19284|0;d=v[4746];b=1<<a;L:{if(!(d&b)){v[4746]=b|d;v[f>>2]=i;v[i+24>>2]=f;break L}a=c<<((a|0)==31?0:25-(a>>>1|0)|0);b=v[f>>2];while(1){d=b;if((v[b+4>>2]&-8)==(c|0)){break C}b=a>>>29|0;a=a<<1;f=d+(b&4)|0;b=v[f+16>>2];if(b){continue}break}v[f+16>>2]=i;v[i+24>>2]=d}v[i+12>>2]=i;v[i+8>>2]=i;break B}d=e-40|0;a=b+8&7?-8-b&7:0;c=d-a|0;v[4748]=c;a=a+b|0;v[4751]=a;v[a+4>>2]=c|1;v[(b+d|0)+4>>2]=40;v[4752]=v[4867];a=(f+(f-39&7?39-f&7:0)|0)-47|0;c=a>>>0<g+16>>>0?g:a;v[c+4>>2]=27;a=v[4860];v[c+16>>2]=v[4859];v[c+20>>2]=a;a=v[4858];v[c+8>>2]=v[4857];v[c+12>>2]=a;v[4859]=c+8;v[4858]=e;v[4857]=b;v[4860]=0;a=c+24|0;while(1){v[a+4>>2]=7;b=a+8|0;a=a+4|0;if(b>>>0<f>>>0){continue}break}if((c|0)==(g|0)){break x}v[c+4>>2]=v[c+4>>2]&-2;f=c-g|0;v[g+4>>2]=f|1;v[c>>2]=f;if(f>>>0<=255){a=f>>>3|0;b=(a<<3)+19020|0;c=v[4745];a=1<<a;M:{if(!(c&a)){v[4745]=a|c;a=b;break M}a=v[b+8>>2]}v[b+8>>2]=g;v[a+12>>2]=g;v[g+12>>2]=b;v[g+8>>2]=a;break x}a=31;v[g+16>>2]=0;v[g+20>>2]=0;if(f>>>0<=16777215){a=f>>>8|0;d=a+1048320>>>16&8;a=a<<d;c=a+520192>>>16&4;a=a<<c;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(c|d))|0;a=(a<<1|f>>>a+21&1)+28|0}v[g+28>>2]=a;d=(a<<2)+19284|0;c=v[4746];b=1<<a;N:{if(!(c&b)){v[4746]=b|c;v[d>>2]=g;v[g+24>>2]=d;break N}a=f<<((a|0)==31?0:25-(a>>>1|0)|0);b=v[d>>2];while(1){c=b;if((f|0)==(v[b+4>>2]&-8)){break A}b=a>>>29|0;a=a<<1;d=c+(b&4)|0;b=v[d+16>>2];if(b){continue}break}v[d+16>>2]=g;v[g+24>>2]=c}v[g+12>>2]=g;v[g+8>>2]=g;break x}a=v[d+8>>2];v[a+12>>2]=i;v[d+8>>2]=i;v[i+24>>2]=0;v[i+12>>2]=d;v[i+8>>2]=a}a=j+8|0;break a}a=v[c+8>>2];v[a+12>>2]=g;v[c+8>>2]=g;v[g+24>>2]=0;v[g+12>>2]=c;v[g+8>>2]=a}a=v[4748];if(a>>>0<=h>>>0){break d}b=a-h|0;v[4748]=b;c=v[4751];a=c+h|0;v[4751]=a;v[a+4>>2]=b|1;v[c+4>>2]=h|3;a=c+8|0;break a}v[4744]=48;a=0;break a}O:{if(!e){break O}c=v[f+28>>2];a=(c<<2)+19284|0;P:{if(v[a>>2]==(f|0)){v[a>>2]=b;if(b){break P}j=Fa(-2,c)&j;v[4746]=j;break O}v[e+(v[e+16>>2]==(f|0)?16:20)>>2]=b;if(!b){break O}}v[b+24>>2]=e;a=v[f+16>>2];if(a){v[b+16>>2]=a;v[a+24>>2]=b}a=v[f+20>>2];if(!a){break O}v[b+20>>2]=a;v[a+24>>2]=b}Q:{if(d>>>0<=15){a=d+h|0;v[f+4>>2]=a|3;a=a+f|0;v[a+4>>2]=v[a+4>>2]|1;break Q}v[f+4>>2]=h|3;e=f+h|0;v[e+4>>2]=d|1;v[d+e>>2]=d;if(d>>>0<=255){a=d>>>3|0;b=(a<<3)+19020|0;c=v[4745];a=1<<a;R:{if(!(c&a)){v[4745]=a|c;a=b;break R}a=v[b+8>>2]}v[b+8>>2]=e;v[a+12>>2]=e;v[e+12>>2]=b;v[e+8>>2]=a;break Q}a=31;if(d>>>0<=16777215){a=d>>>8|0;g=a+1048320>>>16&8;a=a<<g;c=a+520192>>>16&4;a=a<<c;b=a+245760>>>16&2;a=(a<<b>>>15|0)-(b|(c|g))|0;a=(a<<1|d>>>a+21&1)+28|0}v[e+28>>2]=a;v[e+16>>2]=0;v[e+20>>2]=0;b=(a<<2)+19284|0;S:{c=1<<a;T:{if(!(c&j)){v[4746]=c|j;v[b>>2]=e;break T}a=d<<((a|0)==31?0:25-(a>>>1|0)|0);h=v[b>>2];while(1){b=h;if((v[b+4>>2]&-8)==(d|0)){break S}c=a>>>29|0;a=a<<1;c=(c&4)+b|0;h=v[c+16>>2];if(h){continue}break}v[c+16>>2]=e}v[e+24>>2]=b;v[e+12>>2]=e;v[e+8>>2]=e;break Q}a=v[b+8>>2];v[a+12>>2]=e;v[b+8>>2]=e;v[e+24>>2]=0;v[e+12>>2]=b;v[e+8>>2]=a}a=f+8|0;break a}U:{if(!i){break U}c=v[b+28>>2];a=(c<<2)+19284|0;V:{if(v[a>>2]==(b|0)){v[a>>2]=f;if(f){break V}m=18984,n=Fa(-2,c)&j,v[m>>2]=n;break U}v[i+(v[i+16>>2]==(b|0)?16:20)>>2]=f;if(!f){break U}}v[f+24>>2]=i;a=v[b+16>>2];if(a){v[f+16>>2]=a;v[a+24>>2]=f}a=v[b+20>>2];if(!a){break U}v[f+20>>2]=a;v[a+24>>2]=f}W:{if(d>>>0<=15){a=d+h|0;v[b+4>>2]=a|3;a=a+b|0;v[a+4>>2]=v[a+4>>2]|1;break W}v[b+4>>2]=h|3;f=b+h|0;v[f+4>>2]=d|1;v[d+f>>2]=d;if(k){a=k>>>3|0;c=(a<<3)+19020|0;g=v[4750];a=1<<a;X:{if(!(a&e)){v[4745]=a|e;a=c;break X}a=v[c+8>>2]}v[c+8>>2]=g;v[a+12>>2]=g;v[g+12>>2]=c;v[g+8>>2]=a}v[4750]=f;v[4747]=d}a=b+8|0}Q=l+16|0;return a|0}function ja(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,ra=0,sa=0,ta=0,ua=0,va=0,wa=0,xa=0,ya=0,za=0,Aa=0,Ba=0,Ca=0,Da=0;n=v[a+16>>2];j=v[a+12>>2];e=v[a+8>>2];m=v[a+4>>2];l=v[a>>2];while(1){Da=v[a>>2];g=w[b+52|0]|w[b+53|0]<<8|(w[b+54|0]<<16|w[b+55|0]<<24);g=g<<24|g<<8&16711680|(g>>>8&65280|g>>>24);f=w[b+16|0]|w[b+17|0]<<8|(w[b+18|0]<<16|w[b+19|0]<<24);sa=f<<24|f<<8&16711680|(f>>>8&65280|f>>>24);f=w[b+8|0]|w[b+9|0]<<8|(w[b+10|0]<<16|w[b+11|0]<<24);p=f<<24|f<<8&16711680|(f>>>8&65280|f>>>24);f=w[b+40|0]|w[b+41|0]<<8|(w[b+42|0]<<16|w[b+43|0]<<24);x=f<<24|f<<8&16711680|(f>>>8&65280|f>>>24);f=w[b+60|0]|w[b+61|0]<<8|(w[b+62|0]<<16|w[b+63|0]<<24);u=f<<24|f<<8&16711680|(f>>>8&65280|f>>>24);f=Fa(sa^p^x^u,1);h=w[b+28|0]|w[b+29|0]<<8|(w[b+30|0]<<16|w[b+31|0]<<24);ta=h<<24|h<<8&16711680|(h>>>8&65280|h>>>24);h=w[b+20|0]|w[b+21|0]<<8|(w[b+22|0]<<16|w[b+23|0]<<24);ua=h<<24|h<<8&16711680|(h>>>8&65280|h>>>24);h=Fa(f^(g^(ta^ua)),1);d=w[b+48|0]|w[b+49|0]<<8|(w[b+50|0]<<16|w[b+51|0]<<24);o=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);d=w[b+36|0]|w[b+37|0]<<8|(w[b+38|0]<<16|w[b+39|0]<<24);na=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);d=w[b+12|0]|w[b+13|0]<<8|(w[b+14|0]<<16|w[b+15|0]<<24);z=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);d=w[b+4|0]|w[b+5|0]<<8|(w[b+6|0]<<16|w[b+7|0]<<24);Aa=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);d=w[b+56|0]|w[b+57|0]<<8|(w[b+58|0]<<16|w[b+59|0]<<24);B=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);d=Fa(na^(z^Aa)^B,1);i=w[b+24|0]|w[b+25|0]<<8|(w[b+26|0]<<16|w[b+27|0]<<24);va=i<<24|i<<8&16711680|(i>>>8&65280|i>>>24);C=Fa(d^(va^sa^o),1);D=Fa(C^(u^(na^ta)),1);E=Fa(D^(f^(o^x)),1);i=Fa(E^(h^(g^u)),1);k=w[b+44|0]|w[b+45|0]<<8|(w[b+46|0]<<16|w[b+47|0]<<24);oa=k<<24|k<<8&16711680|(k>>>8&65280|k>>>24);k=w[b|0]|w[b+1|0]<<8|(w[b+2|0]<<16|w[b+3|0]<<24);A=k<<24|k<<8&16711680|(k>>>8&65280|k>>>24);k=w[b+32|0]|w[b+33|0]<<8|(w[b+34|0]<<16|w[b+35|0]<<24);pa=k<<24|k<<8&16711680|(k>>>8&65280|k>>>24);F=Fa(g^(A^p^pa),1);G=Fa(oa^(z^ua)^F,1);H=Fa(G^(B^(pa^va)),1);I=Fa(H^(d^(na^oa)),1);J=Fa(I^(C^(o^B)),1);K=Fa(J^(D^(d^u)),1);L=Fa(K^(E^(f^C)),1);k=Fa(L^(h^D^i),1);M=Fa(h^(F^(x^pa)),1);N=Fa(M^(g^oa^G),1);O=Fa(N^(B^F^H),1);P=Fa(O^(d^G^I),1);Q=Fa(P^(C^H^J),1);R=Fa(Q^(D^I^K),1);S=Fa(R^(E^J^L),1);q=Fa(S^(i^K^k),1);T=Fa(f^F^M^i,1);s=Fa(T^(h^G^N),1);U=Fa(E^M^T^k,1);t=Fa(U^(i^N^s),1);V=Fa(L^T^U^q,1);y=Fa(V^(k^s^t),1);W=Fa(H^M^O^s,1);X=Fa(W^(I^N^P),1);Y=Fa(X^(J^O^Q),1);Z=Fa(Y^(K^P^R),1);_=Fa(Z^(L^Q^S),1);$=Fa(_^(k^R^q),1);aa=Fa($^(S^U^V),1);ba=Fa(aa^(q^t^y),1);ca=Fa(O^T^W^t,1);da=Fa(ca^(s^P^X),1);ea=Fa(da^(Q^W^Y),1);fa=Fa(ea^(R^X^Z),1);ga=Fa(fa^(S^Y^_),1);ha=Fa(ga^(q^Z^$),1);qa=Fa(ha^(V^_^aa),1);wa=Fa(qa^(y^$^ba),1);ia=Fa(U^W^ca^y,1);ja=Fa(ia^(t^X^da),1);ka=Fa(ja^(Y^ca^ea),1);la=Fa(ka^(Z^da^fa),1);xa=Fa(la^(_^ea^ga),1);ya=Fa(xa^($^fa^ha),1);Ba=Fa(ya^(aa^ga^qa),1);Ca=Fa(Ba^(ba^ha^wa),1);ma=Fa(V^ca^ia^ba,1);za=Fa(ma^(aa^ia)^wa,1);ra=Fa(y^da^ja^ma,1);A=(((Fa(l,5)+n|0)+((e^j)&m^j)|0)+A|0)+1518500249|0;n=Fa(A,30);r=j;j=Fa(m,30);m=(((r+((j^e)&l^e)|0)+Aa|0)+Fa(A,5)|0)+1518500249|0;l=Fa(l,30);p=((((j^A&(l^j))+e|0)+p|0)+Fa(m,5)|0)+1518500249|0;z=(Fa(p,5)+((j+(l^m&(l^n))|0)+z|0)|0)+1518500249|0;e=Fa(p,30);m=Fa(m,30);l=(((l+(n^p&(m^n))|0)+sa|0)+Fa(z,5)|0)+1518500249|0;p=(Fa(l,5)+((n+(m^z&(e^m))|0)+ua|0)|0)+1518500249|0;j=Fa(p,30);n=Fa(z,30);m=(((m+(e^l&(n^e))|0)+va|0)+Fa(p,5)|0)+1518500249|0;l=Fa(l,30);p=(((e+(n^p&(l^n))|0)+ta|0)+Fa(m,5)|0)+1518500249|0;n=(Fa(p,5)+(pa+(n+(l^m&(j^l))|0)|0)|0)+1518500249|0;e=Fa(p,30);m=Fa(m,30);l=(((l+(j^p&(m^j))|0)+na|0)+Fa(n,5)|0)+1518500249|0;x=(Fa(l,5)+((j+(m^n&(e^m))|0)+x|0)|0)+1518500249|0;j=Fa(x,30);n=Fa(n,30);m=(((m+(e^l&(n^e))|0)+oa|0)+Fa(x,5)|0)+1518500249|0;l=Fa(l,30);o=(((e+(n^x&(l^n))|0)+o|0)+Fa(m,5)|0)+1518500249|0;n=(Fa(o,5)+((n+(l^m&(j^l))|0)+g|0)|0)+1518500249|0;e=Fa(o,30);m=Fa(m,30);l=((B+(l+(j^o&(m^j))|0)|0)+Fa(n,5)|0)+1518500249|0;o=(Fa(l,5)+((j+(m^n&(e^m))|0)+u|0)|0)+1518500249|0;g=Fa(o,30);j=Fa(l,30);u=Fa(n,30);l=(((m+F|0)+(e^l&(u^e))|0)+Fa(o,5)|0)+1518500249|0;d=(Fa(l,5)+((e+d|0)+(u^o&(j^u))|0)|0)+1518500249|0;e=Fa(d,30);o=Fa(l,30);r=(j+G|0)+(g^d&(o^g))|0;j=(Fa(d,5)+((f+u|0)+(j^l&(g^j))|0)|0)+1518500249|0;f=(r+Fa(j,5)|0)+1518500249|0;l=(Fa(f,5)+((g+C|0)+(j^(e^o))|0)|0)+1859775393|0;g=Fa(l,30);d=Fa(f,30);r=h+o|0;h=Fa(j,30);f=((r+(f^(h^e))|0)+Fa(l,5)|0)+1859775393|0;j=(Fa(f,5)+((e+H|0)+(l^(d^h))|0)|0)+1859775393|0;e=Fa(j,30);l=Fa(f,30);r=(d+M|0)+(j^(l^g))|0;d=(Fa(j,5)+((h+D|0)+(f^(d^g))|0)|0)+1859775393|0;f=(r+Fa(d,5)|0)+1859775393|0;j=(Fa(f,5)+((g+I|0)+(d^(e^l))|0)|0)+1859775393|0;g=Fa(j,30);h=Fa(f,30);d=Fa(d,30);f=(((l+E|0)+(f^(d^e))|0)+Fa(j,5)|0)+1859775393|0;j=(Fa(f,5)+((e+N|0)+(j^(d^h))|0)|0)+1859775393|0;e=Fa(j,30);r=h+i|0;i=Fa(f,30);d=(Fa(j,5)+((d+J|0)+(f^(g^h))|0)|0)+1859775393|0;f=((r+(j^(i^g))|0)+Fa(d,5)|0)+1859775393|0;j=(Fa(f,5)+((g+O|0)+(d^(e^i))|0)|0)+1859775393|0;g=Fa(j,30);h=Fa(f,30);d=Fa(d,30);f=(((i+K|0)+(f^(d^e))|0)+Fa(j,5)|0)+1859775393|0;i=(Fa(f,5)+((e+T|0)+(j^(d^h))|0)|0)+1859775393|0;e=Fa(i,30);j=Fa(f,30);d=(Fa(i,5)+((d+P|0)+(f^(g^h))|0)|0)+1859775393|0;f=(((h+L|0)+(i^(j^g))|0)+Fa(d,5)|0)+1859775393|0;g=(Fa(f,5)+((g+s|0)+(d^(e^j))|0)|0)+1859775393|0;h=Fa(g,30);i=Fa(d,30);d=(((j+Q|0)+(f^(i^e))|0)+Fa(g,5)|0)+1859775393|0;j=e+k|0;k=Fa(f,30);f=((j+(g^(k^i))|0)+Fa(d,5)|0)+1859775393|0;e=(Fa(f,5)+((i+W|0)+(d^(h^k))|0)|0)+1859775393|0;g=Fa(f,30);j=k+R|0;k=Fa(d,30);f=((j+(f^(k^h))|0)+Fa(e,5)|0)+1859775393|0;d=(Fa(f,5)+((h+U|0)+(k&(e|g)|e&g)|0)|0)-1894007588|0;h=Fa(d,30);e=Fa(e,30);i=Fa(f,30);j=(g+S|0)+(e&(i|d)|d&i)|0;d=(Fa(d,5)+((k+X|0)+(g&(e|f)|e&f)|0)|0)-1894007588|0;g=(j+Fa(d,5)|0)-1894007588|0;e=(Fa(g,5)+((e+t|0)+(i&(d|h)|d&h)|0)|0)-1894007588|0;f=Fa(g,30);d=Fa(d,30);g=(((i+Y|0)+(h&(d|g)|d&g)|0)+Fa(e,5)|0)-1894007588|0;i=(Fa(g,5)+((h+q|0)+(d&(e|f)|e&f)|0)|0)-1894007588|0;h=Fa(i,30);e=Fa(e,30);k=Fa(g,30);d=(Fa(i,5)+((d+ca|0)+(f&(e|g)|e&g)|0)|0)-1894007588|0;g=(((f+Z|0)+(e&(k|i)|i&k)|0)+Fa(d,5)|0)-1894007588|0;e=(Fa(g,5)+((e+V|0)+(k&(d|h)|d&h)|0)|0)-1894007588|0;f=Fa(g,30);d=Fa(d,30);g=(((k+da|0)+(h&(d|g)|d&g)|0)+Fa(e,5)|0)-1894007588|0;i=(Fa(g,5)+((h+_|0)+(d&(e|f)|e&f)|0)|0)-1894007588|0;h=Fa(i,30);e=Fa(e,30);k=Fa(g,30);d=(Fa(i,5)+((d+y|0)+(f&(e|g)|e&g)|0)|0)-1894007588|0;g=(((f+ea|0)+(e&(k|i)|i&k)|0)+Fa(d,5)|0)-1894007588|0;e=(Fa(g,5)+((e+$|0)+(k&(d|h)|d&h)|0)|0)-1894007588|0;f=Fa(g,30);d=Fa(d,30);g=(((k+ia|0)+(h&(d|g)|d&g)|0)+Fa(e,5)|0)-1894007588|0;i=(Fa(g,5)+((h+fa|0)+(d&(e|f)|e&f)|0)|0)-1894007588|0;h=Fa(i,30);e=Fa(e,30);k=Fa(g,30);g=(Fa(i,5)+((d+aa|0)+(f&(e|g)|e&g)|0)|0)-1894007588|0;f=(((f+ja|0)+(e&(k|i)|i&k)|0)+Fa(g,5)|0)-1894007588|0;d=Fa(f,30);i=Fa(g,30);j=(k+ba|0)+(h&(i|f)|f&i)|0;k=(Fa(f,5)+((e+ga|0)+(k&(g|h)|g&h)|0)|0)-1894007588|0;e=(j+Fa(k,5)|0)-1894007588|0;h=(Fa(e,5)+((h+ka|0)+(k^(d^i))|0)|0)-899497514|0;g=Fa(h,30);f=Fa(e,30);j=i+ha|0;i=Fa(k,30);e=((j+(e^(i^d))|0)+Fa(h,5)|0)-899497514|0;d=(Fa(e,5)+((d+ma|0)+(h^(f^i))|0)|0)-899497514|0;h=Fa(d,30);k=Fa(e,30);j=(f+qa|0)+(d^(k^g))|0;d=(Fa(d,5)+((i+la|0)+(e^(f^g))|0)|0)-899497514|0;e=(j+Fa(d,5)|0)-899497514|0;i=(Fa(e,5)+((g+ra|0)+(d^(h^k))|0)|0)-899497514|0;g=Fa(i,30);f=Fa(e,30);d=Fa(d,30);e=(((k+xa|0)+(e^(d^h))|0)+Fa(i,5)|0)-899497514|0;i=(Fa(e,5)+((h+wa|0)+(i^(d^f))|0)|0)-899497514|0;h=Fa(i,30);k=Fa(e,30);r=(f+ya|0)+(i^(k^g))|0;j=d;d=Fa(ea^ia^ka^ra,1);f=(((j+d|0)+(e^(f^g))|0)+Fa(i,5)|0)-899497514|0;e=(r+Fa(f,5)|0)-899497514|0;g=(Fa(e,5)+((g+za|0)+(f^(h^k))|0)|0)-899497514|0;i=Fa(g,30);q=Fa(e,30);f=Fa(f,30);j=k;k=Fa(d^(fa^ja^la),1);e=(((j+k|0)+(e^(f^h))|0)+Fa(g,5)|0)-899497514|0;g=(((h+Ba|0)+(g^(q^f))|0)+Fa(e,5)|0)-899497514|0;h=Fa(g,30);s=Fa(e,30);t=Fa(ba^ja^ra^za,1);e=(((t+f|0)+(e^(i^q))|0)+Fa(g,5)|0)-899497514|0;y=Fa(k^(ga^ka^xa),1);f=(((y+q|0)+(g^(i^s))|0)+Fa(e,5)|0)-899497514|0;i=(((i+Ca|0)+(h^s^e)|0)+Fa(f,5)|0)-899497514|0;g=Fa(i,30);n=g+v[a+16>>2]|0;v[a+16>>2]=n;t=Fa(t^(d^(ka^ma)),1);d=Fa(e,30);q=(((t+s|0)+(f^(d^h))|0)+Fa(i,5)|0)-899497514|0;s=Fa(q,30);j=s+v[a+12>>2]|0;v[a+12>>2]=j;r=v[a+8>>2];e=Fa(y^(ha^la^ya),1)+h|0;f=Fa(f,30);h=((e+(i^(f^d))|0)+Fa(q,5)|0)-899497514|0;e=r+Fa(h,30)|0;v[a+8>>2]=e;d=(((Fa(ma^qa^za^Ca,1)+d|0)+(q^(f^g))|0)+Fa(h,5)|0)-899497514|0;m=d+v[a+4>>2]|0;v[a+4>>2]=m;l=(((f+(Fa(t^(k^(la^ra)),1)+Da|0)|0)+(h^(g^s))|0)+Fa(d,5)|0)-899497514|0;v[a>>2]=l;b=b- -64|0;c=c-1|0;if(c){continue}break}}function ya(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;i=b+80|0;c=v[b+208>>2];t[i+c|0]=128;e=c+1|0;if(e>>>0>=113){ba(e+i|0,0,127-c|0);ga(b,i,1);e=0}ba(e+i|0,0,112-e|0);f=v[b+64>>2];g=f<<24|f<<8&16711680;c=v[b+68>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;d=b;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|k;t[d+200|0]=c;t[d+201|0]=c>>>8;t[d+202|0]=c>>>16;t[d+203|0]=c>>>24;e=0|g;t[d+204|0]=e;t[d+205|0]=e>>>8;t[d+206|0]=e>>>16;t[d+207|0]=e>>>24;f=v[d+72>>2];g=f<<24|f<<8&16711680;c=v[d+76>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|k;t[d+192|0]=c;t[d+193|0]=c>>>8;t[d+194|0]=c>>>16;t[d+195|0]=c>>>24;e=g|j;t[d+196|0]=e;t[d+197|0]=e>>>8;t[d+198|0]=e>>>16;t[d+199|0]=e>>>24;ga(d,i,1);a:{if(!a){break a}b:{c:{switch(Fa(v[b+212>>2]-28|0,30)|0){case 9:f=v[b>>2];g=f<<24|f<<8&16711680;c=v[b+4>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;i=k;g=e&255|h|g;d=a;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d|0]=c;t[d+1|0]=c>>>8;t[d+2|0]=c>>>16;t[d+3|0]=c>>>24;e=0|g;t[d+4|0]=e;t[d+5|0]=e>>>8;t[d+6|0]=e>>>16;t[d+7|0]=e>>>24;f=v[b+8>>2];g=f<<24|f<<8&16711680;c=v[b+12>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+8|0]=c;t[d+9|0]=c>>>8;t[d+10|0]=c>>>16;t[d+11|0]=c>>>24;e=g|j;t[d+12|0]=e;t[d+13|0]=e>>>8;t[d+14|0]=e>>>16;t[d+15|0]=e>>>24;f=v[b+16>>2];g=f<<24|f<<8&16711680;c=v[b+20>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+16|0]=c;t[d+17|0]=c>>>8;t[d+18|0]=c>>>16;t[d+19|0]=c>>>24;e=g|j;t[d+20|0]=e;t[d+21|0]=e>>>8;t[d+22|0]=e>>>16;t[d+23|0]=e>>>24;f=v[b+24>>2];g=f<<24|f<<8&16711680;c=v[b+28>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+24|0]=c;t[d+25|0]=c>>>8;t[d+26|0]=c>>>16;t[d+27|0]=c>>>24;e=g|j;t[d+28|0]=e;t[d+29|0]=e>>>8;t[d+30|0]=e>>>16;t[d+31|0]=e>>>24;f=v[b+32>>2];g=f<<24|f<<8&16711680;c=v[b+36>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+32|0]=c;t[d+33|0]=c>>>8;t[d+34|0]=c>>>16;t[d+35|0]=c>>>24;e=g|j;t[d+36|0]=e;t[d+37|0]=e>>>8;t[d+38|0]=e>>>16;t[d+39|0]=e>>>24;f=v[b+40>>2];g=f<<24|f<<8&16711680;c=v[b+44>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+40|0]=c;t[d+41|0]=c>>>8;t[d+42|0]=c>>>16;t[d+43|0]=c>>>24;e=g|j;t[d+44|0]=e;t[d+45|0]=e>>>8;t[d+46|0]=e>>>16;t[d+47|0]=e>>>24;f=v[b+48>>2];g=f<<24|f<<8&16711680;c=v[b+52>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+48|0]=c;t[d+49|0]=c>>>8;t[d+50|0]=c>>>16;t[d+51|0]=c>>>24;e=g|j;t[d+52|0]=e;t[d+53|0]=e>>>8;t[d+54|0]=e>>>16;t[d+55|0]=e>>>24;d=v[b+60>>2];c=v[b+56>>2];t[a+62|0]=(d&255)<<24|c>>>8;t[a+61|0]=(d&65535)<<16|c>>>16;t[a+60|0]=(d&16777215)<<8|c>>>24;t[a+59|0]=d;t[a+58|0]=d>>>8;t[a+57|0]=d>>>16;t[a+56|0]=d>>>24;a=a+63|0;break b;case 5:f=v[b>>2];g=f<<24|f<<8&16711680;c=v[b+4>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;i=k;g=e&255|h|g;d=a;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d|0]=c;t[d+1|0]=c>>>8;t[d+2|0]=c>>>16;t[d+3|0]=c>>>24;e=0|g;t[d+4|0]=e;t[d+5|0]=e>>>8;t[d+6|0]=e>>>16;t[d+7|0]=e>>>24;f=v[b+8>>2];g=f<<24|f<<8&16711680;c=v[b+12>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+8|0]=c;t[d+9|0]=c>>>8;t[d+10|0]=c>>>16;t[d+11|0]=c>>>24;e=g|j;t[d+12|0]=e;t[d+13|0]=e>>>8;t[d+14|0]=e>>>16;t[d+15|0]=e>>>24;f=v[b+16>>2];g=f<<24|f<<8&16711680;c=v[b+20>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+16|0]=c;t[d+17|0]=c>>>8;t[d+18|0]=c>>>16;t[d+19|0]=c>>>24;e=g|j;t[d+20|0]=e;t[d+21|0]=e>>>8;t[d+22|0]=e>>>16;t[d+23|0]=e>>>24;f=v[b+24>>2];g=f<<24|f<<8&16711680;c=v[b+28>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+24|0]=c;t[d+25|0]=c>>>8;t[d+26|0]=c>>>16;t[d+27|0]=c>>>24;e=g|j;t[d+28|0]=e;t[d+29|0]=e>>>8;t[d+30|0]=e>>>16;t[d+31|0]=e>>>24;f=v[b+32>>2];g=f<<24|f<<8&16711680;c=v[b+36>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+32|0]=c;t[d+33|0]=c>>>8;t[d+34|0]=c>>>16;t[d+35|0]=c>>>24;e=g|j;t[d+36|0]=e;t[d+37|0]=e>>>8;t[d+38|0]=e>>>16;t[d+39|0]=e>>>24;d=v[b+44>>2];c=v[b+40>>2];t[a+46|0]=(d&255)<<24|c>>>8;t[a+45|0]=(d&65535)<<16|c>>>16;t[a+44|0]=(d&16777215)<<8|c>>>24;t[a+43|0]=d;t[a+42|0]=d>>>8;t[a+41|0]=d>>>16;t[a+40|0]=d>>>24;a=a+47|0;break b;case 1:f=v[b>>2];g=f<<24|f<<8&16711680;c=v[b+4>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;i=k;g=e&255|h|g;d=a;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d|0]=c;t[d+1|0]=c>>>8;t[d+2|0]=c>>>16;t[d+3|0]=c>>>24;e=0|g;t[d+4|0]=e;t[d+5|0]=e>>>8;t[d+6|0]=e>>>16;t[d+7|0]=e>>>24;f=v[b+8>>2];g=f<<24|f<<8&16711680;c=v[b+12>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+8|0]=c;t[d+9|0]=c>>>8;t[d+10|0]=c>>>16;t[d+11|0]=c>>>24;e=g|j;t[d+12|0]=e;t[d+13|0]=e>>>8;t[d+14|0]=e>>>16;t[d+15|0]=e>>>24;f=v[b+16>>2];g=f<<24|f<<8&16711680;c=v[b+20>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+16|0]=c;t[d+17|0]=c>>>8;t[d+18|0]=c>>>16;t[d+19|0]=c>>>24;e=g|j;t[d+20|0]=e;t[d+21|0]=e>>>8;t[d+22|0]=e>>>16;t[d+23|0]=e>>>24;d=v[b+28>>2];c=v[b+24>>2];t[a+30|0]=(d&255)<<24|c>>>8;t[a+29|0]=(d&65535)<<16|c>>>16;t[a+28|0]=(d&16777215)<<8|c>>>24;t[a+27|0]=d;t[a+26|0]=d>>>8;t[a+25|0]=d>>>16;t[a+24|0]=d>>>24;a=a+31|0;break b;case 0:break c;default:break a}}f=v[b>>2];g=f<<24|f<<8&16711680;c=v[b+4>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;i=k;g=e&255|h|g;d=a;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d|0]=c;t[d+1|0]=c>>>8;t[d+2|0]=c>>>16;t[d+3|0]=c>>>24;e=0|g;t[d+4|0]=e;t[d+5|0]=e>>>8;t[d+6|0]=e>>>16;t[d+7|0]=e>>>24;f=v[b+8>>2];g=f<<24|f<<8&16711680;c=v[b+12>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+8|0]=c;t[d+9|0]=c>>>8;t[d+10|0]=c>>>16;t[d+11|0]=c>>>24;e=g|j;t[d+12|0]=e;t[d+13|0]=e>>>8;t[d+14|0]=e>>>16;t[d+15|0]=e>>>24;f=v[b+16>>2];g=f<<24|f<<8&16711680;c=v[b+20>>2];e=c<<24|f>>>8;h=e&65280;e=c<<8|f>>>24;g=e&255|h|g;c=((c&255)<<24|f>>>8)&-16777216|((c&16777215)<<8|f>>>24)&16711680|(c>>>8&65280|c>>>24)|i;t[d+16|0]=c;t[d+17|0]=c>>>8;t[d+18|0]=c>>>16;t[d+19|0]=c>>>24;e=g|j;t[d+20|0]=e;t[d+21|0]=e>>>8;t[d+22|0]=e>>>16;t[d+23|0]=e>>>24;d=v[b+28>>2];t[a+26|0]=d>>>8;t[a+25|0]=d>>>16;t[a+24|0]=d>>>24;c=d;a=a+27|0}t[a|0]=c}}function ha(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0;r=Q+-64|0;if(c){M=v[a+12>>2];N=v[a+28>>2];G=v[a+24>>2];D=v[a+20>>2];x=v[a+16>>2];E=v[a+8>>2];B=v[a+4>>2];y=v[a>>2];while(1){d=w[b|0]|w[b+1|0]<<8|(w[b+2|0]<<16|w[b+3|0]<<24);h=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r>>2]=h;d=w[b+4|0]|w[b+5|0]<<8|(w[b+6|0]<<16|w[b+7|0]<<24);f=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+4>>2]=f;d=w[b+8|0]|w[b+9|0]<<8|(w[b+10|0]<<16|w[b+11|0]<<24);k=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+8>>2]=k;d=w[b+12|0]|w[b+13|0]<<8|(w[b+14|0]<<16|w[b+15|0]<<24);n=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+12>>2]=n;d=w[b+16|0]|w[b+17|0]<<8|(w[b+18|0]<<16|w[b+19|0]<<24);i=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+16>>2]=i;d=w[b+20|0]|w[b+21|0]<<8|(w[b+22|0]<<16|w[b+23|0]<<24);m=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+20>>2]=m;d=w[b+24|0]|w[b+25|0]<<8|(w[b+26|0]<<16|w[b+27|0]<<24);l=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+24>>2]=l;d=w[b+28|0]|w[b+29|0]<<8|(w[b+30|0]<<16|w[b+31|0]<<24);q=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+28>>2]=q;d=w[b+32|0]|w[b+33|0]<<8|(w[b+34|0]<<16|w[b+35|0]<<24);s=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+32>>2]=s;d=w[b+36|0]|w[b+37|0]<<8|(w[b+38|0]<<16|w[b+39|0]<<24);o=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+36>>2]=o;d=w[b+40|0]|w[b+41|0]<<8|(w[b+42|0]<<16|w[b+43|0]<<24);u=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+40>>2]=u;d=w[b+44|0]|w[b+45|0]<<8|(w[b+46|0]<<16|w[b+47|0]<<24);H=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+44>>2]=H;d=w[b+48|0]|w[b+49|0]<<8|(w[b+50|0]<<16|w[b+51|0]<<24);I=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+48>>2]=I;d=w[b+52|0]|w[b+53|0]<<8|(w[b+54|0]<<16|w[b+55|0]<<24);J=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+52>>2]=J;d=w[b+56|0]|w[b+57|0]<<8|(w[b+58|0]<<16|w[b+59|0]<<24);z=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+56>>2]=z;d=w[b+60|0]|w[b+61|0]<<8|(w[b+62|0]<<16|w[b+63|0]<<24);K=d<<24|d<<8&16711680|(d>>>8&65280|d>>>24);v[r+60>>2]=K;h=(h+((((x&D)+(Fa(x,26)^Fa(x,21)^Fa(x,7))|0)+N|0)+((x^-1)&G)|0)|0)+1116352408|0;j=y&B;d=h+((B&E^(j^y&E))+(Fa(y,30)^Fa(y,19)^Fa(y,10))|0)|0;p=j^d&B;j=d&y;g=(Fa(d,30)^Fa(d,19)^Fa(d,10))+(p^j)|0;h=h+M|0;e=Fa(h,26)^Fa(h,21);p=g;g=(f+(((((h^-1)&D)+G|0)+(h&x)|0)+(Fa(h,7)^e)|0)|0)+1899447441|0;f=p+g|0;e=d&f;p=(Fa(f,30)^Fa(f,19)^Fa(f,10))+(e^(j^f&y))|0;j=g+E|0;g=Fa(j,26)^Fa(j,21);g=((((k+D|0)+((j^-1)&x)|0)+(h&j)|0)+(Fa(j,7)^g)|0)-1245643825|0;k=g+p|0;p=e^d&k;e=f&k;p=(Fa(k,30)^Fa(k,19)^Fa(k,10))+(p^e)|0;g=g+B|0;A=Fa(g,26)^Fa(g,21);t=p;p=((((n+x|0)+(h&(g^-1))|0)+(g&j)|0)+(Fa(g,7)^A)|0)-373957723|0;n=t+p|0;A=k&n;C=(Fa(n,30)^Fa(n,19)^Fa(n,10))+(A^(e^f&n))|0;e=p+y|0;p=Fa(e,26)^Fa(e,21);i=((((h+i|0)+(j&(e^-1))|0)+(e&g)|0)+(Fa(e,7)^p)|0)+961987163|0;h=i+C|0;p=h&n;A=(Fa(h,30)^Fa(h,19)^Fa(h,10))+(p^(A^h&k))|0;d=d+i|0;i=Fa(d,26)^Fa(d,21);i=((((j+m|0)+(g&(d^-1))|0)+(d&e)|0)+(Fa(d,7)^i)|0)+1508970993|0;j=i+A|0;m=h&j;p=(Fa(j,30)^Fa(j,19)^Fa(j,10))+(m^(p^j&n))|0;f=f+i|0;i=Fa(f,26)^Fa(f,21);i=((((g+l|0)+(e&(f^-1))|0)+(d&f)|0)+(Fa(f,7)^i)|0)-1841331548|0;g=i+p|0;p=m^g&h;m=g&j;l=(Fa(g,30)^Fa(g,19)^Fa(g,10))+(p^m)|0;k=i+k|0;i=Fa(k,26)^Fa(k,21);i=((((e+q|0)+(d&(k^-1))|0)+(f&k)|0)+(Fa(k,7)^i)|0)-1424204075|0;e=i+l|0;p=m^e&j;m=e&g;l=(Fa(e,30)^Fa(e,19)^Fa(e,10))+(p^m)|0;n=i+n|0;i=Fa(n,26)^Fa(n,21);i=((((d+s|0)+(f&(n^-1))|0)+(k&n)|0)+(Fa(n,7)^i)|0)-670586216|0;d=i+l|0;p=m^d&g;m=d&e;l=(Fa(d,30)^Fa(d,19)^Fa(d,10))+(p^m)|0;h=h+i|0;i=Fa(h,26)^Fa(h,21);f=((((f+o|0)+(k&(h^-1))|0)+(h&n)|0)+(Fa(h,7)^i)|0)+310598401|0;i=f+l|0;l=d&i;m=(Fa(i,30)^Fa(i,19)^Fa(i,10))+(l^(m^e&i))|0;j=f+j|0;f=Fa(j,26)^Fa(j,21);f=((((k+u|0)+(n&(j^-1))|0)+(h&j)|0)+(Fa(j,7)^f)|0)+607225278|0;m=f+m|0;k=i&m;l=(Fa(m,30)^Fa(m,19)^Fa(m,10))+(k^(l^d&m))|0;g=f+g|0;f=Fa(g,26)^Fa(g,21);f=((((n+H|0)+(h&(g^-1))|0)+(g&j)|0)+(Fa(g,7)^f)|0)+1426881987|0;l=f+l|0;o=k^i&l;k=l&m;n=(Fa(l,30)^Fa(l,19)^Fa(l,10))+(o^k)|0;e=e+f|0;f=Fa(e,26)^Fa(e,21);f=((((h+I|0)+(j&(e^-1))|0)+(e&g)|0)+(Fa(e,7)^f)|0)+1925078388|0;h=f+n|0;o=k^h&m;k=h&l;n=(Fa(h,30)^Fa(h,19)^Fa(h,10))+(o^k)|0;f=d+f|0;d=Fa(f,26)^Fa(f,21);d=((((j+J|0)+(g&(f^-1))|0)+(e&f)|0)+(Fa(f,7)^d)|0)-2132889090|0;j=d+n|0;q=h&j;n=(Fa(j,30)^Fa(j,19)^Fa(j,10))+(q^(k^j&l))|0;k=d+i|0;d=Fa(k,26)^Fa(k,21);d=((((g+z|0)+(e&(k^-1))|0)+(f&k)|0)+(Fa(k,7)^d)|0)-1680079193|0;n=d+n|0;i=(Fa(n,30)^Fa(n,19)^Fa(n,10))+(q^(h^j)&n)|0;g=d+m|0;d=Fa(g,26)^Fa(g,21);d=((((e+K|0)+((g^-1)&f)|0)+(g&k)|0)+(Fa(g,7)^d)|0)-1046744716|0;i=d+i|0;m=d+l|0;d=16;while(1){u=d&8;e=(u<<2)+r|0;H=d|1;o=((H&9)<<2)+r|0;q=v[o>>2];l=v[e>>2]+(v[((d+9&9)<<2)+r>>2]+(Fa(q,25)^Fa(q,14)^q>>>3)|0)|0;s=v[((d+14&14)<<2)+r>>2];l=(Fa(s,15)^Fa(s,13)^s>>>10)+l|0;v[e>>2]=l;p=q+v[((d+10&10)<<2)+r>>2]|0;q=v[e+8>>2];p=p+(Fa(q,25)^Fa(q,14)^q>>>3)|0;q=v[((d-1&15)<<2)+r>>2];I=p+(Fa(q,15)^Fa(q,13)^q>>>10)|0;v[o>>2]=I;J=d|2;o=((J&10)<<2)+r|0;z=v[o>>2]+(v[((d+11&11)<<2)+r>>2]+(Fa(l,15)^Fa(l,13)^l>>>10)|0)|0;p=o;o=v[e+12>>2];z=(Fa(o,25)^Fa(o,14)^o>>>3)+z|0;v[p>>2]=z;K=d|3;p=((K&11)<<2)+r|0;o=v[e+16>>2];o=v[p>>2]+(v[((d+12&12)<<2)+r>>2]+(Fa(o,25)^Fa(o,14)^o>>>3)|0)|0;t=p;p=o;o=v[e+4>>2];p=p+(Fa(o,15)^Fa(o,13)^o>>>10)|0;v[t>>2]=p;A=d|4;C=((A&12)<<2)+r|0;o=v[e+20>>2];o=v[C>>2]+(v[((d+13&13)<<2)+r>>2]+(Fa(o,25)^Fa(o,14)^o>>>3)|0)|0;t=o;o=v[e+8>>2];o=t+(Fa(o,15)^Fa(o,13)^o>>>10)|0;v[C>>2]=o;C=d|5;L=((C&13)<<2)+r|0;t=s+v[L>>2]|0;s=v[e+24>>2];s=t+(Fa(s,25)^Fa(s,14)^s>>>3)|0;t=s;s=v[e+12>>2];s=t+(Fa(s,15)^Fa(s,13)^s>>>10)|0;v[L>>2]=s;L=d|6;t=((L&14)<<2)+r|0;F=q+v[t>>2]|0;q=v[e+28>>2];q=F+(Fa(q,25)^Fa(q,14)^q>>>3)|0;F=t;t=q;q=v[e+16>>2];t=t+(Fa(q,15)^Fa(q,13)^q>>>10)|0;v[F>>2]=t;F=d|7;O=((F&15)<<2)+r|0;q=v[((u^8)<<2)+r>>2];q=(l+v[O>>2]|0)+(Fa(q,25)^Fa(q,14)^q>>>3)|0;e=v[e+20>>2];q=(Fa(e,15)^Fa(e,13)^e>>>10)+q|0;v[O>>2]=q;e=l+(v[(d<<2)+1024>>2]+((((g&m)+(Fa(m,26)^Fa(m,21)^Fa(m,7))|0)+f|0)+((m^-1)&k)|0)|0)|0;l=i&n;f=e+((Fa(i,30)^Fa(i,19)^Fa(i,10))+(l^(i^n)&j)|0)|0;u=l^f&n;l=f&i;u=(Fa(f,30)^Fa(f,19)^Fa(f,10))+(u^l)|0;h=e+h|0;e=I+((v[(H<<2)+1024>>2]+((((h^-1)&g)+k|0)+(h&m)|0)|0)+(Fa(h,26)^Fa(h,21)^Fa(h,7))|0)|0;k=u+e|0;u=l^i&k;l=f&k;u=(Fa(k,30)^Fa(k,19)^Fa(k,10))+(u^l)|0;j=e+j|0;e=(((z+(v[(J<<2)+1024>>2]+g|0)|0)+((j^-1)&m)|0)+(h&j)|0)+(Fa(j,26)^Fa(j,21)^Fa(j,7))|0;g=u+e|0;z=l^f&g;l=g&k;z=(Fa(g,30)^Fa(g,19)^Fa(g,10))+(z^l)|0;n=e+n|0;m=(((p+(v[(K<<2)+1024>>2]+m|0)|0)+(h&(n^-1))|0)+(j&n)|0)+(Fa(n,26)^Fa(n,21)^Fa(n,7))|0;e=z+m|0;p=l^e&k;l=e&g;p=(Fa(e,30)^Fa(e,19)^Fa(e,10))+(p^l)|0;i=i+m|0;m=(((o+(h+v[(A<<2)+1024>>2]|0)|0)+(j&(i^-1))|0)+(i&n)|0)+(Fa(i,26)^Fa(i,21)^Fa(i,7))|0;h=p+m|0;o=l^g&h;l=e&h;o=(Fa(h,30)^Fa(h,19)^Fa(h,10))+(o^l)|0;f=f+m|0;m=(((s+(j+v[(C<<2)+1024>>2]|0)|0)+(n&(f^-1))|0)+(f&i)|0)+(Fa(f,26)^Fa(f,21)^Fa(f,7))|0;j=o+m|0;o=l^e&j;l=h&j;o=(Fa(j,30)^Fa(j,19)^Fa(j,10))+(o^l)|0;k=k+m|0;m=(((t+(n+v[(L<<2)+1024>>2]|0)|0)+(i&(k^-1))|0)+(f&k)|0)+(Fa(k,26)^Fa(k,21)^Fa(k,7))|0;n=o+m|0;o=(Fa(n,30)^Fa(n,19)^Fa(n,10))+(l^(h^j)&n)|0;g=g+m|0;m=(((q+(i+v[(F<<2)+1024>>2]|0)|0)+((g^-1)&f)|0)+(g&k)|0)+(Fa(g,26)^Fa(g,21)^Fa(g,7))|0;i=o+m|0;m=e+m|0;e=d>>>0<56;d=d+8|0;if(e){continue}break}N=f+N|0;v[a+28>>2]=N;G=k+G|0;v[a+24>>2]=G;D=g+D|0;v[a+20>>2]=D;x=m+x|0;v[a+16>>2]=x;M=h+M|0;v[a+12>>2]=M;E=j+E|0;v[a+8>>2]=E;B=n+B|0;v[a+4>>2]=B;y=i+y|0;v[a>>2]=y;b=b- -64|0;c=c-1|0;if(c){continue}break}}}function la(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,x=0,y=0,z=0,A=0;if(c){j=v[a>>2];e=v[a+4>>2];d=v[a+8>>2];g=v[a+12>>2];while(1){k=w[b+16|0]|w[b+17|0]<<8|(w[b+18|0]<<16|w[b+19|0]<<24);n=w[b+32|0]|w[b+33|0]<<8|(w[b+34|0]<<16|w[b+35|0]<<24);o=w[b+48|0]|w[b+49|0]<<8|(w[b+50|0]<<16|w[b+51|0]<<24);p=w[b+36|0]|w[b+37|0]<<8|(w[b+38|0]<<16|w[b+39|0]<<24);q=w[b+52|0]|w[b+53|0]<<8|(w[b+54|0]<<16|w[b+55|0]<<24);r=w[b+4|0]|w[b+5|0]<<8|(w[b+6|0]<<16|w[b+7|0]<<24);l=w[b+20|0]|w[b+21|0]<<8|(w[b+22|0]<<16|w[b+23|0]<<24);m=w[b|0]|w[b+1|0]<<8|(w[b+2|0]<<16|w[b+3|0]<<24);f=Fa((m+(((d^g)&e^g)+j|0)|0)-680876936|0,7)+e|0;j=w[b+12|0]|w[b+13|0]<<8|(w[b+14|0]<<16|w[b+15|0]<<24);s=w[b+8|0]|w[b+9|0]<<8|(w[b+10|0]<<16|w[b+11|0]<<24);g=Fa(((g+r|0)+(f&(d^e)^d)|0)-389564586|0,12)+f|0;d=Fa(((s+d|0)+(g&(e^f)^e)|0)+606105819|0,17)+g|0;i=Fa(((e+j|0)+(f^d&(f^g))|0)-1044525330|0,22)+d|0;e=Fa(((f+k|0)+(g^i&(d^g))|0)-176418897|0,7)+i|0;t=w[b+28|0]|w[b+29|0]<<8|(w[b+30|0]<<16|w[b+31|0]<<24);u=w[b+24|0]|w[b+25|0]<<8|(w[b+26|0]<<16|w[b+27|0]<<24);f=d+u|0;d=Fa(((g+l|0)+(d^e&(d^i))|0)+1200080426|0,12)+e|0;f=Fa((f+(i^d&(e^i))|0)-1473231341|0,17)+d|0;g=Fa(((i+t|0)+(e^f&(d^e))|0)-45705983|0,22)+f|0;e=Fa(((e+n|0)+(d^g&(d^f))|0)+1770035416|0,7)+g|0;i=w[b+44|0]|w[b+45|0]<<8|(w[b+46|0]<<16|w[b+47|0]<<24);x=w[b+40|0]|w[b+41|0]<<8|(w[b+42|0]<<16|w[b+43|0]<<24);d=Fa(((d+p|0)+(f^e&(f^g))|0)-1958414417|0,12)+e|0;f=Fa(((f+x|0)+(g^d&(e^g))|0)-42063|0,17)+d|0;g=Fa(((g+i|0)+(e^f&(d^e))|0)-1990404162|0,22)+f|0;e=Fa(((e+o|0)+(d^g&(d^f))|0)+1804603682|0,7)+g|0;y=w[b+60|0]|w[b+61|0]<<8|(w[b+62|0]<<16|w[b+63|0]<<24);A=e+r|0;z=w[b+56|0]|w[b+57|0]<<8|(w[b+58|0]<<16|w[b+59|0]<<24);h=Fa(((d+q|0)+(f^e&(f^g))|0)-40341101|0,12)+e|0;f=Fa(((f+z|0)+(g^h&(e^g))|0)-1502002290|0,17)+h|0;e=Fa(((g+y|0)+(e^f&(e^h))|0)+1236535329|0,22)+f|0;d=Fa((A+((f^e)&h^f)|0)-165796510|0,5)+e|0;g=f+i|0;f=Fa(((h+u|0)+(e^f&(d^e))|0)-1069501632|0,9)+d|0;g=Fa((g+(d^e&(f^d))|0)+643717713|0,14)+f|0;e=Fa(((e+m|0)+(f^d&(f^g))|0)-373897302|0,20)+g|0;d=Fa(((d+l|0)+((g^e)&f^g)|0)-701558691|0,5)+e|0;f=Fa(((f+x|0)+(e^g&(d^e))|0)+38016083|0,9)+d|0;g=Fa(((g+y|0)+(d^e&(f^d))|0)-660478335|0,14)+f|0;e=Fa(((e+k|0)+(f^d&(f^g))|0)-405537848|0,20)+g|0;d=Fa(((d+p|0)+((g^e)&f^g)|0)+568446438|0,5)+e|0;h=d+q|0;f=Fa(((f+z|0)+(e^g&(d^e))|0)-1019803690|0,9)+d|0;g=Fa(((g+j|0)+(d^e&(f^d))|0)-187363961|0,14)+f|0;d=Fa(((e+n|0)+(f^d&(f^g))|0)+1163531501|0,20)+g|0;e=Fa((h+((g^d)&f^g)|0)-1444681467|0,5)+d|0;f=Fa(((f+s|0)+(d^g&(d^e))|0)-51403784|0,9)+e|0;g=Fa(((g+t|0)+(e^d&(f^e))|0)+1735328473|0,14)+f|0;h=f+n|0;A=d+o|0;d=f^g;f=Fa((A+(f^d&e)|0)-1926607734|0,20)+g|0;d=Fa(((e+l|0)+(d^f)|0)-378558|0,4)+f|0;e=Fa((h+(f^g^d)|0)-2022574463|0,11)+d|0;g=Fa(((g+i|0)+(e^(d^f))|0)+1839030562|0,16)+e|0;f=Fa(((f+z|0)+(g^(d^e))|0)-35309556|0,23)+g|0;d=Fa(((d+r|0)+(f^(e^g))|0)-1530992060|0,4)+f|0;e=Fa(((e+k|0)+(d^(f^g))|0)+1272893353|0,11)+d|0;g=Fa(((g+t|0)+(e^(d^f))|0)-155497632|0,16)+e|0;f=Fa(((f+x|0)+(g^(d^e))|0)-1094730640|0,23)+g|0;d=Fa(((d+q|0)+(f^(e^g))|0)+681279174|0,4)+f|0;e=Fa(((e+m|0)+(d^(f^g))|0)-358537222|0,11)+d|0;g=Fa(((g+j|0)+(e^(d^f))|0)-722521979|0,16)+e|0;f=Fa(((f+u|0)+(g^(d^e))|0)+76029189|0,23)+g|0;d=Fa(((d+p|0)+(f^(e^g))|0)-640364487|0,4)+f|0;e=Fa(((e+o|0)+(d^(f^g))|0)-421815835|0,11)+d|0;h=d+m|0;m=d^e;d=Fa(((g+y|0)+(e^(d^f))|0)+530742520|0,16)+e|0;g=Fa(((f+s|0)+(m^d)|0)-995338651|0,23)+d|0;f=Fa((h+((g|e^-1)^d)|0)-198630844|0,6)+g|0;h=g+l|0;l=d+z|0;d=Fa(((e+t|0)+(g^(f|d^-1))|0)+1126891415|0,10)+f|0;g=Fa((l+(f^(d|g^-1))|0)-1416354905|0,15)+d|0;e=Fa((h+((g|f^-1)^d)|0)-57434055|0,21)+g|0;h=g+x|0;j=d+j|0;d=Fa(((f+o|0)+(g^(e|d^-1))|0)+1700485571|0,6)+e|0;g=Fa((j+(e^(d|g^-1))|0)-1894986606|0,10)+d|0;f=Fa((h+((g|e^-1)^d)|0)-1051523|0,15)+g|0;j=g+y|0;h=d+n|0;d=Fa(((e+r|0)+(g^(f|d^-1))|0)-2054922799|0,21)+f|0;g=Fa((h+(f^(d|g^-1))|0)+1873313359|0,6)+d|0;e=Fa((j+((g|f^-1)^d)|0)-30611744|0,10)+g|0;j=g+k|0;k=d+q|0;d=Fa(((f+u|0)+(g^(e|d^-1))|0)-1560198380|0,15)+e|0;k=Fa((k+(e^(d|g^-1))|0)+1309151649|0,21)+d|0;f=Fa((j+((k|e^-1)^d)|0)-145523070|0,6)+k|0;j=f+v[a>>2]|0;v[a>>2]=j;e=Fa(((e+i|0)+(k^(f|d^-1))|0)-1120210379|0,10)+f|0;g=e+v[a+12>>2]|0;v[a+12>>2]=g;i=Fa(((d+s|0)+(f^(e|k^-1))|0)+718787259|0,15)+e|0;d=i+v[a+8>>2]|0;v[a+8>>2]=d;e=(i+v[a+4>>2]|0)+Fa(((k+p|0)+(e^(i|f^-1))|0)-343485551|0,21)|0;v[a+4>>2]=e;b=b- -64|0;c=c-1|0;if(c){continue}break}}}function Da(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;e=Q-32|0;Q=e;g=b;while(1){m=g;g=g+1|0;if(w[m|0]){continue}break}v[e+28>>2]=0;v[e+24>>2]=0;k=a;f=a;while(1){a=f;f=a+1|0;if(w[a|0]){continue}break}p=a-k|0;a=da(p<<1);v[e+28>>2]=a;if(p){f=0;while(1){l=f+k|0;n=w[l|0];a:{if(!(n&128)){t[a+1|0]=0;t[a|0]=n;f=f+1|0;a=a+2|0;break a}if(!(n&32)){g=a;o=a;i=f+1|0;if(i>>>0<p>>>0){i=n<<6&1984|w[k+i|0]&63}else{i=0}t[o|0]=i;t[g+1|0]=i>>>8;f=f+2|0;a=a+2|0;break a}if(!(n&16)){g=a;o=a;i=f+2|0;if(i>>>0<p>>>0){i=w[k+i|0]&63|(n<<12&61440|(w[l+1|0]&63)<<6)}else{i=0}t[o|0]=i;t[g+1|0]=i>>>8;f=f+3|0;a=a+2|0;break a}if(!(n&15)){h=0;b:{g=f+3|0;if(g>>>0<p>>>0){i=w[l+2|0];h=w[g+k|0]&63|(n<<18&1835008|(w[l+1|0]&63)<<12|(i&63)<<6);if(h>>>0>65535){break b}}t[a|0]=h;t[a+1|0]=h>>>8;f=f+4|0;a=a+2|0;break a}t[a+2|0]=h;g=h-65536|0;t[a|0]=g>>>10;t[a+3|0]=i>>>2&3|220;t[a+1|0]=g>>>18&3|216;f=f+4|0;a=a+4|0;break a}if(!(n&8)){h=0;c:{g=f+3|0;if(g>>>0<p>>>0){i=w[l+2|0];h=w[g+k|0]&63|(n<<18&1835008|(w[l+1|0]&63)<<12|(i&63)<<6);if(h>>>0>65535){break c}}t[a|0]=h;t[a+1|0]=h>>>8;f=f+4|0;a=a+2|0;break a}t[a+2|0]=h;g=h-65536|0;t[a|0]=g>>>10;t[a+3|0]=i>>>2&3|220;t[a+1|0]=g>>>18&3|216;f=f+4|0;a=a+4|0;break a}if(!(n&4)){h=0;d:{g=f+4|0;if(g>>>0<p>>>0){i=n<<24&50331648|(w[l+1|0]&63)<<18|(w[l+2|0]&63)<<12|(w[l+3|0]&63)<<6;h=i|w[g+k|0]&63;if(h>>>0>65535){break d}}t[a|0]=h;t[a+1|0]=h>>>8;f=f+5|0;a=a+2|0;break a}t[a+2|0]=h;g=h-65536|0;t[a|0]=g>>>10;t[a+3|0]=i>>>8&3|220;t[a+1|0]=g>>>18&3|216;f=f+5|0;a=a+4|0;break a}h=0;e:{f=f+5|0;if(p>>>0>f>>>0){i=n<<30&1073741824|(w[l+1|0]&63)<<24|(w[l+2|0]&63)<<18|(w[l+3|0]&63)<<12|(w[l+4|0]&63)<<6;h=i|w[f+k|0]&63;if(h>>>0>65535){break e}}t[a|0]=h;t[a+1|0]=h>>>8;a=a+2|0;break a}t[a+2|0]=h;g=h-65536|0;t[a|0]=g>>>10;t[a+3|0]=i>>>8&3|220;t[a+1|0]=g>>>18&3|216;a=a+4|0}if(f>>>0<p>>>0){continue}break}g=v[e+28>>2]}else{g=a}v[e+24>>2]=a-g;i=v[e+24>>2];f=m-b|0;a=i+f|0;v[e+20>>2]=a;q=da(a);o=q;g=0;n=e+20|0;if(!(!b|!n)){r=!o;f:{if((f|0)<=0){break f}p=b+f|0;while(1){l=w[b|0];if(!l){break f}f=0;g:{h:{if(b>>>0<p>>>0){a=0;h=0;while(1){k=l<<24>>24;m=k-65|0;i:{if(m>>>0<26){break i}if(k-97>>>0<=25){m=k-71|0;break i}if(k-48>>>0<=9){m=k+4|0;break i}m=l&255;m=(m|0)==43?62:(m|0)==47?63:-1}k=(m|0)==-1;f=k?f:f+6|0;h=k?h:h<<6|m;b=b+1|0;a=(a-k|0)+1|0;if(p>>>0<=b>>>0|(a|0)>3){break h}l=w[b|0];continue}}if(r){r=1;break g}r=y[n>>2]<g>>>0;break g}k=(f|0)/8|0;j:{k:{l:{if(!r){a=g+k|0;m=v[n>>2];r=a>>>0>m>>>0;if((f|0)<=7){break g}if(a>>>0>m>>>0){break l}l=k&3;f=h<<24-f;if(k-1>>>0>=3){break k}m=f;break j}r=1;if((f|0)<=7){break g}}g=g+k|0;r=1;break g}h=k&-4;while(1){m=0;t[o+3|0]=0;t[o+2|0]=f;t[o+1|0]=f>>>8;t[o|0]=f>>>16;o=o+4|0;f=0;h=h-4|0;if(h){continue}break}}if(l){while(1){t[o|0]=m>>>16;m=m<<8;o=o+1|0;l=l-1|0;if(l){continue}break}}g=a}if(b>>>0<p>>>0){continue}break}}v[n>>2]=g}b=v[e+20>>2];a=v[e+28>>2];ca(b+q|0,a,i);v[e+20>>2]=b+i;ia(a);v[e+16>>2]=0;a=0;m:{n:{o:{switch(d|0){case 0:j=da(20);v[e+16>>2]=j;ua(q,v[e+20>>2],j);a=16;break m;case 1:j=da(20);v[e+16>>2]=j;ta(q,v[e+20>>2],j);a=16;break m;case 2:j=da(20);v[e+16>>2]=j;sa(q,v[e+20>>2],j);a=16;break m;case 3:j=da(24);v[e+16>>2]=j;ra(q,v[e+20>>2],j);a=20;break m;case 4:j=da(24);v[e+16>>2]=j;wa(q,v[e+20>>2],j);a=20;break m;case 5:j=da(36);v[e+16>>2]=j;pa(q,v[e+20>>2],j);a=32;break m;case 6:j=da(52);v[e+16>>2]=j;oa(q,v[e+20>>2],j);a=48;break m;case 7:j=da(68);v[e+16>>2]=j;va(q,v[e+20>>2],j);break n;case 8:break o;default:break m}}j=da(68);v[e+16>>2]=j;qa(q,v[e+20>>2],j)}a=64}ia(q);if(c){b=da(a+4|0);v[e+12>>2]=b;g=0;if((c|0)>0){while(1){t[v[e+16>>2]+a|0]=g;t[(v[e+16>>2]+a|0)+1|0]=g>>>8;t[(v[e+16>>2]+a|0)+2|0]=g>>>16;t[(v[e+16>>2]+a|0)+3|0]=g>>>24;p:{q:{switch(d|0){case 0:ua(v[e+16>>2],a+4|0,v[e+12>>2]);break p;case 1:ta(v[e+16>>2],a+4|0,v[e+12>>2]);break p;case 2:sa(v[e+16>>2],a+4|0,v[e+12>>2]);break p;case 3:ra(v[e+16>>2],a+4|0,v[e+12>>2]);break p;case 4:wa(v[e+16>>2],a+4|0,v[e+12>>2]);break p;case 5:pa(v[e+16>>2],a+4|0,v[e+12>>2]);break p;case 6:oa(v[e+16>>2],a+4|0,v[e+12>>2]);break p;case 7:va(v[e+16>>2],a+4|0,v[e+12>>2]);break p;case 8:break q;default:break p}}qa(v[e+16>>2],a+4|0,v[e+12>>2])}b=v[e+16>>2];v[e+16>>2]=v[e+12>>2];v[e+12>>2]=b;g=g+1|0;if((g|0)!=(c|0)){continue}break}j=v[e+16>>2];b=v[e+12>>2]}ia(b)}Q=e+32|0;return j|0}function qa(a,b,c){var d=0,e=0,f=0,g=0;f=Q-176|0;Q=f;ba(f+8|0,0,168);if(b>>>0>=268435456){while(1){Aa(f+8|0,a,-2147483648);a=a+268435456|0;b=b-268435456|0;if(b>>>0>268435455){continue}break}}if(b){Aa(f+8|0,a,b<<3)}b=c?c:18912;a=f+8|0;c=v[a+128>>2];d=c>>>3|0;e=c&7;a:{if(e){g=(a+d|0)- -64|0;t[g|0]=w[g|0]|128>>>e;break a}t[(a+d|0)- -64|0]=128}e=d+1|0;b:{c:{if(c>>>0>=256){if(c>>>0<=503){ba((a+e|0)- -64|0,0,63-d|0)}fa(a,a- -64|0,1);e=0;break c}if(c>>>0>247){break b}}ba((a+e|0)- -64|0,0,32-e|0)}c=v[a+132>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[a+124|0]=c;t[a+125|0]=c>>>8;t[a+126|0]=c>>>16;t[a+127|0]=c>>>24;c=v[a+136>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[a+120|0]=c;t[a+121|0]=c>>>8;t[a+122|0]=c>>>16;t[a+123|0]=c>>>24;c=v[a+140>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[a+116|0]=c;t[a+117|0]=c>>>8;t[a+118|0]=c>>>16;t[a+119|0]=c>>>24;c=v[a+144>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[a+112|0]=c;t[a+113|0]=c>>>8;t[a+114|0]=c>>>16;t[a+115|0]=c>>>24;c=v[a+148>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[a+108|0]=c;t[a+109|0]=c>>>8;t[a+110|0]=c>>>16;t[a+111|0]=c>>>24;c=v[a+152>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[a+104|0]=c;t[a+105|0]=c>>>8;t[a+106|0]=c>>>16;t[a+107|0]=c>>>24;c=v[a+156>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[a+100|0]=c;t[a+101|0]=c>>>8;t[a+102|0]=c>>>16;t[a+103|0]=c>>>24;c=v[a+160>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[a+96|0]=c;t[a+97|0]=c>>>8;t[a+98|0]=c>>>16;t[a+99|0]=c>>>24;fa(a,a- -64|0,1);if(b){c=w[a+4|0]|w[a+5|0]<<8|(w[a+6|0]<<16|w[a+7|0]<<24);d=w[a|0]|w[a+1|0]<<8|(w[a+2|0]<<16|w[a+3|0]<<24);t[b|0]=d;t[b+1|0]=d>>>8;t[b+2|0]=d>>>16;t[b+3|0]=d>>>24;t[b+4|0]=c;t[b+5|0]=c>>>8;t[b+6|0]=c>>>16;t[b+7|0]=c>>>24;c=w[a+60|0]|w[a+61|0]<<8|(w[a+62|0]<<16|w[a+63|0]<<24);d=w[a+56|0]|w[a+57|0]<<8|(w[a+58|0]<<16|w[a+59|0]<<24);t[b+56|0]=d;t[b+57|0]=d>>>8;t[b+58|0]=d>>>16;t[b+59|0]=d>>>24;t[b+60|0]=c;t[b+61|0]=c>>>8;t[b+62|0]=c>>>16;t[b+63|0]=c>>>24;c=w[a+52|0]|w[a+53|0]<<8|(w[a+54|0]<<16|w[a+55|0]<<24);d=w[a+48|0]|w[a+49|0]<<8|(w[a+50|0]<<16|w[a+51|0]<<24);t[b+48|0]=d;t[b+49|0]=d>>>8;t[b+50|0]=d>>>16;t[b+51|0]=d>>>24;t[b+52|0]=c;t[b+53|0]=c>>>8;t[b+54|0]=c>>>16;t[b+55|0]=c>>>24;c=w[a+44|0]|w[a+45|0]<<8|(w[a+46|0]<<16|w[a+47|0]<<24);d=w[a+40|0]|w[a+41|0]<<8|(w[a+42|0]<<16|w[a+43|0]<<24);t[b+40|0]=d;t[b+41|0]=d>>>8;t[b+42|0]=d>>>16;t[b+43|0]=d>>>24;t[b+44|0]=c;t[b+45|0]=c>>>8;t[b+46|0]=c>>>16;t[b+47|0]=c>>>24;c=w[a+36|0]|w[a+37|0]<<8|(w[a+38|0]<<16|w[a+39|0]<<24);d=w[a+32|0]|w[a+33|0]<<8|(w[a+34|0]<<16|w[a+35|0]<<24);t[b+32|0]=d;t[b+33|0]=d>>>8;t[b+34|0]=d>>>16;t[b+35|0]=d>>>24;t[b+36|0]=c;t[b+37|0]=c>>>8;t[b+38|0]=c>>>16;t[b+39|0]=c>>>24;c=w[a+28|0]|w[a+29|0]<<8|(w[a+30|0]<<16|w[a+31|0]<<24);d=w[a+24|0]|w[a+25|0]<<8|(w[a+26|0]<<16|w[a+27|0]<<24);t[b+24|0]=d;t[b+25|0]=d>>>8;t[b+26|0]=d>>>16;t[b+27|0]=d>>>24;t[b+28|0]=c;t[b+29|0]=c>>>8;t[b+30|0]=c>>>16;t[b+31|0]=c>>>24;c=w[a+20|0]|w[a+21|0]<<8|(w[a+22|0]<<16|w[a+23|0]<<24);d=w[a+16|0]|w[a+17|0]<<8|(w[a+18|0]<<16|w[a+19|0]<<24);t[b+16|0]=d;t[b+17|0]=d>>>8;t[b+18|0]=d>>>16;t[b+19|0]=d>>>24;t[b+20|0]=c;t[b+21|0]=c>>>8;t[b+22|0]=c>>>16;t[b+23|0]=c>>>24;c=w[a+12|0]|w[a+13|0]<<8|(w[a+14|0]<<16|w[a+15|0]<<24);d=w[a+8|0]|w[a+9|0]<<8|(w[a+10|0]<<16|w[a+11|0]<<24);t[b+8|0]=d;t[b+9|0]=d>>>8;t[b+10|0]=d>>>16;t[b+11|0]=d>>>24;t[b+12|0]=c;t[b+13|0]=c>>>8;t[b+14|0]=c>>>16;t[b+15|0]=c>>>24;S[v[4660]](a,0,168)|0}Q=f+176|0}function ma(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,x=0,y=0,z=0,A=0,B=0;if(c){h=v[a>>2];d=v[a+4>>2];g=v[a+8>>2];e=v[a+12>>2];while(1){l=w[b+12|0]|w[b+13|0]<<8|(w[b+14|0]<<16|w[b+15|0]<<24);k=w[b+8|0]|w[b+9|0]<<8|(w[b+10|0]<<16|w[b+11|0]<<24);m=w[b+48|0]|w[b+49|0]<<8|(w[b+50|0]<<16|w[b+51|0]<<24);n=w[b+32|0]|w[b+33|0]<<8|(w[b+34|0]<<16|w[b+35|0]<<24);o=w[b+16|0]|w[b+17|0]<<8|(w[b+18|0]<<16|w[b+19|0]<<24);p=w[b+4|0]|w[b+5|0]<<8|(w[b+6|0]<<16|w[b+7|0]<<24);q=w[b|0]|w[b+1|0]<<8|(w[b+2|0]<<16|w[b+3|0]<<24);f=Fa(q+(((e^g)&d^e)+h|0)|0,3);e=Fa((p+e|0)+(f&(d^g)^g)|0,7);j=Fa((g+k|0)+(e&(d^f)^d)|0,11);d=Fa((d+l|0)+(f^j&(e^f))|0,19);g=Fa((f+o|0)+(e^d&(e^j))|0,3);r=w[b+28|0]|w[b+29|0]<<8|(w[b+30|0]<<16|w[b+31|0]<<24);s=w[b+24|0]|w[b+25|0]<<8|(w[b+26|0]<<16|w[b+27|0]<<24);t=w[b+20|0]|w[b+21|0]<<8|(w[b+22|0]<<16|w[b+23|0]<<24);f=Fa((e+t|0)+(j^g&(d^j))|0,7);e=Fa((s+j|0)+(d^f&(d^g))|0,11);j=Fa((d+r|0)+(g^e&(g^f))|0,19);d=Fa((g+n|0)+(f^j&(e^f))|0,3);u=w[b+44|0]|w[b+45|0]<<8|(w[b+46|0]<<16|w[b+47|0]<<24);x=w[b+40|0]|w[b+41|0]<<8|(w[b+42|0]<<16|w[b+43|0]<<24);g=e+x|0;y=w[b+36|0]|w[b+37|0]<<8|(w[b+38|0]<<16|w[b+39|0]<<24);e=Fa((f+y|0)+(e^d&(e^j))|0,7);g=Fa(g+(j^e&(d^j))|0,11);f=Fa((j+u|0)+(d^g&(d^e))|0,19);d=Fa((d+m|0)+(e^f&(e^g))|0,3);j=w[b+60|0]|w[b+61|0]<<8|(w[b+62|0]<<16|w[b+63|0]<<24);z=w[b+56|0]|w[b+57|0]<<8|(w[b+58|0]<<16|w[b+59|0]<<24);A=w[b+52|0]|w[b+53|0]<<8|(w[b+54|0]<<16|w[b+55|0]<<24);h=Fa((e+A|0)+(g^d&(g^f))|0,7);g=Fa((g+z|0)+(f^h&(d^f))|0,11);e=Fa((f+j|0)+(d^g&(d^h))|0,19);f=g&e;d=Fa(((d+q|0)+(f|h&(e|g))|0)+1518500249|0,3);i=d&e;f=Fa(((h+o|0)+(i|(f|d&g))|0)+1518500249|0,5);h=f&d;g=Fa(((g+n|0)+(h|(i|e&f))|0)+1518500249|0,9);i=g&f;e=Fa(((e+m|0)+(i|(h|d&g))|0)+1518500249|0,13);h=e&g;d=Fa(((d+p|0)+(h|(i|e&f))|0)+1518500249|0,3);i=d&e;f=Fa(((f+t|0)+(i|(h|d&g))|0)+1518500249|0,5);h=f&d;g=Fa(((g+y|0)+(h|(i|e&f))|0)+1518500249|0,9);i=g&f;e=Fa(((e+A|0)+(i|(h|d&g))|0)+1518500249|0,13);h=e&g;d=Fa(((d+k|0)+(h|(i|e&f))|0)+1518500249|0,3);i=d&e;f=Fa(((f+s|0)+(i|(h|d&g))|0)+1518500249|0,5);h=f&d;g=Fa(((g+x|0)+(h|(i|e&f))|0)+1518500249|0,9);i=g&f;e=Fa(((e+z|0)+(i|(h|d&g))|0)+1518500249|0,13);h=e&g;d=Fa(((d+l|0)+(h|(i|e&f))|0)+1518500249|0,3);i=d&e;f=Fa(((f+r|0)+(i|(h|d&g))|0)+1518500249|0,5);h=f&d;B=e+j|0;e=Fa(((g+u|0)+(h|(i|e&f))|0)+1518500249|0,9);g=Fa((B+(h|e&(d|f))|0)+1518500249|0,13);h=e+o|0;i=d+q|0;d=e^g;e=Fa((i+(d^f)|0)+1859775393|0,3);f=Fa(((f+n|0)+(d^e)|0)+1859775393|0,9);d=Fa((h+(e^g^f)|0)+1859775393|0,11);h=e+k|0;e=Fa(((g+m|0)+(d^(e^f))|0)+1859775393|0,15);g=Fa((h+(e^(d^f))|0)+1859775393|0,3);f=Fa(((f+x|0)+(g^(d^e))|0)+1859775393|0,9);d=Fa(((d+s|0)+(f^(e^g))|0)+1859775393|0,11);e=Fa(((e+z|0)+(d^(g^f))|0)+1859775393|0,15);g=Fa(((g+p|0)+(e^(d^f))|0)+1859775393|0,3);k=Fa(((f+y|0)+(g^(d^e))|0)+1859775393|0,9);d=Fa(((d+t|0)+(k^(e^g))|0)+1859775393|0,11);f=g+l|0;l=Fa(((e+A|0)+(d^(g^k))|0)+1859775393|0,15);f=Fa((f+(l^(d^k))|0)+1859775393|0,3);h=f+v[a>>2]|0;v[a>>2]=h;k=Fa(((k+u|0)+(f^(d^l))|0)+1859775393|0,9);e=k+v[a+12>>2]|0;v[a+12>>2]=e;d=Fa(((d+r|0)+(k^(f^l))|0)+1859775393|0,11);g=d+v[a+8>>2]|0;v[a+8>>2]=g;d=v[a+4>>2]+Fa(((j+l|0)+(d^(f^k))|0)+1859775393|0,15)|0;v[a+4>>2]=d;b=b- -64|0;c=c-1|0;if(c){continue}break}}}function pa(a,b,c){var d=0,e=0,f=0,g=0,h=0;d=Q-112|0;Q=d;ba(d+32|0,0,76);v[d+108>>2]=32;v[d+24>>2]=528734635;v[d+28>>2]=1541459225;v[d+16>>2]=1359893119;v[d+20>>2]=-1694144372;v[d+8>>2]=1013904242;v[d+12>>2]=-1521486534;v[d>>2]=1779033703;v[d+4>>2]=-1150833019;a:{if(!b){break a}e=v[d+32>>2];f=e+(b<<3)|0;v[d+32>>2]=f;v[d+36>>2]=v[d+36>>2]+(e>>>0>f>>>0)+(b>>>29);b:{e=v[d+104>>2];if(e){f=d+40|0;g=f+e|0;if(b+e>>>0<64&b>>>0<=63){break b}e=64-e|0;ca(g,a,e);ha(d,f,1);ba(f,0,68);b=b-e|0;a=a+e|0}if(b>>>0>=64){ha(d,a,b>>>6|0);a=(b&-64)+a|0;b=b&63}if(!b){break a}v[d+104>>2]=b;ca(d+40|0,a,b);break a}ca(g,a,b);v[d+104>>2]=v[d+104>>2]+b}a=c?c:18688;b=d+40|0;c=v[d+104>>2];t[b+c|0]=128;e=c+1|0;if(e>>>0>=57){ba(b+e|0,0,63-c|0);ha(d,b,1);e=0}ba(b+e|0,0,56-e|0);c=v[d+36>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[d+96|0]=c;t[d+97|0]=c>>>8;t[d+98|0]=c>>>16;t[d+99|0]=c>>>24;c=v[d+32>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[d+100|0]=c;t[d+101|0]=c>>>8;t[d+102|0]=c>>>16;t[d+103|0]=c>>>24;ha(d,b,1);v[d+104>>2]=0;S[v[4660]](b,0,64)|0;c:{d:{e:{f:{g:{b=v[d+108>>2];switch(b-28|0){case 0:break g;case 4:break e;default:break f}}b=v[d>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a|0]=b;t[a+1|0]=b>>>8;t[a+2|0]=b>>>16;t[a+3|0]=b>>>24;b=v[d+4>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+4|0]=b;t[a+5|0]=b>>>8;t[a+6|0]=b>>>16;t[a+7|0]=b>>>24;b=v[d+8>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+8|0]=b;t[a+9|0]=b>>>8;t[a+10|0]=b>>>16;t[a+11|0]=b>>>24;b=v[d+12>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+12|0]=b;t[a+13|0]=b>>>8;t[a+14|0]=b>>>16;t[a+15|0]=b>>>24;b=v[d+16>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+16|0]=b;t[a+17|0]=b>>>8;t[a+18|0]=b>>>16;t[a+19|0]=b>>>24;b=v[d+20>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+20|0]=b;t[a+21|0]=b>>>8;t[a+22|0]=b>>>16;t[a+23|0]=b>>>24;f=a+24|0;e=27;c=26;g=d+24|0;b=25;break d}if(b>>>0<4|b>>>0>32){break c}e=0;while(1){b=v[(e<<2)+d>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a|0]=b;t[a+1|0]=b>>>8;t[a+2|0]=b>>>16;t[a+3|0]=b>>>24;a=a+4|0;e=e+1|0;if(e>>>0<v[d+108>>2]>>>2>>>0){continue}break}break c}b=v[d>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a|0]=b;t[a+1|0]=b>>>8;t[a+2|0]=b>>>16;t[a+3|0]=b>>>24;b=v[d+4>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+4|0]=b;t[a+5|0]=b>>>8;t[a+6|0]=b>>>16;t[a+7|0]=b>>>24;b=v[d+8>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+8|0]=b;t[a+9|0]=b>>>8;t[a+10|0]=b>>>16;t[a+11|0]=b>>>24;b=v[d+12>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+12|0]=b;t[a+13|0]=b>>>8;t[a+14|0]=b>>>16;t[a+15|0]=b>>>24;b=v[d+16>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+16|0]=b;t[a+17|0]=b>>>8;t[a+18|0]=b>>>16;t[a+19|0]=b>>>24;b=v[d+20>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+20|0]=b;t[a+21|0]=b>>>8;t[a+22|0]=b>>>16;t[a+23|0]=b>>>24;b=v[d+24>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+24|0]=b;t[a+25|0]=b>>>8;t[a+26|0]=b>>>16;t[a+27|0]=b>>>24;f=a+28|0;e=31;c=30;g=d+28|0;b=29}h=f;f=v[g>>2];t[h|0]=f>>>24;t[a+b|0]=f>>>16;t[a+c|0]=f>>>8;t[a+e|0]=f}S[v[4660]](d,0,112)|0;Q=d+112|0}function na(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,u=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,R=0,T=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0;c=Q-48|0;Q=c;e=w[a+35|0];while(1){f=c+d|0;g=a+d|0;i=w[g+36|0];t[f|0]=i;h=w[b+d|0];t[f+16|0]=h;t[f+32|0]=h^i;e=w[g+20|0]^w[((e^h)&255)+1920|0];t[g+20|0]=e;d=d+1|0;if((d|0)!=16){continue}break}d=0;f=w[c+47|0];g=w[c+46|0];e=w[c+45|0];h=w[c+44|0];i=w[c+43|0];b=w[c+42|0];j=w[c+41|0];k=w[c+40|0];l=w[c+39|0];m=w[c+38|0];n=w[c+37|0];o=w[c+36|0];p=w[c+35|0];q=w[c+34|0];r=w[c+33|0];s=w[c+32|0];u=w[c+31|0];x=w[c+30|0];y=w[c+29|0];z=w[c+28|0];A=w[c+27|0];B=w[c+26|0];C=w[c+25|0];D=w[c+24|0];E=w[c+23|0];F=w[c+22|0];G=w[c+21|0];H=w[c+20|0];I=w[c+19|0];J=w[c+18|0];K=w[c+17|0];L=w[c+16|0];M=w[c+15|0];N=w[c+14|0];O=w[c+13|0];P=w[c+12|0];R=w[c+11|0];T=w[c+10|0];U=w[c+9|0];V=w[c+8|0];W=w[c+7|0];X=w[c+6|0];Y=w[c+5|0];Z=w[c+4|0];_=w[c+3|0];$=w[c+2|0];aa=w[c+1|0];ba=w[c|0];while(1){ba=w[(ca&255)+1920|0]^ba;aa=w[(ba&255)+1920|0]^aa;$=w[(aa&255)+1920|0]^$;_=w[($&255)+1920|0]^_;Z=w[(_&255)+1920|0]^Z;Y=w[(Z&255)+1920|0]^Y;X=w[(Y&255)+1920|0]^X;W=w[(X&255)+1920|0]^W;V=w[(W&255)+1920|0]^V;U=w[(V&255)+1920|0]^U;T=w[(U&255)+1920|0]^T;R=w[(T&255)+1920|0]^R;P=w[(R&255)+1920|0]^P;O=w[(P&255)+1920|0]^O;N=w[(O&255)+1920|0]^N;M=w[(N&255)+1920|0]^M;L=w[(M&255)+1920|0]^L;K=w[(L&255)+1920|0]^K;J=w[(K&255)+1920|0]^J;I=w[(J&255)+1920|0]^I;H=w[(I&255)+1920|0]^H;G=w[(H&255)+1920|0]^G;F=w[(G&255)+1920|0]^F;E=w[(F&255)+1920|0]^E;D=w[(E&255)+1920|0]^D;C=w[(D&255)+1920|0]^C;B=w[(C&255)+1920|0]^B;A=w[(B&255)+1920|0]^A;z=w[(A&255)+1920|0]^z;y=w[(z&255)+1920|0]^y;x=w[(y&255)+1920|0]^x;u=w[(x&255)+1920|0]^u;s=w[(u&255)+1920|0]^s;r=w[(s&255)+1920|0]^r;q=w[(r&255)+1920|0]^q;p=w[(q&255)+1920|0]^p;o=w[(p&255)+1920|0]^o;n=w[(o&255)+1920|0]^n;m=w[(n&255)+1920|0]^m;l=w[(m&255)+1920|0]^l;k=w[(l&255)+1920|0]^k;j=w[(k&255)+1920|0]^j;b=w[(j&255)+1920|0]^b;i=w[(b&255)+1920|0]^i;h=w[(i&255)+1920|0]^h;e=w[(h&255)+1920|0]^e;g=w[(e&255)+1920|0]^g;f=w[(g&255)+1920|0]^f;ca=f+d|0;d=d+1|0;if((d|0)!=18){continue}break}t[c+47|0]=f;t[c+46|0]=g;t[c+45|0]=e;t[c+44|0]=h;t[c+43|0]=i;t[c+42|0]=b;t[c+41|0]=j;t[c+40|0]=k;t[c+39|0]=l;t[c+38|0]=m;t[c+37|0]=n;t[c+36|0]=o;t[c+35|0]=p;t[c+34|0]=q;t[c+33|0]=r;t[c+32|0]=s;t[c+31|0]=u;t[c+30|0]=x;t[c+29|0]=y;t[c+28|0]=z;t[c+27|0]=A;t[c+26|0]=B;t[c+25|0]=C;t[c+24|0]=D;t[c+23|0]=E;t[c+22|0]=F;t[c+21|0]=G;t[c+20|0]=H;t[c+19|0]=I;t[c+18|0]=J;t[c+17|0]=K;t[c+16|0]=L;t[c+15|0]=M;t[c+14|0]=N;t[c+13|0]=O;t[c+12|0]=P;t[c+11|0]=R;t[c+10|0]=T;t[c+9|0]=U;t[c+8|0]=V;t[c+7|0]=W;t[c+6|0]=X;t[c+5|0]=Y;t[c+4|0]=Z;t[c+3|0]=_;t[c+2|0]=$;t[c+1|0]=aa;t[c|0]=ba;b=v[c+12>>2];d=v[c+8>>2];t[a+44|0]=d;t[a+45|0]=d>>>8;t[a+46|0]=d>>>16;t[a+47|0]=d>>>24;t[a+48|0]=b;t[a+49|0]=b>>>8;t[a+50|0]=b>>>16;t[a+51|0]=b>>>24;b=v[c+4>>2];d=v[c>>2];t[a+36|0]=d;t[a+37|0]=d>>>8;t[a+38|0]=d>>>16;t[a+39|0]=d>>>24;t[a+40|0]=b;t[a+41|0]=b>>>8;t[a+42|0]=b>>>16;t[a+43|0]=b>>>24;S[v[4660]](c,0,48)|0;Q=c+48|0}function ia(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;a:{if(!a){break a}d=a-8|0;b=v[a-4>>2];a=b&-8;f=d+a|0;b:{if(b&1){break b}if(!(b&3)){break a}b=v[d>>2];d=d-b|0;if(d>>>0<y[4749]){break a}a=a+b|0;if(v[4750]!=(d|0)){if(b>>>0<=255){e=v[d+8>>2];b=b>>>3|0;c=v[d+12>>2];if((c|0)==(e|0)){i=18980,j=v[4745]&Fa(-2,b),v[i>>2]=j;break b}v[e+12>>2]=c;v[c+8>>2]=e;break b}h=v[d+24>>2];b=v[d+12>>2];c:{if((d|0)!=(b|0)){c=v[d+8>>2];v[c+12>>2]=b;v[b+8>>2]=c;break c}d:{e=d+20|0;c=v[e>>2];if(c){break d}e=d+16|0;c=v[e>>2];if(c){break d}b=0;break c}while(1){g=e;b=c;e=b+20|0;c=v[e>>2];if(c){continue}e=b+16|0;c=v[b+16>>2];if(c){continue}break}v[g>>2]=0}if(!h){break b}e=v[d+28>>2];c=(e<<2)+19284|0;e:{if(v[c>>2]==(d|0)){v[c>>2]=b;if(b){break e}i=18984,j=v[4746]&Fa(-2,e),v[i>>2]=j;break b}v[h+(v[h+16>>2]==(d|0)?16:20)>>2]=b;if(!b){break b}}v[b+24>>2]=h;c=v[d+16>>2];if(c){v[b+16>>2]=c;v[c+24>>2]=b}c=v[d+20>>2];if(!c){break b}v[b+20>>2]=c;v[c+24>>2]=b;break b}b=v[f+4>>2];if((b&3)!=3){break b}v[4747]=a;v[f+4>>2]=b&-2;v[d+4>>2]=a|1;v[a+d>>2]=a;return}if(d>>>0>=f>>>0){break a}b=v[f+4>>2];if(!(b&1)){break a}f:{if(!(b&2)){if(v[4751]==(f|0)){v[4751]=d;a=v[4748]+a|0;v[4748]=a;v[d+4>>2]=a|1;if(v[4750]!=(d|0)){break a}v[4747]=0;v[4750]=0;return}if(v[4750]==(f|0)){v[4750]=d;a=v[4747]+a|0;v[4747]=a;v[d+4>>2]=a|1;v[a+d>>2]=a;return}a=(b&-8)+a|0;g:{if(b>>>0<=255){e=v[f+8>>2];b=b>>>3|0;c=v[f+12>>2];if((c|0)==(e|0)){i=18980,j=v[4745]&Fa(-2,b),v[i>>2]=j;break g}v[e+12>>2]=c;v[c+8>>2]=e;break g}h=v[f+24>>2];b=v[f+12>>2];h:{if((f|0)!=(b|0)){c=v[f+8>>2];v[c+12>>2]=b;v[b+8>>2]=c;break h}i:{e=f+20|0;c=v[e>>2];if(c){break i}e=f+16|0;c=v[e>>2];if(c){break i}b=0;break h}while(1){g=e;b=c;e=b+20|0;c=v[e>>2];if(c){continue}e=b+16|0;c=v[b+16>>2];if(c){continue}break}v[g>>2]=0}if(!h){break g}e=v[f+28>>2];c=(e<<2)+19284|0;j:{if(v[c>>2]==(f|0)){v[c>>2]=b;if(b){break j}i=18984,j=v[4746]&Fa(-2,e),v[i>>2]=j;break g}v[h+(v[h+16>>2]==(f|0)?16:20)>>2]=b;if(!b){break g}}v[b+24>>2]=h;c=v[f+16>>2];if(c){v[b+16>>2]=c;v[c+24>>2]=b}c=v[f+20>>2];if(!c){break g}v[b+20>>2]=c;v[c+24>>2]=b}v[d+4>>2]=a|1;v[a+d>>2]=a;if(v[4750]!=(d|0)){break f}v[4747]=a;return}v[f+4>>2]=b&-2;v[d+4>>2]=a|1;v[a+d>>2]=a}if(a>>>0<=255){a=a>>>3|0;b=(a<<3)+19020|0;c=v[4745];a=1<<a;k:{if(!(c&a)){v[4745]=a|c;a=b;break k}a=v[b+8>>2]}v[b+8>>2]=d;v[a+12>>2]=d;v[d+12>>2]=b;v[d+8>>2]=a;return}e=31;v[d+16>>2]=0;v[d+20>>2]=0;if(a>>>0<=16777215){b=a>>>8|0;g=b+1048320>>>16&8;b=b<<g;e=b+520192>>>16&4;b=b<<e;c=b+245760>>>16&2;b=(b<<c>>>15|0)-(c|(e|g))|0;e=(b<<1|a>>>b+21&1)+28|0}v[d+28>>2]=e;g=(e<<2)+19284|0;l:{m:{c=v[4746];b=1<<e;n:{if(!(c&b)){v[4746]=b|c;v[g>>2]=d;v[d+24>>2]=g;break n}e=a<<((e|0)==31?0:25-(e>>>1|0)|0);b=v[g>>2];while(1){c=b;if((v[b+4>>2]&-8)==(a|0)){break m}b=e>>>29|0;e=e<<1;g=c+(b&4)|0;b=v[g+16>>2];if(b){continue}break}v[g+16>>2]=d;v[d+24>>2]=c}v[d+12>>2]=d;v[d+8>>2]=d;break l}a=v[c+8>>2];v[a+12>>2]=d;v[c+8>>2]=d;v[d+24>>2]=0;v[d+12>>2]=c;v[d+8>>2]=a}a=v[4753]-1|0;v[4753]=a?a:-1}}function Aa(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;d=v[a+132>>2];e=d+c|0;v[a+132>>2]=e;j=0-c&7;f=v[a+128>>2];g=f&7;a:{if(d>>>0<=e>>>0){break a}d=v[a+136>>2];e=d+1|0;v[a+136>>2]=e;if(d>>>0<=e>>>0){break a}d=v[a+140>>2];e=d+1|0;v[a+140>>2]=e;if(d>>>0<=e>>>0){break a}d=v[a+144>>2];e=d+1|0;v[a+144>>2]=e;if(d>>>0<=e>>>0){break a}d=v[a+148>>2];e=d+1|0;v[a+148>>2]=e;if(d>>>0<=e>>>0){break a}d=v[a+152>>2];e=d+1|0;v[a+152>>2]=e;if(d>>>0<=e>>>0){break a}d=v[a+156>>2];e=d+1|0;v[a+156>>2]=e;if(d>>>0<=e>>>0){break a}v[a+160>>2]=v[a+160>>2]+1}e=a- -64|0;b:{c:{if(!(g|j)){break c}if(!c){break b}k=8-g|0;l=8-j|0;while(1){d=f>>>3|0;if((g|0)==(j|0)){d=(a+d|0)- -64|0;t[d|0]=w[d|0]|w[b|0]&255>>>g;d=8-g|0;f=d+f|0;if((f|0)==512){fa(a,e,1);f=0}b=b+1|0;c=c-d|0;v[a+128>>2]=f;break c}h=w[b|0]<<j;d:{e:{if(c>>>0>=9){h=h|w[b+1|0]>>>l;f:{if(g){i=(a+d|0)- -64|0;t[i|0]=w[i|0]|(h&255)>>>g;break f}t[(a+d|0)- -64|0]=h}f=f+8|0;if(f>>>0<512){d=d+1|0}else{fa(a,e,1);f=f&511;d=0}b=b+1|0;c=c-8|0;if(!g){break d}h=h<<k;break e}g:{if(g){i=(a+d|0)- -64|0;t[i|0]=w[i|0]|(h&255)>>>g;break g}t[(a+d|0)- -64|0]=h}f=c+f|0;if((f|0)!=512){d=d+1|0}else{fa(a,e,1);f=0;d=0}if(!g){c=0;break d}c=0;h=h<<k}t[(a+d|0)- -64|0]=h}v[a+128>>2]=f;if(c){continue}break}break b}if(!c){break b}while(1){if(!(!f&c>>>0>=512)){d=f>>>3|0;g=512-f|0;h:{if(g>>>0<=c>>>0){h=(a+d|0)- -64|0;d=g>>>3|0;ca(h,b,d);fa(a,e,1);b=b+d|0;f=0;c=c-g|0;break h}ca((a+d|0)- -64|0,b,c>>>3|0);f=c+f|0;c=0}v[a+128>>2]=f;if(!c){break b}continue}fa(a,b,c>>>9|0);b=(c>>>3&536870848)+b|0;f=0;c=c&511;if(c){continue}break}}}function wa(a,b,c){var d=0,e=0,f=0,g=0;d=Q-96|0;Q=d;if(za(d)){a:{if(!b){break a}e=v[d+20>>2];f=e+(b<<3)|0;v[d+20>>2]=f;v[d+24>>2]=v[d+24>>2]+(e>>>0>f>>>0)+(b>>>29);b:{e=v[d+92>>2];if(e){f=d+28|0;g=f+e|0;if(b+e>>>0<64&b>>>0<=63){break b}e=64-e|0;ca(g,a,e);ja(d,f,1);ba(f,0,68);b=b-e|0;a=a+e|0}if(b>>>0>=64){ja(d,a,b>>>6|0);a=(b&-64)+a|0;b=b&63}if(!b){break a}v[d+92>>2]=b;ca(d+28|0,a,b);break a}ca(g,a,b);v[d+92>>2]=v[d+92>>2]+b}a=c?c:18656;b=d+28|0;e=v[d+92>>2];t[b+e|0]=128;c=e+1|0;if(c>>>0>=57){ba(b+c|0,0,63-e|0);ja(d,b,1);c=0}ba(b+c|0,0,56-c|0);c=v[d+24>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[d+84|0]=c;t[d+85|0]=c>>>8;t[d+86|0]=c>>>16;t[d+87|0]=c>>>24;c=v[d+20>>2];c=c<<24|c<<8&16711680|(c>>>8&65280|c>>>24);t[d+88|0]=c;t[d+89|0]=c>>>8;t[d+90|0]=c>>>16;t[d+91|0]=c>>>24;ja(d,b,1);v[d+92>>2]=0;S[v[4660]](b,0,64)|0;b=v[d>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a|0]=b;t[a+1|0]=b>>>8;t[a+2|0]=b>>>16;t[a+3|0]=b>>>24;b=v[d+4>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+4|0]=b;t[a+5|0]=b>>>8;t[a+6|0]=b>>>16;t[a+7|0]=b>>>24;b=v[d+8>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+8|0]=b;t[a+9|0]=b>>>8;t[a+10|0]=b>>>16;t[a+11|0]=b>>>24;b=v[d+12>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+12|0]=b;t[a+13|0]=b>>>8;t[a+14|0]=b>>>16;t[a+15|0]=b>>>24;b=v[d+16>>2];b=b<<24|b<<8&16711680|(b>>>8&65280|b>>>24);t[a+16|0]=b;t[a+17|0]=b>>>8;t[a+18|0]=b>>>16;t[a+19|0]=b>>>24;S[v[4660]](d,0,96)|0}Q=d+96|0}function ua(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0;g=Q+-64|0;Q=g;d=g;v[d+8>>2]=0;v[d+12>>2]=0;v[d+56>>2]=0;v[d+48>>2]=0;v[d+52>>2]=0;v[d+40>>2]=0;v[d+44>>2]=0;v[d+32>>2]=0;v[d+36>>2]=0;v[d+24>>2]=0;v[d+28>>2]=0;v[d+16>>2]=0;v[d+20>>2]=0;d=d+8|0;e=d;if(b){h=e+4|0;a:{b:{f=v[e>>2];if(f){i=(e+f|0)+4|0;if(b+f>>>0<16){break b}ca(i,a,16-f|0);na(e,h);f=v[e>>2];v[e>>2]=0;f=16-f|0;b=b-f|0;a=a+f|0}if(b>>>0>=16){while(1){na(e,a);a=a+16|0;b=b-16|0;if(b>>>0>15){continue}break}}ca(h,a,b);break a}ca(i,a,b);b=v[e>>2]+b|0}v[e>>2]=b}b=c?c:18832;a=v[d>>2];if((a|0)<=15){c=(a+d|0)+4|0;a=16-a|0;ba(c,a,a)}a=d+4|0;na(d,a);c=w[d+32|0]|w[d+33|0]<<8|(w[d+34|0]<<16|w[d+35|0]<<24);e=w[d+28|0]|w[d+29|0]<<8|(w[d+30|0]<<16|w[d+31|0]<<24);t[a+8|0]=e;t[a+9|0]=e>>>8;t[a+10|0]=e>>>16;t[a+11|0]=e>>>24;t[a+12|0]=c;t[a+13|0]=c>>>8;t[a+14|0]=c>>>16;t[a+15|0]=c>>>24;c=w[d+24|0]|w[d+25|0]<<8|(w[d+26|0]<<16|w[d+27|0]<<24);e=w[d+20|0]|w[d+21|0]<<8|(w[d+22|0]<<16|w[d+23|0]<<24);t[a|0]=e;t[a+1|0]=e>>>8;t[a+2|0]=e>>>16;t[a+3|0]=e>>>24;t[a+4|0]=c;t[a+5|0]=c>>>8;t[a+6|0]=c>>>16;t[a+7|0]=c>>>24;na(d,a);t[b|0]=w[d+36|0];t[b+1|0]=w[d+37|0];t[b+2|0]=w[d+38|0];t[b+3|0]=w[d+39|0];t[b+4|0]=w[d+40|0];t[b+5|0]=w[d+41|0];t[b+6|0]=w[d+42|0];t[b+7|0]=w[d+43|0];t[b+8|0]=w[d+44|0];t[b+9|0]=w[d+45|0];t[b+10|0]=w[d+46|0];t[b+11|0]=w[d+47|0];t[b+12|0]=w[d+48|0];t[b+13|0]=w[d+49|0];t[b+14|0]=w[d+50|0];t[b+15|0]=w[d+51|0];S[v[4660]](d,0,52)|0;S[v[4660]](d,0,52)|0;Q=g- -64|0}function ra(a,b,c){var d=0,e=0,f=0,g=0;d=Q-96|0;Q=d;if(za(d)){a:{if(!b){break a}e=v[d+20>>2];f=e+(b<<3)|0;v[d+20>>2]=f;v[d+24>>2]=v[d+24>>2]+(e>>>0>f>>>0)+(b>>>29);b:{e=v[d+92>>2];if(e){f=d+28|0;g=f+e|0;if(b+e>>>0<64&b>>>0<=63){break b}e=64-e|0;ca(g,a,e);ka(d,f,1);ba(f,0,68);b=b-e|0;a=a+e|0}if(b>>>0>=64){ka(d,a,b>>>6|0);a=(b&-64)+a|0;b=b&63}if(!b){break a}v[d+92>>2]=b;ca(d+28|0,a,b);break a}ca(g,a,b);v[d+92>>2]=v[d+92>>2]+b}b=c?c:18880;e=d+28|0;a=v[d+92>>2];t[e+a|0]=128;c=a+1|0;if(c>>>0>=57){ba(c+e|0,0,63-a|0);ka(d,e,1);c=0}ba(c+e|0,0,56-c|0);a=v[d+24>>2];c=v[d+20>>2];t[d+84|0]=c;t[d+85|0]=c>>>8;t[d+86|0]=c>>>16;t[d+87|0]=c>>>24;t[d+88|0]=a;t[d+89|0]=a>>>8;t[d+90|0]=a>>>16;t[d+91|0]=a>>>24;ka(d,e,1);v[d+92>>2]=0;S[v[4660]](e,0,64)|0;a=v[d>>2];t[b|0]=a;t[b+1|0]=a>>>8;t[b+2|0]=a>>>16;t[b+3|0]=a>>>24;a=v[d+4>>2];t[b+4|0]=a;t[b+5|0]=a>>>8;t[b+6|0]=a>>>16;t[b+7|0]=a>>>24;a=v[d+8>>2];t[b+8|0]=a;t[b+9|0]=a>>>8;t[b+10|0]=a>>>16;t[b+11|0]=a>>>24;a=v[d+12>>2];t[b+12|0]=a;t[b+13|0]=a>>>8;t[b+14|0]=a>>>16;t[b+15|0]=a>>>24;a=v[d+16>>2];t[b+16|0]=a;t[b+17|0]=a>>>8;t[b+18|0]=a>>>16;t[b+19|0]=a>>>24;S[v[4660]](d,0,96)|0}Q=d+96|0}function ca(a,b,c){var d=0,e=0,f=0;if(c>>>0>=512){O(a|0,b|0,c|0)|0;return a}e=a+c|0;a:{if(!((a^b)&3)){b:{if(!(a&3)){c=a;break b}if((c|0)<1){c=a;break b}c=a;while(1){t[c|0]=w[b|0];b=b+1|0;c=c+1|0;if(!(c&3)){break b}if(c>>>0<e>>>0){continue}break}}d=e&-4;c:{if(d>>>0<64){break c}f=d+-64|0;if(f>>>0<c>>>0){break c}while(1){v[c>>2]=v[b>>2];v[c+4>>2]=v[b+4>>2];v[c+8>>2]=v[b+8>>2];v[c+12>>2]=v[b+12>>2];v[c+16>>2]=v[b+16>>2];v[c+20>>2]=v[b+20>>2];v[c+24>>2]=v[b+24>>2];v[c+28>>2]=v[b+28>>2];v[c+32>>2]=v[b+32>>2];v[c+36>>2]=v[b+36>>2];v[c+40>>2]=v[b+40>>2];v[c+44>>2]=v[b+44>>2];v[c+48>>2]=v[b+48>>2];v[c+52>>2]=v[b+52>>2];v[c+56>>2]=v[b+56>>2];v[c+60>>2]=v[b+60>>2];b=b- -64|0;c=c- -64|0;if(f>>>0>=c>>>0){continue}break}}if(c>>>0>=d>>>0){break a}while(1){v[c>>2]=v[b>>2];b=b+4|0;c=c+4|0;if(d>>>0>c>>>0){continue}break}break a}if(e>>>0<4){c=a;break a}d=e-4|0;if(d>>>0<a>>>0){c=a;break a}c=a;while(1){t[c|0]=w[b|0];t[c+1|0]=w[b+1|0];t[c+2|0]=w[b+2|0];t[c+3|0]=w[b+3|0];b=b+4|0;c=c+4|0;if(d>>>0>=c>>>0){continue}break}}if(c>>>0<e>>>0){while(1){t[c|0]=w[b|0];b=b+1|0;c=c+1|0;if((e|0)!=(c|0)){continue}break}}return a}function ta(a,b,c){var d=0,e=0,f=0,g=0;d=Q-96|0;Q=d;if(Ba(d)){a:{if(!b){break a}e=v[d+16>>2];f=e+(b<<3)|0;v[d+16>>2]=f;v[d+20>>2]=v[d+20>>2]+(e>>>0>f>>>0)+(b>>>29);b:{e=v[d+88>>2];if(e){f=d+24|0;g=f+e|0;if(b+e>>>0<64&b>>>0<=63){break b}e=64-e|0;ca(g,a,e);ma(d,f,1);ba(f,0,68);b=b-e|0;a=a+e|0}if(b>>>0>=64){ma(d,a,b>>>6|0);a=(b&-64)+a|0;b=b&63}if(!b){break a}v[d+88>>2]=b;ca(d+24|0,a,b);break a}ca(g,a,b);v[d+88>>2]=v[d+88>>2]+b}b=c?c:18848;e=d+24|0;a=v[d+88>>2];t[e+a|0]=128;c=a+1|0;if(c>>>0>=57){ba(c+e|0,0,63-a|0);ma(d,e,1);c=0}ba(c+e|0,0,56-c|0);a=v[d+20>>2];c=v[d+16>>2];t[d+80|0]=c;t[d+81|0]=c>>>8;t[d+82|0]=c>>>16;t[d+83|0]=c>>>24;t[d+84|0]=a;t[d+85|0]=a>>>8;t[d+86|0]=a>>>16;t[d+87|0]=a>>>24;ma(d,e,1);v[d+88>>2]=0;S[v[4660]](e,0,64)|0;a=v[d>>2];t[b|0]=a;t[b+1|0]=a>>>8;t[b+2|0]=a>>>16;t[b+3|0]=a>>>24;a=v[d+4>>2];t[b+4|0]=a;t[b+5|0]=a>>>8;t[b+6|0]=a>>>16;t[b+7|0]=a>>>24;a=v[d+8>>2];t[b+8|0]=a;t[b+9|0]=a>>>8;t[b+10|0]=a>>>16;t[b+11|0]=a>>>24;a=v[d+12>>2];t[b+12|0]=a;t[b+13|0]=a>>>8;t[b+14|0]=a>>>16;t[b+15|0]=a>>>24;S[v[4660]](d,0,92)|0}Q=d+96|0}function sa(a,b,c){var d=0,e=0,f=0,g=0;d=Q-96|0;Q=d;if(Ba(d)){a:{if(!b){break a}e=v[d+16>>2];f=e+(b<<3)|0;v[d+16>>2]=f;v[d+20>>2]=v[d+20>>2]+(e>>>0>f>>>0)+(b>>>29);b:{e=v[d+88>>2];if(e){f=d+24|0;g=f+e|0;if(b+e>>>0<64&b>>>0<=63){break b}e=64-e|0;ca(g,a,e);la(d,f,1);ba(f,0,68);b=b-e|0;a=a+e|0}if(b>>>0>=64){la(d,a,b>>>6|0);a=(b&-64)+a|0;b=b&63}if(!b){break a}v[d+88>>2]=b;ca(d+24|0,a,b);break a}ca(g,a,b);v[d+88>>2]=v[d+88>>2]+b}b=c?c:18864;e=d+24|0;a=v[d+88>>2];t[e+a|0]=128;c=a+1|0;if(c>>>0>=57){ba(c+e|0,0,63-a|0);la(d,e,1);c=0}ba(c+e|0,0,56-c|0);a=v[d+20>>2];c=v[d+16>>2];t[d+80|0]=c;t[d+81|0]=c>>>8;t[d+82|0]=c>>>16;t[d+83|0]=c>>>24;t[d+84|0]=a;t[d+85|0]=a>>>8;t[d+86|0]=a>>>16;t[d+87|0]=a>>>24;la(d,e,1);v[d+88>>2]=0;S[v[4660]](e,0,64)|0;a=v[d>>2];t[b|0]=a;t[b+1|0]=a>>>8;t[b+2|0]=a>>>16;t[b+3|0]=a>>>24;a=v[d+4>>2];t[b+4|0]=a;t[b+5|0]=a>>>8;t[b+6|0]=a>>>16;t[b+7|0]=a>>>24;a=v[d+8>>2];t[b+8|0]=a;t[b+9|0]=a>>>8;t[b+10|0]=a>>>16;t[b+11|0]=a>>>24;a=v[d+12>>2];t[b+12|0]=a;t[b+13|0]=a>>>8;t[b+14|0]=a>>>16;t[b+15|0]=a>>>24;S[v[4660]](d,0,92)|0}Q=d+96|0}function ba(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0;a:{if(!c){break a}d=a+c|0;t[d-1|0]=b;t[a|0]=b;if(c>>>0<3){break a}t[d-2|0]=b;t[a+1|0]=b;t[d-3|0]=b;t[a+2|0]=b;if(c>>>0<7){break a}t[d-4|0]=b;t[a+3|0]=b;if(c>>>0<9){break a}e=0-a&3;d=e+a|0;b=B(b&255,16843009);v[d>>2]=b;e=c-e&-4;c=e+d|0;v[c-4>>2]=b;if(e>>>0<9){break a}v[d+8>>2]=b;v[d+4>>2]=b;v[c-8>>2]=b;v[c-12>>2]=b;if(e>>>0<25){break a}v[d+24>>2]=b;v[d+20>>2]=b;v[d+16>>2]=b;v[d+12>>2]=b;v[c-16>>2]=b;v[c-20>>2]=b;v[c-24>>2]=b;v[c-28>>2]=b;g=d&4|24;c=e-g|0;if(c>>>0<32){break a}e=b>>>16|0;f=B(e,0);h=b&65535;i=(h>>>16|0)+e|0;e=i&65535;R=b+f+(i>>>16)+(e>>>16)|0;f=h|e<<16;e=R;b=d+g|0;while(1){v[b+24>>2]=f;d=e;v[b+28>>2]=d;v[b+16>>2]=f;v[b+20>>2]=d;v[b+8>>2]=f;v[b+12>>2]=d;v[b>>2]=f;v[b+4>>2]=d;b=b+32|0;c=c-32|0;if(c>>>0>31){continue}break}}return a|0}function xa(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0;a:{if(!c){break a}f=v[a+68>>2];i=f;e=c>>>29|0;g=v[a+64>>2];d=c<<3;h=g+d|0;f=f+e|0;f=d>>>0>h>>>0?f+1|0:f;e=h;d=f;if((i|0)==(d|0)&e>>>0<g>>>0|d>>>0<i>>>0){g=v[a+76>>2];e=v[a+72>>2]+1|0;g=e>>>0<1?g+1|0:g;v[a+72>>2]=e;v[a+76>>2]=g}e=a+80|0;v[a+64>>2]=h;v[a+68>>2]=d;d=v[a+208>>2];b:{if(d){f=e+d|0;d=128-d|0;if(d>>>0>c>>>0){ca(f,b,c);c=v[a+208>>2]+c|0;break b}ca(f,b,d);v[a+208>>2]=0;ga(a,e,1);c=c-d|0;b=b+d|0}c:{if(c>>>0<128){break c}if(b&7){while(1){ga(a,ca(e,b,128),1);b=b+128|0;c=c-128|0;if(c>>>0>127){continue}break c}}ga(a,b,c>>>7|0);b=b+c|0;c=c&127;b=b-c|0}if(!c){break a}ca(e,b,c)}v[a+208>>2]=c}}function va(a,b,c){var d=0,e=0;d=Q-224|0;Q=d;v[d+80>>2]=0;v[d+84>>2]=0;v[d+64>>2]=327033209;v[d+68>>2]=1541459225;v[d+56>>2]=-79577749;v[d+60>>2]=528734635;v[d+48>>2]=725511199;v[d+52>>2]=-1694144372;v[d+40>>2]=-1377402159;v[d+44>>2]=1359893119;v[d+32>>2]=1595750129;v[d+36>>2]=-1521486534;v[d+24>>2]=-23791573;v[d+28>>2]=1013904242;v[d+16>>2]=-2067093701;v[d+20>>2]=-1150833019;v[d+8>>2]=-205731576;v[d+12>>2]=1779033703;v[d+72>>2]=0;v[d+76>>2]=0;v[d+216>>2]=0;v[d+220>>2]=64;e=d+8|0;xa(e,a,b);ya(c?c:18768,e);S[v[4660]](e,0,216)|0;Q=d+224|0}function oa(a,b,c){var d=0,e=0;d=Q-224|0;Q=d;v[d+80>>2]=0;v[d+84>>2]=0;v[d+64>>2]=-1090891868;v[d+68>>2]=1203062813;v[d+56>>2]=1694076839;v[d+60>>2]=-619958771;v[d+48>>2]=1750603025;v[d+52>>2]=-1900787065;v[d+40>>2]=-4191439;v[d+44>>2]=1731405415;v[d+32>>2]=-150054599;v[d+36>>2]=355462360;v[d+24>>2]=812702999;v[d+28>>2]=-1856437926;v[d+16>>2]=914150663;v[d+20>>2]=1654270250;v[d+8>>2]=-1056596264;v[d+12>>2]=-876896931;v[d+72>>2]=0;v[d+76>>2]=0;v[d+216>>2]=0;v[d+220>>2]=48;e=d+8|0;xa(e,a,b);ya(c?c:18720,e);S[v[4660]](e,0,216)|0;Q=d+224|0}function Ea(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;a:{b:{switch(c|0){case 0:c=a;a=da(16);ua(c,b,a);break a;case 1:c=a;a=da(16);ta(c,b,a);break a;case 2:c=a;a=da(16);sa(c,b,a);break a;case 3:c=a;a=da(20);ra(c,b,a);break a;case 4:c=a;a=da(20);wa(c,b,a);break a;case 5:c=a;a=da(32);pa(c,b,a);break a;case 6:c=a;a=da(48);oa(c,b,a);break a;case 7:c=a;a=da(64);va(c,b,a);break a;case 8:d=da(64);qa(a,b,d);break;default:break b}}return d|0}return a|0}function Ga(a,b,c){var d=0,e=0,f=0,g=0;g=c&63;f=g;e=f&31;if(f>>>0>=32){f=-1>>>e|0}else{f=-1>>>e|0;d=f;f=d|(1<<e)-1<<32-e}f=f&a;d=b&d;e=g&31;if(g>>>0>=32){d=f<<e;g=0}else{d=(1<<e)-1&f>>>32-e|d<<e;g=f<<e}f=d;e=0-c&63;d=e&31;if(e>>>0>=32){d=-1<<d;c=0}else{c=-1<<d;d=c|(1<<d)-1&-1>>>32-d}a=c&a;b=b&d;d=e&31;if(e>>>0>=32){c=0;a=b>>>d|0}else{c=b>>>d|0;a=((1<<d)-1&b)<<32-d|a>>>d}a=a|g;R=c|f;return a}
function ea(a){var b=0,c=0;b=v[4661];c=a+3&-4;a=b+c|0;a:{if(a>>>0<=b>>>0?c:0){break a}if(a>>>0>T()<<16>>>0){if(!(P(a|0)|0)){break a}}v[4661]=a;return b}v[4744]=48;return-1}function za(a){ba(a+20|0,0,76);v[a+16>>2]=-1009589776;v[a+8>>2]=-1732584194;v[a+12>>2]=271733878;v[a>>2]=1732584193;v[a+4>>2]=-271733879;return 1}function Ba(a){ba(a+16|0,0,76);v[a+8>>2]=-1732584194;v[a+12>>2]=271733878;v[a>>2]=1732584193;v[a+4>>2]=-271733879;return 1}function Fa(a,b){var c=0,d=0;c=b&31;d=(-1>>>c&a)<<c;c=a;a=0-b&31;return d|(c&-1<<a)>>>a}function Ca(){}
// EMSCRIPTEN_END_FUNCS
e=w;p($);var S=c([null,ba]);function T(){return s.byteLength/65536|0}function Y(Z){Z=Z|0;var U=T()|0;var V=U+Z|0;if(U<V&&V<65536){var W=new ArrayBuffer(B(V,65536));var X=new Int8Array(W);X.set(t);t=new Int8Array(W);u=new Int16Array(W);v=new Int32Array(W);w=new Uint8Array(W);x=new Uint16Array(W);y=new Uint32Array(W);z=new Float32Array(W);A=new Float64Array(W);s=W;r.buffer=s;e=w}return U}return{"d":Ca,"e":S,"f":da,"g":Ea,"h":Da,"i":ia}}return _(aa)}
// EMSCRIPTEN_END_ASM




)(asmLibraryArg)},instantiate:function(binary,info){return{then:function(ok){var module=new WebAssembly.Module(binary);ok({"instance":new WebAssembly.Instance(module)})}}},RuntimeError:Error};wasmBinary=[];if(typeof WebAssembly!=="object"){abort("no native wasm support detected")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module["HEAP8"]=HEAP8=new Int8Array(buf);Module["HEAP16"]=HEAP16=new Int16Array(buf);Module["HEAP32"]=HEAP32=new Int32Array(buf);Module["HEAPU8"]=HEAPU8=new Uint8Array(buf);Module["HEAPU16"]=HEAPU16=new Uint16Array(buf);Module["HEAPU32"]=HEAPU32=new Uint32Array(buf);Module["HEAPF32"]=HEAPF32=new Float32Array(buf);Module["HEAPF64"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||2097152;if(Module["wasmMemory"]){wasmMemory=Module["wasmMemory"]}else{wasmMemory=new WebAssembly.Memory({"initial":INITIAL_MEMORY/65536,"maximum":2147483648/65536})}if(wasmMemory){buffer=wasmMemory.buffer}INITIAL_MEMORY=buffer.byteLength;updateGlobalBufferAndViews(buffer);var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[function(){self.onEngineInit();}];var runtimeInitialized=false;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};function abort(what){{if(Module["onAbort"]){Module["onAbort"](what)}}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -s ASSERTIONS=1 for more info.";var e=new WebAssembly.RuntimeError(what);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile="engine.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}var binary=tryParseAsDataURI(file);if(binary){return binary}if(readBinary){return readBinary(file)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise2(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary(wasmBinaryFile)})}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={"a":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmTable=Module["asm"]["e"];addOnInit(Module["asm"]["d"]);removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module["instantiateWasm"]){try{var exports=Module["instantiateWasm"](info,receiveInstance);return exports}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync();return{}}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback(Module);continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){getWasmTableEntry(func)()}else{getWasmTableEntry(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var wasmTableMirror=[];function getWasmTableEntry(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}var ASSERTIONS=false;function intArrayToString(array){var ret=[];for(var i=0;i<array.length;i++){var chr=array[i];if(chr>255){if(ASSERTIONS){assert(false,"Character code "+chr+" ("+String.fromCharCode(chr)+")  at offset "+i+" not in 0x00-0xFF.")}chr&=255}ret.push(String.fromCharCode(chr))}return ret.join("")}var decodeBase64=typeof atob==="function"?atob:function(input){var keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var output="";var chr1,chr2,chr3;var enc1,enc2,enc3,enc4;var i=0;input=input.replace(/[^A-Za-z0-9\+\/\=]/g,"");do{enc1=keyStr.indexOf(input.charAt(i++));enc2=keyStr.indexOf(input.charAt(i++));enc3=keyStr.indexOf(input.charAt(i++));enc4=keyStr.indexOf(input.charAt(i++));chr1=enc1<<2|enc2>>4;chr2=(enc2&15)<<4|enc3>>2;chr3=(enc3&3)<<6|enc4;output=output+String.fromCharCode(chr1);if(enc3!==64){output=output+String.fromCharCode(chr2)}if(enc4!==64){output=output+String.fromCharCode(chr3)}}while(i<input.length);return output};function intArrayFromBase64(s){try{var decoded=decodeBase64(s);var bytes=new Uint8Array(decoded.length);for(var i=0;i<decoded.length;++i){bytes[i]=decoded.charCodeAt(i)}return bytes}catch(_){throw new Error("Converting base64 string to bytes failed.")}}function tryParseAsDataURI(filename){if(!isDataURI(filename)){return}return intArrayFromBase64(filename.slice(dataURIPrefix.length))}var asmLibraryArg={"b":_emscripten_memcpy_big,"c":_emscripten_resize_heap,"a":wasmMemory};var asm=createWasm();var ___wasm_call_ctors=Module["___wasm_call_ctors"]=function(){return(___wasm_call_ctors=Module["___wasm_call_ctors"]=Module["asm"]["d"]).apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return(_malloc=Module["_malloc"]=Module["asm"]["f"]).apply(null,arguments)};var _hash=Module["_hash"]=function(){return(_hash=Module["_hash"]=Module["asm"]["g"]).apply(null,arguments)};var _hash2=Module["_hash2"]=function(){return(_hash2=Module["_hash2"]=Module["asm"]["h"]).apply(null,arguments)};var _free=Module["_free"]=function(){return(_free=Module["_free"]=Module["asm"]["i"]).apply(null,arguments)};var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();


    var HashAlgs = {
        MD2       : 0,
        MD4       : 1,
        MD5       : 2,
        RMD160    : 3,
        SHA1      : 4,
        SHA256    : 5,
        SHA384    : 6,
        SHA512    : 7,
        WHIRLPOOL : 8
    };

    var HashSizes = [
        16,
        16,
        16,
        20,
        20,
        32,
        48,
        64,
        64
    ];

	window["AscCommon"] = window.AscCommon = (window["AscCommon"] || {});
	window.AscCommon["Hash"] = window.AscCommon.Hash = {};
	window.AscCommon.Hash["HashAlgs"] = window.AscCommon.Hash.HashAlgs = HashAlgs;
	window.AscCommon.Hash["HashSizes"] = window.AscCommon.Hash.HashSizes = HashSizes;
	
	function HashObj() { this.buf; }
	HashObj.prototype["buffer"] = HashObj.prototype.buffer = function()	{ return this.buf; };
	HashObj.prototype["base64"] = HashObj.prototype.base64 = function() { return window.AscCommon.Base64.encode(this.buf); };
	HashObj.prototype["hex"] = HashObj.prototype.hex = function() { return window.AscCommon.Hex.encode(this.buf); };
	
	window.AscCommon.Hash["hash"] = window.AscCommon.Hash.hash = function(data, alg)
	{
		if (typeof alg === "string")
		{
			switch (alg)
			{
				case "md2" : alg = HashAlgs.MD2; break;
				case "md4" : alg = HashAlgs.MD4; break;
				case "md5" : alg = HashAlgs.MD5; break;
				case "rmd160" : alg = HashAlgs.RMD160; break;
				case "sha1" : alg = HashAlgs.SHA1; break;
				case "sha256" : alg = HashAlgs.SHA256; break;
				case "sha384" : alg = HashAlgs.SHA384; break;
				case "sha512" : alg = HashAlgs.SHA512; break;
				case "whirlpool" : alg = HashAlgs.WHIRLPOOL; break;
				default:
					alg = HashAlgs.SHA256;
			}
		}
		
		var arrayData = null;
		if (typeof data === "string")
			arrayData = data.toUtf8(true);
		else
			arrayData = data;
		
        var dataPointer = Module["_malloc"](arrayData.length);
        Module["HEAPU8"].set(arrayData, dataPointer);		
		var resultPointer = Module["_hash"](dataPointer, arrayData.length, alg);
		Module["_free"](dataPointer);
		
		var result = new HashObj();
		if (0 != resultPointer)
		{
			var tmp = new Uint8Array(Module["HEAPU8"].buffer, resultPointer, HashSizes[alg]);
			result.buf = new Uint8Array(tmp.length);
			result.buf.set(tmp, 0);
			Module["_free"](resultPointer);
		}
		else
		{
			result.buf = [];
		}
		
		return result;
	};
	
	window.AscCommon.Hash["hashOffice"] = window.AscCommon.Hash.hash = function(password, salt, spinCount, alg)
	{
		if (typeof alg === "string")
		{
			switch (alg)
			{
				case "md2" : alg = HashAlgs.MD2; break;
				case "md4" : alg = HashAlgs.MD4; break;
				case "md5" : alg = HashAlgs.MD5; break;
				case "rmd160" : alg = HashAlgs.RMD160; break;
				case "sha1" : alg = HashAlgs.SHA1; break;
				case "sha256" : alg = HashAlgs.SHA256; break;
				case "sha384" : alg = HashAlgs.SHA384; break;
				case "sha512" : alg = HashAlgs.SHA512; break;
				case "whirlpool" : alg = HashAlgs.WHIRLPOOL; break;
				default:
					alg = HashAlgs.SHA256;
			}
		}

		var passwordData = password.toUtf8();
		var passwordPointer = Module["_malloc"](passwordData.length);
		Module["HEAPU8"].set(passwordData, passwordPointer);

		var saltData = salt.toUtf8();
		var saltPointer = Module["_malloc"](saltData.length);
		Module["HEAPU8"].set(saltData, saltPointer);

		var resultPointer = Module["_hash2"](passwordPointer, saltPointer, spinCount, alg);

		Module["_free"](passwordPointer);
		Module["_free"](saltPointer);

		var result = new HashObj();
		if (0 != resultPointer)
		{
			var tmp = new Uint8Array(Module["HEAPU8"].buffer, resultPointer, HashSizes[alg]);
			result.buf = new Uint8Array(tmp.length);
			result.buf.set(tmp, 0);
			Module["_free"](resultPointer);
		}
		else
		{
			result.buf = [];
		}

		return result;
	};

})(self, undefined);

/*!
 * 
 * * Version: undefined (build: undefined)
 *
 */#ws-canvas-outer{bottom:0;left:0;position:absolute;right:0;top:0}#ws-canvas{border:0}#ws-canvas-graphic,#ws-canvas-graphic-overlay,#ws-canvas-overlay{border:0;left:0;position:absolute;top:0;z-index:1}#ws-v-scrollbar{bottom:18px;overflow:hidden;position:absolute;right:0;top:-1px;width:19px;z-index:10}#ws-v-scroll-helper{width:1px}#ws-h-scrollbar{bottom:0;height:19px;left:0;overflow:hidden;position:absolute;right:18px;z-index:10}#ws-h-scroll-helper{height:1px}#ws-scrollbar-corner{border:0;bottom:0;height:18px;position:absolute;right:0;width:18px;z-index:10}#ws-h-scrollbar .jspHorizontalBar,#ws-h-scrollbar .jspTrack,#ws-scrollbar-corner,#ws-v-scrollbar .jspTrack,#ws-v-scrollbar .jspVerticalBar{background-color:#dce2e8}#ws-h-scrollbar .jspDrag,#ws-v-scrollbar .jspDrag{background-color:silver}#ws-h-scrollbar .jspDrag.jspActive,#ws-h-scrollbar .jspDrag.jspHover,#ws-v-scrollbar .jspDrag.jspActive,#ws-v-scrollbar .jspDrag.jspHover{background-color:grey}#ws-v-scrollbar .jspVerticalBar{border-left:1px solid #c1c6cc;width:7px}#ws-v-scrollbar .jspTrack{width:8px}#ws-h-scrollbar .jspHorizontalBar{border-top:1px solid #c1c6cc;height:7px}#ws-h-scrollbar .jspTrack{height:8px}
/*# sourceMappingURL=css/app.css.map */
{"About": {"textAbout": "About", "textAddress": "Address", "textBack": "Back", "textEditor": "Spreadsheet Editor", "textEmail": "Email", "textPoweredBy": "Powered By", "textTel": "Tel", "textVersion": "Version"}, "Common": {"Collaboration": {"notcriticalErrorTitle": "Warning", "textAddComment": "Add Comment", "textAddReply": "Add Reply", "textBack": "Back", "textCancel": "Cancel", "textCollaboration": "Collaboration", "textComments": "Comments", "textDeleteComment": "Delete Comment", "textDeleteReply": "Delete Reply", "textDone": "Done", "textEdit": "Edit", "textEditComment": "Edit Comment", "textEditReply": "Edit Reply", "textEditUser": "Users who are editing the file:", "textMessageDeleteComment": "Do you really want to delete this comment?", "textMessageDeleteReply": "Do you really want to delete this reply?", "textNoComments": "No Comments", "textOk": "Ok", "textReopen": "Reopen", "textResolve": "Resolve", "textSharingSettings": "Sharing Settings", "textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "textUsers": "Users"}, "ThemeColorPalette": {"textCustomColors": "Custom Colors", "textStandartColors": "Standard Colors", "textThemeColors": "Theme Colors"}}, "ContextMenu": {"errorCopyCutPaste": "Copy, cut, and paste actions using the context menu will be performed within the current file only.", "errorInvalidLink": "The link reference does not exist. Please correct the link or delete it.", "menuAddComment": "Add Comment", "menuAddLink": "Add Link", "menuCancel": "Cancel", "menuCell": "Cell", "menuDelete": "Delete", "menuEdit": "Edit", "menuEditLink": "Edit Link", "menuFreezePanes": "Freeze Panes", "menuHide": "<PERSON>de", "menuMerge": "<PERSON><PERSON>", "menuMore": "More", "menuOpenLink": "Open Link", "menuShow": "Show", "menuUnfreezePanes": "Unfreeze Panes", "menuUnmerge": "Unmerge", "menuUnwrap": "Unwrap", "menuViewComment": "View Comment", "menuWrap": "Wrap", "notcriticalErrorTitle": "Warning", "textCopyCutPasteActions": "Copy, Cut and Paste Actions", "textDoNotShowAgain": "Don't show again", "textOk": "Ok", "txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "warnMergeLostData": "Only the data from the upper-left cell will remain in the merged cell. <br>Are you sure you want to continue?"}, "Controller": {"Main": {"criticalErrorTitle": "Error", "errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please, contact your admin.", "errorOpensource": "Using the free Community version, you can open documents for viewing only. To access mobile web editors, a commercial license is required.", "errorProcessSaveResult": "Saving is failed.", "errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "leavePageText": "You have unsaved changes in this document. Click 'Stay on this Page' to wait for autosave. Click 'Leave this Page' to discard all the unsaved changes.", "notcriticalErrorTitle": "Warning", "SDK": {"txtAccent": "Accent", "txtAll": "(All)", "txtArt": "Your text here", "txtBlank": "(blank)", "txtByField": "%1 of %2", "txtClearFilter": "Clear Filter (Alt+C)", "txtColLbls": "Column Labels", "txtColumn": "Column", "txtConfidential": "Confidential", "txtDate": "Date", "txtDays": "Days", "txtDiagramTitle": "Chart Title", "txtFile": "File", "txtGrandTotal": "Grand Total", "txtGroup": "Group", "txtHours": "Hours", "txtMinutes": "Minutes", "txtMonths": "Months", "txtMultiSelect": "Multi-Select (Alt+S)", "txtOr": "%1 or %2", "txtPage": "Page", "txtPageOf": "Page %1 of %2", "txtPages": "Pages", "txtPreparedBy": "Prepared by", "txtPrintArea": "Print_Area", "txtQuarter": "Qtr", "txtQuarters": "Quarters", "txtRow": "Row", "txtRowLbls": "Row Labels", "txtSeconds": "Seconds", "txtSeries": "Series", "txtStyle_Bad": "Bad", "txtStyle_Calculation": "Calculation", "txtStyle_Check_Cell": "Check Cell", "txtStyle_Comma": "Comma", "txtStyle_Currency": "<PERSON><PERSON><PERSON><PERSON>", "txtStyle_Explanatory_Text": "Explanatory Text", "txtStyle_Good": "Good", "txtStyle_Heading_1": "Heading 1", "txtStyle_Heading_2": "Heading 2", "txtStyle_Heading_3": "Heading 3", "txtStyle_Heading_4": "Heading 4", "txtStyle_Input": "Input", "txtStyle_Linked_Cell": "Linked Cell", "txtStyle_Neutral": "Neutral", "txtStyle_Normal": "Normal", "txtStyle_Note": "Note", "txtStyle_Output": "Output", "txtStyle_Percent": "Percent", "txtStyle_Title": "Title", "txtStyle_Total": "Total", "txtStyle_Warning_Text": "Warning Text", "txtTab": "Tab", "txtTable": "Table", "txtTime": "Time", "txtValues": "Values", "txtXAxis": "X Axis", "txtYAxis": "Y Axis", "txtYears": "Years"}, "textAnonymous": "Anonymous", "textBuyNow": "Visit website", "textClose": "Close", "textContactUs": "Contact sales", "textCustomLoader": "Sorry, you are not entitled to change the loader. Please, contact our sales department to get a quote.", "textDontUpdate": "Don't Update", "textGuest": "Guest", "textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "textNo": "No", "textNoChoices": "There are no choices for filling the cell.<br>Only text values from the column can be selected for replacement.", "textNoLicenseTitle": "License limit reached", "textNoTextFound": "Text not found", "textOk": "Ok", "textPaidFeature": "Paid feature", "textRemember": "Remember my choice", "textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "textReplaceSuccess": "The search has been done. Occurrences replaced: {0}", "textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "textUpdate": "Update", "textWarnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe. If you trust the links, update them to get the latest data.", "textYes": "Yes", "titleLicenseExp": "License expired", "titleServerVersion": "Editor updated", "titleUpdateVersion": "Version changed", "warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only. Contact your administrator to learn more.", "warnLicenseExp": "Your license has expired. Please, update your license and refresh the page.", "warnLicenseLimitedNoAccess": "License expired. You have no access to document editing functionality. Please, contact your admin.", "warnLicenseLimitedRenewed": "License needs to be renewed. You have limited access to document editing functionality.<br>Please contact your administrator to get full access", "warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only. Contact %1 sales team for personal upgrade terms.", "warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "warnProcessRightsChange": "You don't have permission to edit the file."}}, "Error": {"convertationTimeoutText": "Conversion timeout exceeded.", "criticalErrorExtText": "Press 'OK' to go back to the document list.", "criticalErrorTitle": "Error", "downloadErrorText": "Download failed.", "errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please, contact your admin.", "errorArgsRange": "An error in the formula.<br>Incorrect arguments range.", "errorAutoFilterChange": "The operation is not allowed as it is attempting to shift cells in a table on your worksheet.", "errorAutoFilterChangeFormatTable": "The operation could not be done for the selected cells as you cannot move a part of a table.<br>Select another data range so that the whole table is shifted and try again.", "errorAutoFilterDataRange": "The operation could not be done for the selected range of cells.<br>Select a uniform data range inside or outside the table and try again.", "errorAutoFilterHiddenRange": "The operation cannot be performed because the area contains filtered cells.<br>Please, unhide the filtered elements and try again.", "errorBadImageUrl": "Image URL is incorrect", "errorCannotUseCommandProtectedSheet": "You cannot use this command on a protected sheet. To use this command, unprotect the sheet.<br>You might be requested to enter a password.", "errorChangeArray": "You cannot change part of an array.", "errorChangeOnProtectedSheet": "The cell or chart you are trying to change is on a protected sheet. To make a change, unprotect the sheet. You might be requested to enter a password.", "errorConnectToServer": "Can't save this doc. Check your connection settings or contact your admin.<br>When you click the 'OK' button, you will be prompted to download the document.", "errorCopyMultiselectArea": "This command cannot be used with multiple selections.<br>Select a single range and try again.", "errorCountArg": "An error in the formula.<br>Invalid number of arguments.", "errorCountArgExceed": "An error in the formula.<br>Maximum number of arguments exceeded.", "errorCreateDefName": "The existing named ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "errorDatabaseConnection": "External error.<br>Database connection error. Please, contact support.", "errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "errorDataRange": "Incorrect data range.", "errorDataValidate": "The value you entered is not valid.<br>A user has restricted values that can be entered into this cell.", "errorDefaultMessage": "Error code: %1", "errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "errorEditingDownloadas": "An error occurred during the work with the document.<br>Use the 'Download' option to save the file backup copy locally.", "errorFilePassProtect": "The file is password protected and could not be opened.", "errorFileRequest": "External error.<br>File Request. Please, contact support.", "errorFileSizeExceed": "The file size exceeds your server limitation.<br>Please, contact your admin for details.", "errorFileVKey": "External error.<br>Incorrect security key. Please, contact support.", "errorFillRange": "Could not fill the selected range of cells.<br>All the merged cells need to be the same size.", "errorFormulaName": "An error in the formula.<br>Incorrect formula name.", "errorFormulaParsing": "Internal error while the formula parsing.", "errorFrmlMaxLength": "You cannot add this formula as its length exceeds the allowed number of characters.<br>Please, edit it and try again.", "errorFrmlMaxReference": "You cannot enter this formula because it has too many values,<br>cell references, and/or names.", "errorFrmlMaxTextLength": "Text values in formulas are limited to 255 characters.<br>Use the CONCATENATE function or concatenation operator (&)", "errorFrmlWrongReferences": "The function refers to a sheet that does not exist.<br>Please, check the data and try again.", "errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "errorInvalidRef": "Enter a correct name for the selection or a valid reference to go to.", "errorKeyEncrypt": "Unknown key descriptor", "errorKeyExpire": "Key descriptor expired", "errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "errorLockedAll": "The operation could not be done as the sheet has been locked by another user.", "errorLockedCellPivot": "You cannot change data inside a pivot table.", "errorLockedWorksheetRename": "The sheet cannot be renamed at the moment as it is being renamed by another user", "errorMaxPoints": "The maximum number of points in series per chart is 4096.", "errorMoveRange": "Cannot change a part of a merged cell", "errorMultiCellFormula": "Multi-cell array formulas are not allowed in tables.", "errorOpenWarning": "The length of one of the formulas in the file exceeded<br>the allowed number of characters and it was removed.", "errorOperandExpected": "The entered function syntax is not correct. Please, check if you missed one of the parentheses - '(' or ')'.", "errorPasteMaxRange": "The copy and paste area does not match. Please, select an area of the same size or click the first cell in a row to paste the copied cells.", "errorPrintMaxPagesCount": "Unfortunately, it’s not possible to print more than 1500 pages at once in the current version of the program.<br>This restriction will be eliminated in upcoming releases.", "errorSessionAbsolute": "The document editing session has expired. Please, reload the page.", "errorSessionIdle": "The document has not been edited for quite a long time. Please, reload the page.", "errorSessionToken": "The connection to the server has been interrupted. Please, reload the page.", "errorStockChart": "Incorrect row order. To build a stock chart, place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator.", "errorUnexpectedGuid": "External error.<br>Unexpected Guid. Please, contact support.", "errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "errorUserDrop": "The file cannot be accessed right now.", "errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "errorViewerDisconnect": "Connection is lost. You can still view the document,<br>but you won't be able to download or print it until the connection is restored and the page is reloaded.", "errorWrongBracketsCount": "An error in the formula.<br>Wrong number of brackets.", "errorWrongOperator": "An error in the entered formula. Wrong operator is used.<br>Please correct the error.", "notcriticalErrorTitle": "Warning", "openErrorText": "An error has occurred while opening the file", "pastInMergeAreaError": "Cannot change a part of a merged cell", "saveErrorText": "An error has occurred while saving the file", "scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please, reload the page.", "textCancel": "Cancel", "textErrorPasswordIsNotCorrect": "The password you supplied is not correct.<br>Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.", "textOk": "Ok", "unknownErrorText": "Unknown error.", "uploadImageExtMessage": "Unknown image format.", "uploadImageFileCountMessage": "No images uploaded.", "uploadImageSizeMessage": "The image is too big. The maximum size is 25 MB."}, "LongActions": {"advDRMPassword": "Password", "applyChangesTextText": "Loading data...", "applyChangesTitleText": "Loading Data", "confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "confirmMoveCellRange": "The destination cells range can contain data. Continue the operation?", "confirmPutMergeRange": "The source data contains merged cells.<br>They will be unmerged before they are pasted into the table.", "confirmReplaceFormulaInTable": "Formulas in the header row will be removed and converted to static text.<br>Do you want to continue?", "downloadTextText": "Downloading document...", "downloadTitleText": "Downloading Document", "loadFontsTextText": "Loading data...", "loadFontsTitleText": "Loading Data", "loadFontTextText": "Loading data...", "loadFontTitleText": "Loading Data", "loadImagesTextText": "Loading images...", "loadImagesTitleText": "Loading Images", "loadImageTextText": "Loading image...", "loadImageTitleText": "Loading Image", "loadingDocumentTextText": "Loading document...", "loadingDocumentTitleText": "Loading document", "notcriticalErrorTitle": "Warning", "openTextText": "Opening document...", "openTitleText": "Opening Document", "printTextText": "Printing document...", "printTitleText": "Printing Document", "savePreparingText": "Preparing to save", "savePreparingTitle": "Preparing to save. Please wait...", "saveTextText": "Saving document...", "saveTitleText": "Saving Document", "textCancel": "Cancel", "textContinue": "Continue", "textErrorWrongPassword": "The password you supplied is not correct.", "textLoadingDocument": "Loading document", "textNo": "No", "textOk": "Ok", "textUndo": "Undo", "textUnlockRange": "Unlock Range", "textUnlockRangeWarning": "A range you are trying to change is password protected.", "textYes": "Yes", "txtEditingMode": "Set editing mode...", "uploadImageTextText": "Uploading image...", "uploadImageTitleText": "Uploading Image", "waitText": "Please, wait..."}, "Statusbar": {"notcriticalErrorTitle": "Warning", "textCancel": "Cancel", "textDelete": "Delete", "textDuplicate": "Duplicate", "textErrNameExists": "Worksheet with this name already exists.", "textErrNameWrongChar": "A sheet name cannot contains characters: \\, /, *, ?, [, ], : or the character ' as first or last character", "textErrNotEmpty": "Sheet name must not be empty", "textErrorLastSheet": "The workbook must have at least one visible worksheet.", "textErrorRemoveSheet": "Can't delete the worksheet.", "textHidden": "Hidden", "textHide": "<PERSON>de", "textMore": "More", "textMove": "Move", "textMoveBefore": "Move before sheet", "textMoveToEnd": "(Move to end)", "textOk": "Ok", "textRename": "<PERSON><PERSON>", "textRenameSheet": "<PERSON><PERSON>", "textSheet": "Sheet", "textSheetName": "Sheet Name", "textTabColor": "Tab Color", "textUnhide": "Unhide", "textWarnDeleteSheet": "The worksheet maybe has data. Proceed operation?"}, "Toolbar": {"dlgLeaveMsgText": "You have unsaved changes in this document. Click 'Stay on this Page' to wait for autosave. Click 'Leave this Page' to discard all the unsaved changes.", "dlgLeaveTitleText": "You leave the application", "leaveButtonText": "Leave this Page", "stayButtonText": "Stay on this Page"}, "View": {"Add": {"errorMaxRows": "ERROR! The maximum number of data series per chart is 255.", "errorStockChart": "Incorrect row order. To build a stock chart, place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "notcriticalErrorTitle": "Warning", "sCatDateAndTime": "Date and time", "sCatEngineering": "Engineering", "sCatFinancial": "Financial", "sCatInformation": "Information", "sCatLogical": "Logical", "sCatLookupAndReference": "Lookup and Reference", "sCatMathematic": "Math and trigonometry", "sCatStatistical": "Statistical", "sCatTextAndData": "Text and data", "textAddLink": "Add Link", "textAddress": "Address", "textAllTableHint": "Returns the entire contents of the table or specified table columns including column headers, data and total rows", "textBack": "Back", "textCancel": "Cancel", "textChart": "Chart", "textComment": "Comment", "textDataTableHint": "Returns the data cells of the table or specified table columns", "textDisplay": "Display", "textDone": "Done", "textEmptyImgUrl": "You need to specify the image URL.", "textExternalLink": "External Link", "textFilter": "Filter", "textFunction": "Function", "textGroups": "CATEGORIES", "textHeadersTableHint": "Returns the column headers for the table or specified table columns", "textImage": "Image", "textImageURL": "Image URL", "textInsert": "Insert", "textInsertImage": "Insert Image", "textInternalDataRange": "Internal Data Range", "textInvalidRange": "ERROR! Invalid cells range", "textLink": "Link", "textLinkSettings": "<PERSON>s", "textLinkType": "Link Type", "textOk": "Ok", "textOther": "Other", "textPictureFromLibrary": "Picture from library", "textPictureFromURL": "Picture from URL", "textRange": "Range", "textRecommended": "Recommended", "textRequired": "Required", "textScreenTip": "Screen Tip", "textSelectedRange": "Selected Range", "textShape": "<PERSON><PERSON><PERSON>", "textSheet": "Sheet", "textSortAndFilter": "Sort and Filter", "textThisRowHint": "Choose only this row of the specified column", "textTotalsTableHint": "Returns the total rows for the table or specified table columns", "txtExpand": "Expand and sort", "txtExpandSort": "The data next to the selection will not be sorted. Do you want to expand the selection to include the adjacent data or continue with sorting the currently selected cells only?", "txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "txtNo": "No", "txtNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "txtSorting": "Sorting", "txtSortSelected": "Sort selected", "txtYes": "Yes"}, "Edit": {"notcriticalErrorTitle": "Warning", "textAccounting": "Accounting", "textActualSize": "Actual Size", "textAddCustomColor": "Add Custom Color", "textAddress": "Address", "textAlign": "Align", "textAlignBottom": "Align Bottom", "textAlignCenter": "Align Center", "textAlignLeft": "Align Left", "textAlignMiddle": "Align Middle", "textAlignRight": "Align Right", "textAlignTop": "Align Top", "textAllBorders": "All Borders", "textAngleClockwise": "<PERSON>le Clockwise", "textAngleCounterclockwise": "Angle Counterclockwise", "textArrange": "<PERSON><PERSON><PERSON>", "textAuto": "Auto", "textAutomatic": "Automatic", "textAxisCrosses": "Axis Crosses", "textAxisOptions": "Axis Options", "textAxisPosition": "Axis Position", "textAxisTitle": "Axis Title", "textBack": "Back", "textBetweenTickMarks": "Between Tick Marks", "textBillions": "Billions", "textBorder": "Border", "textBorderStyle": "Border Style", "textBottom": "Bottom", "textBottomBorder": "Bottom Border", "textBringToForeground": "Bring to Foreground", "textCancel": "Cancel", "textCell": "Cell", "textCellStyle": "Cell Style", "textCenter": "Center", "textChangeShape": "Change Shape", "textChart": "Chart", "textChartTitle": "Chart Title", "textClearFilter": "Clear Filter", "textColor": "Color", "textCross": "Cross", "textCrossesValue": "Crosses Value", "textCurrency": "<PERSON><PERSON><PERSON><PERSON>", "textCustomColor": "Custom Color", "textDataLabels": "Data Labels", "textDate": "Date", "textDefault": "Selected range", "textDeleteFilter": "Delete Filter", "textDeleteImage": "Delete Image", "textDeleteLink": "Delete Link", "textDesign": "Design", "textDiagonalDownBorder": "Diagonal Down Border", "textDiagonalUpBorder": "Diagonal Up Border", "textDisplay": "Display", "textDisplayUnits": "Display Units", "textDollar": "Dollar", "textDone": "Done", "textEditLink": "Edit Link", "textEffects": "Effects", "textEmptyImgUrl": "You need to specify the image URL.", "textEmptyItem": "{Blanks}", "textErrorMsg": "You must choose at least one value", "textErrorTitle": "Warning", "textEuro": "Euro", "textExternalLink": "External Link", "textFill": "Fill", "textFillColor": "Fill Color", "textFilterOptions": "Filter Options", "textFit": "<PERSON><PERSON>", "textFonts": "Fonts", "textFormat": "Format", "textFraction": "Fraction", "textFromLibrary": "Picture from Library", "textFromURL": "Picture from URL", "textGeneral": "General", "textGridlines": "Gridlines", "textHigh": "High", "textHorizontal": "Horizontal", "textHorizontalAxis": "Horizontal Axis", "textHorizontalText": "Horizontal Text", "textHundredMil": "100 000 000", "textHundreds": "Hundreds", "textHundredThousands": "100 000", "textHyperlink": "Hyperlink", "textImage": "Image", "textImageURL": "Image URL", "textIn": "In", "textInnerBottom": "Inner Bottom", "textInnerTop": "Inner Top", "textInsideBorders": "Inside Borders", "textInsideHorizontalBorder": "Inside Horizontal Border", "textInsideVerticalBorder": "Inside Vertical Border", "textInteger": "Integer", "textInternalDataRange": "Internal Data Range", "textInvalidRange": "Invalid cells range", "textJustified": "Justified", "textLabelOptions": "Label Options", "textLabelPosition": "Label Position", "textLayout": "Layout", "textLeft": "Left", "textLeftBorder": "Left Border", "textLeftOverlay": "Left Overlay", "textLegend": "Legend", "textLink": "Link", "textLinkSettings": "<PERSON>s", "textLinkType": "Link Type", "textLow": "Low", "textMajor": "Major", "textMajorAndMinor": "Major And Minor", "textMajorType": "Major Type", "textMaximumValue": "Maximum Value", "textMedium": "Medium", "textMillions": "Millions", "textMinimumValue": "Minimum Value", "textMinor": "Minor", "textMinorType": "Minor Type", "textMoveBackward": "Move Backward", "textMoveForward": "Move Forward", "textNextToAxis": "Next to Axis", "textNoBorder": "No Border", "textNone": "None", "textNoOverlay": "No Overlay", "textNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "textNumber": "Number", "textOk": "Ok", "textOnTickMarks": "On Tick Marks", "textOpacity": "Opacity", "textOut": "Out", "textOuterTop": "Outer Top", "textOutsideBorders": "Outside Borders", "textOverlay": "Overlay", "textPercentage": "Percentage", "textPictureFromLibrary": "Picture from Library", "textPictureFromURL": "Picture from URL", "textPound": "Pound", "textPt": "pt", "textRange": "Range", "textRecommended": "Recommended", "textRemoveChart": "Remove Chart", "textRemoveShape": "Remove <PERSON>", "textReplace": "Replace", "textReplaceImage": "Replace Image", "textRequired": "Required", "textRight": "Right", "textRightBorder": "Right Border", "textRightOverlay": "Right Overlay", "textRotated": "Rotated", "textRotateTextDown": "Rotate Text Down", "textRotateTextUp": "Rotate Text Up", "textRouble": "Rouble", "textScientific": "Scientific", "textScreenTip": "Screen Tip", "textSelectAll": "Select All", "textSelectObjectToEdit": "Select object to edit", "textSendToBackground": "Send to Background", "textSettings": "Settings", "textShape": "<PERSON><PERSON><PERSON>", "textSheet": "Sheet", "textSize": "Size", "textStyle": "Style", "textTenMillions": "10 000 000", "textTenThousands": "10 000", "textText": "Text", "textTextColor": "Text Color", "textTextFormat": "Text Format", "textTextOrientation": "Text Orientation", "textThick": "<PERSON><PERSON><PERSON>", "textThin": "Thin", "textThousands": "Thousands", "textTickOptions": "Tick Options", "textTime": "Time", "textTop": "Top", "textTopBorder": "Top Border", "textTrillions": "Trillions", "textType": "Type", "textValue": "Value", "textValuesInReverseOrder": "Values in Reverse Order", "textVertical": "Vertical", "textVerticalAxis": "Vertical Axis", "textVerticalText": "Vertical Text", "textWrapText": "Wrap Text", "textYen": "Yen", "txtNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "txtSortHigh2Low": "Sort Highest to Lowest", "txtSortLow2High": "So<PERSON>st to Highest"}, "Settings": {"advCSVOptions": "Choose CSV options", "advDRMEnterPassword": "Your password, please:", "advDRMOptions": "Protected File", "advDRMPassword": "Password", "closeButtonText": "Close File", "notcriticalErrorTitle": "Warning", "strFuncLocale": "Formula Language", "strFuncLocaleEx": "Example: SUM; MIN; MAX; COUNT", "textAbout": "About", "textAddress": "Address", "textApplication": "Application", "textApplicationSettings": "Application Settings", "textAuthor": "Author", "textBack": "Back", "textBottom": "Bottom", "textByColumns": "By columns", "textByRows": "By rows", "textCancel": "Cancel", "textCentimeter": "Centimeter", "textChooseCsvOptions": "Choose CSV Options", "textChooseDelimeter": "Choose <PERSON>", "textChooseEncoding": "Choose Encoding", "textCollaboration": "Collaboration", "textColorSchemes": "Color Schemes", "textComment": "Comment", "textCommentingDisplay": "Commenting Display", "textComments": "Comments", "textCreated": "Created", "textCustomSize": "Custom Size", "textDarkTheme": "Dark Theme", "textDelimeter": "Delimiter", "textDirection": "Direction", "textDisableAll": "Disable All", "textDisableAllMacrosWithNotification": "Disable all macros with a notification", "textDisableAllMacrosWithoutNotification": "Disable all macros without a notification", "textDone": "Done", "textDownload": "Download", "textDownloadAs": "Download As", "textEmail": "Email", "textEnableAll": "Enable All", "textEnableAllMacrosWithoutNotification": "Enable all macros without a notification", "textEncoding": "Encoding", "textExample": "Example", "textFeedback": "Feedback & Support", "textFind": "Find", "textFindAndReplace": "Find and Replace", "textFindAndReplaceAll": "Find and Replace All", "textFormat": "Format", "textFormulaLanguage": "Formula Language", "textFormulas": "Formulas", "textHelp": "Help", "textHideGridlines": "Hide Gridlines", "textHideHeadings": "<PERSON><PERSON> Headings", "textHighlightRes": "Highlight results", "textInch": "Inch", "textLandscape": "Landscape", "textLastModified": "Last Modified", "textLastModifiedBy": "Last Modified By", "textLeft": "Left", "textLeftToRight": "Left To Right", "textLocation": "Location", "textLookIn": "Look In", "textMacrosSettings": "<PERSON><PERSON>", "textMargins": "<PERSON><PERSON>", "textMatchCase": "Match Case", "textMatchCell": "Match Cell", "textNoTextFound": "Text not found", "textOk": "Ok", "textOpenFile": "Enter a password to open the file", "textOrientation": "Orientation", "textOwner": "Owner", "textPoint": "Point", "textPortrait": "Portrait", "textPoweredBy": "Powered By", "textPrint": "Print", "textR1C1Style": "R1C1 Reference Style", "textRegionalSettings": "Regional Settings", "textReplace": "Replace", "textReplaceAll": "Replace All", "textResolvedComments": "Resolved Comments", "textRestartApplication": "Please restart the application for the changes to take effect", "textRight": "Right", "textRightToLeft": "Right To Left", "textSearch": "Search", "textSearchBy": "Search", "textSearchIn": "Search In", "textSettings": "Settings", "textSheet": "Sheet", "textShowNotification": "Show Notification", "textSpreadsheetFormats": "Spreadsheet Formats", "textSpreadsheetInfo": "Spreadsheet Info", "textSpreadsheetSettings": "Spreadsheet Settings", "textSpreadsheetTitle": "Spreadsheet Title", "textSubject": "Subject", "textTel": "Tel", "textTitle": "Title", "textTop": "Top", "textUnitOfMeasurement": "Unit Of Measurement", "textUploaded": "Uploaded", "textValues": "Values", "textVersion": "Version", "textWorkbook": "Workbook", "txtBe": "Belarusian", "txtBg": "Bulgarian", "txtCa": "Catalan", "txtColon": "Colon", "txtComma": "Comma", "txtCs": "Czech", "txtDa": "Danish", "txtDe": "De<PERSON>ch", "txtDelimiter": "Delimiter", "txtDownloadCsv": "Download CSV", "txtEl": "Greek", "txtEn": "English", "txtEncoding": "Encoding", "txtEs": "Spanish", "txtFi": "Finnish", "txtFr": "French", "txtHu": "Hungarian", "txtId": "Indonesian", "txtIncorrectPwd": "Password is incorrect", "txtIt": "Italian", "txtJa": "Japanese", "txtKo": "Korean", "txtLo": "Lao", "txtLv": "Latvian", "txtNb": "Norwegian", "txtNl": "Dutch", "txtOk": "Ok", "txtPl": "Polish", "txtProtected": "Once you enter the password and open the file, the current password to the file will be reset", "txtPtbr": "Portuguese (Brazil)", "txtPtlang": "Portuguese (Portugal)", "txtRo": "Romanian", "txtRu": "Russian", "txtScheme1": "Office", "txtScheme10": "Median", "txtScheme11": "Metro", "txtScheme12": "<PERSON><PERSON><PERSON>", "txtScheme13": "Opulent", "txtScheme14": "Oriel", "txtScheme15": "Origin", "txtScheme16": "Paper", "txtScheme17": "Solstice", "txtScheme18": "Technic", "txtScheme19": "Trek", "txtScheme2": "Grayscale", "txtScheme20": "Urban", "txtScheme21": "Verve", "txtScheme22": "New Office", "txtScheme3": "Apex", "txtScheme4": "Aspect", "txtScheme5": "Civic", "txtScheme6": "Concourse", "txtScheme7": "Equity", "txtScheme8": "Flow", "txtScheme9": "Foundry", "txtSemicolon": "Semicolon", "txtSk": "Slovak", "txtSl": "Slovenian", "txtSpace": "Space", "txtSv": "Swedish", "txtTab": "Tab", "txtTr": "Turkish", "txtUk": "Ukrainian", "txtVi": "Vietnamese", "txtZh": "Chinese", "warnDownloadAs": "If you continue saving in this format all features except the text will be lost.<br>Are you sure you want to continue?"}}}
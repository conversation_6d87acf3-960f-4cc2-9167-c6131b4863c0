{"About": {"textAbout": "About", "textAddress": "Address", "textBack": "Back", "textEmail": "Email", "textPoweredBy": "Powered By", "textTel": "Tel", "textVersion": "Version", "textEditor": "Spreadsheet Editor"}, "Common": {"Collaboration": {"notcriticalErrorTitle": "Warning", "textAddComment": "Add Comment", "textAddReply": "Add Reply", "textBack": "Back", "textCancel": "Cancel", "textCollaboration": "Collaboration", "textComments": "Comments", "textDeleteComment": "Delete Comment", "textDeleteReply": "Delete Reply", "textDone": "Done", "textEdit": "Edit", "textEditComment": "Edit Comment", "textEditReply": "Edit Reply", "textEditUser": "Users who are editing the file:", "textMessageDeleteComment": "Do you really want to delete this comment?", "textMessageDeleteReply": "Do you really want to delete this reply?", "textNoComments": "This document doesn't contain comments", "textReopen": "Reopen", "textResolve": "Resolve", "textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "textUsers": "Users", "textOk": "Ok", "textSharingSettings": "Sharing Settings"}, "ThemeColorPalette": {"textCustomColors": "Custom Colors", "textStandartColors": "Standard Colors", "textThemeColors": "Theme Colors"}}, "ContextMenu": {"errorCopyCutPaste": "Copy, cut, and paste actions using the context menu will be performed within the current file only.", "menuAddComment": "Add Comment", "menuAddLink": "Add Link", "menuCancel": "Cancel", "menuCell": "Cell", "menuDelete": "Delete", "menuEdit": "Edit", "menuFreezePanes": "Freeze Panes", "menuHide": "<PERSON>de", "menuMerge": "<PERSON><PERSON>", "menuMore": "More", "menuOpenLink": "Open Link", "menuShow": "Show", "menuUnfreezePanes": "Unfreeze Panes", "menuUnmerge": "Unmerge", "menuUnwrap": "Unwrap", "menuViewComment": "View Comment", "menuWrap": "Wrap", "notcriticalErrorTitle": "Warning", "textCopyCutPasteActions": "Copy, Cut and Paste Actions", "textDoNotShowAgain": "Don't show again", "warnMergeLostData": "The operation can destroy data in the selected cells. Continue?", "errorInvalidLink": "The link reference does not exist. Please correct the link or delete it.", "textOk": "Ok", "txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "menuEditLink": "Edit Link"}, "Controller": {"Main": {"criticalErrorTitle": "Error", "errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please, contact your admin.", "errorProcessSaveResult": "Saving is failed.", "errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "leavePageText": "You have unsaved changes in this document. Click 'Stay on this Page' to wait for autosave. Click 'Leave this Page' to discard all the unsaved changes.", "notcriticalErrorTitle": "Warning", "SDK": {"txtAccent": "Accent", "txtAll": "(All)", "txtArt": "Your text here", "txtBlank": "(blank)", "txtByField": "%1 of %2", "txtClearFilter": "Clear Filter (Alt+C)", "txtColLbls": "Column Labels", "txtColumn": "Column", "txtConfidential": "Confidential", "txtDate": "Date", "txtDays": "Days", "txtDiagramTitle": "Chart Title", "txtFile": "File", "txtGrandTotal": "Grand Total", "txtGroup": "Group", "txtHours": "Hours", "txtMinutes": "Minutes", "txtMonths": "Months", "txtMultiSelect": "Multi-Select (Alt+S)", "txtOr": "%1 or %2", "txtPage": "Page", "txtPageOf": "Page %1 of %2", "txtPages": "Pages", "txtPreparedBy": "Prepared by", "txtPrintArea": "Print_Area", "txtQuarter": "Qtr", "txtQuarters": "Quarters", "txtRow": "Row", "txtRowLbls": "Row Labels", "txtSeconds": "Seconds", "txtSeries": "Series", "txtStyle_Bad": "Bad", "txtStyle_Calculation": "Calculation", "txtStyle_Check_Cell": "Check Cell", "txtStyle_Comma": "Comma", "txtStyle_Currency": "<PERSON><PERSON><PERSON><PERSON>", "txtStyle_Explanatory_Text": "Explanatory Text", "txtStyle_Good": "Good", "txtStyle_Heading_1": "Heading 1", "txtStyle_Heading_2": "Heading 2", "txtStyle_Heading_3": "Heading 3", "txtStyle_Heading_4": "Heading 4", "txtStyle_Input": "Input", "txtStyle_Linked_Cell": "Linked Cell", "txtStyle_Neutral": "Neutral", "txtStyle_Normal": "Normal", "txtStyle_Note": "Note", "txtStyle_Output": "Output", "txtStyle_Percent": "Percent", "txtStyle_Title": "Title", "txtStyle_Total": "Total", "txtStyle_Warning_Text": "Warning Text", "txtTab": "Tab", "txtTable": "Table", "txtTime": "Time", "txtValues": "Values", "txtXAxis": "X Axis", "txtYAxis": "Y Axis", "txtYears": "Years"}, "textAnonymous": "Anonymous", "textBuyNow": "Visit website", "textClose": "Close", "textContactUs": "Contact sales", "textCustomLoader": "Sorry, you are not entitled to change the loader. Please, contact our sales department to get a quote.", "textGuest": "Guest", "textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "textNo": "No", "textNoLicenseTitle": "License limit reached", "textPaidFeature": "Paid feature", "textRemember": "Remember my choice", "textYes": "Yes", "titleServerVersion": "Editor updated", "titleUpdateVersion": "Version changed", "warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only. Contact your administrator to learn more.", "warnLicenseLimitedNoAccess": "License expired. You have no access to document editing functionality. Please, contact your admin.", "warnLicenseLimitedRenewed": "License needs to be renewed. You have limited access to document editing functionality.<br>Please contact your administrator to get full access", "warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only. Contact %1 sales team for personal upgrade terms.", "warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "warnProcessRightsChange": "You don't have permission to edit the file.", "textNoChoices": "There are no choices for filling the cell.<br>Only text values from the column can be selected for replacement.", "textOk": "Ok", "textReplaceSuccess": "The search has been done. Occurrences replaced: {0}", "textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "textNoTextFound": "Text not found", "errorOpensource": "Using the free Community version, you can open documents for viewing only. To access mobile web editors, a commercial license is required.", "textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "titleLicenseExp": "License expired", "warnLicenseExp": "Your license has expired. Please, update your license and refresh the page.", "textDontUpdate": "Don't Update", "textUpdate": "Update", "textWarnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe. If you trust the links, update them to get the latest data."}}, "Error": {"convertationTimeoutText": "Conversion timeout exceeded.", "criticalErrorExtText": "Press 'OK' to go back to the document list.", "criticalErrorTitle": "Error", "downloadErrorText": "Download failed.", "errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please, contact your admin.", "errorArgsRange": "An error in the formula.<br>Incorrect arguments range.", "errorAutoFilterChange": "The operation is not allowed as it is attempting to shift cells in a table on your worksheet.", "errorAutoFilterChangeFormatTable": "The operation could not be done for the selected cells as you cannot move a part of a table.<br>Select another data range so that the whole table is shifted and try again.", "errorAutoFilterDataRange": "The operation could not be done for the selected range of cells.<br>Select a uniform data range inside or outside the table and try again.", "errorAutoFilterHiddenRange": "The operation cannot be performed because the area contains filtered cells.<br>Please, unhide the filtered elements and try again.", "errorBadImageUrl": "Image url is incorrect", "errorChangeArray": "You cannot change part of an array.", "errorConnectToServer": "Can't save this doc. Check your connection settings or contact your admin.<br>When you click the 'OK' button, you will be prompted to download the document.", "errorCopyMultiselectArea": "This command cannot be used with multiple selections.<br>Select a single range and try again.", "errorCountArg": "An error in the formula.<br>Invalid number of arguments.", "errorCountArgExceed": "An error in the formula.<br>Maximum number of arguments exceeded.", "errorCreateDefName": "The existing named ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "errorDatabaseConnection": "External error.<br>Database connection error. Please, contact support.", "errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "errorDataRange": "Incorrect data range.", "errorDataValidate": "The value you entered is not valid.<br>A user has restricted values that can be entered into this cell.", "errorDefaultMessage": "Error code: %1", "errorEditingDownloadas": "An error occurred during the work with the document.<br>Use the 'Download' option to save the file backup copy locally.", "errorFilePassProtect": "The file is password protected and could not be opened.", "errorFileRequest": "External error.<br>File Request. Please, contact support.", "errorFileSizeExceed": "The file size exceeds your server limitation.<br>Please, contact your admin for details.", "errorFileVKey": "External error.<br>Incorrect security key. Please, contact support.", "errorFillRange": "Could not fill the selected range of cells.<br>All the merged cells need to be the same size.", "errorFormulaName": "An error in the formula.<br>Incorrect formula name.", "errorFormulaParsing": "Internal error while the formula parsing.", "errorFrmlMaxLength": "You cannot add this formula as its length exceeds the allowed number of characters.<br>Please, edit it and try again.", "errorFrmlMaxReference": "You cannot enter this formula because it has too many values,<br>cell references, and/or names.", "errorFrmlMaxTextLength": "Text values in formulas are limited to 255 characters.<br>Use the CONCATENATE function or concatenation operator (&)", "errorFrmlWrongReferences": "The function refers to a sheet that does not exist.<br>Please, check the data and try again.", "errorInvalidRef": "Enter a correct name for the selection or a valid reference to go to.", "errorKeyEncrypt": "Unknown key descriptor", "errorKeyExpire": "Key descriptor expired", "errorLockedAll": "The operation could not be done as the sheet has been locked by another user.", "errorLockedCellPivot": "You cannot change data inside a pivot table.", "errorLockedWorksheetRename": "The sheet cannot be renamed at the moment as it is being renamed by another user", "errorMaxPoints": "The maximum number of points in series per chart is 4096.", "errorMoveRange": "Cannot change a part of a merged cell", "errorMultiCellFormula": "Multi-cell array formulas are not allowed in tables.", "errorOpenWarning": "The length of one of the formulas in the file exceeded<br>the allowed number of characters and it was removed.", "errorOperandExpected": "The entered function syntax is not correct. Please, check if you missed one of the parentheses - '(' or ')'.", "errorPasteMaxRange": "The copy and paste area does not match. Please, select an area of the same size or click the first cell in a row to paste the copied cells.", "errorPrintMaxPagesCount": "Unfortunately, itвЂ™s not possible to print more than 1500 pages at once in the current version of the program.<br>This restriction will be eliminated in upcoming releases.", "errorSessionAbsolute": "The document editing session has expired. Please, reload the page.", "errorSessionIdle": "The document has not been edited for quite a long time. Please, reload the page.", "errorSessionToken": "The connection to the server has been interrupted. Please, reload the page.", "errorStockChart": "Incorrect row order. To build a stock chart, place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "errorUnexpectedGuid": "External error.<br>Unexpected Guid. Please, contact support.", "errorUpdateVersionOnDisconnect": "Internet connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "errorUserDrop": "The file cannot be accessed right now.", "errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "errorViewerDisconnect": "Connection is lost. You can still view the document,<br>but you won't be able to download it until the connection is restored and the page is reloaded.", "errorWrongBracketsCount": "An error in the formula.<br>Wrong number of brackets.", "errorWrongOperator": "An error in the entered formula. Wrong operator is used.<br> Please correct the error.", "notcriticalErrorTitle": "Warning", "openErrorText": "An error has occurred while opening the file", "pastInMergeAreaError": "Cannot change a part of a merged cell", "saveErrorText": "An error has occurred while saving the file", "scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please, reload the page.", "unknownErrorText": "Unknown error.", "uploadImageExtMessage": "Unknown image format.", "uploadImageFileCountMessage": "No images uploaded.", "uploadImageSizeMessage": "The image is too big. The maximum size is 25 MB.", "errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "errorChangeOnProtectedSheet": "The cell or chart you are trying to change is on a protected sheet. To make a change, unprotect the sheet. You might be requested to enter a password.", "textErrorPasswordIsNotCorrect": "The password you supplied is not correct.<br>Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.", "errorCannotUseCommandProtectedSheet": "You cannot use this command on a protected sheet. To use this command, unprotect the sheet.<br>You might be requested to enter a password.", "errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "textCancel": "Cancel", "textOk": "Ok", "errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator."}, "LongActions": {"applyChangesTextText": "Loading data...", "applyChangesTitleText": "Loading Data", "confirmMoveCellRange": "The destination cells range can contain data. Continue the operation?", "confirmPutMergeRange": "The source data contains merged cells.<br>They will be unmerged before they are pasted into the table.", "confirmReplaceFormulaInTable": "Formulas in the header row will be removed and converted to static text.<br>Do you want to continue?", "downloadTextText": "Downloading document...", "downloadTitleText": "Downloading Document", "loadFontsTextText": "Loading data...", "loadFontsTitleText": "Loading Data", "loadFontTextText": "Loading data...", "loadFontTitleText": "Loading Data", "loadImagesTextText": "Loading images...", "loadImagesTitleText": "Loading Images", "loadImageTextText": "Loading image...", "loadImageTitleText": "Loading Image", "loadingDocumentTextText": "Loading document...", "loadingDocumentTitleText": "Loading document", "notcriticalErrorTitle": "Warning", "openTextText": "Opening document...", "openTitleText": "Opening Document", "printTextText": "Printing document...", "printTitleText": "Printing Document", "savePreparingText": "Preparing to save", "savePreparingTitle": "Preparing to save. Please wait...", "saveTextText": "Saving document...", "saveTitleText": "Saving Document", "textLoadingDocument": "Loading document", "textNo": "No", "textOk": "Ok", "textYes": "Yes", "txtEditingMode": "Set editing mode...", "uploadImageTextText": "Uploading image...", "uploadImageTitleText": "Uploading Image", "waitText": "Please, wait...", "advDRMPassword": "Password", "textCancel": "Cancel", "textErrorWrongPassword": "The password you supplied is not correct.", "textUnlockRange": "Unlock Range", "textUnlockRangeWarning": "A range you are trying to change is password protected.", "confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "textUndo": "Undo", "textContinue": "Continue"}, "Statusbar": {"notcriticalErrorTitle": "Warning", "textCancel": "Cancel", "textDelete": "Delete", "textDuplicate": "Duplicate", "textErrNameExists": "Worksheet with this name already exists.", "textErrNameWrongChar": "A sheet name cannot contains characters: \\, /, *, ?, [, ], :", "textErrNotEmpty": "Sheet name must not be empty", "textErrorLastSheet": "The workbook must have at least one visible worksheet.", "textErrorRemoveSheet": "Can't delete the worksheet.", "textHide": "<PERSON>de", "textMore": "More", "textRename": "<PERSON><PERSON>", "textRenameSheet": "<PERSON><PERSON>", "textSheet": "Sheet", "textSheetName": "Sheet Name", "textUnhide": "Unhide", "textWarnDeleteSheet": "The worksheet maybe has data. Proceed operation?", "textOk": "Ok", "textMove": "Move", "textMoveBack": "Move back", "textMoveForward": "Move forward", "textHidden": "Hidden", "textMoveBefore": "Move before sheet", "textMoveToEnd": "(Move to end)", "textTabColor": "Tab Color"}, "Toolbar": {"dlgLeaveMsgText": "You have unsaved changes in this document. Click 'Stay on this Page' to wait for autosave. Click 'Leave this Page' to discard all the unsaved changes.", "dlgLeaveTitleText": "You leave the application", "leaveButtonText": "Leave this Page", "stayButtonText": "Stay on this Page"}, "View": {"Add": {"errorMaxRows": "ERROR! The maximum number of data series per chart is 255.", "errorStockChart": "Incorrect row order. To build a stock chart, place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "notcriticalErrorTitle": "Warning", "sCatDateAndTime": "Date and time", "sCatEngineering": "Engineering", "sCatFinancial": "Financial", "sCatInformation": "Information", "sCatLogical": "Logical", "sCatLookupAndReference": "Lookup and Reference", "sCatMathematic": "Math and trigonometry", "sCatStatistical": "Statistical", "sCatTextAndData": "Text and data", "textAddLink": "Add Link", "textAddress": "Address", "textBack": "Back", "textCancel": "Cancel", "textChart": "Chart", "textComment": "Comment", "textDisplay": "Display", "textEmptyImgUrl": "You need to specify the image URL.", "textExternalLink": "External Link", "textFilter": "Filter", "textFunction": "Function", "textGroups": "CATEGORIES", "textImage": "Image", "textImageURL": "Image URL", "textInsert": "Insert", "textInsertImage": "Insert Image", "textInternalDataRange": "Internal Data Range", "textInvalidRange": "ERROR! Invalid cells range", "textLink": "Link", "textLinkSettings": "<PERSON>s", "textLinkType": "Link Type", "textOther": "Other", "textPictureFromLibrary": "Picture from library", "textPictureFromURL": "Picture from URL", "textRange": "Range", "textRequired": "Required", "textScreenTip": "Screen Tip", "textShape": "<PERSON><PERSON><PERSON>", "textSheet": "Sheet", "textSortAndFilter": "Sort and Filter", "txtExpand": "Expand and sort", "txtExpandSort": "The data next to the selection will not be sorted. Do you want to expand the selection to include the adjacent data or continue with sorting the currently selected cells only?", "txtNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "txtSorting": "Sorting", "txtSortSelected": "Sort selected", "textSelectedRange": "Selected Range", "txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "txtNo": "No", "txtYes": "Yes", "textOk": "Ok", "textThisRowHint": "Choose only this row of the specified column", "textAllTableHint": "Returns the entire contents of the table or specified table columns including column headers, data and total rows", "textDataTableHint": "Returns the data cells of the table or specified table columns", "textHeadersTableHint": "Returns the column headers for the table or specified table columns", "textTotalsTableHint": "Returns the total rows for the table or specified table columns", "textDone": "Done", "textRecommended": "Recommended"}, "Edit": {"notcriticalErrorTitle": "Warning", "textAccounting": "Accounting", "textActualSize": "Actual Size", "textAddCustomColor": "Add Custom Color", "textAddress": "Address", "textAlign": "Align", "textAlignBottom": "Align Bottom", "textAlignCenter": "Align Center", "textAlignLeft": "Align Left", "textAlignMiddle": "Align Middle", "textAlignRight": "Align Right", "textAlignTop": "Align Top", "textAllBorders": "All Borders", "textAngleClockwise": "<PERSON>le Clockwise", "textAngleCounterclockwise": "Angle Counterclockwise", "textAuto": "Auto", "textAxisCrosses": "Axis Crosses", "textAxisOptions": "Axis Options", "textAxisPosition": "Axis Position", "textAxisTitle": "Axis Title", "textBack": "Back", "textBetweenTickMarks": "Between Tick Marks", "textBillions": "Billions", "textBorder": "Border", "textBorderStyle": "Border Style", "textBottom": "Bottom", "textBottomBorder": "Bottom Border", "textBringToForeground": "Bring to Foreground", "textCell": "Cell", "textCellStyles": "Cell Styles", "textCenter": "Center", "textChart": "Chart", "textChartTitle": "Chart Title", "textClearFilter": "Clear Filter", "textColor": "Color", "textCross": "Cross", "textCrossesValue": "Crosses Value", "textCurrency": "<PERSON><PERSON><PERSON><PERSON>", "textCustomColor": "Custom Color", "textDataLabels": "Data Labels", "textDate": "Date", "textDefault": "Selected range", "textDeleteFilter": "Delete Filter", "textDesign": "Design", "textDiagonalDownBorder": "Diagonal Down Border", "textDiagonalUpBorder": "Diagonal Up Border", "textDisplay": "Display", "textDisplayUnits": "Display Units", "textDollar": "Dollar", "textEditLink": "Edit Link", "textEffects": "Effects", "textEmptyImgUrl": "You need to specify the image URL.", "textEmptyItem": "{Blanks}", "textErrorMsg": "You must choose at least one value", "textErrorTitle": "Warning", "textEuro": "Euro", "textExternalLink": "External Link", "textFill": "Fill", "textFillColor": "Fill Color", "textFilterOptions": "Filter Options", "textFit": "<PERSON><PERSON>", "textFonts": "Fonts", "textFormat": "Format", "textFraction": "Fraction", "textFromLibrary": "Picture from Library", "textFromURL": "Picture from URL", "textGeneral": "General", "textGridlines": "Gridlines", "textHigh": "High", "textHorizontal": "Horizontal", "textHorizontalAxis": "Horizontal Axis", "textHorizontalText": "Horizontal Text", "textHundredMil": "100 000 000", "textHundreds": "Hundreds", "textHundredThousands": "100 000", "textHyperlink": "Hyperlink", "textImage": "Image", "textImageURL": "Image URL", "textIn": "In", "textInnerBottom": "Inner Bottom", "textInnerTop": "Inner Top", "textInsideBorders": "Inside Borders", "textInsideHorizontalBorder": "Inside Horizontal Border", "textInsideVerticalBorder": "Inside Vertical Border", "textInteger": "Integer", "textInternalDataRange": "Internal Data Range", "textInvalidRange": "Invalid cells range", "textJustified": "Justified", "textLabelOptions": "Label Options", "textLabelPosition": "Label Position", "textLayout": "Layout", "textLeft": "Left", "textLeftBorder": "Left Border", "textLeftOverlay": "Left Overlay", "textLegend": "Legend", "textLink": "Link", "textLinkSettings": "<PERSON>s", "textLinkType": "Link Type", "textLow": "Low", "textMajor": "Major", "textMajorAndMinor": "Major And Minor", "textMajorType": "Major Type", "textMaximumValue": "Maximum Value", "textMedium": "Medium", "textMillions": "Millions", "textMinimumValue": "Minimum Value", "textMinor": "Minor", "textMinorType": "Minor Type", "textMoveBackward": "Move Backward", "textMoveForward": "Move Forward", "textNextToAxis": "Next to Axis", "textNoBorder": "No Border", "textNone": "None", "textNoOverlay": "No Overlay", "textNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "textNumber": "Number", "textOnTickMarks": "On Tick Marks", "textOpacity": "Opacity", "textOut": "Out", "textOuterTop": "Outer Top", "textOutsideBorders": "Outside Borders", "textOverlay": "Overlay", "textPercentage": "Percentage", "textPictureFromLibrary": "Picture from Library", "textPictureFromURL": "Picture from URL", "textPound": "Pound", "textPt": "pt", "textRange": "Range", "textRemoveChart": "Remove Chart", "textRemoveImage": "Remove Image", "textRemoveLink": "Remove Link", "textRemoveShape": "Remove <PERSON>", "textReorder": "Reorder", "textReplace": "Replace", "textReplaceImage": "Replace Image", "textRequired": "Required", "textRight": "Right", "textRightBorder": "Right Border", "textRightOverlay": "Right Overlay", "textRotated": "Rotated", "textRotateTextDown": "Rotate Text Down", "textRotateTextUp": "Rotate Text Up", "textRouble": "Rouble", "textScientific": "Scientific", "textScreenTip": "Screen Tip", "textSelectAll": "Select All", "textSelectObjectToEdit": "Select object to edit", "textSendToBackground": "Send to Background", "textSettings": "Settings", "textShape": "<PERSON><PERSON><PERSON>", "textSheet": "Sheet", "textSize": "Size", "textStyle": "Style", "textTenMillions": "10 000 000", "textTenThousands": "10 000", "textText": "Text", "textTextColor": "Text Color", "textTextFormat": "Text Format", "textTextOrientation": "Text Orientation", "textThick": "<PERSON><PERSON><PERSON>", "textThin": "Thin", "textThousands": "Thousands", "textTickOptions": "Tick Options", "textTime": "Time", "textTop": "Top", "textTopBorder": "Top Border", "textTrillions": "Trillions", "textType": "Type", "textValue": "Value", "textValuesInReverseOrder": "Values in Reverse Order", "textVertical": "Vertical", "textVerticalAxis": "Vertical Axis", "textVerticalText": "Vertical Text", "textWrapText": "Wrap Text", "textYen": "Yen", "txtNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "txtSortHigh2Low": "Sort Highest to Lowest", "txtSortLow2High": "So<PERSON>st to Highest", "textAutomatic": "Automatic", "textOk": "Ok", "textCellStyle": "Cell Style", "textArrange": "<PERSON><PERSON><PERSON>", "textCancel": "Cancel", "textChangeShape": "Change Shape", "textDeleteImage": "Delete Image", "textDeleteLink": "Delete Link", "textDone": "Done", "textRecommended": "Recommended"}, "Settings": {"advCSVOptions": "Choose CSV options", "advDRMEnterPassword": "Your password, please:", "advDRMOptions": "Protected File", "advDRMPassword": "Password", "closeButtonText": "Close File", "notcriticalErrorTitle": "Warning", "textAbout": "About", "textAddress": "Address", "textApplication": "Application", "textApplicationSettings": "Application Settings", "textAuthor": "Author", "textBack": "Back", "textBottom": "Bottom", "textByColumns": "By columns", "textByRows": "By rows", "textCancel": "Cancel", "textCentimeter": "Centimeter", "textCollaboration": "Collaboration", "textColorSchemes": "Color Schemes", "textComment": "Comment", "textCommentingDisplay": "Commenting Display", "textComments": "Comments", "textCreated": "Created", "textCustomSize": "Custom Size", "textDisableAll": "Disable All", "textDisableAllMacrosWithNotification": "Disable all macros with a notification", "textDisableAllMacrosWithoutNotification": "Disable all macros without a notification", "textDone": "Done", "textDownload": "Download", "textDownloadAs": "Download As", "textEmail": "Email", "textEnableAll": "Enable All", "textEnableAllMacrosWithoutNotification": "Enable all macros without a notification", "textFind": "Find", "textFindAndReplace": "Find and Replace", "textFindAndReplaceAll": "Find and Replace All", "textFormat": "Format", "textFormulaLanguage": "Formula Language", "textFormulas": "Formulas", "textHelp": "Help", "textHideGridlines": "Hide Gridlines", "textHideHeadings": "<PERSON><PERSON> Headings", "textHighlightRes": "Highlight results", "textInch": "Inch", "textLandscape": "Landscape", "textLastModified": "Last Modified", "textLastModifiedBy": "Last Modified By", "textLeft": "Left", "textLocation": "Location", "textLookIn": "Look In", "textMacrosSettings": "<PERSON><PERSON>", "textMargins": "<PERSON><PERSON>", "textMatchCase": "Match Case", "textMatchCell": "Match Cell", "textNoTextFound": "Text not found", "textOpenFile": "Enter a password to open the file", "textOrientation": "Orientation", "textOwner": "Owner", "textPoint": "Point", "textPortrait": "Portrait", "textPoweredBy": "Powered By", "textPrint": "Print", "textR1C1Style": "R1C1 Reference Style", "textRegionalSettings": "Regional Settings", "textReplace": "Replace", "textReplaceAll": "Replace All", "textResolvedComments": "Resolved Comments", "textRight": "Right", "textSearch": "Search", "textSearchBy": "Search", "textSearchIn": "Search In", "textSettings": "Settings", "textSheet": "Sheet", "textShowNotification": "Show Notification", "textSpreadsheetFormats": "Spreadsheet Formats", "textSpreadsheetInfo": "Spreadsheet Info", "textSpreadsheetSettings": "Spreadsheet Settings", "textSpreadsheetTitle": "Spreadsheet Title", "textSubject": "Subject", "textTel": "Tel", "textTitle": "Title", "textTop": "Top", "textUnitOfMeasurement": "Unit Of Measurement", "textUploaded": "Uploaded", "textValues": "Values", "textVersion": "Version", "textWorkbook": "Workbook", "txtDelimiter": "Delimiter", "txtEncoding": "Encoding", "txtIncorrectPwd": "Password is incorrect", "txtProtected": "Once you enter the password and open the file, the current password to the file will be reset", "txtScheme1": "Office", "txtScheme10": "Median", "txtScheme11": "Metro", "txtScheme12": "<PERSON><PERSON><PERSON>", "txtScheme13": "Opulent", "txtScheme14": "Oriel", "txtScheme15": "Origin", "txtScheme16": "Paper", "txtScheme17": "Solstice", "txtScheme18": "Technic", "txtScheme19": "Trek", "txtScheme2": "Grayscale", "txtScheme20": "Urban", "txtScheme21": "Verve", "txtScheme22": "New Office", "txtScheme3": "Apex", "txtScheme4": "Aspect", "txtScheme5": "Civic", "txtScheme6": "Concourse", "txtScheme7": "Equity", "txtScheme8": "Flow", "txtScheme9": "Foundry", "txtSpace": "Space", "txtTab": "Tab", "warnDownloadAs": "If you continue saving in this format all features except the text will be lost.<br>Are you sure you want to continue?", "textOk": "Ok", "textChooseCsvOptions": "Choose CSV Options", "textChooseDelimeter": "Choose <PERSON>", "textChooseEncoding": "Choose Encoding", "textDelimeter": "Delimiter", "textEncoding": "Encoding", "txtColon": "Colon", "txtComma": "Comma", "txtDownloadCsv": "Download CSV", "txtOk": "Ok", "txtSemicolon": "Semicolon", "textExample": "Example", "textDarkTheme": "Dark Theme", "textFeedback": "Feedback & Support", "textLeftToRight": "Left To Right", "textRightToLeft": "Right To Left", "textDirection": "Direction", "textRestartApplication": "Please restart the application for the changes to take effect", "txtEn": "English", "txtDe": "De<PERSON>ch", "txtRu": "Russian", "txtPl": "Polish", "txtEs": "Spanish", "txtFr": "French", "txtIt": "Italian", "txtBe": "Belarusian", "txtBg": "Bulgarian", "txtCa": "Catalan", "txtZh": "Chinese", "txtCs": "Czech", "txtDa": "Danish", "txtNl": "Dutch", "txtFi": "Finnish", "txtEl": "Greek", "txtHu": "Hungarian", "txtId": "Indonesian", "txtJa": "Japanese", "txtKo": "Korean", "txtLv": "Latvian", "txtLo": "Lao", "txtNb": "Norwegian", "txtPtlang": "Portuguese (Portugal)", "txtPtbr": "Portuguese (Brazil)", "txtRo": "Romanian", "txtSk": "Slovak", "txtSl": "Slovenian", "txtSv": "Swedish", "txtTr": "Turkish", "txtUk": "Ukrainian", "txtVi": "Vietnamese", "txtExampleEn": "SUM; MIN; MAX; COUNT", "txtExampleDe": "SUMME; MIN; MAX; ANZAHL", "txtExampleRu": "СУММ; МИН; МАКС; СЧЁТ", "txtExamplePl": "SUMA; MIN; MAX; ILE.LICZB", "txtExampleEs": "SUMA; MIN; MAX; CALCULAR", "txtExampleFr": "SOMME; MIN; MAX; NB", "txtExampleIt": "SOMMA; MIN; MAX; CONTA.NUMERI", "strFuncLocale": "Formula Language", "strFuncLocaleEx": "Example: SUM; MIN; MAX; COUNT", "txtExampleBe": "СУММ; МИН; МАКС; СЧЁТ", "txtExampleCa": "SUMA; MIN; MAX; COMPT", "txtExampleCs": "SUMA; MIN; MAX; POČET", "txtExampleDa": "SUM; MIN; MAKS; TÆL", "txtExampleNl": "SOM; MIN; MAX; AANTAL", "txtExampleFi": "SUMMA; MIN; MAKS; LASKE", "txtExampleHu": "SZUM; MIN; MAX; DARAB", "txtExampleNb": "SUMMER; MIN; STØRST; ANTALL", "txtExamplePt": "SOMA; MÍNIMO; MÁXIMO; CONTAR", "txtExamplePtbr": "SOMA; MÍNIMO; MÁXIMO; CONT.NÚM", "txtExampleSv": "SUMMA; MIN; MAX; ANTAL", "txtExampleTr": "TOPLA; MİN; MAK; BAĞ_DEĞ_SAY"}}}
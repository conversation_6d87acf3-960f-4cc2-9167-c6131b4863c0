{"DATE": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>)", "d": "Gibt die Zahl zurück, die das Datum im Datums-/Uhrzeitcode darstellt"}, "DATEDIF": {"a": "(Ausgangsdatum;Enddatum;Einheit)", "d": "Berechnet der Differenz zwischen zwei Datumsangaben (Start- und Enddatum), basierend auf der angegebenen Einheit"}, "DATEVALUE": {"a": "(Datumstext)", "d": "Wandelt ein als Text vorliegendes Datum in eine Zahl um, die das Datum im Datums-/Uhrzeitcode darstellt"}, "DAY": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine fortlaufende Zahl in eine Zahl von 1 bis 31 für den Tag des Monats um"}, "DAYS": {"a": "(Zieldatum; Ausgangsdatum)", "d": "Gibt die Anzahl der Tage zwischen den beiden Datumswerten zurück."}, "DAYS360": {"a": "(Ausgangsdatum; Enddatum; [Methode])", "d": "<PERSON><PERSON><PERSON><PERSON>, aus<PERSON><PERSON> von <PERSON>, das 360 (12 Monate mit je 30 Tagen)<PERSON><PERSON> umfasst, die Anzahl der zwischen zwei Tagesdaten liegenden Tage"}, "EDATE": {"a": "(Ausgangsdatum; Monate)", "d": "Gibt die fortlaufende Zahl des Datums zurück, das eine bestimmte Anzahl von <PERSON>ten vor bzw. nach dem Ausgangsdatum liegt"}, "EOMONTH": {"a": "(Ausgangsdatum; Monate)", "d": "Gibt die fortlaufende Zahl des letzten Tags des Monats vor oder nach einer bestimmten Anzahl von Monaten zurück."}, "HOUR": {"a": "(<PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON> den Wert für die Stunde von 0 (00:00:00 Uhr) bis 23 (23:00:00 Uhr) zurück."}, "ISOWEEKNUM": {"a": "(Datum)", "d": "Gibt die ISO-Wochennummer des Jahres für ein angegebenes Datum zurück"}, "MINUTE": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine fortlaufende Zahl in einen Wert zwischen 0 und 59 für Minuten um"}, "MONTH": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t eine Zahl von 1 (<PERSON><PERSON><PERSON>) bis 12 (Dezember) für den Monat zurück."}, "NETWORKDAYS": {"a": "(Ausgangsdatum; Enddatum; [<PERSON><PERSON><PERSON>_Tage])", "d": "Gibt die Anzahl der Arbeitstage in einem Zeitintervall zurück"}, "NETWORKDAYS.INTL": {"a": "(Ausgangsdatum; Enddatum; [<PERSON><PERSON><PERSON><PERSON>]; [<PERSON><PERSON><PERSON>_Tage])", "d": "Gibt die Anzahl der vollständigen Arbeitstage zwischen zwei Daten mit benutzerdefinierten Wochenendparametern zurück"}, "NOW": {"a": "()", "d": "Gibt die fortlaufende Zahl des aktuellen Datums und der aktuellen Uhrzeit zurück."}, "SECOND": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine fortlaufende Zahl in einen Wert von 0 bis 59 für die Sekunde um."}, "TIME": {"a": "(Stunde; Minute; Sekunde)", "d": "Gibt die fortlaufende Zahl einer bestimmten Uhrzeit zurück"}, "TIMEVALUE": {"a": "(Zeit)", "d": "Wandelt eine als Text vorliegende Zeitangabe in eine fortlaufende Zahl von 0 (00:00:00) bis 0.999988426 (23:59:59) um. Formatieren Sie die Zahl nach Eingabe der Formel mit einem Zeitformat."}, "TODAY": {"a": "()", "d": "Gibt die fortlaufende Zahl des heutigen Datums zurück."}, "WEEKDAY": {"a": "(<PERSON><PERSON>; [Typ])", "d": "Wandelt eine fortlaufende Zahl in einen Wochentag um."}, "WEEKNUM": {"a": "(<PERSON><PERSON><PERSON><PERSON>_Zahl; [<PERSON><PERSON>_<PERSON><PERSON>])", "d": "Gibt die Wochennummer für das Jahr zurück"}, "WORKDAY": {"a": "(Ausgangsdatum; Tage; [<PERSON><PERSON><PERSON>_Tage])", "d": "Gibt die fortlaufende Zahl des Datums zurück, vor oder nach einer bestimmten Anzahl von Arbeitstagen"}, "WORKDAY.INTL": {"a": "(Ausgangsdatum; Tage; [<PERSON><PERSON><PERSON>nde]; [<PERSON><PERSON><PERSON>_Tage])", "d": "Gibt die fortlaufende Nummer des Datums vor oder nach einer angegebenen Anzahl von Arbeitstagen mit benutzerdefinierten Wochenendparametern zurück"}, "YEAR": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine fortlaufende Zahl im Bereich von 1900 - 9999 in eine Jahreszahl um."}, "YEARFRAC": {"a": "(Ausgangsdatum; Enddatum; [Basis])", "d": "Wandelt die Anzahl der ganzen Tage zwischen Ausgangsdatum und Enddatum in Bruchteile von Jahren um"}, "BESSELI": {"a": "(x; n)", "d": "Gibt die geänderte Besselfunktion In(x) zurück"}, "BESSELJ": {"a": "(x; n)", "d": "Gibt die Besselfunktion Jn(x) zurück"}, "BESSELK": {"a": "(x; n)", "d": "Gibt die modifizierte Besselfunktion Kn(x) zurück"}, "BESSELY": {"a": "(x; n)", "d": "Gibt die Besselfunktion Yn(x) zurück"}, "BIN2DEC": {"a": "(<PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON> eine binäre <PERSON> (Dualzahl) in eine dezimale Zahl um"}, "BIN2HEX": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> eine binä<PERSON> (Dualzahl) in eine hexadezimale <PERSON> um"}, "BIN2OCT": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON> eine binäre <PERSON> (Dualzahl) in eine oktale Zahl um"}, "BITAND": {"a": "(<PERSON><PERSON>1; <PERSON><PERSON>2)", "d": "Gibt ein bitweises 'Und' zweier Zahlen zurück"}, "BITLSHIFT": {"a": "(Zahl; Verschiebebetrag)", "d": "Gibt eine Zahl zurück, der um Verschiebebetrag Bits nach links verschoben ist"}, "BITOR": {"a": "(<PERSON><PERSON>1; <PERSON><PERSON>2)", "d": "Gibt ein bitweises 'Oder' zweier Zahlen zurück"}, "BITRSHIFT": {"a": "(Zahl; Verschiebebetrag)", "d": "Gibt eine Zahl zurück, die um Verschiebebetrag Bits nach rechts verschoben ist"}, "BITXOR": {"a": "(<PERSON><PERSON>1; <PERSON><PERSON>2)", "d": "Gibt ein bitweises 'Ausschließliches Oder' zweier Zahlen zurück"}, "COMPLEX": {"a": "(Realteil; Imaginärteil; [Suffix])", "d": "Wandelt den Real- und Imaginärteil in eine komplexe Zahl um"}, "CONVERT": {"a": "(<PERSON><PERSON>; Von_Maßeinheit; In_Maßeinheit)", "d": "Wandelt eine Zahl von einem Maßsystem in ein anderes um"}, "DEC2BIN": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine dezimale Zahl in eine binäre Zahl (Dualzahl) um"}, "DEC2HEX": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine dezimale Zahl in eine hexadezimale Zahl um"}, "DEC2OCT": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine dezimale Zahl in eine oktale Zahl um"}, "DELTA": {"a": "(<PERSON>ahl1; [<PERSON>ahl2])", "d": "Überprü<PERSON>, ob zwei Werte gleich sind"}, "ERF": {"a": "(<PERSON><PERSON><PERSON>_Grenze; [<PERSON><PERSON><PERSON>_<PERSON><PERSON>])", "d": "Gibt die Gauss'sche Fehlerfunktion zurück"}, "ERF.PRECISE": {"a": "(X)", "d": "Gibt die Gauss'sche Fehlerfunktion zurück"}, "ERFC": {"a": "(Untere_Grenze)", "d": "Gibt das Komplement zur Gauss'schen Fehlerfunktion zurück"}, "ERFC.PRECISE": {"a": "(X)", "d": "Gibt das Komplement zur Gauss'schen Fehlerfunktion zurück"}, "GESTEP": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ob eine Zahl größer als ein gegebener Schwellenwert ist"}, "HEX2BIN": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON>t eine hexadezimale Zahl in eine Binärzahl um"}, "HEX2DEC": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine hexadezimale <PERSON> in eine dezimale Zahl um"}, "HEX2OCT": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine hexadezimale Zahl in eine oktale Zahl um"}, "IMABS": {"a": "(Komplexe_Zahl)", "d": "Gibt den Absolutwert einer komplexen Zahl zurück"}, "IMAGINARY": {"a": "(Komplexe_Zahl)", "d": "Gibt den Imaginärteil einer komplexen Zahl zurück"}, "IMARGUMENT": {"a": "(Komplexe_Zahl)", "d": "G<PERSON>t den Winkel im Bogenmaß zur Darstellung der komplexen Zahl in trigonometrischer Schreibweise zurück"}, "IMCONJUGATE": {"a": "(Komplexe_Zahl)", "d": "Gibt die konjugiert komplexe Zahl zu einer komplexen Zahl zurück"}, "IMCOS": {"a": "(Komplexe_Zahl)", "d": "G<PERSON>t den Kosinus einer komplexen Zahl zurück"}, "IMCOSH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Kosinus einer komplexen Zahl zurück"}, "IMCOT": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Kotangens einer komplexen Zahl zurück"}, "IMCSC": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Kosekans einer komplexen Zahl zurück"}, "IMCSCH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Kosekans einer komplexen Zahl zurück"}, "IMDIV": {"a": "(Komplexe_Zahl1; Komplexe_Zahl2)", "d": "Gibt den Quotient zweier komplexer Zahlen zurück"}, "IMEXP": {"a": "(Komplexe_Zahl)", "d": "Gibt die algebraische Form einer in exponentieller Schreibweise vorliegenden komplexen Zahl zurück"}, "IMLN": {"a": "(Komplexe_Zahl)", "d": "Gibt den natürlichen Logarithmus einer komplexen Zahl zurück"}, "IMLOG10": {"a": "(Komplexe_Zahl)", "d": "Gibt den Logarithmus einer komplexen Zahl zur Basis 10 zurück"}, "IMLOG2": {"a": "(Komplexe_Zahl)", "d": "Gibt den Logarithmus einer komplexen Zahl zur Basis 2 zurück"}, "IMPOWER": {"a": "(Komplexe_Zahl; Potenz)", "d": "Potenziert eine komplexe Zahl mit einer ganzen Zahl"}, "IMPRODUCT": {"a": "(Komplexe_Zahl1; [Komplexe_Zahl2]; ...)", "d": "Gibt das Produkt von 1 bis 255 komplexen Zahlen zurück"}, "IMREAL": {"a": "(Komplexe_Zahl)", "d": "Gibt den Realteil einer komplexen Zahl zurück"}, "IMSEC": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Sekans einer komplexen Zahl zurück"}, "IMSECH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Sekans einer komplexen Zahl zurück"}, "IMSIN": {"a": "(Komplexe_Zahl)", "d": "G<PERSON>t den Sinus einer komplexen Zahl zurück"}, "IMSINH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Sinus einer komplexen Zahl zurück"}, "IMSQRT": {"a": "(Komplexe_Zahl)", "d": "Gibt die Quadratwurzel einer komplexen Zahl zurück"}, "IMSUB": {"a": "(Komplexe_Zahl1; Komplexe_Zahl2)", "d": "Gibt die Differenz zweier komplexer Zahlen zurück"}, "IMSUM": {"a": "(Komplexe_Zahl1; [Komplexe_Zahl2]; ...)", "d": "Gibt die Summe von komplexen Zahlen zurück"}, "IMTAN": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Tangens einer komplexen Zahl zurück"}, "OCT2BIN": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine oktale Zahl in eine binäre Zahl (Dualzahl) um"}, "OCT2DEC": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine oktale Zahl in eine dezimale Zahl um"}, "OCT2HEX": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine oktale Zahl in eine hexadezimale Zahl um"}, "DAVERAGE": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Gibt den Mittelwert aus den ausgewählten Datenbankeinträgen zurück"}, "DCOUNT": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Zä<PERSON><PERSON> die Zellen in einem Feld (Spalte) einer Datenbank, deren Inhalte mit den Suchkriterien übereinstimmen"}, "DCOUNTA": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Zä<PERSON>t die Zellen einer Datenbank, deren Inhalte mit den Suchkriterien übereinstimmen und die nicht leer sind"}, "DGET": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "G<PERSON>t den Datensatz in einer Datenbank zurück, der mit den angegebenen Suchkriterien übereinstimmt"}, "DMAX": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Gibt den größten Wert aus den ausgewählten Datenbankeinträgen zurück"}, "DMIN": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Gibt den kleinsten Wert aus den ausgewählten Datenbankeinträgen zurück"}, "DPRODUCT": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Multipliziert die Werte eines bestimmten Felds der Datensätze, die innerhalb einer Datenbank mit den Suchkriterien übereinstimmen"}, "DSTDEV": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Schätzt die Standardabweichung, ausge<PERSON> von einer Stichprobe aus bestimmten Datenbankeinträgen"}, "DSTDEVP": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Berechnet die Standardabweichung, ausgehend von der Grundgesamtheit aus bestimmten Datenbankeinträgen"}, "DSUM": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "<PERSON><PERSON><PERSON><PERSON>, die in einer Datenbank abgelegt sind"}, "DVAR": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Schät<PERSON><PERSON> die Varianz, ausge<PERSON> von einer Stichprobe aus bestimmten Datenbankeinträgen"}, "DVARP": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Berech<PERSON> die Varianz, ausgehend von der Grundgesamtheit aus bestimmten Datenbankeinträgen"}, "CHAR": {"a": "(<PERSON><PERSON>)", "d": "Gibt das der Codezahl entsprechende Zeichen zurück"}, "CLEAN": {"a": "(Text)", "d": "Lö<PERSON>t alle nicht druckbaren Zeichen aus einem Text"}, "CODE": {"a": "(Text)", "d": "Gibt die Codezahl des ersten Zeichens in einem Text zurück (abhä<PERSON><PERSON> von Zeichensatz, der auf Ihrem Computer eingestellt ist)"}, "CONCATENATE": {"a": "(Text1; [Text2]; ...)", "d": "Verknüpft mehrere Zeichenfolgen zu einer Zeichenfolge"}, "CONCAT": {"a": "(Text1; ...)", "d": "Verkettet eine Liste oder einen Bereich aus Textzeichenfolgen."}, "DOLLAR": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> eine Zahl in einen Text im Währungsformat um"}, "EXACT": {"a": "(Text1; Text2)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob zwei Zeichenfolgen identisch sind und gibt WAHR oder FALSCH zurück. IDENTISCH unterscheidet Groß- und Kleinschreibung"}, "FIND": {"a": "(Suchtext; Text; [<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Sucht eine Zeichenfolge innerhalb einer anderen (Groß-/Kleinschreibung wird beachtet)"}, "FINDB": {"a": "(Suchtext;Text;[<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Sucht eine Zeichenfolge (Suchtext) innerhalb einer anderen (Text) und gibt die Position der gesuchten Zeichenfolge ab dem ersten Zeichen der anderen Zeichenfolge an, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc."}, "FIXED": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>]; [<PERSON><PERSON>_<PERSON><PERSON>])", "d": "Formatiert eine Zahl als Text mit einer festen Anzahl an Nachkommastellen"}, "LEFT": {"a": "(Text; [<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Gibt das erste oder die ersten Zeichen einer Zeichenfolge zurück"}, "LEFTB": {"a": "(Text;[<PERSON><PERSON><PERSON>_<PERSON><PERSON>])", "d": "Gibt auf der Grundlage der Anzahl von Bytes, die Si<PERSON> angeben, das oder die erste(n) Zeichen in einer Textzeichenfolge zurück, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc."}, "LEN": {"a": "(Text)", "d": "Gibt die Anzahl der Zeichen einer Zeichenfolge zurück"}, "LENB": {"a": "(Text)", "d": "Gibt die Anzahl von Bytes zurück, die zum Darstellen der Zeichen in einer Zeichenfolge verwendet werden, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc."}, "LOWER": {"a": "(Text)", "d": "Wandelt einen Text in Kleinbuchstaben um"}, "MID": {"a": "(Text; <PERSON><PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>_<PERSON>ei<PERSON>)", "d": "Gibt eine bestimmte Anzahl Zeichen einer Zeichenfolge ab der von Ihnen bestimmten Stelle zurück"}, "MIDB": {"a": "(Text;<PERSON><PERSON><PERSON>_<PERSON>;<PERSON><PERSON><PERSON>_Byte)", "d": "Gibt auf der Grundlage der angegebenen Anzahl von Bytes eine bestimmte Anzahl von Zeichen einer Zeichenfolge ab der von Ihnen angegebenen Position zurück, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc."}, "NUMBERVALUE": {"a": "(Text; [Dezimaltrennzeichen]; [Gruppentrennzeichen])", "d": "Konvertiert auf vom Gebietsschema unabhängige Weise Text in Zahlen"}, "PROPER": {"a": "(Text)", "d": "Wandelt eine Textzeichenfolge in die geeignete Groß-/Kleinschreibung um: den ersten Buchstaben jedes Worts in Großbuchstaben, alle anderen Buchstaben in Kleinbuchstaben"}, "REPLACE": {"a": "(<PERSON><PERSON>_Text; <PERSON><PERSON><PERSON>_<PERSON>ei<PERSON>; <PERSON><PERSON><PERSON>_Zeichen; Neuer_Text)", "d": "Ersetzt eine bestimmte Anzahl Zeichen ab einer bestimmten Stelle innerhalb eines Textes"}, "REPLACEB": {"a": "(<PERSON><PERSON>_Text;<PERSON><PERSON><PERSON>_<PERSON>;<PERSON><PERSON><PERSON>_Bytes;Neuer_Text)", "d": "Ersetzt eine Zeichenfolge, basierend auf der Anzahl der Zeichen und der angegebenen Startposition, durch eine neue Zeichengruppe, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc."}, "REPT": {"a": "(Text; Multiplikator)", "d": "<PERSON><PERSON><PERSON><PERSON>t einen Text so oft wie angegeben"}, "RIGHT": {"a": "(Text; [<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Gibt das letzte oder die letzten Zeichen einer Zeichenfolge zurück"}, "RIGHTB": {"a": "(Text;[<PERSON><PERSON><PERSON>_<PERSON><PERSON>])", "d": "Gibt auf der Grundlage der angegebenen Anzahl von Bytes das bzw. die letzten Zeichen einer Zeichenfolge zurück, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc."}, "SEARCH": {"a": "(Suchtext; Text; [<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Sucht eine Zeichenfolge innerhalb einer anderen (Groß-/Kleinschreibung wird nicht beachtet)"}, "SEARCHB": {"a": "(Suchtext;Text;[<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Gibt die Position der angegebenen Teilzeichenfolge in einer Zeichenfolge zurück, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc."}, "SUBSTITUTE": {"a": "(Text; Alter_Text; Neuer_Text; [ntes_Auftreten])", "d": "Tauscht einen alten Text durch einen neuen Text in einer Zeichenfolge aus"}, "T": {"a": "(Wert)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob ein Wert ein Text ist, und gibt den Text zurück, falls dies zutrifft. Wenn der Wert kein Text ist, werden doppelte Anführungsstriche (leerer Text) zurückgegeben."}, "TEXT": {"a": "(Wert; Textformat)", "d": "Formatiert eine Zahl und wandelt sie in einen Text um"}, "TEXTJOIN": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>_<PERSON>; Text1; ...)", "d": "Verkettet eine Liste oder einen Bereich aus Textzeichenfolgen unter Verwendung eines Trennzeichens."}, "TRIM": {"a": "(Text)", "d": "<PERSON><PERSON>scht Leerzeichen in einem Text"}, "UNICHAR": {"a": "(<PERSON><PERSON>)", "d": "Gibt das Unicode-Zeichen zurück, das durch den angegebenen Zahlenwert bezeichnet wird"}, "UNICODE": {"a": "(Text)", "d": "<PERSON><PERSON><PERSON> die Zahl (Codepoint) zurück, die dem ersten Zeichen des Texts entspricht"}, "UPPER": {"a": "(Text)", "d": "Wandelt einen Text in Großbuchstaben um"}, "VALUE": {"a": "(Text)", "d": "Wandelt ein als Text angegebenes Argument in eine Zahl um"}, "AVEDEV": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die durchschnittliche absolute Abweichung von Datenpunkten von ihrem Mittelwert zurück. Die Argumente können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein, die Zahlen enthalten"}, "AVERAGE": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "G<PERSON>t den Mittelwert (arithmetisches Mittel) der Argumente zurück, bei denen es sich um Zahlen oder Namen, Arrays oder Bezüge handeln kann, die Zahlen enthalten"}, "AVERAGEA": {"a": "(Wert1; [Wert2]; ...)", "d": "Gibt den Mittelwert der Argumente zurück. Dabei werden Text und FALSCH als 0 interpretiert, WAHR wird als 1 interpretiert. Argumente können <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein."}, "AVERAGEIF": {"a": "(Bereich; Kriterien; [<PERSON><PERSON><PERSON><PERSON><PERSON>_Bereich])", "d": "Sucht den Mittelwert (arithmetisches Mittel) für die von einer bestimmten Bedingung oder bestimmten Kriterien festgelegten Zellen"}, "AVERAGEIFS": {"a": "(Mittelwert_Bereich; Kriterien_Bereich; Kriterien; ...)", "d": "Sucht den Mittelwert (arithmetisches Mittel) für die Zellen, die durch bestimmte Bedingungen oder Kriterien angegeben sind"}, "BETADIST": {"a": "(x; Alpha; Beta; [A]; [B])", "d": "Gibt Werte der Verteilungsfunktion einer betaverteilten Zufallsvariablen zurück"}, "BETAINV": {"a": "(<PERSON><PERSON><PERSON>; Alpha; Beta; [A]; [B])", "d": "Gibt Perzentile der Betaverteilung zurück"}, "BETA.DIST": {"a": "(x; Alpha; Beta; kumuliert; [A]; [B])", "d": "Gibt Werte der Verteilungsfunktion einer betaverteilten Zufallsvariablen zurück"}, "BETA.INV": {"a": "(<PERSON><PERSON><PERSON>; Alpha; Beta; [A]; [B])", "d": "Gibt Perzentile der Betaverteilung (BETA.VERT) zurück"}, "BINOMDIST": {"a": "(Zahl_Erfolge; Versuche; Erfolgswahrsch; Kumuliert)", "d": "G<PERSON>t Wahrscheinlichkeiten einer binomialverteilten Zufallsvariablen zurück."}, "BINOM.DIST": {"a": "(Zahl_Erfolge; Versuche; Erfolgswahrsch; Kumuliert)", "d": "G<PERSON>t Wahrscheinlichkeiten einer binomialverteilten Zufallsvariablen zurück."}, "BINOM.DIST.RANGE": {"a": "(Versuche; Erfolgswahrscheinlichkeit; Zahl_Erfolge; [Zahl2_Erfolge])", "d": "Gibt die Erfolgswahrscheinlichkeit eines Versuchsergebnisses als Binomialverteilung zurück"}, "BINOM.INV": {"a": "(V<PERSON><PERSON>; Erfolgswahrsch; Alpha)", "d": "Gibt den kleinsten Wert zurück, für den die kumulierten Wahrscheinlichkeiten der Binomialverteilung größer oder gleich einer Grenzwahrscheinlichkeit sind"}, "CHIDIST": {"a": "(x; Freiheits<PERSON>)", "d": "Gibt Werte der Verteilungsfunktion (1-Alpha) einer Chi-Quadrat-verteilten Zufallsgröße zurück."}, "CHIINV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "G<PERSON>t Perzentile der Chi-Quadrat-Verteilung zurück, eine Zahl von einschließlich 0 bis einschließlich 1"}, "CHITEST": {"a": "(<PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>_<PERSON>)", "d": "Gibt die Teststatistik eines Chi-Quadrat-Unabhängigkeitstests zurück"}, "CHISQ.DIST": {"a": "(x; <PERSON><PERSON>heits<PERSON>; kumuli<PERSON>)", "d": "Gibt Werte der linksseitigen Verteilungsfunktion (1-Alpha) einer Chi-Quadrat-verteilten Zufallsgröße zurück."}, "CHISQ.DIST.RT": {"a": "(x; Freiheits<PERSON>)", "d": "Gibt Werte der rechtsseitigen Verteilungsfunktion (1-Alpha) einer Chi-Quadrat-verteilten Zufallsgröße zurück."}, "CHISQ.INV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "Gibt Perzentile der linksseitigen Chi-Quadrat-Verteilung zurück, eine Zahl von einschließlich 0 bis einschließlich 1"}, "CHISQ.INV.RT": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "Gibt Perzentile der rechtsseitigen Chi-Quadrat-Verteilung zurück, eine Zahl von einschließlich 0 bis einschließlich 1"}, "CHISQ.TEST": {"a": "(<PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>_<PERSON>)", "d": "Gibt die Teststatistik eines Chi-Quadrat-Unabhängigkeitstests zurück"}, "CONFIDENCE": {"a": "(Alpha; Standabwn; Umfang_S)", "d": "Ermöglicht die Berechnung des 1-Alpha Konfidenzintervalls für den Erwartungswert einer Zufallsvariablen und verwendet dazu die Normalverteilung"}, "CONFIDENCE.NORM": {"a": "(Alpha; Standardabwn; Umfang)", "d": "Gibt das Konfidenzintervall für den Erwartungswert einer Zufallsvariablen zurück"}, "CONFIDENCE.T": {"a": "(Alpha; Standardabwn; Umfang)", "d": "Gibt das Konfidenzintervall für den Erwartungswert einer (Student) t-verteilten Zufallsvariablen zurück"}, "CORREL": {"a": "(Matrix1; Matrix2)", "d": "Gibt den Korrelationskoeffizient zweier Reihen von Merkmalsausprägungen zurück."}, "COUNT": {"a": "(Wert1; [Wert2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, wie viele Zellen in einem Bereich Zahlen enthalten"}, "COUNTA": {"a": "(Wert1; [Wert2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> die Anzahl nicht leerer Zellen in einem Bereich"}, "COUNTBLANK": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON>t die leeren Zellen in einem Zellbereich"}, "COUNTIF": {"a": "(Bereich; Suchkriterien)", "d": "<PERSON><PERSON><PERSON>t die nichtleeren Zellen eines Bereichs, deren Inhalte mit dem Suchkriterium übereinstimmen"}, "COUNTIFS": {"a": "(Kriterienberei<PERSON>; Kriterien; ...)", "d": "<PERSON><PERSON><PERSON>t die Anzahl der Zellen, die durch eine bestimmte Menge von Bedingungen oder Kriterien festgelegt ist"}, "COVAR": {"a": "(Matrix1; Matrix2)", "d": "Gibt die Kovarianz, den Mittelwert der für alle Datenpunktpaare gebildeten Produkte der Abweichungen zurück."}, "COVARIANCE.P": {"a": "(Array1; Array2)", "d": "Gibt die Kovarianz einer Grundgesamtheit, den Mittelwert der für alle Datenpunktpaare gebildeten Produkte der Abweichungen zurück."}, "COVARIANCE.S": {"a": "(Array1; Array2)", "d": "Gibt die Kovarianz einer Stichprobe, den Mittelwert der für alle Datenpunktpaare gebildeten Produkte der Abweichungen zurück."}, "CRITBINOM": {"a": "(V<PERSON><PERSON>; Erfolgswahrsch; Alpha)", "d": "G<PERSON><PERSON> den kleinsten Wert, für den die kumulierten Wahrscheinlichkeiten der Binomialverteilung größer oder gleich einer Grenzwahrscheinlichkeit sind zurück"}, "DEVSQ": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die Summe der quadrierten Abweichungen der Datenpunkte von ihrem Stichprobenmittelwert zurück."}, "EXPONDIST": {"a": "(x; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer exponentialverteilten Zufallsvariablen zurück"}, "EXPON.DIST": {"a": "(x; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer exponentialverteilten Zufallsvariablen zurück"}, "FDIST": {"a": "(x; Freiheitsgrade1; Freiheitsgrade2)", "d": "Gibt Werte der Verteilungsfunktion (1-Alpha) einer rechtsseitigen F-verteilten Zufallsvariablen zurück"}, "FINV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade1; Freiheitsgrade2)", "d": "Gibt Perzentile der rechtsseitigen F-Verteilung zurück"}, "FTEST": {"a": "(Matrix1; Matrix2)", "d": "Gibt die Teststatistik eines F-Tests zurück, die zweiseitige Wahrscheinlichkeit darstellt, dass sich die Varianzen in Matrix1 und Matrix2 nicht signifikant unterscheiden"}, "F.DIST": {"a": "(x; Freiheitsgrade1; Freiheitsgrade2; kumuli<PERSON>)", "d": "Gibt Werte der Verteilungsfunktion (1-Alpha) einer (linksseitigen) F-verteilten Zufallsvariablen zurück"}, "F.DIST.RT": {"a": "(x; Freiheitsgrade1; Freiheitsgrade2)", "d": "Gibt Werte der Verteilungsfunktion (1-Alpha) einer (rechtsseitigen) F-verteilten Zufallsvariablen zurück"}, "F.INV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade1; Freiheitsgrade2)", "d": "Gibt Perzentile der (linksseitigen) F-Verteilung zurück: wenn p = F.VERT(x,...), dann FINV(p,...) = x"}, "F.INV.RT": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade1; Freiheitsgrade2)", "d": "Gibt Perzentile der (rechtsseitigen) F-Verteilung zurück: wenn p = F.VERT.RE(x,...), dann F.INV.RE(p,...) = x"}, "F.TEST": {"a": "(Matrix1; Matrix2)", "d": "Gibt die Teststatistik eines F-Tests zurück, die zweiseitige Wahrscheinlichkeit darstellt, dass sich die Varianzen in Matrix1 und Matrix2 nicht signifikant unterscheiden"}, "FISHER": {"a": "(x)", "d": "Gibt die Fisher-Transformation zurück"}, "FISHERINV": {"a": "(y)", "d": "Gibt die Umkehrung der Fisher-Transformation zurück"}, "FORECAST": {"a": "(x; <PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON>rte)", "d": "Berechnet oder prognostiziert einen Schätzwert für einen linearen Trend unter Verwendung vorhandener Werte"}, "FORECAST.ETS": {"a": "(<PERSON><PERSON>_<PERSON>; Werte; Zeitachse; [Saisonalität]; [Daten_Vollständigkeit]; [Aggregation])", "d": "G<PERSON><PERSON> den Schätzwert für ein bestimmtes Zieldatum zurück, wobei eine exponentielle Glättungsmethode verwendet wird."}, "FORECAST.ETS.CONFINT": {"a": "(<PERSON><PERSON>_<PERSON>; Werte; Zeitachse; [Konfidenz_Niveau]; [Saisonalität]; [Daten_Vollständigkeit]; [Aggregation])", "d": "Gibt ein Konfidenzintervall für den Schätzwert zum angegebenen Zieldatum zurück."}, "FORECAST.ETS.SEASONALITY": {"a": "(Werte; Zeitachse; [Daten_Vollständigkeit]; [Aggregation])", "d": "Gibt die Länge des Wiederholungsmusters zurück, das von eine Anwendung für die angegebene Zeitserie erkannt wird."}, "FORECAST.ETS.STAT": {"a": "(Werte; Zeitachse; Statistik_Typ; [Saisonalität]; [Daten_Vollständigkeit]; [Aggregation])", "d": "Gibt die angeforderte Statistik für die Vorhersage zurück."}, "FORECAST.LINEAR": {"a": "(x; <PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON>rte)", "d": "Berechnet oder prognostiziert einen Schätzwert für einen linearen Trend unter Verwendung vorhandener Werte"}, "FREQUENCY": {"a": "(Daten; Klassen)", "d": "Gibt eine Häufigkeitsverteilung als einspaltige Matrix zurück"}, "GAMMA": {"a": "(x)", "d": "Gibt den Wert der Gammafunktion zurück"}, "GAMMADIST": {"a": "(x; Alpha; Beta; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer gammaverteilten Zufallsvariablen zurück"}, "GAMMA.DIST": {"a": "(x; Alpha; Beta; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer gammaverteilten Zufallsvariablen zurück"}, "GAMMAINV": {"a": "(<PERSON><PERSON><PERSON>; Alpha; Beta)", "d": "Gibt Perzentile der Gammaverteilung zurück"}, "GAMMA.INV": {"a": "(<PERSON><PERSON><PERSON>; Alpha; Beta)", "d": "Gibt den Kehrwert der kumulierten Gammaverteilung zurück: wenn p = GAMMA.VERT(x,...), dann GAMMA.INV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Gibt den natürlichen Logarithmus der Gammafunktion zurück"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Gibt den natürlichen Logarithmus der Gammafunktion zurück"}, "GAUSS": {"a": "(x)", "d": "Gibt 0,5 weniger als die kumulierte Normalverteilung zurück"}, "GEOMEAN": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt das geometrische Mittel eines Arrays oder Bereichs von positiven numerischen Daten zurück."}, "GROWTH": {"a": "(<PERSON>_<PERSON><PERSON>; [<PERSON>_<PERSON><PERSON>]; [<PERSON><PERSON><PERSON>_x_Werte]; [<PERSON><PERSON><PERSON>])", "d": "G<PERSON>t Werte aus einem exponentiellen Wachstumstrend zurück, die bekannten Datenpunkten entsprechen"}, "HARMEAN": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt das harmonische Mittel eines Datensatzes mit positiven Zahlen zurück: den Umkehrwert des arithmetischen Mittels der Umkehrwerte"}, "HYPGEOM.DIST": {"a": "(Erfolge_S; Umfang_S; Erfolge_G; Umfang_G; kumuliert)", "d": "<PERSON><PERSON>t Wahrscheinlichkeiten einer hypergeometrisch-verteilten Zufallsvariablen zurück."}, "HYPGEOMDIST": {"a": "(Erfolge_S; Umfang_S; Erfolge_G; Umfang_G)", "d": "<PERSON><PERSON>t Wahrscheinlichkeiten einer hypergeometrisch-verteilten Zufallsvariablen zurück."}, "INTERCEPT": {"a": "(<PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON><PERSON>)", "d": "Berechnet den Schnittpunkt einer Geraden mit der Y-Achse unter Verwendung einer Ausgleichsregressionsgeraden, die durch die X- und Y-Werte gezeichnet wird"}, "KURT": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die Kurtosis (Exzess) einer Datengruppe zurück"}, "LARGE": {"a": "(Matrix; k)", "d": "Gibt den k-größten Wert einer Datengruppe zurück"}, "LINEST": {"a": "(Y_<PERSON>rte; [<PERSON>_<PERSON><PERSON>]; [<PERSON>nst<PERSON>]; [Stats])", "d": "Gibt statistische Werte zurück, die einen linearen Trend beschreiben, der bekannten Datenpunkten entspricht, indem eine Gerade mithilfe der Methode der kleinesten Quadrate ausgeglichen wird"}, "LOGEST": {"a": "(Y_<PERSON>rte; [<PERSON>_<PERSON><PERSON>]; [<PERSON>nst<PERSON>]; [Stats])", "d": "Gibt statistische Werte zurück, die eine exponentielle Kurve beschreiben, die bekannten Datenpunkten entspricht"}, "LOGINV": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Standab<PERSON>)", "d": "Gibt Perzentile der Lognormalverteilung zurück"}, "LOGNORM.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; kumuli<PERSON>)", "d": "Gibt Werte der Verteilungsfunktion einer lognormalverteilten Zufallsvariablen zurück, wobei ln(x) mit den Parametern Mittelwert und Standabwn normalverteilt ist."}, "LOGNORM.INV": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Standab<PERSON>)", "d": "Gibt Perzentile der Lognormalverteilung zurück"}, "LOGNORMDIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON>; Standabwn)", "d": "Gibt Werte der Verteilungsfunktion einer lognormalverteilten Zufallsvariablen zurück."}, "MAX": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den größten Wert innerhalb einer Wertemenge zurück. Logische Werte und Textwerte werden ignoriert"}, "MAXA": {"a": "(Wert1; [Wert2]; ...)", "d": "Gibt den größten Wert innerhalb einer Wertemenge zurück. Logische Werte und Text werden nicht ignoriert"}, "MAXIFS": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>_<PERSON>; Kriterien; ...)", "d": "Gibt den in Zellen vorhandenen Maximalwert zurück, der durch einen angegebenen Satz von Bedingungen oder Kriterien festgelegt ist."}, "MEDIAN": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den Median bzw. die Zahl in der Mitte der Menge von angegebenen Zahlen zurück"}, "MIN": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den kleinsten Wert innerhalb einer Wertemenge zurück. Logische Werte und Text werden ignoriert"}, "MINA": {"a": "(Wert1; [Wert2]; ...)", "d": "Gibt den kleinsten Wert innerhalb einer Wertemenge zurück. Logische Werte und Text werden nicht ignoriert"}, "MINIFS": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>_<PERSON>; Kriterien; ...)", "d": "Gibt den in Zellen vorhandenen Minimalwert zurück, der durch einen angegebenen Satz von Bedingungen oder Kriterien festgelegt ist."}, "MODE": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den häufigsten Wert in einem Array oder einer Datengruppe zurück"}, "MODE.MULT": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt ein vertikales Array der am häufigsten vorkommenden oder wiederholten Werte in einem Array oder einem Datenbereich zurück. Verwenden Sie für ein horizontales Array =MTRANS(MODUS.VIELF(Zahl1,Zahl2,...))"}, "MODE.SNGL": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den häufigsten Wert in einem Array oder einer Datengruppe zurück"}, "NEGBINOM.DIST": {"a": "(Zahl_Mißerfolge; Zahl_Erfolge; Erfolgswahrsch; kumuliert)", "d": "<PERSON><PERSON><PERSON> Wahrscheinlichkeiten einer negativen, binomial verteilten Zufallsvariablen zurück"}, "NEGBINOMDIST": {"a": "(Zahl_Mißerfolge; Zahl_Erfolge; Erfolgswahrsch)", "d": "<PERSON><PERSON><PERSON> Wahrscheinlichkeiten einer negativen, binomial verteilten Zufallsvariablen zurück"}, "NORM.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON>; Standab<PERSON>; <PERSON><PERSON><PERSON>t)", "d": "G<PERSON>t Wahrscheinlichkeiten einer normal verteilten Zufallsvariablen zurück."}, "NORMDIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON>; Standab<PERSON>; <PERSON><PERSON><PERSON>t)", "d": "G<PERSON>t Wahrscheinlichkeiten einer normal verteilten Zufallsvariablen zurück."}, "NORM.INV": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Standab<PERSON>)", "d": "Gibt Perzentile der Normalverteilung zurück"}, "NORMINV": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Standab<PERSON>)", "d": "Gibt Perzentile der Normalverteilung zurück"}, "NORM.S.DIST": {"a": "(z; kumuliert)", "d": "Gibt Werte der Verteilungsfunktion standardmäßigen, normal verteilten Zufallsvariablen zurück."}, "NORMSDIST": {"a": "(z)", "d": "Gibt Werte der Verteilungsfunktion standardmäßigen, normal verteilten Zufallsvariablen zurück."}, "NORM.S.INV": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Gibt Perzentile der Standardnormalverteilung zurück"}, "NORMSINV": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Gibt Perzentile der Standardnormalverteilung zurück"}, "PEARSON": {"a": "(Matrix1; Matrix2)", "d": "Gibt den Pearsonschen Korrelationskoeffizienten zurück"}, "PERCENTILE": {"a": "(Matrix; Alpha)", "d": "Gibt das Alphaquantil einer Gruppe von Daten zurück"}, "PERCENTILE.EXC": {"a": "(Array; k)", "d": "Gibt das k-Quantil von Werten in einem Bereich zurück, wobei k im Bereich von 0..1 ausschließlich liegt"}, "PERCENTILE.INC": {"a": "(Array; k)", "d": "Gibt das k-Quantil von Werten in einem Bereich zurück, wobei k im Bereich von 0..1 einsch<PERSON>ß<PERSON> liegt"}, "PERCENTRANK": {"a": "(Matrix; x; [Genauigke<PERSON>])", "d": "Gibt den prozentualen Rang (Alpha) eines Wertes zurück"}, "PERCENTRANK.EXC": {"a": "(<PERSON><PERSON>y; x; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Gibt den prozentualen Rang (Alpha) eines Werts in einem Dataset als Prozentsatz des Datasets (0..1 ausschließlich) zurück"}, "PERCENTRANK.INC": {"a": "(<PERSON><PERSON>y; x; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Gibt den prozentualen Rang (Alpha) eines Wertes in einem Dataset als Prozentsatz des Datasets (0..1 einschließlich) zurück"}, "PERMUT": {"a": "(n; k)", "d": "Gibt die Anzahl der Möglichkeiten zurück, um k Elemente aus einer Menge von n Elementen ohne Zurücklegen zu ziehen."}, "PERMUTATIONA": {"a": "(Zahl; gewählte_Zahl)", "d": "Gibt die Anzahl der Permutationen für eine angegebene Anzahl von Objekten zurück (mit Wiederholungen), die aus der Gesamtmenge der Objekte ausgewählt werden können"}, "PHI": {"a": "(x)", "d": "Gibt den Wert der Dichtefunktion für eine Standardnormalverteilung zurück"}, "POISSON": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>)", "d": "Gibt Wahrscheinlichkeiten einer Poisson-verteilten Zufallsvariablen zurück. (Die verwendete Gleichung ist in der Hilfe genauer beschrieben.)"}, "POISSON.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>)", "d": "Gibt Wahrscheinlichkeiten einer Poisson-verteilten Zufallsvariablen zurück. (Die verwendete Gleichung ist in der Hilfe genauer beschrieben.)"}, "PROB": {"a": "(<PERSON><PERSON>_<PERSON>; <PERSON><PERSON>_<PERSON>; <PERSON><PERSON>ze; [<PERSON><PERSON><PERSON><PERSON>])", "d": "Gibt die Wahrscheinlichkeit für ein von zwei Werten eingeschlossenes Intervall zurück"}, "QUARTILE": {"a": "(Matrix; Quartile)", "d": "Gibt die Quartile der Datengruppe zurück"}, "QUARTILE.INC": {"a": "(Array; Quartile)", "d": "Gibt die Quartile eines Datasets zurück, basierend auf Perzentilwerten von 0..1 einsch<PERSON><PERSON>lich"}, "QUARTILE.EXC": {"a": "(Array; Quartile)", "d": "Gibt die Quartile eines Datasets zurück, basierend auf Perzentilwerten von 0..1 ausschließlich"}, "RANK": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> den <PERSON>ng, den eine Zahl innerhalb einer Liste von Zahlen einnimmt zurück"}, "RANK.AVG": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> den <PERSON>ng, den eine Zahl innerhalb einer Liste von Zahlen einnimmt, zurück: die Größe relativ zu anderen Werten in der Liste; wenn mehrere Werte die gleiche Rangzahl aufweisen, wird die durchschnittliche Rangzahl zurückgegeben"}, "RANK.EQ": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> den Rang, den eine Zahl innerhalb einer Liste von Zahlen einnimmt, zurück: die Größe relativ zu anderen Werten in der Liste; wenn mehrere Werte die gleiche Rangzahl aufweisen, wird der oberste Rang dieser Gruppe von Werten zurückgegeben"}, "RSQ": {"a": "(<PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON><PERSON>)", "d": "Gibt das Quadrat des Pearsonschen Korrelationskoeffizienten durch die gegebenen Datenpunkte zurück"}, "SKEW": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die Schiefe einer Verteilung zurück: eine Charakterisierung des Assymmetriegrads einer Verteilung um seinen Mittelwert"}, "SKEW.P": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die Schiefe einer Verteilung auf der Basis einer Grundgesamtheit zurück: eine Charakterisierung des Asymmetriegrads einer Verteilung um ihren Mittelwert"}, "SLOPE": {"a": "(<PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON><PERSON>)", "d": "Gibt die Steigung der linearen Regressionsgeraden durch die gegebenen Datenpunkte zurück"}, "SMALL": {"a": "(Matrix; k)", "d": "Gibt den k-kleinsten Wert einer Datengruppe zurück"}, "STANDARDIZE": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON>; Standabwn)", "d": "Gibt den standardisierten Wert zurück."}, "STDEV": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Schätzt die Standardabweichung ausgehend von einer Stichprobe (logische Werte und Text werden im Beispiel ignoriert)"}, "STDEV.P": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Berechnet die Standardabweichung, ausgehend von der Grundgesamtheit angegeben als Argumente (logische Werte und Text werden ignoriert)"}, "STDEV.S": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Schätzt die Standardabweichung ausgehend von einer Stichprobe (logische Werte und Text werden im Beispiel ignoriert)"}, "STDEVA": {"a": "(Wert1; [Wert2]; ...)", "d": "Schätzt die Standardabweichung, ausge<PERSON> von einer Stichprobe, einschließlich logischer Werte und Text. Dabei werden Text und FALSCH als 0 interpretiert, WAHR wird als 1 interpretiert."}, "STDEVP": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Berechnet die Standardabweichung, ausgehend von der Grundgesamtheit angegeben als Argumente (logische Werte und Text werden ignoriert)"}, "STDEVPA": {"a": "(Wert1; [Wert2]; ...)", "d": "Berechnet die Standardabweichung ausgehend von der Grundgesamtheit, einschließlich logischer Werte und Text. Dabei werden Text und FALSCH als 0 interpretiert, WAHR wird als 1 interpretiert."}, "STEYX": {"a": "(<PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON><PERSON>)", "d": "Gibt den Standardfehler des vorhergesagten Y-Werts für alle x-Werte in einer Regression zurück"}, "TDIST": {"a": "(x; Freiheitsgrade; Seiten)", "d": "Gibt Werte der Verteilungsfunktion (1-Alpha) e<PERSON> (Student) t-verteilten Zufallsvariablen zurück"}, "TINV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "Gibt Perzentile der zweiseitigen t-Verteilung zurück"}, "T.DIST": {"a": "(x; <PERSON><PERSON>heits<PERSON>; kumuli<PERSON>)", "d": "<PERSON><PERSON><PERSON> die (Student) t-Verteilung der linken Endfläche zurück"}, "T.DIST.2T": {"a": "(x; Freiheits<PERSON>)", "d": "<PERSON><PERSON><PERSON> die (Student) t-Verteilung für zwei Endflächen zurück"}, "T.DIST.RT": {"a": "(x; Freiheits<PERSON>)", "d": "<PERSON><PERSON><PERSON> die (Student) t-Verteilung für die rechte Endfläche zurück"}, "T.INV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "Gibt linksseitige Quantile der (Student) t-Verteilung zurück"}, "T.INV.2T": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "Gibt zweiseitige Quantile der (Student) t-Verteilung zurück"}, "T.TEST": {"a": "(Matrix1; Matrix2; Seiten; Typ)", "d": "Gibt die Teststatistik eines Studentschen t-Tests zurück"}, "TREND": {"a": "(<PERSON>_<PERSON><PERSON>; [<PERSON>_<PERSON><PERSON>]; [<PERSON><PERSON><PERSON>_x_Werte]; [<PERSON><PERSON><PERSON>])", "d": "G<PERSON>t Werte aus einem linearen Trend zurück, die bekannten Datenpunkten entsprechen, unter Verwendung der Methode der kleinsten Quadrate"}, "TRIMMEAN": {"a": "(Matrix; Prozent)", "d": "G<PERSON>t den Mittelwert einer Datengruppe, ohne seine Werte an den Rändern zurück"}, "TTEST": {"a": "(Matrix1; Matrix2; Seiten; Typ)", "d": "Gibt die Teststatistik eines Studentschen t-Tests zurück"}, "VAR": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "<PERSON>hät<PERSON><PERSON> die Varianz, ausgehend von einer Stichprobe (logische Werte und Text werden in der Stichprobe ignoriert)"}, "VAR.P": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Berech<PERSON> die Varianz, ausgehend von der Grundgesamtheit. Logische Werte und Text werden ignoriert"}, "VAR.S": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "<PERSON>hät<PERSON><PERSON> die Varianz, ausgehend von einer Stichprobe (logische Werte und Text werden in der Stichprobe ignoriert)"}, "VARA": {"a": "(Wert1; [Wert2]; ...)", "d": "Schät<PERSON><PERSON> die Varianz, ausge<PERSON> von einer Stichprobe, einschließlich logischer Werte und Text. Dabei werden Text und FALSCH als 0 interpretiert, WAHR wird als 1 interpretiert."}, "VARP": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Berech<PERSON> die Varianz, ausgehend von der Grundgesamtheit. Logische Werte und Text werden ignoriert"}, "VARPA": {"a": "(Wert1; [Wert2]; ...)", "d": "Berech<PERSON> die Varianz, aus<PERSON><PERSON> von der Grundgesamtheit, einschließlich logischer Werte und Text. Dabei werden Text und FALSCH als 0 interpretiert, WAHR wird als 1 interpretiert."}, "WEIBULL": {"a": "(x; Alpha; Beta; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer Weibull-verteilten Zufallsvariablen zurück"}, "WEIBULL.DIST": {"a": "(x; Alpha; Beta; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer Weibull-verteilten Zufallsvariablen zurück"}, "Z.TEST": {"a": "(Matrix; x; [Sigma])", "d": "Gibt die einseitige Prüfstatistik für einen Gaußtest (Normalverteilung) zurück."}, "ZTEST": {"a": "(Matrix; x; [Sigma])", "d": "Gibt die einseitige Prüfstatistik für einen Gaußtest (Normalverteilung) zurück."}, "ACCRINT": {"a": "(Emission; <PERSON><PERSON><PERSON>_<PERSON>min; Abre<PERSON>nung; Satz; Nennwert; Häufigkeit; [Basis]; [Berechnungsmethode])", "d": "Gibt die aufgelaufenen Zinsen für ein Wertpapier zurück, das regelmäßig Zinsen abwirft."}, "ACCRINTM": {"a": "(Emission; Abrechnung; Nominalzins; Nennwert; [Basis])", "d": "Gibt die aufgelaufenen Zinsen (Stückzinsen) eines Wertpapiers zurück, die bei Fälligkeit ausgezahlt werden"}, "AMORDEGRC": {"a": "(<PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>_Zinstermin; Restwert; <PERSON>rmin; Satz; [<PERSON><PERSON>])", "d": "Gibt eine anteilige lineare Abschreibung eines Wirtschaftsguts für jeden Abrechnungszeitraum zurück."}, "AMORLINC": {"a": "(<PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>_Zinstermin; Restwert; <PERSON>rmin; Satz; [<PERSON><PERSON>])", "d": "Gibt eine anteilige lineare Abschreibung eines Wirtschaftsguts für jeden Abrechnungszeitraum zurück."}, "COUPDAYBS": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt die Anzahl der Tage vom Anfang des Zinstermins bis zum Abrechnungstermin zurück"}, "COUPDAYS": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt die Anzahl der Tage der Zinsperiode zurück, die den Abrechnungstermin einschließt"}, "COUPDAYSNC": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt die Anzahl der Tage vom Abrechnungstermin bis zum nächsten Zinstermin zurück"}, "COUPNCD": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt das Datum des ersten Zinstermins nach dem Abrechnungstermin zurück"}, "COUPNUM": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt die Anzahl der Zinstermine zwischen Abrechnungs- und Fälligkeitdatum zurück"}, "COUPPCD": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt das Datum des letzten Zinstermins vor dem Abrechnungstermin zurück"}, "CUMIPMT": {"a": "(<PERSON><PERSON>; Zzr; Bw; Zeitraum_Anfang; Zeitraum_Ende; F)", "d": "Berechnet die kumulierten Zinsen, die zwischen zwei Perioden zu zahlen sind"}, "CUMPRINC": {"a": "(<PERSON><PERSON>; Zzr; Bw; Zeitraum_Anfang; Zeitraum_Ende; F)", "d": "Berechnet die aufgelaufene Tilgung eines Darlehens, die zwischen zwei Perioden zu zahlen ist"}, "DB": {"a": "(Ansch_<PERSON>; Restwert; Nutzungsdauer; Periode; [<PERSON><PERSON>])", "d": "Gibt die geometrisch-degressive Abschreibung eines Wirtschaftsguts für eine bestimmte Periode zurück."}, "DDB": {"a": "(Ansch_<PERSON>; Restwert; Nutzungsdauer; Periode; [<PERSON><PERSON><PERSON>])", "d": "Gibt die degressive Doppelraten-Abschreibung eines Wirtschaftsguts für eine bestimmte Periode zurück"}, "DISC": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; Fälligkeit; <PERSON>; Rückzahlung; [Ba<PERSON>])", "d": "Gibt den in Prozent ausgedrückten Abschlag (Disagio) eines Wertpapiers zurück"}, "DOLLARDE": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Konvertiert eine Notierung, die als Dezimalbruch ausgedrückt wurde, in eine Dezimalzahl"}, "DOLLARFR": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Konvertiert eine Notierung in dezimaler Schreibweise in einen gemischten Dezimalbruch"}, "DURATION": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Nominalzins; Rendite; Häufigkeit; [Basis])", "d": "Gibt die jährliche Duration eines Wertpapiers mit periodischen Zinszahlungen zurück"}, "EFFECT": {"a": "(Nominalzins; Perioden)", "d": "Gibt die jährliche Effektivverzinsung zurück"}, "FV": {"a": "(<PERSON><PERSON>; Zzr; Rmz; [Bw]; [F])", "d": "Gibt den zukünftigen Wert (Endwert) einer Investition auf der Basis periodischer, konstanter Zahlungen und einem konstanten Zinssatz zurück"}, "FVSCHEDULE": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>)", "d": "Gibt den aufgezinsten Wert des Anfangskapitals für eine Reihe periodisch unterschiedlicher Zinssätze zurück"}, "INTRATE": {"a": "(Abre<PERSON>nung; Fälligkeit; Anlage; Rückzahlung; [Basis])", "d": "Gibt den Zinssatz eines voll investierten Wertpapiers zurück"}, "IPMT": {"a": "(<PERSON><PERSON>; Zr; Zzr; Bw; [Zw]; [F])", "d": "Gibt die Zinszahlung einer Investition für eine angegebene Periode auf der Basis von periodischen, konstanten Zahlungen und einem konstanten Zinssatz zurück"}, "IRR": {"a": "(<PERSON>rte; [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Gibt den internen Zinsfuß einer Investition ohne Finanzierungskosten oder Reinvestitionsgewinne zurück"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Gibt die während eines bestimmten Zeitraums gezahlten Zinsen einer Investition zurück"}, "MDURATION": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Coupon; Rendite; Häufigkeit; [Basis])", "d": "Gibt die geänderte Dauer für ein Wertpapier mit einem angenommenen Nennwert von 100 EUR zurück"}, "MIRR": {"a": "(Werte; Investition; Reinvestition)", "d": "Gibt die interne Rendite für eine Reihe nicht periodisch anfallender Zahlungen zurück, wobei sowohl die Investitionkosten als auch die Zinssätze auf Wiederanlagen berücksichtigt werden"}, "NOMINAL": {"a": "(Effektiver_Zins; Perioden)", "d": "Gibt die jährliche Nominalverzinsung zurück"}, "NPER": {"a": "(Z<PERSON>; Rmz; Bw; [Zw]; [F])", "d": "Gibt die Anzahl der Zahlungsperioden einer Investition auf der Basis periodischer, konstanter Zahlungen einem konstanten Zinssatz zurück"}, "NPV": {"a": "(Z<PERSON>; Wert1; [Wert2]; ...)", "d": "G<PERSON>t den Nettobarwert einer Investition auf Basis eines Abzinsungsfaktors für eine Reihe zukünftiger Zahlungen (negative Werte) und Erträge (positive Werte) zurück"}, "ODDFPRICE": {"a": "(Abrechnung; Fälligkeit; Emission; <PERSON><PERSON><PERSON>_Zinstermin; Zins; Rendite; Rückzahlung; Häufigkeit; [Basis])", "d": "G<PERSON>t den Kurs pro 100 EUR Nennwert eines Wertpapiers mit einem unregelmäßigen ersten Zinstermin zurück"}, "ODDFYIELD": {"a": "(Abrechnung; Fälligkeit; Emission; <PERSON><PERSON><PERSON>_Zinstermin; <PERSON><PERSON>; <PERSON>; Rückzahlung; Häufigkeit; [Basis])", "d": "Gibt die Rendite eines Wertpapiers mit einem unregelmäßigen ersten Zinstermin zurück"}, "ODDLPRICE": {"a": "(Abrechnung; Fälligkeit; Letzter_Zinstermin; Zins; Rendite; Rückzahlung; Häufigkeit; [Basis])", "d": "G<PERSON>t den Kurs pro 100 EUR Nennwert eines Wertpapiers mit einem unregelmäßigen letzten Zinstermin zurück"}, "ODDLYIELD": {"a": "(Abrechnung; Fälligkeit; Letzter_Zinstermin; <PERSON><PERSON>; <PERSON>; Rückzahlung; Häufigkeit; [Basis])", "d": "Gibt die Rendite eines Wertpapiers mit einem unregelmäßigen letzten Zinstermin zurück"}, "PDURATION": {"a": "(Z<PERSON>; Bw; Zw)", "d": "Gibt die Anzahl der Zahlungsperioden zurück, die eine Investition zum Erreichen eines angegebenen Werts benötigt."}, "PMT": {"a": "(<PERSON><PERSON>; Zzr; Bw; [Zw]; [F])", "d": "Berechnet die Zahlung für einen Kredit auf der Basis konstanzer Zahlungen und einem konstanten Zinssatz"}, "PPMT": {"a": "(<PERSON><PERSON>; Zr; Zzr; Bw; [Zw]; [F])", "d": "Gibt die Kapitalrückzahlung einer Investition für eine angegebene Periode auf der Basis periodischer, konstanter Zahlungen und einem konstanten Zinssatz zurück"}, "PRICE": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; Fälligkeit; Zins; Ren<PERSON>e; Rückzahlung; Häufigkeit; [Basis])", "d": "<PERSON><PERSON><PERSON> den Kurs pro 100 EUR Nennwert eines Wertpapiers zurück, das periodisch Zinsen auszahlt"}, "PRICEDISC": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; Fälligkeit; Disagio; Rückzahlung; [Basis])", "d": "G<PERSON>t den Kurs pro 100 EUR Nennwert eines unverzinslichen Wertpapiers zurück"}, "PRICEMAT": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Emission; Zins; Rendite; [<PERSON><PERSON>])", "d": "G<PERSON><PERSON> den Kurs pro 100 EUR Nennwert eines Wertpapiers zurück, das Zinsen am Fälligkeitsdatum auszahlt"}, "PV": {"a": "(<PERSON><PERSON>; Zzr; Rmz; [Zw]; [F])", "d": "Gibt den Barwert einer Investition zurück: den heutigen Gesamtwert einer Reihe zukünftiger Zahlungen"}, "RATE": {"a": "(Zzr; Rmz; Bw; [Zw]; [F]; [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Gibt den Zinssatz einer Annuität pro Periode zurück. Verwenden Sie z. B. 6%/4 für Quartalszahlungen von 6%"}, "RECEIVED": {"a": "(<PERSON><PERSON><PERSON><PERSON>ng; Fälligkeit; Anlage; Disagio; [Ba<PERSON>])", "d": "Gibt den Auszahlungsbetrag eines voll investierten Wertpapiers am Fälligkeitstermin zurück"}, "RRI": {"a": "(Zzr; Bw; Zw)", "d": "Gibt den effektiven Jahreszins für den Wertzuwachs einer Investition zurück"}, "SLN": {"a": "(Ansch_Wert; Restwert; Nutzungsdauer)", "d": "Gibt die lineare Abschreibung eines Wirtschaftsguts pro Periode zurück"}, "SYD": {"a": "(Ansch_Wert; Restwert; Nutzungsdauer; Zr)", "d": "Gibt die arithmetisch-degressive Abschreibung eines Wirtschaftsguts für eine bestimmte Periode zurück"}, "TBILLEQ": {"a": "(Abre<PERSON>nung; Fälligkeit; Abzinsungssatz)", "d": "Gibt die Rendite eines Wertpapiers zurück"}, "TBILLPRICE": {"a": "(Abre<PERSON>nung; Fälligkeit; Abzinsungssatz)", "d": "G<PERSON>t den Kurs pro 100 EUR Nennwert eines Wertpapiers zurück"}, "TBILLYIELD": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; Fälligkeit; pr)", "d": "Gibt die Rendite eines Wertpapiers zurück"}, "VDB": {"a": "(<PERSON><PERSON>_<PERSON>; Restwert; Nutzungsdauer; Anfang; Ende; [<PERSON><PERSON><PERSON>]; [<PERSON><PERSON>_wechs<PERSON>n])", "d": "Gibt die degressive Doppelraten-Abschreibung eines Wirtschaftsguts für eine bestimmte Periode oder Teilperiode zurück"}, "XIRR": {"a": "(<PERSON><PERSON>; Zeitpkte; [Schätzwer<PERSON>])", "d": "Gibt den internen Zinsfuß einer Reihe nicht periodisch anfallender Zahlungen zurück"}, "XNPV": {"a": "(<PERSON><PERSON>; <PERSON><PERSON>; Zeitpkte)", "d": "G<PERSON>t den Nettobarwert (Kapitalwert) einer Reihe nicht periodisch anfallender Zahlungen zurück"}, "YIELD": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Zins; <PERSON>; Rückzahlung; Häufigkeit; [Basis])", "d": "Gibt die Rendite eines Wertpapiers zurück, das periodisch Zinsen auszahlt"}, "YIELDDISC": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; Fälligkeit; <PERSON>; Rückzahlung; [Ba<PERSON>])", "d": "Gibt die jährliche Rendite eines unverzinslichen Wertpapiers zurück"}, "YIELDMAT": {"a": "(A<PERSON><PERSON>nung; Fälligkeit; Emission; Zins; Kurs; [<PERSON><PERSON>])", "d": "Gibt die jährliche Rendite eines Wertpapiers zurück, das Zinsen am Fälligkeitsdatum auszahlt"}, "ABS": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Absolutwert einer Zahl zurück"}, "ACOS": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Arkuskosinus einer Zahl im Bereich von 0 bis Pi zurück"}, "ACOSH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den umgekehrten hyperbolischen Kosinus einer Zahl zurück."}, "ACOT": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Arkuskotangens einer Zahl als Bogenmaß im Bereich 0 bis Pi zurück."}, "ACOTH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den umgekehrten hyperbolischen Kotangens einer Zahl zurück"}, "AGGREGATE": {"a": "(Funk<PERSON>; Optionen; Bezug1; ...)", "d": "Gibt ein Aggregat in einer Liste oder Datenbank zurück"}, "ARABIC": {"a": "(Text)", "d": "Konvertiert eine römische Zahl in eine arabische Zahl"}, "ASC": {"a": "(Text)", "d": "<PERSON><PERSON><PERSON> mit einem Double-Byte-Z<PERSON>satz (DBCS) werden in dieser Funktion Zeichen normaler Breite (Double-Byte-Zeichen) in Zeichen halber Breite (Single-Byte-Zeichen) umgewandelt"}, "ASIN": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Arkussinus einer Zahl im Bereich von -Pi/2 bis Pi/2 zurück"}, "ASINH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den umgekehrten hyperbolischen Sinus einer Zahl zurück."}, "ATAN": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Arkustangens einer Zahl in RAD in einem Bereich von -Pi/2 bis Pi/2 zurück"}, "ATAN2": {"a": "(x_Koordinate; y_Koordinate)", "d": "Gibt den Arkustangens ausgehend von einer x- und einer y-Koordinate zurück in RAD von -Pi bis Pi (ohne -Pi selbst)"}, "ATANH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den umgekehrten hyperbolischen Tangens einer Zahl zurück."}, "BASE": {"a": "(<PERSON><PERSON>; <PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Konvertiert eine Zahl in eine Textdarstellung mit der angegebenen Basis"}, "CEILING": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Rundet eine Zahl betragsmäßig auf das kleinste Vielfache von <PERSON>hr<PERSON> auf"}, "CEILING.MATH": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>]; [<PERSON><PERSON>])", "d": "Rundet eine Zahl betragsmäßig auf das kleinste Vielfache von <PERSON>hr<PERSON> auf"}, "CEILING.PRECISE": {"a": "(<PERSON><PERSON>;[<PERSON><PERSON><PERSON>])", "d": "G<PERSON>t eine Zahl zurück, die auf die nächste Ganzzahl oder auf das kleinste Vielfache von „Schritt“ gerundet wurde"}, "COMBIN": {"a": "(n; k)", "d": "Gibt die Anzahl der Kombinationen ohne Wiederholung von k Elementen aus einer Menge von n Elementen zurück"}, "COMBINA": {"a": "(Zahl; gewählte_Zahl)", "d": "Gibt die Anzahl der Kombinationen mit Wiederholung für eine angegebene Anzahl von Elementen zurück"}, "COS": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Kosinus einer Zahl zurück"}, "COSH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Kosinus einer Zahl zurück. "}, "COT": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Kotangens eines Winkels zurück"}, "COTH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Kotangens einer Zahl zurück"}, "CSC": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Kosekans eines Winkels zurück"}, "CSCH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Kosekans eines Winkels zurück"}, "DECIMAL": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Konvertiert eine Textdarstellung einer Zahl mit einer angegebenen Basis in eine Dezimalzahl"}, "DEGREES": {"a": "(<PERSON><PERSON>)", "d": "Wandelt Bogenmaß (Radiant) in Grad um"}, "ECMA.CEILING": {"a": "(<PERSON><PERSON>;<PERSON>)", "d": "Rundet eine Zahl auf die nächste Ganzzahl oder auf das kleinste Vielfache des angegebenen Schritts"}, "EVEN": {"a": "(<PERSON><PERSON>)", "d": "Rundet eine positive Zahl auf die nächste gerade ganze Zahl auf und eine negative Zahl auf die nächste gerade ganze Zahl ab"}, "EXP": {"a": "(<PERSON><PERSON>)", "d": "Potenziert die Basis e mit der als Argument angegebenen Zahl"}, "FACT": {"a": "(<PERSON><PERSON>)", "d": "Gibt die Fakultät einer Zahl zurück (Fakultät n = 1*2*3...*n)"}, "FACTDOUBLE": {"a": "(<PERSON><PERSON>)", "d": "Gibt die Fakultät zu Zahl mit Schrittlänge 2 zurück"}, "FLOOR": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Rundet eine Zahl auf das nächstliegende Vielfache von Schritt ab"}, "FLOOR.PRECISE": {"a": "(<PERSON><PERSON>;[<PERSON><PERSON><PERSON>])", "d": "G<PERSON>t eine Zahl zurück, die auf die nächste Ganzzahl oder auf das kleinste Vielfache von „Schritt“ gerundet wurde"}, "FLOOR.MATH": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>]; [<PERSON><PERSON>])", "d": "Rundet eine Zahl betragsmäßig auf das kleinste Vielfache von <PERSON>hr<PERSON> ab"}, "GCD": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den größten gemeinsamen Teiler zurück"}, "INT": {"a": "(<PERSON><PERSON>)", "d": "Rundet eine Zahl auf die nächstkleinere ganze Zahl ab"}, "ISO.CEILING": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Gibt eine Zahl zurück, die auf die nächste Ganzzahl oder auf das kleinste Vielfache von „Schritt“ gerundet wurde. Die Zahl wird unabhängig von ihrem Vorzeichen aufgerundet. Ist „Zahl“ oder „Schritt“ gleich 0, wird 0 zurückgegeben."}, "LCM": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt das kleinste gemeinsame Vielfache zurück"}, "LN": {"a": "(<PERSON><PERSON>)", "d": "Gibt den natürlichen Logarithmus einer Zahl zurück"}, "LOG": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON>])", "d": "Gibt den Logarithmus einer Zahl zu der angegebenen Basis zurück"}, "LOG10": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Logarithmus einer Zahl zur Basis 10 zurück"}, "MDETERM": {"a": "(Matrix)", "d": "Gibt die Determinante einer Matrix zurück"}, "MINVERSE": {"a": "(Matrix)", "d": "Gibt die Inverse einer Matrix (die zu einer Matrix gehörende Kehrmatrix) zurück"}, "MMULT": {"a": "(Array1; Array2)", "d": "Gibt das Produkt von zwei Arrays zurück, ein Array mit der gleichen Anzahl von Zeilen wie Array1 und Spalten wie Array2"}, "MOD": {"a": "(<PERSON><PERSON>; Divisor)", "d": "Gibt den Rest einer Division zurück"}, "MROUND": {"a": "(Zahl; Vielfaches)", "d": "Gibt eine auf das gewünschte Vielfache gerundete Zahl"}, "MULTINOMIAL": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den Polynomialkoeffizienten einer Gruppe von <PERSON>en"}, "MUNIT": {"a": "(Größe)", "d": "Gibt die Einheitsmatrix für die angegebene Größe zurück"}, "ODD": {"a": "(<PERSON><PERSON>)", "d": "Rundet eine positive Zahl auf die nächste ungerade ganze Zahl auf und eine negative Zahl auf die nächste ungerade genaue Zahl ab"}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON> den Wert PI, 3.14159265358979, mit 15 Stellen Genauigkeit zurück"}, "POWER": {"a": "(<PERSON><PERSON>; <PERSON>z)", "d": "Gibt als Ergebnis eine potenzierte Zahl zurück"}, "PRODUCT": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Multipliziert alle als Argumente angegebenen Zahlen"}, "QUOTIENT": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>)", "d": "Gibt den ganzzahligen Anteil einer Division zurück"}, "RADIANS": {"a": "(<PERSON><PERSON>)", "d": "Wandelt Grad in Bogenmaß (Radiant) um"}, "RAND": {"a": "()", "d": "Gibt eine Zufallszahl gleichmässig zwischen 0 und 1 verteilt zurück. Das Ergebnis ändert sich bei jeder Neuberechnung"}, "RANDARRAY": {"a": "([<PERSON><PERSON><PERSON>]; [<PERSON><PERSON>]; [min]; [max]; [gan<PERSON>_<PERSON>])", "d": "G<PERSON><PERSON> ein A<PERSON><PERSON> von Zufallszahlen zurück."}, "RANDBETWEEN": {"a": "(<PERSON><PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>_<PERSON>ahl)", "d": "Gibt eine ganze Zufallszahl aus dem festgelegten Bereich zurück"}, "ROMAN": {"a": "(<PERSON><PERSON>; [Typ])", "d": "Wandelt eine arabische Zahl in eine römische Zahl als Text um"}, "ROUND": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>_<PERSON>)", "d": "Rundet eine Zahl auf eine bestimmte Anzahl an Dezimalstellen"}, "ROUNDDOWN": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>_<PERSON>)", "d": "Rundet die Zahl auf Anzahl_Stellen ab"}, "ROUNDUP": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>_<PERSON>)", "d": "Rundet die Zahl auf Anzahl_Stellen auf"}, "SEC": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Sekans eines Winkels zurück"}, "SECH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Sekans eines Winkels zurück"}, "SERIESSUM": {"a": "(x; n; m; Koeffizienten)", "d": "Gibt die Summe von Potenzen (zur Berechnung von Potenzreihen und dichotomen Wahrscheinlichkeiten) zurück"}, "SIGN": {"a": "(<PERSON><PERSON>)", "d": "Gibt das Vorzeichen einer Zahl zurück: 1 wenn die Zahl positiv ist, 0 wenn die Zahl 0 ist, -1 wenn die Zahl negativ ist"}, "SIN": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Sinus einer Zahl zurück"}, "SINH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Sinus einer Zahl zurück. (Die verwendete Gleichung ist in der Hilfe genauer beschrieben.)"}, "SQRT": {"a": "(<PERSON><PERSON>)", "d": "Gibt die Quadratwurzel einer Zahl"}, "SQRTPI": {"a": "(<PERSON><PERSON>)", "d": "Gibt die Wurzel aus der mit Pi multiplizierten Zahl zurück"}, "SUBTOTAL": {"a": "(<PERSON><PERSON>; Bezug1; ...)", "d": "Gibt ein Teilergebnis in einer Liste oder Datenbank zurück"}, "SUM": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Summier<PERSON> die Zahlen in einem Zellenbereich"}, "SUMIF": {"a": "(Bereich; Suchkriterien; [<PERSON><PERSON>_Bereich])", "d": "<PERSON><PERSON><PERSON>, die mit dem Suchkriterium übereinstimmen"}, "SUMIFS": {"a": "(<PERSON><PERSON>_<PERSON>; Kriterien_Be<PERSON>ich; Kriterien; ...)", "d": "Addiert die Zellen, die von einer bestimmten Gruppe von Bedingungen oder Kriterien angegeben sind"}, "SUMPRODUCT": {"a": "(Array1; [Array2]; [Array3]; ...)", "d": "Gibt die Summe der Produkte der entsprechenden Bereiche und Arrays zurück"}, "SUMSQ": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die Summe der quadrierten Argumente zurück. Die Argumente können Zahlen, <PERSON><PERSON><PERSON>, <PERSON>n oder Bezüge auf Z<PERSON>n sein, die Zahlen enthalten"}, "SUMX2MY2": {"a": "(Matrix_x; Matrix_y)", "d": "Summiert für zusammengehörige Komponenten zweier Matrizen die Differenzen der Quadrate"}, "SUMX2PY2": {"a": "(Matrix_x; Matrix_y)", "d": "Summiert für zusammengehörige Komponenten zweier Matrizen die Summen der Quadrate"}, "SUMXMY2": {"a": "(Matrix_x; Matrix_y)", "d": "Summiert für zusammengehörige Komponenten zweier Matrizen die quadrierten Differenzen."}, "TAN": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Tangens einer Zahl zurück"}, "TANH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Tangens einer Zahl zurück."}, "TRUNC": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Schneidet die Kommastellen der Zahl ab und gibt als Ergebnis eine ganze Zahl zurück"}, "ADDRESS": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; [Abs]; [A1]; [Tabellenname])", "d": "Gibt einen Verweis auf eine Zelle einer Tabelle als Text zurück"}, "CHOOSE": {"a": "(Index; Wert1; [Wert2]; ...)", "d": "W<PERSON><PERSON>t einen Wert aus einer Liste von Werten"}, "COLUMN": {"a": "([Bezug])", "d": "Gibt die Spaltennummer eines Bezuges zurück"}, "COLUMNS": {"a": "(Matrix)", "d": "Gibt die Anzahl der Spalten eines Bezuges zurück"}, "FORMULATEXT": {"a": "(Bezug)", "d": "Gibt eine Formel als Zeichenfolge zurück"}, "HLOOKUP": {"a": "(Suchkriterium; Matrix; Zeilenindex; [<PERSON><PERSON>ich_Verweis])", "d": "Durchsucht die erste Zeile einer Matrix und durchläuft die Spalte nach unten, um den Wert einer Zelle zurückzugeben"}, "HYPERLINK": {"a": "(Hyperlink_Adresse; [Freundlicher_Name])", "d": "Erstellt eine Verknüpfung, die zur Hyperlink_Adresse verbindet"}, "INDEX": {"a": "(<PERSON>; <PERSON><PERSON><PERSON>; [<PERSON><PERSON><PERSON>]!<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; [<PERSON><PERSON><PERSON>]; [<PERSON><PERSON><PERSON>])", "d": "Verwendet einen Index, um aus einem Bezug oder einer Matrix einen Wert zu wählen"}, "INDIRECT": {"a": "(<PERSON>zug; [A1])", "d": "G<PERSON>t den Bezug eines Textwertes zurück"}, "LOOKUP": {"a": "(Suchkriterium; Suchvektor; [Ergebnisvektor]!Suchkriterium; Matrix)", "d": "Durchsucht die Werte eines Vektors oder einer Matrix"}, "MATCH": {"a": "(Suchkriterium; Suchmatrix; [Vergleichstyp])", "d": "Sucht Werte innerhalb eines Bezuges oder einer Matrix"}, "OFFSET": {"a": "(Bezug; Zeilen; Spalten; [<PERSON><PERSON><PERSON>]; [<PERSON><PERSON><PERSON>])", "d": "Gibt einen Bezug zurück, der gegenüber dem angegebenen Bezug versetzt ist"}, "ROW": {"a": "([Bezug])", "d": "Gibt die Zeilennummer eines Bezuges zurück"}, "ROWS": {"a": "(Matrix)", "d": "Gibt die Anzahl der Zeilen eines Bezuges zurück"}, "TRANSPOSE": {"a": "(Matrix)", "d": "Gibt die transponierte Matrix der angegebenen Matrix zurück"}, "UNIQUE": {"a": "(<PERSON>; [nach_<PERSON>lte]; [gena<PERSON>_e<PERSON><PERSON>])", "d": "Gibt die eindeutigen Werte eines Bereichs oder Arrays zurück."}, "VLOOKUP": {"a": "(Suchkriterium; Matrix; Spaltenindex; [Bereich_Verweis])", "d": "Durchsucht die erste Spalte einer Matrix und durchläuft die Zeile nach rechts, um den Wert einer Zelle zurückzugeben"}, "XLOOKUP": {"a": "(Suchkriterium; Suchmatrix; Rückgabematrix; [wenn_nicht_gefunden]; [Vergleichsmodus]; [Suchmodus])", "d": "Durchsu<PERSON> einen Bereich oder eine Matrix auf eine Übereinstimmung und gibt das entsprechende Element aus einem zweiten Bereich oder einer zweiten Matrix zurück. Standardmäßig wird eine exakte Übereinstimmung verwendet."}, "CELL": {"a": "(info_type; [reference])", "d": "Werden Informationen zur Formatierung, zur Position oder zum Inhalt einer Zelle zurückgegeben"}, "ERROR.TYPE": {"a": "(Fehlerwert)", "d": "Gibt eine Zahl entsprechend dem vorliegenden Fehlerwert zurück."}, "ISBLANK": {"a": "(Wert)", "d": "Gibt WAHR zurück, wenn der Wert eine leere Zelle ist"}, "ISERR": {"a": "(Wert)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob ein Wert ein anderer Fehler als #NV ist, und gibt WAHR oder FALSCH zurück"}, "ISERROR": {"a": "(Wert)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob ein Wert ein Fehler ist, und gibt WAHR oder FALSCH zurück"}, "ISEVEN": {"a": "(<PERSON><PERSON>)", "d": "Gibt WAHR zurück, wenn es sich um eine gerade Zahl handelt"}, "ISFORMULA": {"a": "(Bezug)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ob ein Bezug auf eine Zelle verweist, die eine Formel enthält, und gibt WAHR oder FALSCH zurück"}, "ISLOGICAL": {"a": "(Wert)", "d": "Gibt WAHR zurück, wenn der Wert ein Wahrheitswert ist"}, "ISNA": {"a": "(Wert)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob ein Wert #NV ist und gibt WAHR oder FALSCH zurück"}, "ISNONTEXT": {"a": "(Wert)", "d": "Gibt WAHR zurück, wenn der Wert ein Element ist, das keinen Text enthält"}, "ISNUMBER": {"a": "(Wert)", "d": "Gibt WAHR zurück, wenn der Wert eine Zahl ist"}, "ISODD": {"a": "(<PERSON><PERSON>)", "d": "Gibt WAHR zurück, wenn es sich um eine ungerade Zahl handelt"}, "ISREF": {"a": "(Wert)", "d": "Gibt WAHR zurück, wenn der Wert ein Bezug ist"}, "ISTEXT": {"a": "(Wert)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob ein Wert ein Text ist und gibt WAHR zurück, wenn dies zutrifft, bzw. FALSCH, wenn dies nicht zutrifft."}, "N": {"a": "(Wert)", "d": "Wandelt einen nicht-numerischen Wert in eine Zahl, ein <PERSON> in eine serielle Zahl, und WAHR in die Zahl 1 um. Alle anderen Werte werden in die Zahl 0 umgewandelt"}, "NA": {"a": "()", "d": "Gibt den Fehlerwert #NV (Wert nicht verfügbar) zurück"}, "SHEET": {"a": "([Wert])", "d": "Gibt die Blattnummer des Blatts zurück, auf das verwiesen wird"}, "SHEETS": {"a": "([Bezug])", "d": "Gibt die Anzahl der Blätter in einem Bezug zurück"}, "TYPE": {"a": "(Wert)", "d": "Gibt eine ganze Zahl zurück, die den Datentyp des angegebenen Wertes darstellt: Zahl = 1; Text = 2; Logischer Wert = 4; Fehlerwert = 16; Matrix = 64; Zusammengesetzte Daten = 128"}, "AND": {"a": "(Wahrheitswert1; [Wahrheitswert2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob alle Argumente WAHR sind; gibt WAHR zurück, wenn alle Argumente WAHR sind"}, "FALSE": {"a": "()", "d": "Gibt den Wahrheitswert FALSCH zurück"}, "IF": {"a": "(Wahrheitstest; [Wert_wenn_wahr]; [Wert_wenn_falsch])", "d": "Gibt einen Wahrheitstest an, der durchgeführt werden soll"}, "IFS": {"a": "(Wahrheitstest; Wert_wenn_wahr; ...)", "d": "<PERSON>ber<PERSON><PERSON><PERSON><PERSON>, ob mindestens eine Bedingung erfüllt ist, und gibt einen Wert entsprechend der ersten erfüllten Bedingung (WAHR) zurück."}, "IFERROR": {"a": "(Wert; Wert_falls_<PERSON>hler)", "d": "Gibt einen Wert_falls_<PERSON>hler aus, falls es sich bei dem Ausdruck um einen Fehler handelt, und anderenfalls den Wert des Ausdrucks selbst"}, "IFNA": {"a": "(Wert; Wert_bei_NV)", "d": "Gibt den Wert zurück, den Si<PERSON> angeben, wenn der Ausdruck zu \"#NV\" ausgewertet wird, gibt andern<PERSON> das Ergebnis des Ausdrucks zurück"}, "NOT": {"a": "(Wahrheitswert)", "d": "Kehrt den Wert ihres Argumentes um"}, "OR": {"a": "(Wahrheitswert1; [Wahrheitswert2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob eines der Argumente WAHR ist und gibt WAHR oder FALSCH zurück. Nur wenn alle Argumente FALSCH sind, wird FALSCH zurückgegeben"}, "SWITCH": {"a": "(Ausdruck; Wert1; <PERSON>rgebnis1; [Standard_oder_Wert2]; [Ergebnis2]; ...)", "d": "Wertet einen Ausdruck anhand einer Wertliste aus und gibt das Ergebnis zurück, das dem ersten übereinstimmenden Wert entspricht. Wenn es keine Übereinstimmung gibt, wird ein optionaler Standardwert zurückgegeben"}, "TRUE": {"a": "()", "d": "Gibt den Wahrheitswert WAHR zurück"}, "XOR": {"a": "(Wahrheitswert1; [Wahrheitswert2]; ...)", "d": "Gibt ein logisches \"Ausschließliches Oder\" aller Argumente zurück"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Gibt Text zurück, der vor Trennzeichen steht."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Gibt Text zurück, der nach Trennzeichen steht."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Teilt Text mithil<PERSON> von Trennzeichen in Zeilen oder Spalten auf."}, "WRAPROWS": {"a": "(<PERSON><PERSON><PERSON>, wrap_count, [pad_with])", "d": " Umschließt einen Zeilen- oder Spaltenvektor nach einer angegebenen Anzahl von <PERSON>rten."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Stapelt Matrizes vertikal in eine Matrix."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Stapelt Matrizes horizontal in eine Matrix."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "G<PERSON>t Zeilen aus einer Matrix oder einem Verweis zurück."}, "CHOOSECOLS": {"a": "(array, row_num1, [row_num2], ...)", "d": "G<PERSON>t Spalten aus einer Matrix oder einem Verweis zurück."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Gibt die Matrix als eine Spalte zurück."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "G<PERSON>t das Array als eine Zeile zurück."}, "WRAPCOLS": {"a": "(<PERSON><PERSON><PERSON>, wrap_count, [pad_with])", "d": " Umschließt einen Zeilen- oder Spaltenvektor nach einer angegebenen Anzahl von <PERSON>rten."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Gibt Zeilen oder Spalten vom Anfang oder Ende der Matrix zurück."}, "DROP": {"a": "(array, rows, [columns])", "d": "Löscht Zeilen oder Spalten vom Anfang oder Ende der Matrix."}}
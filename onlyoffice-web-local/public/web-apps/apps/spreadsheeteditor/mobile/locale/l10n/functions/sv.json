{"DATE": "DATUM", "DATEDIF": "DATEDIF", "DATEVALUE": "DATUMVÄRDE", "DAY": "DAG", "DAYS": "DAGAR", "DAYS360": "DAGAR360", "EDATE": "EDATUM", "EOMONTH": "SLUTMÅNAD", "HOUR": "TIMME", "ISOWEEKNUM": "ISOVECKONR", "MINUTE": "MINUT", "MONTH": "MÅNAD", "NETWORKDAYS": "NETTOARBETSDAGAR", "NETWORKDAYS.INTL": "NETTOARBETSDAGAR.INT", "NOW": "NU", "SECOND": "SEKUND", "TIME": "KLOCKSLAG", "TIMEVALUE": "TIDVÄRDE", "TODAY": "IDAG", "WEEKDAY": "VECKODAG", "WEEKNUM": "VECKONR", "WORKDAY": "ARBETSDAGAR", "WORKDAY.INTL": "ARBETSDAGAR.INT", "YEAR": "ÅR", "YEARFRAC": "ÅRDEL", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BIN.TILL.DEC", "BIN2HEX": "BIN.TILL.HEX", "BIN2OCT": "BIN.TILL.OKT", "BITAND": "BITOCH", "BITLSHIFT": "BITVSKIFT", "BITOR": "BITELLER", "BITRSHIFT": "BITHSKIFT", "BITXOR": "BITXELLER", "COMPLEX": "KOMPLEX", "CONVERT": "KONVERTERA", "DEC2BIN": "DEC.TILL.BIN", "DEC2HEX": "DEC.TILL.HEX", "DEC2OCT": "DEC.TILL.OKT", "DELTA": "DELTA", "ERF": "FELF", "ERF.PRECISE": "FELF.EXAKT", "ERFC": "FELFK", "ERFC.PRECISE": "FELFK.EXAKT", "GESTEP": "SLSTEG", "HEX2BIN": "HEX.TILL.BIN", "HEX2DEC": "HEX.TILL.DEC", "HEX2OCT": "HEX.TILL.OKT", "IMABS": "IMABS", "IMAGINARY": "IMAGINÄR", "IMARGUMENT": "IMARGUMENT", "IMCONJUGATE": "IMKONJUGAT", "IMCOS": "IMCOS", "IMCOSH": "IMCOSH", "IMCOT": "IMCOT", "IMCSC": "IMCSC", "IMCSCH": "IMCSCH", "IMDIV": "IMDIV", "IMEXP": "IMEUPPHÖJT", "IMLN": "IMLN", "IMLOG10": "IMLOG10", "IMLOG2": "IMLOG2", "IMPOWER": "IMUPPHÖJT", "IMPRODUCT": "IMPRODUKT", "IMREAL": "IMREAL", "IMSEC": "IMSEK", "IMSECH": "IMSEKH", "IMSIN": "IMSIN", "IMSINH": "IMSINH", "IMSQRT": "IMROT", "IMSUB": "IMDIFF", "IMSUM": "IMSUM", "IMTAN": "IMTAN", "OCT2BIN": "OKT.TILL.BIN", "OCT2DEC": "OKT.TILL.DEC", "OCT2HEX": "OKT.TILL.HEX", "DAVERAGE": "DMEDEL", "DCOUNT": "DANTAL", "DCOUNTA": "DANTALV", "DGET": "DHÄMTA", "DMAX": "DMAX", "DMIN": "DMIN", "DPRODUCT": "DPRODUKT", "DSTDEV": "DSTDAV", "DSTDEVP": "DSTDAVP", "DSUM": "DSUMMA", "DVAR": "DVARIANS", "DVARP": "DVARIANSP", "CHAR": "TECKENKOD", "CLEAN": "STÄDA", "CODE": "KOD", "CONCATENATE": "SAMMANFOGA", "CONCAT": "SAMMAN", "DOLLAR": "VALUTA", "EXACT": "EXAKT", "FIND": "HITTA", "FINDB": "FINDB", "FIXED": "FASTTAL", "LEFT": "VÄNSTER", "LEFTB": "LEFTB", "LEN": "LÄNGD", "LENB": "LENB", "LOWER": "GEMENER", "MID": "EXTEXT", "MIDB": "MIDB", "NUMBERVALUE": "TALVÄRDE", "PROPER": "INITIAL", "REPLACE": "ERSÄTT", "REPLACEB": "REPLACEB", "REPT": "REP", "RIGHT": "HÖGER", "RIGHTB": "RIGHTB", "SEARCH": "SÖK", "SEARCHB": "SEARCHB", "SUBSTITUTE": "BYT.UT", "T": "T", "T.TEST": "T.TEST", "TEXT": "TEXT", "TEXTJOIN": "TEXTJOIN", "TREND": "TREND", "TRIM": "RENSA", "TRIMMEAN": "TRIMMEDEL", "TTEST": "TTEST", "UNICHAR": "UNITECKENKOD", "UNICODE": "UNICODE", "UPPER": "VERSALER", "VALUE": "TEXTNUM", "AVEDEV": "MEDELAVV", "AVERAGE": "MEDEL", "AVERAGEA": "AVERAGEA", "AVERAGEIF": "MEDEL.OM", "AVERAGEIFS": "MEDEL.OMF", "BETADIST": "BETAFÖRD", "BETAINV": "BETAINV", "BETA.DIST": "BETA.FÖRD", "BETA.INV": "BETA.INV", "BINOMDIST": "BINOMFÖRD", "BINOM.DIST": "BINOM.FÖRD", "BINOM.DIST.RANGE": "BINOM.FÖRD.INTERVALL", "BINOM.INV": "BINOM.INV", "CHIDIST": "CHI2FÖRD", "CHIINV": "CHI2INV", "CHITEST": "CHI2TEST", "CHISQ.DIST": "CHI2.FÖRD", "CHISQ.DIST.RT": "CHI2.FÖRD.RT", "CHISQ.INV": "CHI2.INV", "CHISQ.INV.RT": "CHI2.INV.RT", "CHISQ.TEST": "CHI2.TEST", "CONFIDENCE": "KONFIDENS", "CONFIDENCE.NORM": "KONFIDENS.NORM", "CONFIDENCE.T": "KONFIDENS.T", "CORREL": "KORREL", "COUNT": "ANTAL", "COUNTA": "ANTALV", "COUNTBLANK": "ANTAL.TOMMA", "COUNTIF": "ANTAL.OM", "COUNTIFS": "ANTAL.OMF", "COVAR": "KOVAR", "COVARIANCE.P": "KOVARIANS.P", "COVARIANCE.S": "KOVARIANS.S", "CRITBINOM": "KRITBINOM", "DEVSQ": "KVADAVV", "EXPON.DIST": "EXPON.FÖRD", "EXPONDIST": "EXPONFÖRD", "FDIST": "FFÖRD", "FINV": "FINV", "FTEST": "FTEST", "F.DIST": "F.F<PERSON>", "F.DIST.RT": "F.FÖRD.RT", "F.INV": "F.INV", "F.INV.RT": "F.INV.RT", "F.TEST": "F.TEST", "FISHER": "FISHER", "FISHERINV": "FISHERINV", "FORECAST": "PREDIKTION", "FORECAST.ETS": "PROGNOS.ETS", "FORECAST.ETS.CONFINT": "PROGNOS.ETS.KONFINT", "FORECAST.ETS.SEASONALITY": "PROGNOS.ETS.SÄSONGSBEROENDE", "FORECAST.ETS.STAT": "PROGNOS.ETS.STAT", "FORECAST.LINEAR": "PROGNOS.LINJÄR", "FREQUENCY": "FREKVENS", "GAMMA": "GAMMA", "GAMMADIST": "GAMMAFÖRD", "GAMMA.DIST": "GAMMA.FÖRD", "GAMMAINV": "GAMMAINV", "GAMMA.INV": "GAMMA.INV", "GAMMALN": "GAMMALN", "GAMMALN.PRECISE": "GAMMALN.EXAKT", "GAUSS": "GAUSS", "GEOMEAN": "GEOMEDEL", "GROWTH": "EXPTREND", "HARMEAN": "HARMMEDEL", "HYPGEOM.DIST": "HYPGEOM.FÖRD", "HYPGEOMDIST": "HYPGEOMFÖRD", "INTERCEPT": "SKÄRNINGSPUNKT", "KURT": "TOPPIGHET", "LARGE": "STÖRSTA", "LINEST": "REGR", "LOGEST": "EXPREGR", "LOGINV": "LOGINV", "LOGNORM.DIST": "LOGNORM.FÖRD", "LOGNORM.INV": "LOGNORM.INV", "LOGNORMDIST": "LOGNORMFÖRD", "MAX": "MAX", "MAXA": "MAXA", "MAXIFS": "MAXIFS", "MEDIAN": "MEDIAN", "MIN": "MIN", "MINA": "MINA", "MINIFS": "MINIFS", "MODE": "TYPVÄRDE", "MODE.MULT": "TYPVÄRDE.FLERA", "MODE.SNGL": "TYPVÄRDE.ETT", "NEGBINOM.DIST": "NEGBINOM.FÖRD", "NEGBINOMDIST": "NEGBINOMFÖRD", "NORM.DIST": "NORM.FÖRD", "NORM.INV": "NORM.INV", "NORM.S.DIST": "NORM.S.FÖRD", "NORM.S.INV": "NORM.S.INV", "NORMDIST": "NORMFÖRD", "NORMINV": "NORMINV", "NORMSDIST": "NORMSFÖRD", "NORMSINV": "NORMSINV", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTIL", "PERCENTILE.EXC": "PERCENTIL.EXK", "PERCENTILE.INC": "PERCENTIL.INK", "PERCENTRANK": "PROCENTRANG", "PERCENTRANK.EXC": "PROCENTRANG.EXK", "PERCENTRANK.INC": "PROCENTRANG.INK", "PERMUT": "PERMUT", "PERMUTATIONA": "PERMUTATIONA", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "POISSON.FÖRD", "PROB": "SANNOLIKHET", "QUARTILE": "KVARTIL", "QUARTILE.INC": "KVARTIL.INK", "QUARTILE.EXC": "KVARTIL.EXK", "RANK.AVG": "RANG.MED", "RANK.EQ": "RANG.EKV", "RANK": "RANG", "RSQ": "RKV", "SKEW": "SNEDHET", "SKEW.P": "SNEDHET.P", "SLOPE": "LUTNING", "SMALL": "MINSTA", "STANDARDIZE": "STANDARDISERA", "STDEV": "STDAV", "STDEV.P": "STDAV.P", "STDEV.S": "STDAV.S", "STDEVA": "STDEVA", "STDEVP": "STDAVP", "STDEVPA": "STDEVPA", "STEYX": "STDFELYX", "TDIST": "TFÖRD", "TINV": "TINV", "T.DIST": "T.F<PERSON>", "T.DIST.2T": "T.FÖRD.2T", "T.DIST.RT": "T.FÖRD.RT", "T.INV": "T.INV", "T.INV.2T": "T.INV.2T", "VAR": "VARIANS", "VAR.P": "VARIANS.P", "VAR.S": "VARIANS.S", "VARA": "VARA", "VARP": "VARIANSP", "VARPA": "VARPA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "WEIBULL.FÖRD", "Z.TEST": "Z.TEST", "ZTEST": "ZTEST", "ACCRINT": "UPPLRÄNTA", "ACCRINTM": "UPPLOBLRÄNTA", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "KUPDAGBB", "COUPDAYS": "KUPDAGB", "COUPDAYSNC": "KUPDAGNK", "COUPNCD": "KUPNKD", "COUPNUM": "KUPANT", "COUPPCD": "KUPFKD", "CUMIPMT": "KUMRÄNTA", "CUMPRINC": "KUMPRIS", "DB": "DB", "DDB": "DEGAVSKR", "DISC": "DISK", "DOLLARDE": "DECTAL", "DOLLARFR": "BRÅK", "DURATION": "LÖPTID", "EFFECT": "EFFRÄNTA", "FV": "SLUTVÄRDE", "FVSCHEDULE": "FÖRRÄNTNING", "INTRATE": "ÅRSRÄNTA", "IPMT": "RBETALNING", "IRR": "IR", "ISPMT": "RALÅN", "MDURATION": "MLÖPTID", "MIRR": "MODIR", "NOMINAL": "NOMRÄNTA", "NPER": "PERIODER", "NPV": "NETNUVÄRDE", "ODDFPRICE": "UDDAFPRIS", "ODDFYIELD": "UDDAFAVKASTNING", "ODDLPRICE": "UDDASPRIS", "ODDLYIELD": "UDDASAVKASTNING", "PDURATION": "PLÖPTID", "PMT": "BETALNING", "PPMT": "AMORT", "PRICE": "PRIS", "PRICEDISC": "PRISDISK", "PRICEMAT": "PRISFÖRF", "PV": "NUVÄRDE", "RATE": "RÄNTA", "RECEIVED": "BELOPP", "RRI": "AVKPÅINVEST", "SLN": "LINAVSKR", "SYD": "ÅRSAVSKR", "TBILLEQ": "SSVXEKV", "TBILLPRICE": "SSVXPRIS", "TBILLYIELD": "SSVXRÄNTA", "VDB": "VDEGRAVSKR", "XIRR": "XIRR", "XNPV": "XNUVÄRDE", "YIELD": "NOMAVK", "YIELDDISC": "NOMAVKDISK", "YIELDMAT": "NOMAVKFÖRF", "ABS": "ABS", "ACOS": "ARCCOS", "ACOSH": "ARCCOSH", "ACOT": "ARCCOT", "ACOTH": "ARCCOTH", "AGGREGATE": "MÄNGD", "ARABIC": "ARABISKA", "ASC": "ASC", "ASIN": "ARCSIN", "ASINH": "ARCSINH", "ATAN": "ARCTAN", "ATAN2": "ARCTAN2", "ATANH": "ARCTANH", "BASE": "BAS", "CEILING": "RUNDA.UPP", "CEILING.MATH": "RUNDA.UPP.MATEMATISKT", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "KOMBIN", "COMBINA": "KOMBINA", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "DECIMAL", "DEGREES": "GRADER", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "JÄMN", "EXP": "EXP", "FACT": "FAKULTET", "FACTDOUBLE": "DUBBELFAKULTET", "FLOOR": "RUNDA.NER", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "RUNDA.NER.MATEMATISKT", "GCD": "SGD", "INT": "HELTAL", "ISO.CEILING": "ISO.CEILING", "LCM": "MGM", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MDETERM", "MINVERSE": "MINVERT", "MMULT": "MMULT", "MOD": "REST", "MROUND": "MAVRUNDA", "MULTINOMIAL": "MULTINOMIAL", "MUNIT": "MENHET", "ODD": "UDDA", "PI": "PI", "POWER": "UPPHÖJT.TILL", "PRODUCT": "PRODUKT", "QUOTIENT": "KVOT", "RADIANS": "RADIANER", "RAND": "SLUMP", "RANDARRAY": "SLUMPMATRIS", "RANDBETWEEN": "SLUMP.MELLAN", "ROMAN": "ROMERSK", "ROUND": "AVRUNDA", "ROUNDDOWN": "AVRUNDA.NEDÅT", "ROUNDUP": "AVRUNDA.UPPÅT", "SEC": "SEK", "SECH": "SEKH", "SERIESSUM": "SERIESUMMA", "SIGN": "TECKEN", "SIN": "SIN", "SINH": "SINH", "SQRT": "ROT", "SQRTPI": "ROTPI", "SUBTOTAL": "DELSUMMA", "SUM": "SUMMA", "SUMIF": "SUMMA.OM", "SUMIFS": "SUMMA.OMF", "SUMPRODUCT": "PRODUKTSUMMA", "SUMSQ": "KVADRATSUMMA", "SUMX2MY2": "SUMMAX2MY2", "SUMX2PY2": "SUMMAX2PY2", "SUMXMY2": "SUMMAXMY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "AVKORTA", "ADDRESS": "ADRESS", "CHOOSE": "VÄLJ", "COLUMN": "KOLUMN", "COLUMNS": "KOLUMNER", "FORMULATEXT": "FORMELTEXT", "HLOOKUP": "LETAKOLUMN", "HYPERLINK": "HYPERLÄNK", "INDEX": "INDEX", "INDIRECT": "INDIREKT", "LOOKUP": "LETAUPP", "MATCH": "PASSA", "OFFSET": "FÖRSKJUTNING", "ROW": "RAD", "ROWS": "RADER", "TRANSPOSE": "TRANSPONERA", "UNIQUE": "UNIK", "VLOOKUP": "LETARAD", "XLOOKUP": "XLETAUPP", "CELL": "CELL", "ERROR.TYPE": "FEL.TYP", "ISBLANK": "ÄRTOM", "ISERR": "ÄRF", "ISERROR": "ÄRFEL", "ISEVEN": "ÄRJÄMN", "ISFORMULA": "ÄRFORMEL", "ISLOGICAL": "ÄRLOGISK", "ISNA": "ÄRSAKNAD", "ISNONTEXT": "ÄREJTEXT", "ISNUMBER": "ÄRTAL", "ISODD": "ÄRUDDA", "ISREF": "ÄRREF", "ISTEXT": "ÄRTEXT", "N": "N", "NA": "SAKNAS", "SHEET": "BLAD", "SHEETS": "ANTALBLAD", "TYPE": "VÄRDETYP", "AND": "OCH", "FALSE": "FALSKT", "IF": "OM", "IFS": "IFS", "IFERROR": "OMFEL", "IFNA": "OMSAKNAS", "NOT": "ICKE", "OR": "ELLER", "SWITCH": "VÄXLA", "TRUE": "SANT", "XOR": "XELLER", "TEXTBEFORE": "TEXTFÖRE", "TEXTAFTER": "TEXTEFTER", "TEXTSPLIT": "DELATEXT", "WRAPROWS": "BRYTRAD", "VSTACK": "VSTACK", "HSTACK": "HSTACK", "CHOOSEROWS": "VÄLJRADER", "CHOOSECOLS": "VÄLJKOL", "TOCOL": "TILLKOL", "TOROW": "TILLRAD", "WRAPCOLS": "BRYTKOLUMN", "TAKE": "TA", "DROP": "UTESLUT", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!"}}}
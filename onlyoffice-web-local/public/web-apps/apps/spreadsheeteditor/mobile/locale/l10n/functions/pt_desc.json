{"DATE": {"a": "(ano; mês; dia)", "d": "Devolve o número que representa a data no código de data e hora"}, "DATEDIF": {"a": "(data_inicial; data_final; unidade)", "d": "Calcula o número de dias, meses ou anos entre duas datas"}, "DATEVALUE": {"a": "(texto_data)", "d": "Converte uma data em forma de texto para um número que representa a data no código de data e hora"}, "DAY": {"a": "(núm_série)", "d": "Devolve o dia do mês, um número entre 1 e 31"}, "DAYS": {"a": "(data_de_fim; data_de_in<PERSON>cio)", "d": "Devolve o número de dias entre duas datas."}, "DAYS360": {"a": "(data_inicial; data_final; [método])", "d": "Devolve o número de dias decorridos entre duas datas, com base num ano de 360 dias (doze meses de 30 dias)"}, "EDATE": {"a": "(data_inicial; meses)", "d": "Devolve o número de série da data que é o número indicador dos meses antes ou depois da data inicial"}, "EOMONTH": {"a": "(data_inicial; meses)", "d": "Devolve o número de série do último dia do mês antes ou depois de um dado número de meses"}, "HOUR": {"a": "(núm_série)", "d": "Devolve a hora como um número de 0 (00:00) a 23 (23:00)."}, "ISOWEEKNUM": {"a": "(data)", "d": "Devolve o número de semana ISO do ano de uma determinada data"}, "MINUTE": {"a": "(núm_série)", "d": "Devol<PERSON> os minutos, um número de 0 a 59."}, "MONTH": {"a": "(núm_série)", "d": "<PERSON><PERSON><PERSON> o mês, um número de 1 (janeiro) a 12 (de<PERSON><PERSON><PERSON>)."}, "NETWORKDAYS": {"a": "(data_inicial; data_final; [feriados])", "d": "Devolve o número de dias úteis entre duas datas"}, "NETWORKDAYS.INTL": {"a": "(data_inicial; data_final; [fim_de_semana]; [feriados])", "d": "Devolve o número de dias úteis completos entre duas datas com parâmetros de fim de semana personalizados"}, "NOW": {"a": "()", "d": "Devolve a data e hora atuais com o formato de data e hora."}, "SECOND": {"a": "(núm_série)", "d": "Devolve os segundos, um número de 0 a 59."}, "TIME": {"a": "(horas; minutos; segundos)", "d": "Converte horas, minutos e segundos, correspondentes a números, num número de série, com um formato de hora"}, "TIMEVALUE": {"a": "(texto_hora)", "d": "Converte uma hora de texto num número de série para uma hora, um número de 0 (00:00:00) a 0,999988426 (23:59:59). Formate o número com um formato de hora depois de introduzir a fórmula"}, "TODAY": {"a": "()", "d": "Devolve a data atual formatada como uma data."}, "WEEKDAY": {"a": "(núm_série; [tipo_devolvido])", "d": "Devolve um número de 1 a 7 identificando o dia da semana."}, "WEEKNUM": {"a": "(núm_série; [tipo_retorno])", "d": "Devolve o número da semana no ano"}, "WORKDAY": {"a": "(data_inicial; dias; [feriados])", "d": "Devolve o número de série da data antes ou depois de um dado número de dias úteis"}, "WORKDAY.INTL": {"a": "(data_inicial; dias; [fim_de_semana]; [feriados])", "d": "Devolve o número de série da data anterior ou posterior a um número específico de dias úteis com parâmetros de fim de semana personalizados"}, "YEAR": {"a": "(núm_série)", "d": "Devolve o ano de uma data, um número inteiro do intervalo 1900-9999."}, "YEARFRAC": {"a": "(data_inicial; data_final; [base])", "d": "Devolve a fração do ano que representa o número de dias entre data_inicial e data_final"}, "BESSELI": {"a": "(x; n)", "d": "Devolve a função de Bessel modificada ln(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Devolve a função de Bessel Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Devolve a função de Bessel modificada Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Devolve a função de Bessel Yn(x)"}, "BIN2DEC": {"a": "(núm)", "d": "Converte um número binário em decimal"}, "BIN2HEX": {"a": "(núm; [casas])", "d": "Converte um número binário em hexadecimal"}, "BIN2OCT": {"a": "(núm; [casas])", "d": "Converte um número binário em octal"}, "BITAND": {"a": "(número1; número2)", "d": "Devolve um \"E\" bit-a-bit de dois números"}, "BITLSHIFT": {"a": "(númer<PERSON>; valor_da_deslocação)", "d": "Devolve um número deslocado à esquerda por valor_da_deslocação bits"}, "BITOR": {"a": "(número1; número2)", "d": "Devolve um \"Ou\" bit-a-bit de dois números"}, "BITRSHIFT": {"a": "(númer<PERSON>; valor_da_deslocação)", "d": "Devolve um número deslocado à direita por valor_da_deslocação bits"}, "BITXOR": {"a": "(número1; número2)", "d": "Devolve um \"Ou Exclusivo\" bit-a-bit de dois números"}, "COMPLEX": {"a": "(núm_real; i_núm; [sufixo])", "d": "Converte coeficientes reais e imaginários num número complexo"}, "CONVERT": {"a": "(núm; de_unidade; para_unidade)", "d": "Converte um número de um sistema de medida para outro"}, "DEC2BIN": {"a": "(núm; [casas])", "d": "Converte um número decimal em binário"}, "DEC2HEX": {"a": "(núm; [casas])", "d": "Converte um número decimal em hexadecimal"}, "DEC2OCT": {"a": "(núm; [casas])", "d": "Converte um número decimal em octal"}, "DELTA": {"a": "(núm1; [núm2])", "d": "Testa se dois números são iguais"}, "ERF": {"a": "(limite_inferior; [limite_superior])", "d": "Devolve a função de erro"}, "ERF.PRECISE": {"a": "(X)", "d": "Devolve a função de erro"}, "ERFC": {"a": "(x)", "d": "Devolve a função complementar de erro"}, "ERFC.PRECISE": {"a": "(X)", "d": "Devolve a função complementar de erro"}, "GESTEP": {"a": "(núm; [passo])", "d": "Testa se um número é maior do que um valor limite"}, "HEX2BIN": {"a": "(núm; [casas])", "d": "Converte um número hexadecimal em binário"}, "HEX2DEC": {"a": "(núm)", "d": "Converte um número hexadecimal em decimal"}, "HEX2OCT": {"a": "(núm; [casas])", "d": "Converte um número hexadecimal em octal"}, "IMABS": {"a": "(inúm)", "d": "Devolve o valor absoluto (módulo) de um número complexo"}, "IMAGINARY": {"a": "(inúm)", "d": "Devolve o coeficiente imaginário de um número complexo"}, "IMARGUMENT": {"a": "(inúm)", "d": "Devolve o argumento q, um ângulo expresso em radianos"}, "IMCONJUGATE": {"a": "(inúm)", "d": "Devolve o conjugado complexo de um número complexo"}, "IMCOS": {"a": "(inúm)", "d": "Devolve o cosseno de um número complexo"}, "IMCOSH": {"a": "(número)", "d": "Devolve o cosseno de um número complexo"}, "IMCOT": {"a": "(número)", "d": "Devolve a cotangente de um número complexo"}, "IMCSC": {"a": "(número)", "d": "Devolve a cossecante de um número complexo"}, "IMCSCH": {"a": "(número)", "d": "Devolve a cossecante hiperbólica de um número complexo"}, "IMDIV": {"a": "(inúm1; inúm2)", "d": "Devolve o quociente de dois números complexos"}, "IMEXP": {"a": "(inúm)", "d": "Devolve o exponencial de um número complexo"}, "IMLN": {"a": "(inúm)", "d": "Devolve o logaritmo natural de um número complexo"}, "IMLOG10": {"a": "(inúm)", "d": "Devolve o logaritmo de base 10 de um número complexo"}, "IMLOG2": {"a": "(inúm)", "d": "Devolve o logaritmo de base 2 de um número complexo"}, "IMPOWER": {"a": "(inúm; núm)", "d": "Devolve um número complexo elevado a uma potência inteira"}, "IMPRODUCT": {"a": "(inúm1; [inúm2]; ...)", "d": "Devolve o produto de 1 a 255 números complexos"}, "IMREAL": {"a": "(inúm)", "d": "Devolve o coeficiente real de um número complexo"}, "IMSEC": {"a": "(número)", "d": "Devolve a secante de um número complexo"}, "IMSECH": {"a": "(número)", "d": "Devolve a secante hiperbólica de um número complexo"}, "IMSIN": {"a": "(inúm)", "d": "Devolve o seno de um número complexo"}, "IMSINH": {"a": "(número)", "d": "Devolve o seno hiperbólico de um número complexo"}, "IMSQRT": {"a": "(inúm)", "d": "Devolve a raiz quadrada de um número complexo"}, "IMSUB": {"a": "(inúm1; inúm2)", "d": "Devolve a diferença de dois números complexos"}, "IMSUM": {"a": "(inúm1; [inúm2]; ...)", "d": "Devolve a soma de números complexos"}, "IMTAN": {"a": "(número)", "d": "Devolve a tangente de um número complexo"}, "OCT2BIN": {"a": "(núm; [casas])", "d": "Converte um número octal em binário"}, "OCT2DEC": {"a": "(núm)", "d": "Converte um número octal em decimal"}, "OCT2HEX": {"a": "(núm; [casas])", "d": "Converte um número octal em hexadecimal"}, "DAVERAGE": {"a": "(base_dados; campo; critérios)", "d": "Calcula a média dos valores de uma coluna numa lista ou base de dados que cumprem as condições especificadas"}, "DCOUNT": {"a": "(base_dados; campo; critérios)", "d": "<PERSON><PERSON> as c<PERSON><PERSON><PERSON> que contêm números no campo (coluna) de dados da base de dados que cumpre as condições especificadas"}, "DCOUNTA": {"a": "(base_dados; campo; critérios)", "d": "<PERSON><PERSON> as células não em branco de um campo (coluna) de dados da base de dados que cumpre as condições especificadas"}, "DGET": {"a": "(base_dados; campo; critérios)", "d": "Extrai de uma base de dados um único registo que cumpre as condições especificadas"}, "DMAX": {"a": "(base_dados; campo; critérios)", "d": "Devolve o maior número de um campo (coluna) de registos da base de dados que cumpre as condições especificadas."}, "DMIN": {"a": "(base_dados; campo; critérios)", "d": "Devolve o número mais pequeno de um campo (coluna) de registos da base de dados que cumpre as condições especificadas"}, "DPRODUCT": {"a": "(base_dados; campo; critérios)", "d": "Multiplica os valores de um campo (colunas) de registos da base de dados que cumpre as condições especificadas"}, "DSTDEV": {"a": "(base_dados; campo; critérios)", "d": "Calcula o desvio-padrão a partir de uma amostra de entradas selecionadas da base de dados"}, "DSTDEVP": {"a": "(base_dados; campo; critérios)", "d": "Calcula o desvio-padrão com base na população total das entradas selecionadas da base de dados"}, "DSUM": {"a": "(base_dados; campo; critérios)", "d": "Adiciona os números de um campo (coluna) de registos da base de dados que cumpre as condições especificadas"}, "DVAR": {"a": "(base_dados; campo; critérios)", "d": "Calcula a variância a partir de uma amostra de entradas selecionadas da base de dados"}, "DVARP": {"a": "(base_dados; campo; critérios)", "d": "Calcula a variância com base na população total de entradas selecionadas da base de dados"}, "CHAR": {"a": "(núm)", "d": "Devolve o caráter especificado pelo número de código, a partir do conjunto de carateres do computador"}, "CLEAN": {"a": "(texto)", "d": "Remove todos os carateres do texto que não é possível imprimir"}, "CODE": {"a": "(texto)", "d": "Devolve um código numérico para o primeiro caráter de uma cadeia de texto, utilizando o conjunto de carateres utilizado pelo computador"}, "CONCATENATE": {"a": "(texto1; [texto2]; ...)", "d": "Junta várias cadeias de texto numa só"}, "CONCAT": {"a": "(texto1; ...)", "d": "Concatena uma lista ou intervalo de cadeias de texto"}, "DOLLAR": {"a": "(núm; [decimais])", "d": "Converte um número em texto, utilizando o formato monetário"}, "EXACT": {"a": "(texto1; texto2)", "d": "Compara se duas cadeias de texto são iguais e devolve VERDADEIRO ou FALSO. EXATO se for sensível às maiúsculas e minúsculas"}, "FIND": {"a": "(texto_a_localizar; no_texto; [núm_inicial])", "d": "Devolve a posição de partida de uma cadeia de texto dentro de outra. LOCALIZAR é sensível a maiúsculas e minúsculas"}, "FINDB": {"a": "(texto_a_localizar; no_texto; [núm_inicial])", "d": "Localiza uma cadeia de texto dentro de uma segunda cadeia de texto e devolve o número da posição inicial da primeira cadeia de texto a partir do primeiro caráter da segunda cadeia de texto, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japonês, chinês e coreano"}, "FIXED": {"a": "(núm; [decimais]; [sem_sep_milhar])", "d": "Arredonda um número para as casas decimais especificadas e devolve o resultado como texto, com ou sem separadores de milhares"}, "LEFT": {"a": "(texto; [núm_caract])", "d": "Devolve o número especificado de carateres do início de uma cadeia de texto"}, "LEFTB": {"a": "(texto; [núm_caract])", "d": "Devolve o primeiro caráter ou carateres numa cadeia de texto, com base no número de bytes que especificar, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japon<PERSON><PERSON>, chinês e coreano"}, "LEN": {"a": "(texto)", "d": "Devolve o número de carateres de uma cadeia de texto"}, "LENB": {"a": "(texto)", "d": "Devolve o número de bytes utilizado para representar os carateres numa cadeia de texto, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japonês, chinês e coreano"}, "LOWER": {"a": "(texto)", "d": "Converte to<PERSON> as letras de uma cadeia de texto em minúsculas"}, "MID": {"a": "(texto; núm_inicial; núm_caract)", "d": "Devolve um número específico de carateres de uma cadeia de texto, com início na posição especificada"}, "MIDB": {"a": "(texto; núm_inicial; núm_caract)", "d": "Devolve um número específico de carateres de uma cadeia de texto, começando na posição que especificar, com base no número de bytes especificado, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japon<PERSON><PERSON>, chinês e coreano"}, "NUMBERVALUE": {"a": "(texto; [separador_decimal]; [separador_grupo])", "d": "Converte o texto em números independentemente da região"}, "PROPER": {"a": "(texto)", "d": "Converte uma cadeia de texto em maiúsculas/minúsculas adequadas; a primeira letra de cada palavra para maiúsculas e todas as outras letras para minúsculas"}, "REPLACE": {"a": "(texto_antigo; núm_inicial; núm_caract; texto_novo)", "d": "Substitui parte de uma cadeia de texto por outra diferente"}, "REPLACEB": {"a": "(texto_antigo; núm_inicial; núm_caract; texto_novo)", "d": "Substitui parte de uma cadeia de texto, com base no número de bytes que especificar, por uma cadeia de texto diferente, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japon<PERSON>s, chinês e coreano"}, "REPT": {"a": "(texto; núm_vezes)", "d": "Repete o texto um dado número de vezes. Utilize REPETIR para preencher uma célula com um número de ocorrências da cadeia de texto"}, "RIGHT": {"a": "(texto; [núm_caract])", "d": "Devolve o número especificado de carateres do fim de uma cadeia de texto"}, "RIGHTB": {"a": "(texto; [núm_caract])", "d": "Devolve o último caráter ou carateres numa cadeia de texto, baseado no número de bytes especificados, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japon<PERSON><PERSON>, chinês e coreano"}, "SEARCH": {"a": "(texto_a_localizar; no_texto; [núm_inicial])", "d": "Devolve o número do caráter no qual é localizado pela primeira vez uma cadeia de texto específica, lida da esquerda para a direita (não distingue maiúsculas e minúsculas)"}, "SEARCHB": {"a": "(texto_a_localizar; no_texto; [núm_inicial])", "d": "Localiza uma cadeia de texto dentro de uma segunda cadeia de texto e devolve o número da posição de início da primeira cadeia de texto do primeiro caráter da segunda cadeia de texto, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japonês, chinês e coreano"}, "SUBSTITUTE": {"a": "(texto; texto_antigo; texto_novo; [núm_ocorrência])", "d": "Substitui um texto existente por um novo numa cadeia de texto"}, "T": {"a": "(valor)", "d": "Verifica se o valor é texto e devolve o texto se referir a texto ou devolve aspas (texto em branco) se não for"}, "TEXT": {"a": "(valor; formato_texto)", "d": "Converte um valor para texto num determinado formato numérico"}, "TEXTJOIN": {"a": "(delimitador; ignorar_vazio; texto1; ...)", "d": "Concatena uma lista ou intervalo de cadeias de texto através de um delimitador"}, "TRIM": {"a": "(texto)", "d": "Remove todos os espaços de uma cadeia de texto, à exceção de espaços simples entre palavras"}, "UNICHAR": {"a": "(número)", "d": "Devolve o caráter Unicode referenciado pelo valor numérico dado"}, "UNICODE": {"a": "(texto)", "d": "Devolve o número (ponto de código) correspondente ao primeiro caráter do texto"}, "UPPER": {"a": "(texto)", "d": "Converte uma cadeia de texto em maiúsculas"}, "VALUE": {"a": "(texto)", "d": "Converte num número uma cadeia de texto que representa um número"}, "AVEDEV": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a média aritmética dos desvios absolutos à média dos pontos de dados. Os argumentos são números ou nomes, matrizes ou referências que contêm números"}, "AVERAGE": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a média aritmética dos argumentos, que podem ser números ou nomes, matrizes ou referências que contêm números"}, "AVERAGEA": {"a": "(valor1; [valor2]; ...)", "d": "Devolve a média aritmética dos argumentos, que podem ser números, nomes, matrizes ou referências, fazendo texto e FALSO = 0; VERDADEIRO = 1"}, "AVERAGEIF": {"a": "(intervalo; critérios; [intervalo_médio])", "d": "Calcula a média (média aritmética) das células especificadas por uma determinada condição ou critério"}, "AVERAGEIFS": {"a": "(intervalo_médio; intervalo_critérios; critérios; ...)", "d": "Calcula a média (média aritmética) das células especificadas por um determinado conjunto de condições ou critérios"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Devolve a função de densidade de probabilidade beta cumulativa"}, "BETAINV": {"a": "(probabilidade; alfa; beta; [A]; [B])", "d": "Devolve o inverso da função de densidade de probabilidade beta cumulativa (DISTBETA)"}, "BETA.DIST": {"a": "(x; alfa; beta; cumulativo; [A]; [B])", "d": "Devolve a função de distribuição de probabilidade beta"}, "BETA.INV": {"a": "(probabilidade; alfa; beta; [A]; [B])", "d": "Devolve o inverso da função de densidade de probabilidade beta cumulativa (DIST.BETA)"}, "BINOMDIST": {"a": "(núm_s; tentativas; probabilidade_s; cumulativo)", "d": "Devolve a probabilidade da distribuição binomial do termo individual"}, "BINOM.DIST": {"a": "(núm_s; tentativas; probabilidade_s; cumulativo)", "d": "Devolve a probabilidade da distribuição binomial do termo individual"}, "BINOM.DIST.RANGE": {"a": "(tentativas; probabilidade_s; número_s; [número_s2])", "d": "Devolve a probabilidade de um resultado tentado utilizando uma distribuição binomial"}, "BINOM.INV": {"a": "(tentativas; probabilidade_s; alfa)", "d": "Devolve o menor valor para o qual a distribuição binomial cumulativa é maior ou igual a um valor de critério"}, "CHIDIST": {"a": "(x; graus_liberdade)", "d": "Devolve a probabilidade unilateral à direita da distribuição chi-quadrado"}, "CHIINV": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso da probabilidade unilateral à direita da distribuição chi-quadrado"}, "CHITEST": {"a": "(intervalo_real; intervalo_esperado)", "d": "Devolve o teste de independência: o valor da distribuição chi-quadrado para a estatística, com os graus de liberdade adequados"}, "CHISQ.DIST": {"a": "(x; graus_liberdade; cumulativo)", "d": "Devolve a probabilidade unilateral à esquerda da distribuição chi-quadrado"}, "CHISQ.DIST.RT": {"a": "(x; graus_liberdade)", "d": "Devolve a probabilidade unilateral à direita da distribuição chi-quadrado"}, "CHISQ.INV": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso da probabilidade unilateral à esquerda da distribuição chi-quadrado"}, "CHISQ.INV.RT": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso da probabilidade unilateral à direita da distribuição chi-quadrado"}, "CHISQ.TEST": {"a": "(intervalo_real; intervalo_esperado)", "d": "Devolve o teste de independência: o valor da distribuição chi-quadrado para a estatística, com os graus de liberdade adequados"}, "CONFIDENCE": {"a": "(alfa; desv_padrão; tamanho)", "d": "Devolve o intervalo de confiança para uma média da população utilizando uma distribuição normal"}, "CONFIDENCE.NORM": {"a": "(alfa; desv_padrão; tamanho)", "d": "Devolve o intervalo de confiança para uma média da população"}, "CONFIDENCE.T": {"a": "(alfa; desv_padrão; tamanho)", "d": "Devolve o intervalo de confiança para uma média da população, utilizando uma distribuição T de Student"}, "CORREL": {"a": "(matriz1; matriz2)", "d": "Devolve o coeficiente de correlação entre dois conjuntos de dados"}, "COUNT": {"a": "(valor1; [valor2]; ...)", "d": "Conta o número de células num intervalo que contém números"}, "COUNTA": {"a": "(valor1; [valor2]; ...)", "d": "Conta o número de células não em branco num intervalo"}, "COUNTBLANK": {"a": "(intervalo)", "d": "<PERSON><PERSON> as células em branco dentro de um intervalo de células especificado"}, "COUNTIF": {"a": "(intervalo; critérios)", "d": "Conta o número de células de um intervalo que respeitam uma dada condição"}, "COUNTIFS": {"a": "(intervalo_critérios; critérios; ...)", "d": "Conta o número de células especificado por um determinado conjunto de condições ou critérios"}, "COVAR": {"a": "(matriz1; matriz2)", "d": "Devolve a covariância, a média dos produtos de desvios para cada par de ponto de dados em dois conjuntos de dados"}, "COVARIANCE.P": {"a": "(matriz1; matriz2)", "d": "Devolve a covariância da população, a média dos produtos dos desvios para cada par de pontos de dados em dois conjuntos de dados"}, "COVARIANCE.S": {"a": "(matriz1; matriz2)", "d": "Devolve a covariância da amostra, a média dos produtos dos desvios para cada par de pontos de dados em dois conjuntos de dados"}, "CRITBINOM": {"a": "(tentativas; probabilidade_s; alfa)", "d": "Devolve o menor valor para o qual a distribuição binomial cumulativa é maior ou igual a um valor de critério"}, "DEVSQ": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a soma dos quadrados dos desvios de pontos de dados em relação à média da sua amostra"}, "EXPONDIST": {"a": "(x; lambda; cumulativo)", "d": "Devolve a distribuição exponencial"}, "EXPON.DIST": {"a": "(x; lambda; cumulativo)", "d": "Devolve a distribuição exponencial"}, "FDIST": {"a": "(x; graus_liberdade1; graus_liberdade2)", "d": "Devolve a distribuição (unilateral à direita) de probabilidade F (grau de diversidade) para dois conjuntos de dados"}, "FINV": {"a": "(probabilidade; graus_liberdade1; graus_liberdade2)", "d": "Devolve o inverso da distribuição (unilateral à direita) de probabilidade F: se p = DISTF(x,...), então INVF(p,....) = x"}, "FTEST": {"a": "(matriz1; matriz2)", "d": "Devolve o resultado de um teste F, a probabilidade bicaudal de as variações de Matriz1 e Matriz2 não serem significativamente diferentes"}, "F.DIST": {"a": "(x; grau_liberdade1; grau_liberdade2; cumulativo)", "d": "Devolve a distribuição (unilateral à esquerda) de probabilidade F (grau de diversidade) de dois conjuntos de dados"}, "F.DIST.RT": {"a": "(x; grau_liberdade1; grau_liberdade2)", "d": "Devolve a distribuição (unilateral à direita) de probabilidade F (grau de diversidade) de dois conjuntos de dados"}, "F.INV": {"a": "(probabilidade; grau_liberdade1; grau_liberdade2)", "d": "Devolve o inverso da distribuição (unilateral à esquerda) de probabilidade F: se p = DIST.F(x,...), então INV.F(p,...) = x"}, "F.INV.RT": {"a": "(probabilidade; grau_liberdade1; grau_liberdade2)", "d": "Devolve o inverso da distribuição (unilateral à direita) de probabilidade F: se p = DIST.F.DIR(x,...), então INV.F.DIR(p,...) = x"}, "F.TEST": {"a": "(matriz1; matriz2)", "d": "Devolve o resultado de um teste F, a probabilidade bicaudal de as variações de Matriz1 e Matriz2 não serem significativamente diferentes"}, "FISHER": {"a": "(x)", "d": "Devolve a transformação Fisher"}, "FISHERINV": {"a": "(y)", "d": "Devolve o inverso da transformação Fisher: se y = FISHER(x), então FISHERINV(y) = x"}, "FORECAST": {"a": "(x; val_conhecidos_y; val_conhecidos_x)", "d": "Calcula ou prevê um valor futuro ao longo de uma tendência linear ao utilizar valores existentes"}, "FORECAST.ETS": {"a": "(data_alvo; valores; linha_cronológica; [sazonalidade]; [conclusão_dos_dados]; [agregação])", "d": "Devolve o valor previsto de uma data alvo futura específica utilizando o método de nivelamento exponencial."}, "FORECAST.ETS.CONFINT": {"a": "(data_alvo; valores; linha_cronológica; [nível_de_confiança]; [sazonalidade]; [conclusão_dos_dados]; [agregação])", "d": "Devolve um intervalo de confiança para o valor de previsão na data alvo especificada."}, "FORECAST.ETS.SEASONALITY": {"a": "(valores; linha_cronológica; [conclusão_dos_dados]; [agregação])", "d": "Devolve o comprimento do padrão repetitivo que um aplicativo detecta para a série de tempo especificada."}, "FORECAST.ETS.STAT": {"a": "(valores; linha_cronológica; tipo_estatístico; [sazonabilidade]; [conclusão_dos_dados]; [agregação])", "d": "Devolve a estatística solicitada para a previsão."}, "FORECAST.LINEAR": {"a": "(x; val_conhecidos_y; val_conhecidos_x)", "d": "Calcula ou prevê um valor futuro ao longo de uma tendência linear ao utilizar valores existentes"}, "FREQUENCY": {"a": "(matriz_dados; matriz_classe)", "d": "Calcula a frequência com que ocorrem valores num intervalo e devolve a matriz vertical de números com mais um elemento que Matriz_classe"}, "GAMMA": {"a": "(x)", "d": "Devolve o valor da função Gama"}, "GAMMADIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Devolve a distribuição gama."}, "GAMMA.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Devolve a distribuição gama"}, "GAMMAINV": {"a": "(probabilidade; alfa; beta)", "d": "Devolve o inverso da distribuição cumulativa gama: se p = DISTGAMA(x,...), então INVGAMA(p,...) = x"}, "GAMMA.INV": {"a": "(probabilidade; alfa; beta)", "d": "Devolve o inverso da distribuição cumulativa gama: se p = DIST.GAMA(x,...), então INV.GAMA(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Devolve o logaritmo natural da função gama"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Devolve o logaritmo natural da função gama"}, "GAUSS": {"a": "(x)", "d": "Devolve menos 0,5 do que a distribuição cumulativa normal padrão"}, "GEOMEAN": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a média geométrica de uma matriz ou intervalo de dados numéricos positivos"}, "GROWTH": {"a": "(val_conhecidos_y; [val_conhecidos_x]; [novos_valores_x]; [constante])", "d": "Devolve números com base na tendência de crescimento exponencial correspondente a pontos de dados conhecidos"}, "HARMEAN": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a média harmónica de um conjunto de dados de números positivos: o recíproco da média aritmética dos recíprocos"}, "HYPGEOM.DIST": {"a": "(tam_amostra; número_amostra; tam_população; número_pop; cumulativo)", "d": "Devolve a distribuição hipergeométrica"}, "HYPGEOMDIST": {"a": "(exemplo_s; exemplo_núm; população_s; núm_população)", "d": "Devolve a distribuição hipergeométrica"}, "INTERCEPT": {"a": "(val_conhecidos_y; val_conhecidos_x)", "d": "Calcula o ponto onde uma linha intercetará o eixo dos YY através da melhor linha de regressão desenhada com os valores conhecidos de X e Y"}, "KURT": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a curtose de um conjunto de dados"}, "LARGE": {"a": "(matriz; k)", "d": "Devolve o n-ésimo maior valor de um conjunto de dados. Por exemplo, o quinto número maior"}, "LINEST": {"a": "(val_conhecidos_y; [val_conhecidos_x]; [constante]; [estatística])", "d": "Devolve estatísticas que descrevem uma tendência linear que coincide com os dados conhecidos, baseada numa reta obtida por aplicação do método dos quadrados mínimos aos valores conhecidos"}, "LOGEST": {"a": "(val_conhecidos_y; [val_conhecidos_x]; [constante]; [estatística])", "d": "Devolve estatísticas que descrevem uma curva exponencial que coincide com os dados conhecidos"}, "LOGINV": {"a": "(probabilidade; média; desv_padrão)", "d": "Devolve o inverso da função de distribuição normal logarítmica cumulativa de x, onde ln(x) tem uma distribuição normal com parâmetros Média e Desv_padrão"}, "LOGNORM.DIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Devolve a distribuição normal logarítmica de x, em que ln(x) tem uma distribuição normal com os parâmetros Média e Desv_Padrão"}, "LOGNORM.INV": {"a": "(probabilidade; média; desv_padrão)", "d": "Devolve o inverso da função de distribuição normal logarítmica cumulativa de x, onde ln(x) tem uma distribuição normal com Média e Desv_padrão"}, "LOGNORMDIST": {"a": "(x; média; desv_padrão)", "d": "Devolve a distribuição normal logarítmica cumulativa de x, em que ln(x) tem uma distribuição normal com os parâmetros Média e Desv_padrão"}, "MAX": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o valor máximo de uma lista de argumentos. Ignora os valores lógicos e texto"}, "MAXA": {"a": "(valor1; [valor2]; ...)", "d": "Devolve o valor máximo de um conjunto de valores. Não ignora valores lógicos e texto"}, "MAXIFS": {"a": "(intervalo_máximo; intervalo_critérios; critérios; ...)", "d": "Devolve o valor máximo entre células especificadas por um determinado conjunto de condições ou critérios"}, "MEDIAN": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a mediana ou o número no meio de um conjunto de números indicados"}, "MIN": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o valor mais pequeno de um conjunto de valores. Ignora valores lógicos e texto"}, "MINA": {"a": "(valor1; [valor2]; ...)", "d": "Devolve o valor mais pequeno contido num conjunto de valores. Não ignora valores lógicos e texto"}, "MINIFS": {"a": "(intervalo_mínimo; intervalo_critérios; critérios; ...)", "d": "Devolve o valor mínimo entre células especificadas por um determinado conjunto de condições ou critérios"}, "MODE": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o valor mais frequente ou repetitivo numa matriz ou intervalo de dados"}, "MODE.MULT": {"a": "(número1; [número2]; ...)", "d": "Devolve uma matriz vertical dos valores que ocorrem mais frequentemente, ou repetitivos, numa matriz ou intervalo de dados. Para uma matriz horizontal, utilize =TRANSPOR(MODO.MÚLT(número1,número2,...))"}, "MODE.SNGL": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o valor mais frequente numa matriz ou intervalo de dados"}, "NEGBINOM.DIST": {"a": "(núm_i; núm_s; probabilidade_s; cumulativo)", "d": "Devolve a distribuição binomial negativa, a probabilidade de Núm_i insucessos antes do Núm_s.º sucesso, com Probabilidade_s de sucesso"}, "NEGBINOMDIST": {"a": "(núm_i; núm_s; probabilidade_s)", "d": "Devolve a distribuição binomial negativa, a probabilidade de Núm_i insucessos antes de Núm_s sucessos, com Probabilidade_s probabilidades de um sucesso"}, "NORM.DIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Devolve a distribuição cumulativa normal para a média e o desvio-padrão especificados"}, "NORMDIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Devolve a distribuição cumulativa normal para a média e o desvio-padrão especificados"}, "NORM.INV": {"a": "(probabilidade; média; desv_padrão)", "d": "Devolve o inverso da distribuição cumulativa normal para a média e o desvio-padrão especificados"}, "NORMINV": {"a": "(probabilidade; média; desv_padrão)", "d": "Devolve o inverso da distribuição cumulativa normal para a média e o desvio-padrão especificados"}, "NORM.S.DIST": {"a": "(z; cumulativo)", "d": "Devolve a distribuição normal padrão (tem uma média de zero e um desvio padrão de um)"}, "NORMSDIST": {"a": "(z)", "d": "Devolve a distribuição cumulativa normal padrão (tem uma média de 0 e um desvio-padrão de 1)"}, "NORM.S.INV": {"a": "(probabilidade)", "d": "Devolve o inverso da distribuição cumulativa normal padrão (tem uma média de 0 e um desvio-padrão de 1)"}, "NORMSINV": {"a": "(probabilidade)", "d": "Devolve o inverso da distribuição cumulativa normal padrão (tem uma média de 0 e um desvio-padrão de 1)"}, "PEARSON": {"a": "(matriz1; matriz2)", "d": "Devolve o coeficiente de correlação do momento do produto Pearson, r"}, "PERCENTILE": {"a": "(matriz; k)", "d": "Devolve o enésimo percentil de valores num intervalo"}, "PERCENTILE.EXC": {"a": "(matriz; k)", "d": "Devolve o enésimo percentil de valores num intervalo, em que k está no intervalo 0..1, exclusive"}, "PERCENTILE.INC": {"a": "(matriz; k)", "d": "Devolve o enésimo percentil de valores num intervalo, em que k está no intervalo 0..1, inclusive"}, "PERCENTRANK": {"a": "(matriz; x; [significância])", "d": "Devolve a classificação de um valor num conjunto de dados como percentagem desse conjunto"}, "PERCENTRANK.EXC": {"a": "(matriz; x; [significância])", "d": "Devolve a classificação de um valor num conjunto de dados como percentagem (0..1, exclusive) desse conjunto"}, "PERCENTRANK.INC": {"a": "(matriz; x; [significância])", "d": "Devolve a classificação de um valor num conjunto de dados como percentagem (0..1, inclusive) desse conjunto"}, "PERMUT": {"a": "(núm; núm_escolhido)", "d": "Devolve o número de permutações para um dado número de objetos que pode ser selecionado de entre a totalidade dos objetos"}, "PERMUTATIONA": {"a": "(número; número_escolhido)", "d": "Devolve o número de permutações para um determinado número de objetos (com repetições) que pode ser selecionado do total de objetos"}, "PHI": {"a": "(x)", "d": "Devolve o valor da função de densidade para uma distribuição normal padrão"}, "POISSON": {"a": "(x; média; cumulativo)", "d": "Devolve a distribuição de Poisson"}, "POISSON.DIST": {"a": "(x; média; cumulativo)", "d": "Devolve a distribuição de Poisson"}, "PROB": {"a": "(intervalo_x; intervalo_prob; limite_inferior; [limite_superior])", "d": "Devolve a probabilidade de valores de um intervalo estarem entre dois limites ou serem iguais ao limite inferior"}, "QUARTILE": {"a": "(matriz; quarto)", "d": "Devolve o quartil de um conjunto de dados"}, "QUARTILE.INC": {"a": "(matriz; quarto)", "d": "Devolve o quartil de um conjunto de dados, baseado em valores de percentil de 0..1, inclusive"}, "QUARTILE.EXC": {"a": "(matriz; quarto)", "d": "Devolve o quartil de um conjunto de dados, baseado em valores de percentil de 0..1, exclusive"}, "RANK": {"a": "(núm; ref; [ordem])", "d": "Devolve a classificação de um número numa lista de números: o tamanho do mesmo em relação a outros valores na lista"}, "RANK.AVG": {"a": "(núm; ref; [ordem])", "d": "Devolve a classificação de um número numa lista de números: o seu tamanho em relação a outros valores na lista; se mais de um valor tiver a mesma classificação, é devolvida a classificação média"}, "RANK.EQ": {"a": "(núm; ref; [ordem])", "d": "Devolve a classificação de um número numa lista de números: o seu tamanho em relação a outros valores na lista; se mais de um valor tiver a mesma classificação, é devolvida a classificação superior desse conjunto de valores"}, "RSQ": {"a": "(val_conhecidos_y; val_conhecidos_x)", "d": "Devolve o quadrado do coeficiente de correlação do momento do produto de Pearson através dos pontos dados"}, "SKEW": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a distorção de uma distribuição: uma caracterização do grau de assimetria da distribuição em torno da média"}, "SKEW.P": {"a": "(número1; [número2]; ...)", "d": "Devolve a distorção de uma distribuição baseada numa população: uma caracterização do grau de assimetria de uma distribuição em torno da média"}, "SLOPE": {"a": "(val_conhecidos_y; val_conhecidos_x)", "d": "Devolve o declive da reta de regressão linear através dos pontos dados"}, "SMALL": {"a": "(matriz; k)", "d": "Devolve o n-ésimo menor valor de um conjunto de dados. Por exemplo, o quinto número menor"}, "STANDARDIZE": {"a": "(x; média; desv_padrão)", "d": "Devolve um valor normalizado de uma distribuição caracterizada por uma média e um desvio-padrão"}, "STDEV": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio-padrão a partir de uma amostra (ignora valores lógicos e texto na amostra)"}, "STDEV.P": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio-padrão com base na população total fornecida como argumento (ignora valores lógicos e texto)"}, "STDEV.S": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio-padrão a partir de uma amostra (ignora valores lógicos e texto na amostra)"}, "STDEVA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula o desvio-padrão a partir de uma amostra, incluindo valores lógicos e texto. Texto e FALSO têm valor 0; VERDADEIRO tem o valor 1"}, "STDEVP": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio-padrão com base na população total fornecida como argumento (ignora valores lógicos e texto)"}, "STDEVPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula o desvio-padrão com base na população total, incluindo valores lógicos e texto. Texto e FALSO têm valor 0; VERDADEIRO tem o valor 1"}, "STEYX": {"a": "(val_conhecidos_y; val_conhecidos_x)", "d": "Devolve o erro padrão do valor de y previsto para cada x da regressão"}, "TDIST": {"a": "(x; graus_liberdade; caudas)", "d": "Devolve a distribuição t de Student"}, "TINV": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso bicaudal da distribuição t de Student"}, "T.DIST": {"a": "(x; graus_liberdade; cumulativo)", "d": "Devolve a distribuição t de Student unilateral à esquerda"}, "T.DIST.2T": {"a": "(x; graus_liberdade)", "d": "Devolve a distribuição t de Student bicaudal"}, "T.DIST.RT": {"a": "(x; graus_liberdade)", "d": "Devolve a distribuição t de Student unilateral à direita"}, "T.INV": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso unilateral à esquerda da distribuição t de Student"}, "T.INV.2T": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso bicaudal da distribuição t de Student"}, "T.TEST": {"a": "(matriz1; matriz2; caudas; tipo)", "d": "Devolve a probabilidade associada ao teste t de Student"}, "TREND": {"a": "(val_conhecidos_y; [val_conhecidos_x]; [novos_valores_x]; [constante])", "d": "Devolve valores numa tendência linear correspondente a pontos de dados conhecidos por aplicação do método dos quadrados mínimos"}, "TRIMMEAN": {"a": "(matriz; percentagem)", "d": "Devolve a média da porção interior de um conjunto de valores de dados"}, "TTEST": {"a": "(matriz1; matriz2; caudas; tipo)", "d": "Devolve a probabilidade associada ao teste t de Student"}, "VAR": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variância a partir de uma amostra (ignora valores lógicos e texto da amostra)"}, "VAR.P": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variância a partir da população total (ignora valores lógicos e texto da população)"}, "VAR.S": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variância a partir de uma amostra (ignora valores lógicos e texto da amostra)"}, "VARA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula a variância a partir de uma amostra, incluindo valores lógicos e texto. Texto e o valor lógico FALSO = 0; o valor lógico VERDADEIRO = 1"}, "VARP": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variância a partir da população total (ignora valores lógicos e texto da população)"}, "VARPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula a variância com base na população total, incluindo valores lógicos e texto. Texto e o valor lógico FALSO = 0; o valor lógico VERDADEIRO = 1"}, "WEIBULL": {"a": "(x; alfa; beta; cumulativo)", "d": "Devolve a distribuição Weibull"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Devolve a distribuição Weibull"}, "Z.TEST": {"a": "(matriz; x; [sigma])", "d": "Devolve o valor P unicaudal de um teste z"}, "ZTEST": {"a": "(matriz; x; [sigma])", "d": "Devolve o valor P unicaudal de um teste z"}, "ACCRINT": {"a": "(emissão; primeiro_juro; liquidação; taxa; paridade; frequência; [base]; [método_calc])", "d": "Devolve os juros decorridos de um título que paga juros periódicos"}, "ACCRINTM": {"a": "(emissão; liquidação; taxa; valor_nominal; [base])", "d": "Devolve os juros decorridos de um título que paga juros no vencimento"}, "AMORDEGRC": {"a": "(custo; data_aquisição; prim_período; recuperação; período; taxa; [base])", "d": "Devolve a depreciação linear pro rata de um bem para cada período contabilístico"}, "AMORLINC": {"a": "(custo; data_aquisição; prim_período; recuperação; período; taxa; [base])", "d": "Devolve a depreciação linear pro rata de um bem para cada período contabilístico"}, "COUPDAYBS": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve o número de dias entre o início do período do cupão e a data de liquidação"}, "COUPDAYS": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve o número de dias no período do cupão que contém a data de liquidação"}, "COUPDAYSNC": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve o número de dias entre a data de liquidação e a data seguinte do cupão"}, "COUPNCD": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve a data seguinte do cupão depois da data de liquidação"}, "COUPNUM": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve o número de cupões a pagar entre a data de liquidação e a data do vencimento"}, "COUPPCD": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve a última data do cupão antes da data de liquidação"}, "CUMIPMT": {"a": "(taxa; nper; va; início_período; final_período; tipo_pgto)", "d": "Devolve os juros acumulados pagos entre dois períodos"}, "CUMPRINC": {"a": "(taxa; nper; va; início_período; final_período; tipo_pgto)", "d": "Devolve o capital acumulado pago por um empréstimo entre dois períodos"}, "DB": {"a": "(custo; val_residual; vida_útil; período; [mês])", "d": "Devolve a depreciação de um bem num determinado período, utilizando o método de redução fixa do saldo"}, "DDB": {"a": "(custo; val_residual; vida_útil; período; [fator])", "d": "Devolve a depreciação de um bem para um determinado período utilizando o método de redução dupla do saldo ou qualquer outro método especificado"}, "DISC": {"a": "(liquidação; vencimento; pr; resgate; [base])", "d": "Devolve a taxa de desconto de um título"}, "DOLLARDE": {"a": "(moeda_fraccionária; fração)", "d": "Converte a expressão de um preço em moeda de uma fração para um número decimal"}, "DOLLARFR": {"a": "(moeda_decimal; fração)", "d": "Converte a expressão de um preço em moeda de um número decimal para uma fração"}, "DURATION": {"a": "(liquidação; vencimento; cupão; lcr; frequência; [base])", "d": "Devolve a duração anual de um título com pagamentos de juros periódicos"}, "EFFECT": {"a": "(taxa_nominal; núm_por_ano)", "d": "Devolve a taxa de juros anual efetiva"}, "FV": {"a": "(taxa; nper; pgto; [va]; [tipo])", "d": "Devolve o valor futuro de um investimento a partir de pagamentos periódicos constantes e de uma taxa de juros constante."}, "FVSCHEDULE": {"a": "(capital; plano)", "d": "Devolve o valor futuro de um capital inicial depois de ter sido aplicada uma série de taxas de juros compostos"}, "INTRATE": {"a": "(liquidação; vencimento; investimento; resgate; [base])", "d": "Devolve a taxa de juros de um título totalmente investido"}, "IPMT": {"a": "(taxa; período; nper; va; [vf]; [tipo])", "d": "Devolve o pagamento dos juros de um investimento durante um dado período, a partir de pagamentos periódicos e uma taxa de juro constantes"}, "IRR": {"a": "(valores; [estimativa])", "d": "Devolve a taxa interna de rentabilidade de uma série de fluxos monetários"}, "ISPMT": {"a": "(taxa; período; nper; va)", "d": "Devolve os juros dos pagamentos de um empréstimo simples durante um período específico"}, "MDURATION": {"a": "(liquidação; vencimento; cupão; lcr; frequência; [base])", "d": "Devolve a duração modificada de Macauley de um título com um valor par atribuído de 100 €"}, "MIRR": {"a": "(valores; taxa_financ; taxa_reinvest)", "d": "Devolve a taxa interna de rentabilidade de fluxos monetários periódicos, avaliando custos de investimento e juros de reinvestimento dos valores líquidos"}, "NOMINAL": {"a": "(taxa_efetiva; núm_por_ano)", "d": "Devolve a taxa de juros nominal anual"}, "NPER": {"a": "(taxa; pgto; va; [vf]; [tipo])", "d": "Devolve o número de períodos de um investimento, com base em pagamentos periódicos constantes e uma taxa de juros constante"}, "NPV": {"a": "(taxa; valor1; [valor2]; ...)", "d": "Devolve o valor atual líquido de um investimento, com uma taxa de desconto e uma série de pagamentos futuros (valor negativo) e rendimentos (valor positivo)"}, "ODDFPRICE": {"a": "(liquidação; vencimento; emissão; prim_cupão; taxa; lcr; resgate; frequência; [base])", "d": "Devolve o preço por cada 100 € do valor nominal de um título com um período inicial incompleto"}, "ODDFYIELD": {"a": "(liquidação; vencimento; emissão; prim_cupão; taxa; pr; resgate; frequência; [base])", "d": "Devolve o rendimento de um título com um período inicial incompleto"}, "ODDLPRICE": {"a": "(liquidação; vencimento; último_juros; taxa; lcr; resgate; frequência; [base])", "d": "Devolve o preço por cada 100 € do valor nominal de um título com um período final incompleto"}, "ODDLYIELD": {"a": "(liquidação; vencimento; último_juros; taxa; pr; resgate; frequência; [base])", "d": "Devolve o rendimento de um título com um período final incompleto"}, "PDURATION": {"a": "(taxa; va; vf)", "d": "Devolve o número de períodos necessários a um investimento para atingir um valor específico"}, "PMT": {"a": "(taxa; nper; va; [vf]; [tipo])", "d": "Calcula o pagamento de um empréstimo, a partir de pagamentos constantes e uma taxa de juros constante"}, "PPMT": {"a": "(taxa; período; nper; va; [vf]; [tipo])", "d": "Devolve o pagamento sobre o montante de um investimento, a partir de pagamentos constantes e periódicos e uma taxa de juros constante"}, "PRICE": {"a": "(liquidação; vencimento; taxa; lcr; resgate; frequência; [base])", "d": "Devolve o preço por cada 100 € do valor nominal de um título que paga juros periódicos"}, "PRICEDISC": {"a": "(liquidação; vencimento; desconto; resgate; [base])", "d": "Devolve o preço por cada 100 € do valor nominal de um título descontável"}, "PRICEMAT": {"a": "(liquidação; vencimento; emissão; taxa; lcr; [base])", "d": "Devolve o preço por cada 100 € do valor nominal de um título que paga juros no vencimento"}, "PV": {"a": "(taxa; nper; pgto; [vf]; [tipo])", "d": "Devolve o valor atual de um investimento: o montante total que vale agora uma série de pagamentos futuros"}, "RATE": {"a": "(nper; pgto; va; [vf]; [tipo]; [estimativa])", "d": "Devolve a taxa de juros por período de um empréstimo ou um investimento. Por exemplo, utilize 6%/4 para pagamentos trimestrais a 6% APR"}, "RECEIVED": {"a": "(liquidação; vencimento; investimento; desconto; [base])", "d": "Devolve a quantia recebida no vencimento de um título totalmente investido"}, "RRI": {"a": "(nper; va; vf)", "d": "Devolve uma taxa de juro equivalente para o crescimento de um investimento"}, "SLN": {"a": "(custo; val_residual; vida_útil)", "d": "Devolve a depreciação linear de um bem durante um período"}, "SYD": {"a": "(custo; val_residual; vida_útil; período)", "d": "Devolve a depreciação por algarismos da soma dos anos de um bem para um período especificado"}, "TBILLEQ": {"a": "(liquidação; vencimento; desconto)", "d": "Devolve o rendimento de um título do Tesouro equivalente a um novo título"}, "TBILLPRICE": {"a": "(liquidação; vencimento; desconto)", "d": "Devolve o preço por cada 100 € do valor nominal de um título do Tesouro"}, "TBILLYIELD": {"a": "(liquidação; vencimento; pr)", "d": "Devolve o rendimento de um título do Tesouro"}, "VDB": {"a": "(custo; val_residual; vida_útil; início_período; final_período; [fator]; [sem_mudança])", "d": "Devolve a depreciação de um bem em qualquer período dado, incluindo parciais, utilizando o método de redução dupla do saldo ou outro especificado"}, "XIRR": {"a": "(valores; datas; [estimativa])", "d": "Devolve a taxa de rentabilidade interna de uma sucessão de fluxos monetários"}, "XNPV": {"a": "(taxa; valores; datas)", "d": "Devolve o valor atual líquido de uma sucessão de fluxos monetários"}, "YIELD": {"a": "(liquidação; vencimento; taxa; pr; resgate; frequência; [base])", "d": "Devolve o rendimento de um título que paga juros periódicos"}, "YIELDDISC": {"a": "(liquidação; vencimento; pr; resgate; [base])", "d": "Devolve o rendimento anual de um título descontável, como, por exemplo, os títulos do Tesouro"}, "YIELDMAT": {"a": "(liquidação; vencimento; emissão; taxa; pr; [base])", "d": "Devolve o rendimento anual de um título que paga juros no vencimento"}, "ABS": {"a": "(núm)", "d": "Devolve o valor absoluto de um número, um número sem o respetivo sinal"}, "ACOS": {"a": "(núm)", "d": "Devolve o arco de cosseno de um número, em radianos, no intervalo de 0 a Pi. O arco de cosseno é o ângulo cujo cosseno é Núm"}, "ACOSH": {"a": "(núm)", "d": "Devolve o cosseno hiperbólico inverso de um número"}, "ACOT": {"a": "(número)", "d": "Devolve o arco tangente de um número em radianos no intervalo 0 a Pi."}, "ACOTH": {"a": "(número)", "d": "Devolve a cotangente hiperbólica inverso de um número"}, "AGGREGATE": {"a": "(núm_função; opções; ref1; ...)", "d": "Devolve uma agregação numa lista ou base de dados"}, "ARABIC": {"a": "(texto)", "d": "Converte um número Romano em Arábico"}, "ASC": {"a": "(texto)", "d": "Em tipos de linguagem de conjuntos de carateres de bytes duplos (DBCS), a função altera os carateres de largura total (bytes duplos) para carateres de largura média (byte único)"}, "ASIN": {"a": "(núm)", "d": "Devolve o arco de seno de um número em radianos, no intervalo de -Pi/2 a Pi/2"}, "ASINH": {"a": "(núm)", "d": "Devolve o seno hiperbólico inverso de um número"}, "ATAN": {"a": "(núm)", "d": "Devolve o arco de tangente de um número em radianos, num intervalo de -Pi/2 a Pi/2"}, "ATAN2": {"a": "(núm_x; núm_y)", "d": "Devolve o arco de tangente das coordenadas x e y especificadas, em radianos, de -Pi a Pi, excluindo -Pi"}, "ATANH": {"a": "(núm)", "d": "Devolve a tangente hiperbólica inversa de um número"}, "BASE": {"a": "(número; base; [comprimento_mín])", "d": "Converte um número numa representação textual com a base dada"}, "CEILING": {"a": "(núm; significância)", "d": "Arredonda um número por excesso para o múltiplo significativo mais próximo"}, "CEILING.MATH": {"a": "(número; [significância]; [modo])", "d": "Arredonda um número por excesso para o número inteiro ou para o múltiplo significativo mais próximo"}, "CEILING.PRECISE": {"a": "(número; [significância])", "d": "Devolve um número arredondado para o número inteiro mais próximo ou para o múltiplo de significância mais próximo"}, "COMBIN": {"a": "(núm; núm_escolhido)", "d": "Devolve o número de combinações para um determinado conjunto de itens"}, "COMBINA": {"a": "(número; número_escolhido)", "d": "Devolve o número de combinações com repetições para um determinado número de itens"}, "COS": {"a": "(núm)", "d": "Devolve o cosseno de um ângulo"}, "COSH": {"a": "(núm)", "d": "Devolve o cosseno hiperbólico de um número"}, "COT": {"a": "(número)", "d": "Devolve a cotangente de um ângulo"}, "COTH": {"a": "(número)", "d": "Devolve a cotangente hiperbólica de um número"}, "CSC": {"a": "(número)", "d": "Devolve a cossecante de um ângulo"}, "CSCH": {"a": "(número)", "d": "Devolve a cossecante hiperbólica de um ângulo"}, "DECIMAL": {"a": "(número; base)", "d": "Converte uma representação textual de um número numa determinada base em um número decimal"}, "DEGREES": {"a": "(â<PERSON><PERSON>)", "d": "Converte radianos em graus"}, "ECMA.CEILING": {"a": "(núm; significância)", "d": "Arredonda um número por excesso para o múltiplo significativo mais próximo"}, "EVEN": {"a": "(núm)", "d": "Arredonda um número positivo para cima e um número negativo para baixo para o valor inteiro mais próximo"}, "EXP": {"a": "(núm)", "d": "Devolve 'e' elevado à potência de um número dado"}, "FACT": {"a": "(núm)", "d": "Devolve o fatorial de um número, igual a 1*2*3*...*núm"}, "FACTDOUBLE": {"a": "(núm)", "d": "Devolve o fatorial duplo de um número"}, "FLOOR": {"a": "(núm; significância)", "d": "Arredonda um número por defeito para o múltiplo significativo mais próximo"}, "FLOOR.PRECISE": {"a": "(número; [significância])", "d": "Devolve um número arredondado por defeito para o número inteiro mais próximo ou para o múltiplo de significância mais próximo"}, "FLOOR.MATH": {"a": "(número; [significância]; [modo])", "d": "Arredonda um número por defeito para o número inteiro ou para o múltiplo significativo mais próximo"}, "GCD": {"a": "(núm1; [núm2]; ...)", "d": "devolve o maior divisor comum"}, "INT": {"a": "(núm)", "d": "Arredonda um número por defeito até ao número inteiro mais próximo"}, "ISO.CEILING": {"a": "(número; [significância])", "d": "Devolve um número arredondado para o número inteiro mais próximo ou para o múltiplo de significância mais próximo. Independentemente do sinal do número, o número é arredondado. Contudo, se o número ou a significância for zero, é devolvido zero."}, "LCM": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o mínimo múltiplo comum"}, "LN": {"a": "(núm)", "d": "Devolve o logaritmo natural de um número"}, "LOG": {"a": "(núm; [base])", "d": "Devolve o logaritmo de um número na base especificada"}, "LOG10": {"a": "(núm)", "d": "Devolve o logaritmo de base 10 de um número"}, "MDETERM": {"a": "(matriz)", "d": "Devolve o determinante de uma matriz"}, "MINVERSE": {"a": "(matriz)", "d": "Devolve a inversa de uma matriz armazenada numa matriz"}, "MMULT": {"a": "(matriz1; matriz2)", "d": "Devolve a matriz produto de duas matrizes, uma matriz com o mesmo número de linhas que matriz1 e de colunas que matriz2"}, "MOD": {"a": "(núm; divisor)", "d": "Devolve o resto depois de um número ser dividido por Divisor"}, "MROUND": {"a": "(núm; múltiplo)", "d": "Devolve um número arredondado para o múltiplo desejado"}, "MULTINOMIAL": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o polinomial de um conjunto de números"}, "MUNIT": {"a": "(dimensão)", "d": "Devolve a matriz identidade para a dimensão especificada"}, "ODD": {"a": "(núm)", "d": "Arredonda um número positivo para cima e um número negativo para baixo até ao número ímpar inteiro mais próximo"}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON> o valor de Pi, 3,14159265358979, com uma precisão de 13 casas decimais"}, "POWER": {"a": "(núm; potência)", "d": "Devolve o resultado de um número elevado a uma potência"}, "PRODUCT": {"a": "(núm1; [núm2]; ...)", "d": "Multiplica todos os números apresentados como argumentos"}, "QUOTIENT": {"a": "(numerador; denominador)", "d": "Devolve a parte inteira de uma divisão"}, "RADIANS": {"a": "(â<PERSON><PERSON>)", "d": "Converte graus em radianos"}, "RAND": {"a": "()", "d": "Devolve um número aleatório maior ou igual a 0 e menor que 1, segundo uma distribuição uniforme (altera ao voltar a calcular)"}, "RANDARRAY": {"a": "([linhas]; [colunas]; [mín]; [máx]; [nú<PERSON><PERSON>_inteiro])", "d": "Devolve uma matriz de números aleatórios"}, "RANDBETWEEN": {"a": "(inferior; superior)", "d": "Devolve um número aleatório de entre os números especificados"}, "ROMAN": {"a": "(núm; [forma])", "d": "Converte um número árabe em romano, como texto"}, "ROUND": {"a": "(núm; núm_dígitos)", "d": "Arredonda um valor para um número de algarismos especificado"}, "ROUNDDOWN": {"a": "(núm; núm_dígitos)", "d": "Arredonda um número por defeito, em valor absoluto"}, "ROUNDUP": {"a": "(núm; núm_dígitos)", "d": "Arredonda um número por excesso, em valor absoluto"}, "SEC": {"a": "(número)", "d": "Devolve a secante de um ângulo"}, "SECH": {"a": "(número)", "d": "Devolve a secante hiperbólica de um ângulo"}, "SERIESSUM": {"a": "(x; n; m; coeficientes)", "d": "Devolve a soma de uma série de potência baseada na fórmula"}, "SIGN": {"a": "(núm)", "d": "Devolve o sinal de um número: 1, se o número for positivo, zero se o número for zero ou -1 se o número for negativo"}, "SIN": {"a": "(núm)", "d": "Devolve o seno de um ângulo"}, "SINH": {"a": "(núm)", "d": "Devolve o seno hiperbólico de um número"}, "SQRT": {"a": "(núm)", "d": "Devolve a raiz quadrada de um número"}, "SQRTPI": {"a": "(núm)", "d": "Devolve a raiz quadrada de um número a multiplicar por Pi"}, "SUBTOTAL": {"a": "(núm_função; ref1; ...)", "d": "Devolve um subtotal numa lista ou base de dados"}, "SUM": {"a": "(núm1; [núm2]; ...)", "d": "Adiciona todos os números de um intervalo de células"}, "SUMIF": {"a": "(intervalo; critérios; [intervalo_soma])", "d": "Adiciona as células especificadas por uma determinada condição ou critério"}, "SUMIFS": {"a": "(intervalo_soma; intervalo_critérios; critérios; ...)", "d": "Adiciona as células especificadas por um determinado conjunto de condições ou critérios"}, "SUMPRODUCT": {"a": "(matriz1; [matriz2]; [matriz3]; ...)", "d": "Devolve a soma dos produtos dos intervalos ou matrizes correspondentes"}, "SUMSQ": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a soma dos quadrados dos argumentos. Os argumentos podem ser números, nomes, matrizes ou referências a células que contenham números"}, "SUMX2MY2": {"a": "(matriz_x; matriz_y)", "d": "<PERSON><PERSON> as diferen<PERSON>s dos quadrados de números correspondentes em dois intervalos ou matrizes"}, "SUMX2PY2": {"a": "(matriz_x; matriz_y)", "d": "Devolve o total das somas dos quadrados de números correspondentes em dois intervalos ou matrizes"}, "SUMXMY2": {"a": "(matriz_x; matriz_y)", "d": "Soma os quadrados das diferenças de valores correspondentes em dois intervalos ou matrizes"}, "TAN": {"a": "(núm)", "d": "Devolve a tangente de um ângulo"}, "TANH": {"a": "(núm)", "d": "Devolve a tangente hiperbólica de um número"}, "TRUNC": {"a": "(núm; [núm_dígitos])", "d": "Trunca um número, tornando-o um número inteiro, ao remover a sua parte decimal ou fraccional"}, "ADDRESS": {"a": "(núm_linha; núm_coluna; [núm_abs]; [a1]; [texto_folha])", "d": "Cria uma referência de célula como texto, a partir de números de linhas e colunas especificados"}, "CHOOSE": {"a": "(núm_índice; valor1; [valor2]; ...)", "d": "Seleciona um valor ou ação a executar a partir de uma lista de valores, baseada num número de índice"}, "COLUMN": {"a": "([referência])", "d": "Devolve o número da coluna de uma referência"}, "COLUMNS": {"a": "(matriz)", "d": "Devolve o número de colunas de uma matriz ou referência"}, "FORMULATEXT": {"a": "(referência)", "d": "Devolve uma fórmula como uma cadeia"}, "HLOOKUP": {"a": "(valor_proc; matriz_tabela; núm_índice_linha; [procurar_intervalo])", "d": "Procura um valor na linha superior de uma tabela ou matriz de valores e devolve o valor na mesma coluna de uma linha especificada"}, "HYPERLINK": {"a": "(local_vínculo; [nome_abrev])", "d": "Cria um atalho ou salto que abre um documento armazenado no disco rígido, num servidor da rede ou na Internet"}, "INDEX": {"a": "(matriz; núm_linha; [núm_coluna]!referência; núm_linha; [núm_coluna]; [núm_area])", "d": "Devolve um valor ou a referência da célula na interseção de uma linha e coluna em particular, num determinado intervalo"}, "INDIRECT": {"a": "(ref_texto; [a1])", "d": "Devolve uma referência especificada por um valor de texto"}, "LOOKUP": {"a": "(valor_proc; vetor_proc; [vetor_result]!valor_proc; matriz)", "d": "Devolve um valor, quer de um intervalo de uma linha ou de uma coluna, quer de uma matriz. Fornecido para compatibilidade com conversões anteriores"}, "MATCH": {"a": "(valor_proc; matriz_proc; [tipo_corresp])", "d": "Devolve a posição relativa de um item numa matriz que corresponde a um valor especificado por uma ordem especificada"}, "OFFSET": {"a": "(referência; linhas; colunas; [altura]; [largura])", "d": "Devolve, a partir de uma célula ou intervalo de células, uma referência a um intervalo"}, "ROW": {"a": "([referência])", "d": "Devolve o número da linha de uma referência"}, "ROWS": {"a": "(matriz)", "d": "Devolve o número de linhas numa referência ou matriz"}, "TRANSPOSE": {"a": "(matriz)", "d": "Converte um intervalo vertical de células para um intervalo horizontal, ou vice-versa"}, "UNIQUE": {"a": "(matriz; [por_col]; [exatamente_uma_vez])", "d": "Devolve os valores exclusivos de um intervalo ou matriz."}, "VLOOKUP": {"a": "(valor_proc; matriz_tabela; núm_índice_coluna; [procurar_intervalo])", "d": "Procura um valor na coluna mais à esquerda de uma tabela e devolve um valor na mesma linha de uma dada coluna. Por predefinição, a tabela tem de ser ordenada por ordem ascendente"}, "XLOOKUP": {"a": "(valor_pesquisa; matriz_pesquisa; matriz_devolver; [se_não_for_encontrado]; [modo_corresp]; [modo_pesquisa])", "d": "Procura uma correspondência num intervalo ou matriz e devolve o item correspondente de um segundo intervalo ou matriz. Por predefinição, é utilizada uma correspondência exata"}, "CELL": {"a": "(tipo_info; [referência])", "d": "Devolve informações sobre a formatação, localização ou conteúdo de uma célula"}, "ERROR.TYPE": {"a": "(val_erro)", "d": "Devolve um número que corresponde a um valor de erro."}, "ISBLANK": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se valor se referir a uma célula vazia"}, "ISERR": {"a": "(valor)", "d": "Verifica se um valor é um erro diferente de #N/D (valor não disponível) e devolve VERDADEIRO ou FALSO"}, "ISERROR": {"a": "(valor)", "d": "Verifica se um valor é um erro e devolve VERDADEIRO ou FALSO"}, "ISEVEN": {"a": "(núm)", "d": "Devolve VERDADEIRO se o número for par"}, "ISFORMULA": {"a": "(referência)", "d": "Verifica se uma referência se relaciona com uma célula que contém uma fórmula e devolve VERDADEIRO ou FALSO"}, "ISLOGICAL": {"a": "(valor)", "d": "Verifica se Valor é um valor lógico (VERDADEIRO ou FALSO) e devolve VERDADEIRO ou FALSO"}, "ISNA": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se for um valor de erro #N/D (valor não disponível)"}, "ISNONTEXT": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se um valor não for texto (células em branco não são texto)"}, "ISNUMBER": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se valor for um número"}, "ISODD": {"a": "(núm)", "d": "Devolve VERDADEIRO se o número for ímpar"}, "ISREF": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se um valor for uma referência"}, "ISTEXT": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se valor for texto"}, "N": {"a": "(valor)", "d": "Converte um valor não numérico para um número, datas para números de série, VERDADEIRO para 1, o resto para 0 (zero)"}, "NA": {"a": "()", "d": "Devolve o valor de erro #N/D (valor não disponível)"}, "SHEET": {"a": "([valor])", "d": "Devolve o número de folha da folha referenciada"}, "SHEETS": {"a": "([referência])", "d": "Devolve o número de folhas numa referência"}, "TYPE": {"a": "(valor)", "d": "Devolve um número inteiro que representa o tipo de dados de um valor: número = 1; texto = 2; valor lógico = 4; valor de erro = 16; matriz = 64; dados compostos = 128"}, "AND": {"a": "(valor_lógico1; [valor_lógico2]; ...)", "d": "Devolve VERDADEIRO, se todos os argumentos forem VERDADEIRO;"}, "FALSE": {"a": "()", "d": "Devolve o valor lógico FALSO"}, "IF": {"a": "(teste_lógico; [valor_se_verdadeiro]; [valor_se_falso])", "d": "Devolve um valor se a condição especificada equivaler a VERDADEIRO e outro valor se equivaler a FALSO"}, "IFS": {"a": "(teste_lógico; valor_se_verdadeiro; ...)", "d": "Verifica se uma ou mais condições são cumpridas e devolve um valor correspondente à primeira condição VERDADEIRA"}, "IFERROR": {"a": "(valor; valor_se_erro)", "d": "Devolve valor_se_erro se a expressão for um erro e o valor da própria expressão não o for"}, "IFNA": {"a": "(valor; valor_se_nd)", "d": "Devolve o valor especificado se a expressão devolver #N/D, caso contrário devolve o resultado da expressão"}, "NOT": {"a": "(valor_lógico)", "d": "Altera FALSO para VERDADEIRO e VERDADEIRO para FALSO"}, "OR": {"a": "(valor_lógico1; [valor_lógico2]; ...)", "d": "Verifica se algum dos argumentos é VERDADEIRO e devolve VERDADEIRO ou FALSO. Devolve FALSO se todos os argumentos forem FALSO"}, "SWITCH": {"a": "(expressão; valor1; resultado1; [predefinição_ou_valor2]; [resultado2]; ...)", "d": "Avalia uma expressão em relação a uma lista de valores e devolve o resultado do primeiro valor correspondente. Se não existir correspondência, é devolvido um valor predefinido opcional"}, "TRUE": {"a": "()", "d": "Devolve o valor lógico VERDADEIRO"}, "XOR": {"a": "(lógica1; [lógica2]; ...)", "d": "Devolve um \"Ou Exclusivo\" lógico de todos os argumentos"}, "TEXTBEFORE": {"a": "(texto, delimitador, [núm_instância], [corresp_mod], [corresp_final], [se_não_for_encontrado])", "d": "Devolve texto que está antes dos carateres delimitadores."}, "TEXTAFTER": {"a": "(texto, delimitador, [núm_instância], [corresp_mod], [corresp_final], [se_não_for_encontrado])", "d": "Devolve texto que está depois dos carateres delimitadores."}, "TEXTSPLIT": {"a": "(texto, delimitador_de_coluna, [delimitador_de_linha], [corresp], [modo], [preencher_com])", "d": "Divide o texto em linhas ou colunas usando delimitadores."}, "WRAPROWS": {"a": "(vetor, contagem_de_moldagens, [preencher_com])", "d": "Molda um vetor de linha ou coluna após um número especificado de valores."}, "VSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Empilha verticalmente várias matrizes numa única matriz."}, "HSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Empilha horizontalmente várias matrizes numa única matriz."}, "CHOOSEROWS": {"a": "(matriz, núm_linha1, [núm_linha2], ...)", "d": "Devolve linhas a partir de uma matriz ou referência."}, "CHOOSECOLS": {"a": "(matriz, núm_coluna1, [núm_coluna2], ...)", "d": "Devolve colunas a partir de uma matriz ou referência."}, "TOCOL": {"a": "(matriz, [ignorar], [analisar_por_coluna])", "d": "Devolve a matriz como uma coluna."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Devolve a matriz como uma linha."}, "WRAPCOLS": {"a": "(vetor, contagem_de_moldagens, [preencher_com])", "d": "Molda um vetor de linha ou coluna após um número especificado de valores."}, "TAKE": {"a": "(matriz, lin<PERSON>, [colunas])", "d": "Devol<PERSON> linhas ou colunas a partir do início ou fim da matriz."}, "DROP": {"a": "(matriz, lin<PERSON>, [colunas])", "d": "Remove linhas ou colunas a partir do início ou fim da matriz."}}
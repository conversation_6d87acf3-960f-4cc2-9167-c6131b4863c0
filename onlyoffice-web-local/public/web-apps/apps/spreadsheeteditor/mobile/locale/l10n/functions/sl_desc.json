{"DATE": {"a": "(leto; mesec; dan)", "d": "<PERSON><PERSON> število, ki predstavlja datum v kodi za datum in uro."}, "DATEDIF": {"a": "(za<PERSON><PERSON>ni_datum; končni_datum; enota)", "d": "Izračuna število dni, mesecev ali let med dvema datumoma"}, "DATEVALUE": {"a": "(besedilo_datuma)", "d": "Pretvori datum v obliki besedila v število, ki predstavlja datum v kodi za datum in uro."}, "DAY": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "Vrne dan v mesecu, število med 1 in 31."}, "DAYS": {"a": "(končni_datum; začetni_datum)", "d": "<PERSON><PERSON>o dni med dvema datumoma."}, "DAYS360": {"a": "(za<PERSON><PERSON>ni_datum; končni_datum; [metoda])", "d": "<PERSON><PERSON> število dni med dvema datumoma v 360-dnevnem letu (dvanajst 30-dnevnih mesecev)."}, "EDATE": {"a": "(za<PERSON><PERSON>ni_datum; meseci)", "d": "<PERSON>rne zaporedno število datum<PERSON>, ki je do<PERSON><PERSON><PERSON>tevilo mesecev pred ali po začetnem datumu"}, "EOMONTH": {"a": "(za<PERSON><PERSON>ni_datum; meseci)", "d": "Vrne zaporedno število zadnjega dneva meseca pred ali po navedenem številu mesecev"}, "HOUR": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "<PERSON><PERSON>, ki je število med 0 (<PERSON><PERSON><PERSON><PERSON>) in 23 (23:00)."}, "ISOWEEKNUM": {"a": "(datum)", "d": "Vrne število ISO številke tedna v letu za dani datum"}, "MINUTE": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "<PERSON><PERSON> minute, ki so c<PERSON> med 0 in 59."}, "MONTH": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "<PERSON><PERSON> mese<PERSON>, ki je število od 1 (januar) do 12 (december)."}, "NETWORKDAYS": {"a": "(za<PERSON><PERSON>ni_datum; končni_datum; [prazniki])", "d": "<PERSON><PERSON> celot<PERSON>h delovnih dni med dvema datumoma"}, "NETWORKDAYS.INTL": {"a": "(za<PERSON><PERSON>ni_datum; končni_datum; [vikend]; [prazniki])", "d": "<PERSON><PERSON> celo<PERSON>h delovnih dni med dvema datumoma s parametri vikendov po meri."}, "NOW": {"a": "()", "d": "<PERSON>rne trenutni datum in uro, oblikovano kot datum in ura."}, "SECOND": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "<PERSON><PERSON> se<PERSON>, ki so celo <PERSON> med 0 in 59."}, "TIME": {"a": "(ura; minuta; sekunda)", "d": "Pretvori ure, minute in sekunde, navedene kot števila, v zaporedno število v časovni obliki."}, "TIMEVALUE": {"a": "(besedilo_ure)", "d": "Pretvori uro, zapisano v besedilni obliki, v zaporedno število za uro; števila od 0 (12:00:00) do 0,999988426 (23:59:59). Po vnosu formule oblikujte število z obliko zapisa ure."}, "TODAY": {"a": "()", "d": "<PERSON><PERSON> trenutni datum, oblikovan kot datum."}, "WEEKDAY": {"a": "(se<PERSON><PERSON><PERSON>_številka; [vrsta_rezultata])", "d": "Vrne število od 1 do 7, ki označuje dan v tednu datuma."}, "WEEKNUM": {"a": "(se<PERSON><PERSON><PERSON>_številka; [vrsta_vrednosti])", "d": "Vrne število tednov v letu"}, "WORKDAY": {"a": "(za<PERSON><PERSON><PERSON>_datum; dnevi; [prazniki])", "d": "Vrne zaporedno številko datuma pred ali po navedenem številu delovnih dni"}, "WORKDAY.INTL": {"a": "(za<PERSON><PERSON><PERSON>_datum; dnevi; [vikend]; [prazniki])", "d": "Vrne zaporedno številko datuma pred ali po navedenem številu delovnih dni s parametri vikendov po meri"}, "YEAR": {"a": "(se<PERSON><PERSON>ska_številka)", "d": "<PERSON><PERSON> leto da<PERSON>, ki je celo števil<PERSON> v intervalu od 1900 - 9999."}, "YEARFRAC": {"a": "(za<PERSON><PERSON><PERSON>_datum; končni_datum; [osnova])", "d": "<PERSON><PERSON> ulomek leta, ki predstavlja število celih dni med začetnim in končnim datumom"}, "BESSELI": {"a": "(x; n)", "d": "Vrne spremenjeno Besselovo funkcijo In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Vrne Besselovo funkcijo Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Vrne spremenjeno Besselovo funkcijo Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "<PERSON>rne Besselovo funkcijo Yn(x)"}, "BIN2DEC": {"a": "(števil<PERSON>)", "d": "Pretvori dvojiško število v desetiško"}, "BIN2HEX": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori dvojiško število v šestnajstiško"}, "BIN2OCT": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori dvojiško število v osmiško"}, "BITAND": {"a": "(število1; število2)", "d": "Vrne bitno vrednost »And« dveh števil"}, "BITLSHIFT": {"a": "(število; shift_amount)", "d": "<PERSON><PERSON>, ki jih biti shift_amount premaknejo na levo"}, "BITOR": {"a": "(število1; število2)", "d": "Vrne bitno vrednost »Or« dveh števil"}, "BITRSHIFT": {"a": "(število; shift_amount)", "d": "<PERSON><PERSON>, ki jih biti shift_amount premaknejo na desno"}, "BITXOR": {"a": "(number1; number2)", "d": "Vrne bitno vrednost »Exclusive Or« dveh števil"}, "COMPLEX": {"a": "(realno_št; i_št; [pripona])", "d": "Pretvori realne in imaginarne koeficiente v kompleksna števila"}, "CONVERT": {"a": "(š<PERSON><PERSON><PERSON>; od_enote; do_enote)", "d": "Pretvori število iz enega merskega sistema v drugega"}, "DEC2BIN": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori desetiško število v dvojiško"}, "DEC2HEX": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori desetiško število v šestnajstiško"}, "DEC2OCT": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori desetiško število v osmiško"}, "DELTA": {"a": "(število1; [število2])", "d": "<PERSON><PERSON><PERSON><PERSON>, ali sta dve <PERSON><PERSON> enaki"}, "ERF": {"a": "(spodnja_meja; [zgor<PERSON>_meja])", "d": "<PERSON><PERSON> funk<PERSON> napake"}, "ERF.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON> funk<PERSON> napake"}, "ERFC": {"a": "(x)", "d": "<PERSON>rne komplementarno funkcijo napake"}, "ERFC.PRECISE": {"a": "(X)", "d": "<PERSON>rne komplementarno funkcijo napake"}, "GESTEP": {"a": "(število; [korak])", "d": "<PERSON><PERSON><PERSON><PERSON>, ali je število večje od mejne vrednosti"}, "HEX2BIN": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori šestnajstiško število v dvojiško"}, "HEX2DEC": {"a": "(števil<PERSON>)", "d": "Pretvori šestnajstiško število v desetiško"}, "HEX2OCT": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori šestnajstiško število v osmiško"}, "IMABS": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne absolutno vrednost (modul) kompleksnega števila"}, "IMAGINARY": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne imaginarni koeficient kompleksnega števila"}, "IMARGUMENT": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne argument q, kot, izražen v radianih"}, "IMCONJUGATE": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne kompleksno izpeljanko kompleksnega števila"}, "IMCOS": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON> kosinus kompleksnega števila"}, "IMCOSH": {"a": "(števil<PERSON>)", "d": "Vrne hiperbolični kosinus kompleksnega števila"}, "IMCOT": {"a": "(inumber)", "d": "Vrne kotangens kompleksnega števila"}, "IMCSC": {"a": "(inumber)", "d": "Vrne kosekans kompleksnega števila"}, "IMCSCH": {"a": "(inumber)", "d": "Vrne hiperbolični kosekans kompleksnega števila"}, "IMDIV": {"a": "(ištevilo1; ištevilo2)", "d": "Vrne količnik dveh kompleksnih števil"}, "IMEXP": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne eksponent kompleksnega števila"}, "IMLN": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne naravni logaritem kompleksnega števila"}, "IMLOG10": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne logaritem z osnovo 10 kompleksnega števila"}, "IMLOG2": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne logaritem z osnovo 2 kompleksnega števila"}, "IMPOWER": {"a": "(i<PERSON><PERSON><PERSON><PERSON>; število)", "d": "Vrne kompleksno število potencirano na celo število"}, "IMPRODUCT": {"a": "(iš<PERSON>vilo1; [ištevilo2]; ...)", "d": "Vrne zmnožek 1 do 255 kompleksnih števil"}, "IMREAL": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne realni koeficient kompleksnega števila"}, "IMSEC": {"a": "(inumber)", "d": "Vrne sekans kompleksnega števila"}, "IMSECH": {"a": "(inumber)", "d": "Vrne hiperbolični sekans kompleksnega števila"}, "IMSIN": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne sinus kompleksnega števila"}, "IMSINH": {"a": "(števil<PERSON>)", "d": "Vrne hiperbolični sinus kompleksnega števila"}, "IMSQRT": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Vrne kvadratni koren kompleksnega števila"}, "IMSUB": {"a": "(ištevilo1; ištevilo2)", "d": "Vrne razliko dveh kompleksnih števil"}, "IMSUM": {"a": "(iš<PERSON>vilo1; [ištevilo2]; ...)", "d": "Vrne vsoto kompleksnih števil"}, "IMTAN": {"a": "(inumber)", "d": "Vrne tangens kompleksnega števila"}, "OCT2BIN": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori osmiško število v dvojiško"}, "OCT2DEC": {"a": "(števil<PERSON>)", "d": "Pretvori osmiško število v desetiško"}, "OCT2HEX": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [mesta])", "d": "Pretvori osmiško število v šestnajstiško"}, "DAVERAGE": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Izračuna povprečje vrednosti v stolpcu, na seznamu ali v zbirki podatkov, ki ustrezajo navedenim pogojem."}, "DCOUNT": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Prešteje celice s števili v polju (stolpcu) zapisov zbirke podatkov, ki ustrezajo pogojem, ki ste jih določili."}, "DCOUNTA": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Prešteje neprazne celice v polju (stolpcu) zapisov zbirke podatkov, ki ustrezajo pogojem, ki ste jih določili."}, "DGET": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "<PERSON>z zbirke podatkov izvleče en zapis, ki ustreza pogojem, ki ste jih določili."}, "DMAX": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Vrne največje število v polju (stolpcu) zapisov zbirke podatkov, ki ustreza pogojem, ki ste jih določili."}, "DMIN": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Vrne najmanjše število v polju (stolpcu) zapisov zbirke podatkov, ki ustreza pogojem, ki ste jih določili."}, "DPRODUCT": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Zmnoži vrednosti v polju (stolpcu) zapisov zbirke podatkov, ki ustrezajo pogojem, ki ste jih določili."}, "DSTDEV": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Na osnovi izbranih vnosov zbirke podatkov oceni standardni odklon. Ocena temelji na vzorcu."}, "DSTDEVP": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Izračuna standardni odklon, ki temelji na celotni populaciji izbranih vnosov zbirke podatkov."}, "DSUM": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Sešteje števila v polju (stolpcu) zapisov zbirke podatkov, ki ustrezajo pogojem, ki ste jih določili."}, "DVAR": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Na osnovi izbranih vnosov zbirke podatkov oceni varianco. Ocena temelji na vzorcu."}, "DVARP": {"a": "(zbir<PERSON>_podatkov; polje; pogoji)", "d": "Izračuna varianco celotne populacije izbranih vnosov zbirke podatkov."}, "CHAR": {"a": "(števil<PERSON>)", "d": "<PERSON><PERSON> z<PERSON>, ki ga določa kodno število, iz nabora znakov vašega računalnika."}, "CLEAN": {"a": "(be<PERSON><PERSON>)", "d": "<PERSON>z besedila odstrani vse znake, ki se ne tiskajo."}, "CODE": {"a": "(be<PERSON><PERSON>)", "d": "Vrne številsko kodo prvega znaka v besedilnem nizu, v naboru znakov, ki ga uporablja računalnik."}, "CONCATENATE": {"a": "(besedilo1; [besedilo2]; ...)", "d": "Združi več besedilnih nizov v enega."}, "CONCAT": {"a": "(text1; ...)", "d": "Zdr<PERSON>ži seznam ali obseg besedilnih nizov"}, "DOLLAR": {"a": "(š<PERSON><PERSON><PERSON>; [decimalna_mesta])", "d": "Pretvori število v besedilo z uporabo valutne oblike."}, "EXACT": {"a": "(besedilo1; besedilo2)", "d": "<PERSON><PERSON><PERSON>, ali sta dva besedilna niza popolnoma enaka, in vrne TRUE ali FALSE. EXACT lo<PERSON>i velike in male črke."}, "FIND": {"a": "(iskano_besedilo; v_besedilu; [za<PERSON><PERSON><PERSON>_znak])", "d": "Vrne začetni položaj besedilnega niza v drugem besedilnem nizu. FIND loči velike in male črke."}, "FINDB": {"a": "(iskano_besedilo; v_besedilu; [za<PERSON><PERSON><PERSON>_znak])", "d": "Je mogoče en besedilni niz poiskati z drugim in vrniti število začetnih mest prvega besedilnega niza iz prvega znaka drugega besedilnega niza, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japon<PERSON><PERSON><PERSON>, kit<PERSON>š<PERSON>ina in korejščina"}, "FIXED": {"a": "(š<PERSON><PERSON><PERSON>; [decimalna_mesta]; [brez_vejic])", "d": "Zaokroži številko na določeno število decimalnih mest in vrne rezultat kot besedilo z vejicami ali brez njih."}, "LEFT": {"a": "(besed<PERSON>; [št_znakov])", "d": "Vrne določeno število znakov od začetka besedilnega niza."}, "LEFTB": {"a": "(besed<PERSON>; [št_znakov])", "d": "Vrne prvi znak ali znake v nizu besedila na osnovi navedenega števila bajtov, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japonščina, kitajščina in korejščina"}, "LEN": {"a": "(be<PERSON><PERSON>)", "d": "Vrne število znakov v besedilnem nizu."}, "LENB": {"a": "(be<PERSON><PERSON>)", "d": "Vrne število b<PERSON>, ki predstavljajo znake v besedilnem nizu, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japonščina, kitajščina in korejščina"}, "LOWER": {"a": "(be<PERSON><PERSON>)", "d": "Pretvori vse črke v besedilnem nizu v male črke."}, "MID": {"a": "(besed<PERSON>; prvi_znak; št_znakov)", "d": "Vrne znake iz sredine besedilnega niza, če sta podana začetni položaj in dolžina."}, "MIDB": {"a": "(besed<PERSON>; prvi_znak; št_znakov)", "d": "Vrne določeno število znakov iz besedilnega niza z začetkom pri navedenem položaju in na osnovi navedenega števila bajtov, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japonščina, kitajščina in korejščina"}, "NUMBERVALUE": {"a": "(besedilo; [decimalno_ločilo]; [sku<PERSON>_ločilo])", "d": "Pretvori besedilo v številko, neodvisno od lokalnega načina"}, "PROPER": {"a": "(be<PERSON><PERSON>)", "d": "Pretvori besedilni niz v velike in male črke; vsako prvo črko v besedi v veliko začetnico, vse preostale črke pa pretvori v male."}, "REPLACE": {"a": "(staro_besedilo; mesto_znaka; št_znakov; novo_besedilo)", "d": "Zamenja del besedilnega niza z drugim besedilnim nizom."}, "REPLACEB": {"a": "(staro_besedilo; mesto_znaka; št_znakov; novo_besedilo)", "d": "Nadomesti del besedilnega niza z drugim besedilnim nizom glede na navedeno število bajtov, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japonščina, kitajščina in korejščina"}, "REPT": {"a": "(besedilo; št_ponovitev)", "d": "<PERSON><PERSON><PERSON> be<PERSON>ilo <PERSON>, kolikor krat je navedeno. Uporabite REPT, če želite zapolniti celico z več ponovitvami besedilnega niza."}, "RIGHT": {"a": "(besed<PERSON>; [št_znakov])", "d": "Vrne določeno število znakov od konca besedilnega niza."}, "RIGHTB": {"a": "(besed<PERSON>; [št_znakov])", "d": "Vrne zadnji znak ali znake v besedilnem nizu, in sicer na podlagi navedenega števila bajtov, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japonščina, kitajščina in korejščina"}, "SEARCH": {"a": "(iskano_besedilo; v_besedilu; [št_za<PERSON>etka])", "d": "<PERSON><PERSON>vil<PERSON> znaka, kjer je prvič – gledano z leve proti desni - najden poseben znak ali besedilni niz (ne loči velikih in malih črk)."}, "SEARCHB": {"a": "(iskano_besedilo; v_besedilu; [št_za<PERSON>etka])", "d": "Je mogoče en besedilni niz poiskati z drugim in vrniti število začetnih mest prvega besedilnega niza iz prvega znaka drugega besedilnega niza, uporablja v jezikih z dvobajtnim naborom znakov (DBCS) - japon<PERSON><PERSON><PERSON>, kit<PERSON>š<PERSON>ina in korejščina"}, "SUBSTITUTE": {"a": "(besedilo; staro_besedilo; novo_besedilo; [št_primerka])", "d": "Zamenja staro besedilo z novim v besedilnem nizu."}, "T": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vred<PERSON>t besedilo; če je, vrne besedilo, če ni, vrne dvojne narekovaje (prazno besedilo)."}, "TEXT": {"a": "(vrednost; oblika_besedila)", "d": "Vrednost pretvori v besedilo v točno določeni obliki zapisa števila"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "Združi seznam ali obseg besedilnih nizov z ločilom"}, "TRIM": {"a": "(be<PERSON><PERSON>)", "d": "<PERSON>z besedilnega niza odstrani vse presledke, razen enojnih presledkov med besedami."}, "UNICHAR": {"a": "(števil<PERSON>)", "d": "Vrne znak Unicode, na katerega se sklicuje številska vrednost"}, "UNICODE": {"a": "(be<PERSON><PERSON>)", "d": "<PERSON><PERSON> (kodno točko), ki ustreza prvemu znaku besedila"}, "UPPER": {"a": "(be<PERSON><PERSON>)", "d": "Pretvori besedilni niz v vse velike črke."}, "VALUE": {"a": "(be<PERSON><PERSON>)", "d": "Pretvori besedilni niz, ki predstavlja število, v število."}, "AVEDEV": {"a": "(število1; [število2]; ...)", "d": "Vrne povprečje absolutnih odstopanj podatkovnih točk od srednje vrednosti. Argumenti so lah<PERSON> ali imena, mat<PERSON><PERSON> ali <PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "AVERAGE": {"a": "(število1; [število2]; ...)", "d": "<PERSON><PERSON> aritmetično povprečno vrednost njegovih argumentov, ki so <PERSON><PERSON><PERSON>, imena, matrike al<PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "AVERAGEA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Vrne aritmetično povprečno vrednost svojih argumentov. Besedilo in logična vrednost FALSE v argumentih se ovrednoti kot 0, logična vrednost TRUE pa kot 1. <PERSON>rg<PERSON><PERSON> so <PERSON><PERSON><PERSON>, im<PERSON>, matrike ali <PERSON>."}, "AVERAGEIF": {"a": "(obseg; pogoji; [obseg_za_povprečje])", "d": "<PERSON>j<PERSON> povprečje (aritmetično povprečno vrednost) za celice, navedene z danim pogojem ali kriterijem"}, "AVERAGEIFS": {"a": "(obseg_za_povprečje; obseg_pogojev; pogoji; ...)", "d": "Najde povprečje (aritmetično povprečno vrednost) za celice, navedene v danem nizu pogojev ali kriterijev"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Vrne kumulativno porazdelitev beta gostote verjetnosti"}, "BETAINV": {"a": "(verjetnost; alfa; beta; [A]; [B])", "d": "Vrne inverzno kumulativno porazdelitev beta gostote verjetnosti (BETADIST)."}, "BETA.DIST": {"a": "(x; alfa; beta; kumulativno; [A]; [B])", "d": "Vrne beta porazdelitev verjetnosti."}, "BETA.INV": {"a": "(verjetnost; alfa; beta; [A]; [B])", "d": "Vrne inverzno kumulativno beta porazdelitev gostote verjetnosti (BETA.DIST)."}, "BINOMDIST": {"a": "(število_s; poskusi; verjetnost_s; kumulativno)", "d": "Vrne posamezno binomsko porazdelitveno verjetnost."}, "BINOM.DIST": {"a": "(število_s; poskusi; verjetnost_s; kumulativno)", "d": "Vrne posamezno binomsko porazdelitveno verjetnost."}, "BINOM.DIST.RANGE": {"a": "(poskusi; verjetnost_s; število_s; [število_s2])", "d": "Vrne verjetnost preskusnega rezultata z binomsko porazdelitvijo"}, "BINOM.INV": {"a": "(poskusi; verjetnost_s; alfa)", "d": "<PERSON><PERSON> najmanj<PERSON><PERSON>, za katero je kumulativna binomska porazdelitev večja ali enaka vrednosti kriterija."}, "CHIDIST": {"a": "(x; stop_prostosti)", "d": "Vrne verjetnost dvorepe porazdelitve Hi-kvadrat."}, "CHIINV": {"a": "(verjetnost; stop_prostosti)", "d": "Vrne inverzno verjetnost dvorepe porazdelitve Hi-kvadrat."}, "CHITEST": {"a": "(dejan<PERSON>_obseg; pri<PERSON><PERSON><PERSON>ni_obseg)", "d": "Vrne preizkus neodvisnosti: vrednost iz porazdelitve hi-kvadrat za statistične in ustrezne stopnje prostosti."}, "CHISQ.DIST": {"a": "(x; stop_prostosti; kumula<PERSON>vno)", "d": "Vrne levorepo verjetnost porazdelitve hi-kvadrat."}, "CHISQ.DIST.RT": {"a": "(x; stop_prostosti)", "d": "Vrne levorepo verjetnost porazdelitve hi-kvadrat."}, "CHISQ.INV": {"a": "(verjetnost; stop_prostosti)", "d": "Vrne inverzno verjetnost levorepe porazdelitve hi-kvadrat."}, "CHISQ.INV.RT": {"a": "(verjetnost; stop_prostosti)", "d": "Vrne inverzno verjetnost desnorepe porazdelitve hi-kvadrat."}, "CHISQ.TEST": {"a": "(dejan<PERSON>_obseg; pri<PERSON><PERSON><PERSON>ni_obseg)", "d": "<PERSON><PERSON> preskus neodvisnosti: vrednost iz porazdelitve hi-kvadrat za statistične in ustrezne stopnje prostosti."}, "CONFIDENCE": {"a": "(alfa; standardni_odklon; velikost)", "d": "Vrne interval zaupanja za populacijsko srednjo vrednost"}, "CONFIDENCE.NORM": {"a": "(alfa; standardni_odklon; velikost)", "d": "Z normalno porazdelitvijo vrne interval zaupanja za populacijsko srednjo vrednost."}, "CONFIDENCE.T": {"a": "(alfa; standardni_odklon; velikost)", "d": "S Studentovo t-porazdelitvijo vrne interval zaupanja za populacijsko srednjo vrednost."}, "CORREL": {"a": "(matrika1; matrika2)", "d": "<PERSON><PERSON> k<PERSON>lacijski koeficient med dvema naboroma podatkov."}, "COUNT": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Prešteje celice v obsegu, ki vsebujejo števila"}, "COUNTA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Prešteje neprazne celice v obsegu"}, "COUNTBLANK": {"a": "(obseg)", "d": "Izračuna število praznih celic v navedenem obsegu."}, "COUNTIF": {"a": "(obseg; pogoji)", "d": "Prešteje celice v obsegu, ki se ujemajo z danim pogojem."}, "COUNTIFS": {"a": "(obseg_pogojev; pogoji; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>, ki jih navaja dani niz pogojev ali kriterijev"}, "COVAR": {"a": "(matrika1; matrika2)", "d": "<PERSON><PERSON> k<PERSON>, ki je povprečje produktov odklonov za vsak par podatkovnih točk v dveh podatkovnih množicah."}, "COVARIANCE.P": {"a": "(matrika1; matrika2)", "d": "Vrne populacijsko kovarianco, ki je povprečje produktov odklonov za vsak par podatkovnih točk v dveh podatkovnih množicah."}, "COVARIANCE.S": {"a": "(matrika1; matrika2)", "d": "Vrne vzorčno kovarianco, ki je povprečje rezultatov odklonov za vsak par podatkovnih točk v dveh podatkovnih množicah."}, "CRITBINOM": {"a": "(poskusi; verjetnost_s; alfa)", "d": "<PERSON><PERSON> najmanj<PERSON><PERSON>, za katero je kumulativna binomska porazdelitev večja ali enaka vrednosti kriterija."}, "DEVSQ": {"a": "(število1; [število2]; ...)", "d": "Vrne vsoto kvadratov odklonov podatkovnih točk iz njihovih vzorčnih srednjih vrednosti."}, "EXPONDIST": {"a": "(x; lambda; kumulativno)", "d": "Vrne eksponentno porazdelitev."}, "EXPON.DIST": {"a": "(x; lambda; kumulativno)", "d": "Vrne eksponentno porazdelitev."}, "FDIST": {"a": "(x; stop_prostosti1; stop_prostosti2)", "d": "Vrne F-porazdelitev (dvorepo) verjetnosti (stopnja razpršenosti) za dve podatkovni množici."}, "FINV": {"a": "(verjetnost; stop_prostosti1; stop_prostosti2)", "d": "Vrne inverzno verjetnostno F-porazdelitev (dvorepo): če p = FDIST(x,...), potem FINV(p,...) = x."}, "FTEST": {"a": "(matrika1; matrika2)", "d": "Vrne rezultat F-preizkusa, ki je dvor<PERSON>a verjetnost, da se varianci argumentov »matrika1« in »matrika2« bistveno ne razlikujeta."}, "F.DIST": {"a": "(x; stop_prostosti1; stop_prostosti2; kumula<PERSON>vno)", "d": "Vrne F-porazdelitev (levorepo) verjetnosti (stopnja razpršenosti) za dve podatkovni množici."}, "F.DIST.RT": {"a": "(x; stop_prostosti1; stop_prostosti2)", "d": "Vrne F-porazdelitev (desnorepo) verjetnosti (stopnja razpršenosti) za dve podatkovni množici."}, "F.INV": {"a": "(verjetnost; stop_prostosti1; stop_prostosti2)", "d": "Vrne inverzno F verjetnostno (levorepo) porazdelitev: če p = F.DIST(x, ...), potem F.INV(p, ...) = x."}, "F.INV.RT": {"a": "(verjetnost; stop_prostosti1; stop_prostosti2)", "d": "Vrne inverzno F verjetnostno (desnorepo) porazdelitev: če p = F.DIST.RT(x, ...), potem F.INV.RT(p, ...) = x."}, "F.TEST": {"a": "(matrika1; matrika2)", "d": "Vrne rezultat preskusa F, ki je dvorepa verjetnost, da se varianci argumentov »matrika1« in »matrika2« bistveno ne razlikujeta."}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON> transformacijo."}, "FISHERINV": {"a": "(y)", "d": "Vrne inverzno Fischerjevo transformacijo: če y = FISHER(x), potem FISHERINV(y) = x."}, "FORECAST": {"a": "(x; known_ys; known_xs)", "d": "Izračuna ali predvidi bodočo vrednost vzdolž linearnega trenda z uporabo obstoječih vrednosti."}, "FORECAST.ETS": {"a": "(cil<PERSON>i_datum; vred<PERSON><PERSON>; časovna_premica; [se<PERSON><PERSON><PERSON>]; [do<PERSON><PERSON><PERSON><PERSON>_podatkov]; [združevanje])", "d": "Vrne predvideno vrednost za določen prihodnji datum tako, da uporabi metodo eksponentnega glajenja."}, "FORECAST.ETS.CONFINT": {"a": "(cil<PERSON><PERSON>_datum; vred<PERSON>ti; č<PERSON>ovna_premica; [raven_z<PERSON><PERSON><PERSON>]; [se<PERSON><PERSON><PERSON>]; [dokon<PERSON><PERSON><PERSON>_podatkov]; [zdr<PERSON>ževanje])", "d": "Vrne interval zanesljivosti za vrednost napovedi za določen ciljni datum."}, "FORECAST.ETS.SEASONALITY": {"a": "(v<PERSON><PERSON><PERSON>; časovna_premica; [do<PERSON><PERSON><PERSON><PERSON>_podatkov]; [zdr<PERSON><PERSON>evan<PERSON>])", "d": "Vrne dolžino ponavljajočega se vzorca, ki ga aplikacijo zazna za določen časovni niz."}, "FORECAST.ETS.STAT": {"a": "(v<PERSON><PERSON>ti; časovna_premica; vrsta_statistike; [se<PERSON><PERSON><PERSON>]; [do<PERSON><PERSON><PERSON><PERSON>_podatkov]; [zdr<PERSON><PERSON>evanje])", "d": "Vrne zahtevane statistične podatke za napoved."}, "FORECAST.LINEAR": {"a": "(x; znani_y-i; znani_x-i)", "d": "Izračuna ali predvidi bodočo vrednost vzdolž linearnega trenda z uporabo obstoječih vrednosti."}, "FREQUENCY": {"a": "(matrika_podatkov; matrika_raz<PERSON>v)", "d": "Preračuna, kako pogosto se vrednosti pojavljajo v obsegu vrednosti, in nato vrne navpično matriko števil, ki ima en element več kot »matrika_razredov«."}, "GAMMA": {"a": "(x)", "d": "Vrne vrednost funkcije Gama"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulativno)", "d": "<PERSON>rne gama porazdelitev."}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulativno)", "d": "<PERSON>rne gama porazdelitev."}, "GAMMAINV": {"a": "(verjetnost; alfa; beta)", "d": "Vrne inverzno gama kumulativno porazdelitev: če je p = GAMMADIST(x,...), potem je GAMMAINV(p,...) = x."}, "GAMMA.INV": {"a": "(verjetnost; alfa; beta)", "d": "Vrne inverzno gama kumulativno porazdelitev: če je p = GAMMA.DIST(x, ...), potem je GAMMA.INV(p, ...) = x."}, "GAMMALN": {"a": "(x)", "d": "Vrne naravni logaritem gama funkcije."}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Vrne naravni logaritem gama funkcije."}, "GAUSS": {"a": "(x)", "d": "Vrne 0,5 manj, kot je standardna normalna kumulativna porazdelitev"}, "GEOMEAN": {"a": "(število1; [število2]; ...)", "d": "Vrne geometrično srednjo vrednost matrike ali obsega pozitivnih številskih podatkov."}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Vrne števila v trendu eksponentne rasti, ujemajočem z znanimi podatkovnimi točkami."}, "HARMEAN": {"a": "(število1; [število2]; ...)", "d": "Vrne harmonično srednjo vrednost za nabor pozitivnih števil. Harmonična srednja vrednost je obratna vrednost aritmetične srednje vrednosti za obratne (recipročne) vrednosti."}, "HYPGEOM.DIST": {"a": "(vzorec_s; številka_vzorca; populacija_s; številka_populacije; kumulativno)", "d": "Vrne hipergeometrično porazdelitev."}, "HYPGEOMDIST": {"a": "(vzorec_s; velikost_vzorca; populacija_s; velikost_populacije)", "d": "Vrne hipergeometrično porazdelitev."}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "Izračuna presečišče regresijske premice, ki gre skozi podatkovne točke znanih_x-ov in znanih_y-ov, z osjo y."}, "KURT": {"a": "(število1; [število2]; ...)", "d": "Vrne sploščenost podatkovne množice."}, "LARGE": {"a": "(matrika; k)", "d": "Vrne k-to največjo vrednost nabora pod<PERSON>kov, na primer peto najve<PERSON>tevilo."}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "<PERSON><PERSON> statistiko, ki opisuje linearni trend, ujemajoč z znanimi podatkovnimi točkami, s prilagajanjem premici po metodi najmanjših k<PERSON>dratov."}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "<PERSON><PERSON> statist<PERSON>, s katero je opisana eksponentna krivulja, ki ustreza znanim podatkovnim točkam."}, "LOGINV": {"a": "(verjetnost; srednja_vrednost; standardni_odklon)", "d": "Vrne inverzno logaritmično normalno kumulativno porazdelitev funkcije x-a, kjer je ln(x) normalno porazdeljen, s parametroma »Srednja_vrednost« in »Standardni_odklon«."}, "LOGNORM.DIST": {"a": "(x; srednja_vrednost; standardni_odklon; kumulativno)", "d": "Vrne logaritmično normalno porazdelitev za x, kjer je ln(x) normalno porazdeljen, s parametroma »srednja_vrednost« in »standardni_odklon«."}, "LOGNORM.INV": {"a": "(verjetnost; srednja_vrednost; standardni_odklon)", "d": "Vrne inverzno logaritmično normalno kumulativno porazdelitev funkcije x-a, kjer je ln(x) normalno porazdeljen, s parametroma Mean in Standard_dev."}, "LOGNORMDIST": {"a": "(x; srednja_vrednost; standardni_odklon)", "d": "Vrne kumulativno logaritmično normalno porazdelitev za x, kjer je ln(x) normalno porazdeljen, s parametroma »Srednja_vrednost« in »Standardni_odklon«."}, "MAX": {"a": "(število1; [število2]; ...)", "d": "Vrne največjo vrednost v množici vrednosti. Prezre logične vrednosti in besedilo."}, "MAXA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Vrne največjo vrednost v množici vrednosti. Ne prezre logičnih vrednosti ali besedila."}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Vrne največjo vrednost med celicami, določeni z danim naborom pogojev ali meril"}, "MEDIAN": {"a": "(število1; [število2]; ...)", "d": "Vrne mediano ali število v sredini množice danih števil."}, "MIN": {"a": "(število1; [število2]; ...)", "d": "Vrne najmanjšo vrednost v množici vrednosti. Prezre logične vrednosti in besedilo."}, "MINA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Vrne najmanjšo vrednost v množici vrednosti. Ne prezre logičnih vrednosti in besedila."}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Vrne najmanjšo vrednost med celicami, določeni z danim naborom pogojev ali meril"}, "MODE": {"a": "(število1; [število2]; ...)", "d": "Vrne najpogostejšo vrednost v matriki ali v obsegu podatkov."}, "MODE.MULT": {"a": "(število1; [število2]; ...)", "d": "Vrne navpično matriko najpogostejših ali ponavljajočih se vrednosti v matriki ali obsegu podatkov. Za vodoravno matriko uporabite =TRANSPOSE(MODE.MULT(število1,število2, ...))"}, "MODE.SNGL": {"a": "(število1; [število2]; ...)", "d": "Vrne najpogostejšo vrednost v matriki ali v obsegu podatkov."}, "NEGBINOM.DIST": {"a": "(število_f; število_s; verjetnost_s; kumulativno)", "d": "<PERSON><PERSON> negativno binoms<PERSON> p<PERSON>v, ki je ver<PERSON><PERSON><PERSON>, da boste do<PERSON><PERSON><PERSON>_f ne<PERSON><PERSON><PERSON> pred uspehom, ki je po vrstnem redu število_s, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da je konstantna verjetnost uspeha enaka verjetnost_s."}, "NEGBINOMDIST": {"a": "(število_f; število_s; verjetnost_s)", "d": "<PERSON>rne negativno binomsko porazdelitev, ki je verjet<PERSON><PERSON>, da boste dož<PERSON> »število_f« neus<PERSON>hov pred uspehom, ki je po vrstnem redu »število_s«, up<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da je konstantna verjetnost uspeha enaka »verjetnost_s«."}, "NORM.DIST": {"a": "(x; srednja_vrednost; standardni_odklon; kumulativno)", "d": "Vrne normalno porazdelitev za navedeno srednjo vrednost in standardni odklon."}, "NORMDIST": {"a": "(x; srednja_vrednost; standardni_odklon; kumulativno)", "d": "Vrne normalno kumulativno porazdelitev za navedeno srednjo vrednost in standardni odklon."}, "NORM.INV": {"a": "(verjetnost; srednja_vrednost; standardni_odklon)", "d": "Vrne inverzno normalno kumulativno porazdelitev za navedeno srednjo vrednost in standardni odklon."}, "NORMINV": {"a": "(verjetnost; srednja_vrednost; standardni_odklon)", "d": "Vrne inverzno normalno kumulativno porazdelitev za navedeno srednjo vrednost in standardni odklon."}, "NORM.S.DIST": {"a": "(z; kumulativno)", "d": "Vrne standardno normalno porazdelitev (ima srednjo vrednost nič in standardni odklon ena)."}, "NORMSDIST": {"a": "(z)", "d": "Vrne standardno normalno kumulativno porazdelitev (ima srednjo vrednost nič in standardni odklon ena)."}, "NORM.S.INV": {"a": "(verjetnost)", "d": "Vrne inverzno standardno normalno kumulativno porazdelitev (ima srednjo vrednost nič in standardni odklon ena)."}, "NORMSINV": {"a": "(verjetnost)", "d": "Vrne inverzno standardno normalno kumulativno porazdelitev (ima srednjo vrednost nič in standardni odklon ena)."}, "PEARSON": {"a": "(matrika1; matrika2)", "d": "<PERSON><PERSON> korelacijski koeficient r."}, "PERCENTILE": {"a": "(matrika; k)", "d": "Vrne k-ti percentil vrednosti v obsegu."}, "PERCENTILE.EXC": {"a": "(matrika; k)", "d": "Vrne k-ti percentil vrednosti v obsegu, kjer je k v obsegu med 0 in izključno 1."}, "PERCENTILE.INC": {"a": "(matrika; k)", "d": "Vrne k-ti percentil vrednosti v obsegu, kjer je k v obsegu med 0 in vključno 1."}, "PERCENTRANK": {"a": "(matrika; x; [pomembnost])", "d": "Vrne rang vrednosti v množici podatkov kot odstotek podatkovne množice."}, "PERCENTRANK.EXC": {"a": "(matrika; x; [spomembnost])", "d": "Vrne rang vrednosti v množici podatkov kot odstotek podatkovne množice (od 0 do izključno 1)."}, "PERCENTRANK.INC": {"a": "(matrika; x; [pomembnost])", "d": "Vrne rang vrednosti v množici podatkov kot odstotek podatkovne množice (od 0 do vključno 1)."}, "PERMUT": {"a": "(število; število_iz<PERSON>nih)", "d": "Vrne število permutacij za dano število predmetov ki so lahko izbrani izmed vseh predmetov."}, "PERMUTATIONA": {"a": "(število; število_izbrano)", "d": "<PERSON><PERSON> število permutacij za dano <PERSON><PERSON> pred<PERSON> (s ponovitvami), ki jih je mogoče izbrati med skupnim številom predmetov"}, "PHI": {"a": "(x)", "d": "Vrne vrednost porazdelitve gostote za standardno normalno porazdelitev"}, "POISSON": {"a": "(x; srednja_vrednost; kunulativno)", "d": "Vrne Poissonovo porazdelitev."}, "POISSON.DIST": {"a": "(x; srednja_vrednost; kumulativno)", "d": "Vrne Poissonovo porazdelitev."}, "PROB": {"a": "(x_obseg; verjet_obseg; spodnja_meja; [zgor<PERSON>_meja])", "d": "<PERSON><PERSON>, da so vrednosti obsega med obema mejama ali enake spodnji meji."}, "QUARTILE": {"a": "(matrika; k<PERSON><PERSON>)", "d": "<PERSON><PERSON> na<PERSON>a <PERSON>."}, "QUARTILE.INC": {"a": "(matrika; k<PERSON><PERSON>)", "d": "Vrne kvartil nabora podatkov na podlagi vrednosti percentila med 0 in vključno 1."}, "QUARTILE.EXC": {"a": "(matrika; k<PERSON><PERSON>)", "d": "Vrne kvartil nabora podatkov na osnovi vrednosti percentila med 0 in izključno 1."}, "RANK": {"a": "(števil<PERSON>; sklic; [vrstni_red])", "d": "<PERSON><PERSON> rang števila na seznamu števil, ki je relativna velikost števila glede na druge vrednosti na seznamu."}, "RANK.AVG": {"a": "(števil<PERSON>; sklic; [vrstni_red])", "d": "Vrne rang števila na seznamu števil, ki je relativna velikost števila glede na druge vrednosti na seznamu. Če ima več vrednosti enak rang, vrne povprečni rang."}, "RANK.EQ": {"a": "(števil<PERSON>; sklic; [vrstni_red])", "d": "Vrne rang števila na seznamu števil, ki je relativna velikost števila glede na druge vrednosti na seznamu. Če ima več vrednosti enak rang, vrne najvišji rang tiste množice vrednosti."}, "RSQ": {"a": "(known_ys; known_xs)", "d": "Vrne kvadrat Pearsonovega korelacijskega koeficienta."}, "SKEW": {"a": "(število1; [število2]; ...)", "d": "Vrne asimetrijo porazdelitve, ki je označitev stopnje asimetrije porazdelitve okoli njene srednje vrednosti."}, "SKEW.P": {"a": "(število1; [število2]; ...)", "d": "Vrne asimetrijo porazdelitve glede na populacijo: označitev stopnje asimetrije porazdelitve okoli njene srednje vrednosti."}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Vrne naklon regresijske premice skozi dane podatkovne toč<PERSON>."}, "SMALL": {"a": "(matrika; k)", "d": "Vrne k-to najmanjšo vrednost nabora pod<PERSON>kov, na primer peto najmanjše število."}, "STANDARDIZE": {"a": "(x; srednja_vrednost; standardni_odklon)", "d": "Vrne normalizirano vrednost iz porazdelitve, ki je označena s srednjo vrednostjo in standardnim odklonom."}, "STDEV": {"a": "(število1; [število2]; ...)", "d": "Oceni standardni odklon glede na vzorec (v vzorcu prezre logične vrednosti in besedilo)."}, "STDEV.P": {"a": "(število1; [število2]; ...)", "d": "Izračuna standardni odklon na osnovi celotne populacije, podane v obliki argumentov (prezre logične vrednosti in besedilo)."}, "STDEV.S": {"a": "(število1; [število2]; ...)", "d": "Oceni standardni odklon vzorca na osnovi vzorca (v vzorcu prezre logične vrednosti in besedilo)."}, "STDEVA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Ugotovi standardni odklon, ki temelji na vzorcu, ki vsebuje logične vrednosti in besedilo. Besedilo in logična vrednost FALSE imata vrednost 0, logična vrednost TRUE pa ima vrednost 1."}, "STDEVP": {"a": "(število1; [število2]; ...)", "d": "Izračuna standardni odklon na podlagi celotne populacije, navedene v obliki argumentov (prezre logične vrednosti in besedilo)."}, "STDEVPA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Izračuna standardni odklon, ki temelji na celotni populaciji, vključno z logičnimi vrednostmi in besedilom. Besedilo in logična vrednost FALSE imata vrednost 0, logična vrednost TRUE pa ima vrednost 1."}, "STEYX": {"a": "(known_ys; known_xs)", "d": "Vrne standardno napako predvidenih y-vrednosti za vsak x v regresiji."}, "TDIST": {"a": "(x; stop_prostosti; repi)", "d": "Vrne študentovo t-porazdelitev."}, "TINV": {"a": "(verjetnost; stop_Prostosti)", "d": "Vrne dvorepo inverzno Studentovo t-porazdelitev."}, "T.DIST": {"a": "(x; stop_prostosti; kumula<PERSON>vno)", "d": "Vrne levorepo Studentovo t-porazdelitev."}, "T.DIST.2T": {"a": "(x; stop_prostosti)", "d": "Vrne dvorepo Studentovo t-porazdelitev."}, "T.DIST.RT": {"a": "(x; stop_prostosti)", "d": "Vrne desnorepo Studentovo t-porazdelitev."}, "T.INV": {"a": "(verjetnost; stop_prostosti)", "d": "Vrne levorepo inverzno Studentovo t-porazdelitev."}, "T.INV.2T": {"a": "(verjetnost; stop_prostosti)", "d": "Vrne desnorepo inverzno Studentovo t-porazdelitev."}, "T.TEST": {"a": "(matrika1; matrika2; repi; vrsta)", "d": "<PERSON><PERSON>, povezano s Studentovim t-preizkusom."}, "TREND": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Vrne števila v linearnem trendu, ujemajočem z znanimi podatkovnimi točkami po metodi najmanjših kvadratov."}, "TRIMMEAN": {"a": "(matrika; odstotek)", "d": "Vrne srednjo vrednost iz množice podatkovnih vrednosti."}, "TTEST": {"a": "(matrika1; matrika2; repi; vrsta)", "d": "<PERSON><PERSON>, povezano s Studentovim t-preskusom."}, "VAR": {"a": "(število1; [število2]; ...)", "d": "Oceni odmik glede na vzorec (v vzorcu prezre logične vrednosti in besedilo)."}, "VAR.P": {"a": "(število1; [število2]; ...)", "d": "Izračuna varianco na osnovi celotne populacije (v populaciji prezre logične vrednosti in besedilo)."}, "VAR.S": {"a": "(število1; [število2]; ...)", "d": "Oceni varianco na osnovi vzorca (v vzorcu prezre logične vrednosti in besedilo)."}, "VARA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>, ki temelji na vzorcu, ki vseb<PERSON>je tudi logične vrednosti in besedilo. Besedilo in logična vrednost FALSE imata vrednost 0, logična vrednost TRUE pa ima vrednost 1."}, "VARP": {"a": "(število1; [število2]; ...)", "d": "Izračuna odmik na podlagi celotne populacije (prezre logične vrednosti in besedilo v populaciji)."}, "VARPA": {"a": "(vrednost1; [vrednost2]; ...)", "d": "Izračuna varianco, ki temelji na celotni populaciji, ki vsebuje tudi logične vrednosti in besedilo. Besedilo in logična vrednost FALSE imata vrednost 0, logična vrednost TRUE pa ima vrednost 1."}, "WEIBULL": {"a": "(x; alfa; beta; kumulativno)", "d": "<PERSON>rne <PERSON>ovo porazdelitev."}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulativno)", "d": "<PERSON>rne <PERSON>ovo porazdelitev."}, "Z.TEST": {"a": "(matrika; x; [sigma])", "d": "Vrne enorepo P-vrednost z-preizkusa."}, "ZTEST": {"a": "(matrika; x; [sigma])", "d": "Vrne enorepo P-vrednost z-preizkusa."}, "ACCRINT": {"a": "(i<PERSON><PERSON>; p<PERSON>_o<PERSON>ti; poravnava; mera; vrednost; pogostost; [osnova]; [nač<PERSON>_izrač<PERSON>])", "d": "Vrne povečane obresti za vrednostni papir, ki periodično izplačuje obresti."}, "ACCRINTM": {"a": "(izdaja; poravnava; mera; vrednost; [osnova])", "d": "Vrne povečane obresti za vrednostni papir, ki izplača obresti ob zapadlosti"}, "AMORDEGRC": {"a": "(s<PERSON><PERSON><PERSON>; datum_nakupa; prvo_obdobje; amortizacija; obdobje; stopnja; [osnova])", "d": "Vrne sorazmerno linearno razvrednotenje sredstva za posamezno računovodsko obdobje."}, "AMORLINC": {"a": "(s<PERSON><PERSON><PERSON>; datum_nakupa; prvo_obdobje; amortizacija; obdobje; stopnja; [osnova])", "d": "Vrne sorazmerno linearno razvrednotenje sredstva za posamezno računovodsko obdobje."}, "COUPDAYBS": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "Vrne število dni od začetka obdobja vrednostnega papirja do datuma poravnave"}, "COUPDAYS": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "Vrne število dni v obdobju vrednostnega papirja, ki vsebuje datum poravnave"}, "COUPDAYSNC": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "Vrne število dni od datuma poravnave do naslednjega datuma vrednostnega papirja"}, "COUPNCD": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "Vrne naslednji datum poravnave po datumu poravnave"}, "COUPNUM": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "<PERSON><PERSON> število vrednostnih papirjev, ki bodo plačani med datumom poravnave in datumom zapadlosti"}, "COUPPCD": {"a": "(poravnava; zapadlost; pogostost; [osnova])", "d": "Vrne prejšnji datum vrednostnega papirja pred datumom poravnave"}, "CUMIPMT": {"a": "(mera; št_obdobij; sv; začetno_obdobje; končno_obdobje; vrsta)", "d": "<PERSON><PERSON> kumulativ<PERSON>, plačane med dvema obdobjema"}, "CUMPRINC": {"a": "(mera; št_obdobij; sv; začetno_obdobje; končno_obdobje; vrsta)", "d": "<PERSON><PERSON> kumulativno glavnico, plačano s posojilom med dvema obdobjema"}, "DB": {"a": "(s<PERSON><PERSON><PERSON>; vrednost_po_amor; št_obdobij; obdobje; [mesec])", "d": "Vrne amortizacijo sredstva za določeno obdobje po metodi fiksnopojemajočega salda."}, "DDB": {"a": "(s<PERSON><PERSON><PERSON>; vrednost_po_amor; št_obdobij; obdbo<PERSON>; [faktor])", "d": "Vrne amortizacijo sredstva za določeno obdobje z metodo dvojnopojemajočega salda ali s kakšno drugo metodo, ki jo dolo<PERSON>."}, "DISC": {"a": "(poravnava; zapadlost; cena; odkup; [osnova])", "d": "<PERSON>rne stopnjo rabata vrednostnega papirja"}, "DOLLARDE": {"a": "(ulomek_valuta; imenovalec)", "d": "Pretvori ceno v dolarjih, izraženo kot ulomek, v ceno v dolarjih, izraženo kot decimalno število"}, "DOLLARFR": {"a": "(decimalka_valuta; imenovalec)", "d": "Pretvori ceno v dolarjih, izraženo kot decimalno število, v ceno v dolarjih, izraženo kot ulomek"}, "DURATION": {"a": "(p<PERSON>vna<PERSON>; zapadlost; o<PERSON><PERSON>; donos; pogo<PERSON><PERSON>; [osnova])", "d": "Vrne letno trajanje vrednostnega papirja s periodičnimi plačili o<PERSON>"}, "EFFECT": {"a": "(nominalna_obr_mera; št_obdobij_leto)", "d": "Vrne efektivno letno obrestno mero"}, "FV": {"a": "(mera; obdobja; pla<PERSON>ilo; [sv]; [vrsta])", "d": "<PERSON><PERSON> bodočo vrednost naložbe, ki temelji na periodičnih, en<PERSON>h pla<PERSON> in nespremenljivi obrestni meri."}, "FVSCHEDULE": {"a": "(glavnica; razpored)", "d": "Vrne bodočo vrednost začetne glavnice po uporabi niza sestavljenih obrestnih mer"}, "INTRATE": {"a": "(poravnava; zapadlost; naložba; odkup; [osnova])", "d": "Vrne obrestno mero za v celoti vloženi vrednostni papir"}, "IPMT": {"a": "(mera; obdobje; št_plačil; sv; [pv]; [vrsta])", "d": "Vrne plačilo obresti za naložbo v navedenem obdobju, ki temelji na periodičnih, enakih plačilih in nespremenljivi obrestni meri."}, "IRR": {"a": "(v<PERSON><PERSON><PERSON>; [domneva])", "d": "<PERSON><PERSON> notranjo stopnjo donosa za vrsto denarnih tokov."}, "ISPMT": {"a": "(mera; obdobje; št_obdobij; sv)", "d": "Vrne obresti, plačane v določenem obdobju naložbe."}, "MDURATION": {"a": "(p<PERSON>vna<PERSON>; zapadlost; o<PERSON><PERSON>; donos; pogo<PERSON><PERSON>; [osnova])", "d": "Vrne trajanje vrednostnega papirja, spremenjeno po <PERSON>u, z domnevno enako vrednostjo 100 €"}, "MIRR": {"a": "(v<PERSON><PERSON><PERSON>; obrestna_mera; mera_reinvesticije)", "d": "Vrne notranjo stopnjo donosa za vrsto periodičnih denarnih tokov, upoštevajoč oba: ceno naložbe in obresti na vnovično naložbo denarja."}, "NOMINAL": {"a": "(efektivna_obr_mera; št_obdobij_leto)", "d": "Vrne nominalno letno obrestno mero"}, "NPER": {"a": "(mera; plačilo; sv; [pv]; [vrsta])", "d": "<PERSON>rne število obdobij za naložbo, ki temelji na periodičnih, enakih plačilih in nespremenljivi obrestni meri."}, "NPV": {"a": "(stopnja; vrednost1; [vrednost2]; ...)", "d": "Vrne sedanjo neto vrednost naložbe, ki temelji na diskontni stopnji in na vrsti bodočih plačil (negativne vrednosti) in prihodku (pozitivne vrednosti)."}, "ODDFPRICE": {"a": "(poravnava; zapadlost; izdaja; p<PERSON>_o<PERSON><PERSON>; mera; dnoso; odkup; pogostost; [osnova])", "d": "Vrne ceno vrednostnega papirja na 100 € imenske vrednosti, z lihim prvim obdobjem"}, "ODDFYIELD": {"a": "(poravnava; zapadlost; izdaja; p<PERSON>_o<PERSON><PERSON>; mera; cena; odkup; pogostost; [osnova])", "d": "Vrne donos vrednostnega papirja z lihim prvim obdobjem"}, "ODDLPRICE": {"a": "(poravnava; zapadlost; zadn<PERSON>_obresti; mera; donos; odkup; pogostost; [osnova])", "d": "Vrne ceno vrednostnega papirja na 100 € imenske vrednosti, z lihim zadnjim obdobjem"}, "ODDLYIELD": {"a": "(poravnava; zapadlost; zadn<PERSON>_obresti; mera; cena; odkup; pogostost; [osnova])", "d": "Vrne donos vrednostnega papirja z lihim zadnjim obdobjem"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "<PERSON><PERSON>, ki jih investicija zah<PERSON>va, če ž<PERSON> doseči določeno vrednost"}, "PMT": {"a": "(mera; obdobja; sv; [pv]; [vrsta])", "d": "Izračuna plačilo za posojilo, ki temelji na enakih plačilih in nespremenljivi obrestni meri."}, "PPMT": {"a": "(mera; obdobja; št_plačil; sv; [pv]; [vrsta])", "d": "Vrne plačilo na glavnico za naložbo, ki temelji na enakih plačilih in nespremenljivi obrestni meri."}, "PRICE": {"a": "(poravnava; zapadlost; stopnja; obresti; odkup; pogostost; [osnova])", "d": "Vrne ceno vrednostnega papirja na 100 € imenske vrednosti, ki izplača periodične obresti"}, "PRICEDISC": {"a": "(poravnava; zapadlost; rabata; odkup; [osnova])", "d": "Vrne ceno vrednostnega papirja z rabatom na 100 € imenske vrednosti"}, "PRICEMAT": {"a": "(poravnava; zapadlost; izdaja; stopnja; obresti; [osnova])", "d": "Vrne ceno vrednostnega papirja na 100 € imenske vrednosti, ki izplača obresti ob zapadlosti"}, "PV": {"a": "(mera; obdobja; pla<PERSON>ilo; [sv]; [vrsta])", "d": "Vrne sedanjo vrednost naložbe: celotna vsota vrednosti vrste bodočih plačil v tem trenutku."}, "RATE": {"a": "(obdo<PERSON><PERSON>; plačilo; sv; [pv]; [vrsta]; [domneva])", "d": "Vrne obrestno mero na obdobje posojila ali naložbe. Za četrtletna plačila pri 6 % APR na primer uporabite 6 %/4."}, "RECEIVED": {"a": "(poravnava; zapadlost; naložba; rabat; [osnova])", "d": "<PERSON><PERSON>, ki je prejeta ob zapadlosti za v celoti vloženi vrednostni papir"}, "RRI": {"a": "(nper; pv; fv)", "d": "Vrne ekvivalentno obrestno mero za rast investicije"}, "SLN": {"a": "(stro<PERSON><PERSON>; vrednost_po_amor; št_obdobij)", "d": "Vrne linearno amortizacijo sredstva za eno obdobje."}, "SYD": {"a": "(stro<PERSON><PERSON>; vrednost_po_amor; št_obdobij; obdobje)", "d": "Vrne amortizacijo po metodi vsote letnih števk za sredstvo prek določenega obdobja."}, "TBILLEQ": {"a": "(poravnava; zapadlost; rabat)", "d": "<PERSON><PERSON>, ki je enak o<PERSON>vez<PERSON>, za zakladno menico"}, "TBILLPRICE": {"a": "(poravnava; zapadlost; rabat)", "d": "Vrne ceno zakladne menice na 100 € imenske vrednosti"}, "TBILLYIELD": {"a": "(poravnava; zapadlost; cena)", "d": "Vrne donos za zakladno menico"}, "VDB": {"a": "(s<PERSON><PERSON><PERSON>; vred<PERSON>t_po_amor; št_obdobij; začetno_obdobje; končno_obdobje; [faktor]; [brez_preklopa])", "d": "Vrne amortizacijo sredstva za poljubno obdobje (tudi za delna obdobja), ki ga dolo<PERSON>ite, z metodo dvojnopojemajočega salda ali s kakšno drugo metodo, ki jo določite."}, "XIRR": {"a": "(v<PERSON><PERSON><PERSON>; datumi; [domneva])", "d": "Vrne notranjo stopnjo povračila za razpored pretokov denarja"}, "XNPV": {"a": "(stopnja; vrednosti; datumi)", "d": "Vrne sedanjo neto vrednost za razpored pretokov denarja"}, "YIELD": {"a": "(poravnava; zapadlost; stopnja; cena; odkup; pogostost; [osnova])", "d": "Vrne donos na vrednostnem papirju, ki izplačuje periodične obresti"}, "YIELDDISC": {"a": "(poravnava; zapadlost; cena; odkup; [osnova])", "d": "<PERSON><PERSON> letni donos za vrednostni papir z rabatom. Na primer, zakladna menica"}, "YIELDMAT": {"a": "(poravnava; zapadlost; izdaja; mera; cena; [osnova])", "d": "Vrne letni donos vrednostnega papirja, ki izplača obresti ob zapadlosti"}, "ABS": {"a": "(števil<PERSON>)", "d": "Vrne absolutno vrednost <PERSON>, število brez predz<PERSON>."}, "ACOS": {"a": "(števil<PERSON>)", "d": "Vrne arkus kosinus <PERSON>, v radianih, iz obsega od 0 do Pi. Arkus kosinus je kot, ka<PERSON><PERSON>a kosinus je dano <PERSON>."}, "ACOSH": {"a": "(števil<PERSON>)", "d": "Vrne inverzni hiperbolični kosinus <PERSON>."}, "ACOT": {"a": "(number)", "d": "Vrne arccot števila, v radianih v obsegu od 0 do Pi."}, "ACOTH": {"a": "(number)", "d": " Vrne inverzni hiperbolični kotangens števila"}, "AGGREGATE": {"a": "(št_funkcije; možnosti; sklic1; ...)", "d": "Vrne agregat s seznama ali iz zbirke podatkov."}, "ARABIC": {"a": "(be<PERSON><PERSON>)", "d": "Pretvori rimsko številko v arabsko"}, "ASC": {"a": "(be<PERSON><PERSON>)", "d": "Za jezike z dvobajtnim naborom znakov (DBCS) spremeni funkcija znake polne širine (dvobajtne) v znake polovične (enobajtne) širine"}, "ASIN": {"a": "(števil<PERSON>)", "d": "Vrne arkus sinus števila v radianih, iz obsega od -Pi/2 do Pi/2."}, "ASINH": {"a": "(števil<PERSON>)", "d": "Vrne inverzni hiperbolični sinus števila."}, "ATAN": {"a": "(števil<PERSON>)", "d": "Vrne arkus tangens števila v radianih, iz obsega od -Pi/2 do Pi/2."}, "ATAN2": {"a": "(št_x; št_y)", "d": "Vrne arkus tangens podanih x- in y-koordinat v radianih, iz obsega od -Pi do Pi, brez -Pi."}, "ATANH": {"a": "(števil<PERSON>)", "d": "Vrne inverzni hiperbolični tangens števila."}, "BASE": {"a": "(<PERSON><PERSON><PERSON><PERSON>; koren; [min_do<PERSON><PERSON><PERSON>])", "d": "Pretvori število v besedilo z danim korenom (osnova)"}, "CEILING": {"a": "(število; pomembnost)", "d": "Zaokroži število navzgor, na najbližji mnogokratnik značilnega števila."}, "CEILING.MATH": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [osnova]; [način])", "d": "Zaokroži število na najbližje celo število ali večkratnik osnove navzgor"}, "CEILING.PRECISE": {"a": "(š<PERSON><PERSON><PERSON>; [pomembnost])", "d": "<PERSON><PERSON> število, ki je zaokroženo na najbližje celo število ali na najbližji mnogokratnik značilnega števila"}, "COMBIN": {"a": "(šte<PERSON><PERSON>; izbra<PERSON>_število)", "d": "<PERSON>rne število kombinacij za dano število pred<PERSON>ov."}, "COMBINA": {"a": "(število; število_izbrano)", "d": "<PERSON><PERSON> število kombinacij za dano <PERSON>te<PERSON> elementov (s ponovitvami), ki jih je mogoče izbrati med skupnim številom elementov"}, "COS": {"a": "(števil<PERSON>)", "d": "<PERSON><PERSON> kosinus kota."}, "COSH": {"a": "(števil<PERSON>)", "d": "<PERSON>rne hiperbolični k<PERSON>."}, "COT": {"a": "(number)", "d": "Vrne kotangens kota"}, "COTH": {"a": "(number)", "d": "Vrne hiperbolični kotangens števila"}, "CSC": {"a": "(number)", "d": "<PERSON>rne kosekans kota"}, "CSCH": {"a": "(number)", "d": "Vrne hiperbolični kosekans kota"}, "DECIMAL": {"a": "(število; koren)", "d": "Pretvori besedilo, ki predstavlja številko, v danem korenu v decimalno število"}, "DEGREES": {"a": "(kot)", "d": "Pretvori radiane v stopinje."}, "ECMA.CEILING": {"a": "(število; pomembnost)", "d": "Zaokroži število navzgor, na najbližji mnogokratnik značilnega števila"}, "EVEN": {"a": "(števil<PERSON>)", "d": "Zaokroži pozitivno število navzgor in negativno število navzdol na najbližje sodo celo število."}, "EXP": {"a": "(števil<PERSON>)", "d": " Vrne e na potenco navedenega števila."}, "FACT": {"a": "(števil<PERSON>)", "d": "<PERSON><PERSON> fak<PERSON>, ki je enaka 1*2*3*...*<PERSON>te<PERSON><PERSON>."}, "FACTDOUBLE": {"a": "(števil<PERSON>)", "d": "Vrne dvojno fakulteto števila"}, "FLOOR": {"a": "(število; pomembnost)", "d": "Zaokroži število navzdol do najbližjega mnogokratnika značilnega števila."}, "FLOOR.PRECISE": {"a": "(š<PERSON><PERSON><PERSON>; [pomembnost])", "d": "<PERSON><PERSON>vilo, ki je zaokroženo navzdol na najbližje celo število ali na najbližji večkratnik osnove"}, "FLOOR.MATH": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [osnova]; [način])", "d": "Zaokroži število na najbližje celo število ali večkratnik osnove navzdol"}, "GCD": {"a": "(število1; [število2]; ...)", "d": "Vrne največji skupni delitelj"}, "INT": {"a": "(števil<PERSON>)", "d": "Število zaokroži navzdol do najbližjega celega števila."}, "ISO.CEILING": {"a": "(š<PERSON><PERSON><PERSON>; [pomembnost])", "d": "<PERSON><PERSON> š<PERSON>vilo, ki je zaokroženo na najbližje celo število ali na najbližji mnogokratnik značilnega števila. Ne glede na znak števila, je število zaokroženo navzgor. Če je vrednost števila ali značilnega števila nič, je vrnjena vrednost nič."}, "LCM": {"a": "(število1; [število2]; ...)", "d": "Vrne najmanjši skupni mnogokratnik"}, "LN": {"a": "(števil<PERSON>)", "d": "Vrne naravni logaritem števila."}, "LOG": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [osnova])", "d": "Vrne logaritem števila z osnovo, ki jo določ<PERSON>."}, "LOG10": {"a": "(števil<PERSON>)", "d": "Vrne desetiški logaritem števila."}, "MDETERM": {"a": "(matrika)", "d": "<PERSON>rne determinanto matrike."}, "MINVERSE": {"a": "(matrika)", "d": "Vrne inverzno matriko matrike, shranjene v polju."}, "MMULT": {"a": "(matrika1; matrika2)", "d": "Vrne produkt dveh matrik, ki je matrika z enakim številom vrstic kot »matrika1« in z enakim številom stolpcev kot »matrika2«"}, "MOD": {"a": "(<PERSON><PERSON><PERSON><PERSON>; delitelj)", "d": "<PERSON><PERSON> o<PERSON>."}, "MROUND": {"a": "(število; večkratnik)", "d": "Vrne število, zaokroženo na želeni večkratnik"}, "MULTINOMIAL": {"a": "(število1; [število2]; ...)", "d": "Vrne mnogočlenski niz števil"}, "MUNIT": {"a": "(dimenzija)", "d": "Vrne matriko enote za določeno dimenzijo"}, "ODD": {"a": "(števil<PERSON>)", "d": "Zaokroži pozitivno število navzgor in negativno število navzdol do najbližjega lihega celega števila."}, "PI": {"a": "()", "d": "Vrne vrednost Pi na 15 decimalnih mest točno (3,14159265358979)."}, "POWER": {"a": "(število; potenca)", "d": "<PERSON><PERSON> poten<PERSON>."}, "PRODUCT": {"a": "(število1; [število2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, ki so bila podana kot argumenti."}, "QUOTIENT": {"a": "(deljenec; delitelj)", "d": "Vrne del celega števila deljenja"}, "RADIANS": {"a": "(kot)", "d": "Pretvori stopinje v radiane."}, "RAND": {"a": "()", "d": "Vrne naključno število, ki je večje ali enako 0 in manjše od 1, enakomerno porazdeljeno (spremembe vnovičnega izračuna)."}, "RANDARRAY": {"a": "([vrstice]; [stolpce]; [min]; [max]; [c<PERSON>_<PERSON><PERSON>])", "d": "Vrne niz naklju<PERSON>h <PERSON>vil"}, "RANDBETWEEN": {"a": "(najmnajše; največje)", "d": "<PERSON><PERSON> naklju<PERSON> število med navedenimi števili"}, "ROMAN": {"a": "(š<PERSON><PERSON><PERSON>; [oblika])", "d": "Pretvori arabsko številko v rimsko v obliki besedila."}, "ROUND": {"a": "(število; št_števk)", "d": "Zaokroži število prek določenega števila števk."}, "ROUNDDOWN": {"a": "(število; št_števk)", "d": "Zaokroži število navzdol proti nič."}, "ROUNDUP": {"a": "(število; št_števk)", "d": "Zaokroži število navzgor, stran od nič."}, "SEC": {"a": "(number)", "d": "<PERSON><PERSON> sekans kota"}, "SECH": {"a": "(number)", "d": "Vrne hiperbolični sekans kota"}, "SERIESSUM": {"a": "(x; n; m; koe<PERSON>i)", "d": "Vrne vsoto potenciranih nizov glede na formulo"}, "SIGN": {"a": "(števil<PERSON>)", "d": "Vrne predznak <PERSON>: 1, če je število pozitivno, ni<PERSON>, če je število nič, ali -1, če je število negativno."}, "SIN": {"a": "(pš<PERSON><PERSON><PERSON>)", "d": "Vrne sinus kota."}, "SINH": {"a": "(števil<PERSON>)", "d": "<PERSON>rne hiperbolični sinus števila."}, "SQRT": {"a": "(števil<PERSON>)", "d": "Vrne pozitivni kvadratni koren števila."}, "SQRTPI": {"a": "(števil<PERSON>)", "d": "Vrne k<PERSON>dratni koren za (število * pi)"}, "SUBTOTAL": {"a": "(št_funkcije; sklic1; ...)", "d": "<PERSON>rne delno vsoto s seznama ali iz zbirke podatkov."}, "SUM": {"a": "(število1; [število2]; ...)", "d": "Sešteje vsa števila v obsegu celic."}, "SUMIF": {"a": "(obseg; pogoji; [obseg_se<PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>, ki jih določa podan pogoj ali kriterij."}, "SUMIFS": {"a": "(obseg_se<PERSON><PERSON><PERSON><PERSON>; obseg_pogojev; pogoji; ...)", "d": "<PERSON><PERSON> celi<PERSON>, ki jih navaja dani niz pogojev ali kriterijev"}, "SUMPRODUCT": {"a": "(matrika1; [matrika2]; [matrika3]; ...)", "d": "Vrne vsoto produktov ustreznih obsegov ali matrik."}, "SUMSQ": {"a": "(število1; [število2]; ...)", "d": "<PERSON><PERSON> vs<PERSON> k<PERSON> argumentov. <PERSON>rg<PERSON><PERSON> so <PERSON><PERSON><PERSON>, mat<PERSON><PERSON>, imena <PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON>."}, "SUMX2MY2": {"a": "(matrika_x; matrika_y)", "d": "Izračuna vsoto razlik kvadratov pripadajočih števil v dveh obsegih ali matrikah."}, "SUMX2PY2": {"a": "(matrika_x; matrika_y)", "d": "Izračuna skupno vsoto vseh vsot kvadratov števil v dveh pripadajočih obsegih ali matrikah."}, "SUMXMY2": {"a": "(matrika_x; matrika_y)", "d": "Izračuna vsoto kvadratov razlik v dveh pripadajočih obsegih ali matrikah."}, "TAN": {"a": "(števil<PERSON>)", "d": "Vrne tangens kota."}, "TANH": {"a": "(števil<PERSON>)", "d": "Vrne hiperbolični tangens števila."}, "TRUNC": {"a": "(število; [št_števk])", "d": "Prireže število na celo število tako, da odstrani decimalni del števila ali ulomek."}, "ADDRESS": {"a": "(št_vrstice; št_stolpca; [abs_št]; [a1]; [besedilo_lista])", "d": "Ustvari sklic na celico kot besedilo. Podati morate številko vrstice in številko stolpca."}, "CHOOSE": {"a": "(št_indeksa; vrednost1; [vrednost2]; ...)", "d": "<PERSON>zbere vrednost ali <PERSON>, ki naj se izvede, s seznama vrednosti, ki temelji na indeksni številki."}, "COLUMN": {"a": "([sklic])", "d": "Vrne številko stolpca danega sklica."}, "COLUMNS": {"a": "(matrika)", "d": "Vrne število stolpcev v matriki ali v sklicu."}, "FORMULATEXT": {"a": "(sklic)", "d": "<PERSON><PERSON> formulo kot niz"}, "HLOOKUP": {"a": "(iskana_vrednost; matrika_tabele; št_indeksa_vrstice; [obseg_iskanja])", "d": "Poišče vrednost v zgornji vrstici tabele ali matrike vrednosti in vrne vrednost iz istega stolpca in vrstice, ki jo vi določite."}, "HYPERLINK": {"a": "(mesto_povezave; [prija<PERSON><PERSON>_ime])", "d": "Ustvari bližnjico ali skok, ki odpre dokument, shranjen na vašem disku, v omrežnem strežniku ali v internetu."}, "INDEX": {"a": "(matrika; št_vrstice; [št_stolpca]!sklic; št_vrstice; [št_stolpca]; [št_podro<PERSON>ja])", "d": "Vrne vrednost ali sklic na celico v preseku določene vrstice in stolpca v navedenem obsegu."}, "INDIRECT": {"a": "(besedilo_sklica; [a1])", "d": "<PERSON><PERSON> s<PERSON>, ki ga določa besedilni niz."}, "LOOKUP": {"a": "(iskana_vrednost; vektor_iskanja; [vektor_rezultata]!iskana_vrednost; matrika)", "d": "Poišče vrednost bodisi iz obsega, ki vsebuje le eno vrstico ali le en stolpec, bodisi iz matrike.Na voljo zaradi združljivosti s prejšnjimi različicami."}, "MATCH": {"a": "(iskana_vrednost; matrika_iskanja; [vrsta_ujemanja])", "d": "Vrne relativni položaj elementa v matriki, ki se ujema z navedeno vrednostjo v navedenem vrstnem redu."}, "OFFSET": {"a": "(sklic; vrstice; stolpci; [viš<PERSON>]; [širina])", "d": "<PERSON>rne sklic na obseg, ki je dano <PERSON> vrst<PERSON> in stolpcev iz danega sklica."}, "ROW": {"a": "([sklic])", "d": "Vrne številko vrstice za sklic."}, "ROWS": {"a": "(matrika)", "d": "Vrne število vrstic v sklicu ali v matriki."}, "TRANSPOSE": {"a": "(matrika)", "d": "Pretvori navpični obseg celic v vodoravni obseg in obratno."}, "UNIQUE": {"a": "(polje; [po_kol]; [natančno_enkrat])", "d": "Vrne enolične vrednosti obsega ali polja."}, "VLOOKUP": {"a": "(iskana_vrednost; matrika_tabele; št_indeksa_stolpca; [obseg_iskana])", "d": "Poišče vrednost v skrajnem levem stolpcu tabele in vrne vrednost v isti vrstici iz stolpca, ki ga navedete. Privzeto mora biti tabela urejena v naraščajočem vrstnem redu."}, "XLOOKUP": {"a": "(iskana_vrednost; iskani_niz; vrni_niz; [če_ni_najdeno]; [način_ujeman<PERSON>]; [način_iskanja])", "d": "Poišče ujemanje v obsegu ali polju in vrne ustrezen element iz drugega obsega ali polja. Privzeto je uporabljeno natančno ujemanje"}, "CELL": {"a": "(vrsta_informacij; [sklic])", "d": "Vrne informacije o oblikovanju, mestu ali vs<PERSON><PERSON> celice"}, "ERROR.TYPE": {"a": "(vrednost_napake)", "d": "<PERSON><PERSON>, ki ustreza vrednosti napake."}, "ISBLANK": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali gre za sklic na prazno celico, in vrne TRUE ali FALSE."}, "ISERR": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost napaka, ki ni #N/V, in vrne TRUE ali FALSE"}, "ISERROR": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost napaka, in vrne TRUE ali FALSE"}, "ISEVEN": {"a": "(števil<PERSON>)", "d": "<PERSON><PERSON> TRUE, če je število sodo"}, "ISFORMULA": {"a": "(sklic)", "d": "<PERSON><PERSON><PERSON>, ali je sklic povezan s celico, ki vs<PERSON><PERSON><PERSON> formulo, in vrne TRUE ali FALSE"}, "ISLOGICAL": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost logična vrednost (TRUE ali FALSE), in vrne TRUE ali FALSE."}, "ISNA": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost  #N/V, in vrne TRUE ali FALSE."}, "ISNONTEXT": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali vrednost ni besedilo (prazne celice niso besedilo), in vrne TRUE ali FALSE."}, "ISNUMBER": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost število, in vrne TRUE ali FALSE."}, "ISODD": {"a": "(števil<PERSON>)", "d": "<PERSON>rne TRUE, če je število liho"}, "ISREF": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost sklic, in vrne TRUE ali FALSE."}, "ISTEXT": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ali je vrednost besedilo, in vrne TRUE ali FALSE."}, "N": {"a": "(vrednost)", "d": "Pretvori neštevilsko vrednost v število, datume v zaporedna števila, TRUE v 1, kar koli drugega pa v 0 (nič)."}, "NA": {"a": "()", "d": "Vrne vrednost napake #N/V (vrednost ni na voljo)."}, "SHEET": {"a": "([vrednost])", "d": "Vrne število listov sklicevanega lista"}, "SHEETS": {"a": "([sklic])", "d": "Vrne število listov v sklicu"}, "TYPE": {"a": "(vrednost)", "d": "<PERSON><PERSON><PERSON>, ki predstavlja podatkovni tip vrednosti: številka = 1; besedilo = 2; logična vrednost = 4; vrednost napake = 16; polje = 64; sestavljeni podatki = 128"}, "AND": {"a": "(logično1; [logično2]; ...)", "d": "<PERSON><PERSON><PERSON>, ali imajo vsi argumenti vrednost TRUE, in vrne vrednost TRUE, če imajo vsi argumenti vrednost TRUE."}, "FALSE": {"a": "()", "d": "Vrne logično vrednost FALSE"}, "IF": {"a": "(logični_test; [vrednost_če_je_true]; [vrednost_če_je_false])", "d": "<PERSON><PERSON><PERSON>, ali je pogoj <PERSON>, in vrne eno vrednost, če je TRUE, in drugo vrednost, če je FALSE."}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "<PERSON><PERSON><PERSON>, ali je izpolnjen eden ali več pogojev in vrne vrednost glede na prvi pogoj TRUE"}, "IFERROR": {"a": "(vrednost; vrednost_če_napaka)", "d": "Vrne value_if_error, če je izraz <PERSON>aka, in vrednost samega izraza"}, "IFNA": {"a": "(vrednost; vrednost_če_nv)", "d": "<PERSON>rne vrednost, ki jo <PERSON>, če se izraz razreši v #N/V, v nasprotnem primeru vrne vrednost izraza"}, "NOT": {"a": "(logično)", "d": "Spremeni FALSE v TRUE ali TRUE v FALSE."}, "OR": {"a": "(logično1; [logično2]; ...)", "d": "<PERSON><PERSON><PERSON>, ali ima kateri argument vrednost TRUE; vrne vrednost TRUE ali FALSE. Vrne vrednost FALSE, če imajo vsi argumenti vrednost FALSE"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "Ovrednoti izraz glede na seznam vrednosti in vrne rezultat, ki ustreza prvi ujemajoči se vrednosti. Če ni nobenega zadetka, je vrnjena opcijska privzeta vrednost"}, "TRUE": {"a": "()", "d": "Vrne logično vrednost TRUE."}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "<PERSON>rne logi<PERSON> »Exclusive Or« vseh argumentov"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON>, ki je pred loč<PERSON> znakov."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON>, ki je za ločilo<PERSON> znakov."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Razdeli besedilo v vrstice ali stolpce z ločili."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": " Prelomi vektor vrstice ali stolpca za določenim številom vrednosti."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Navpično zloži matrike v eno polje."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Vodoravno zloži matrike v eno polje."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Vrne vrstice iz matrike ali sklica."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "<PERSON>rne stolpce iz matrike ali sklica."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Vrne matriko kot en stolpec."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Vrne matriko kot eno vrstico."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": " Prelomi vektor vrstice ali stolpca za določenim številom vrednosti."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Vrne vrstice ali stolpce z začetka ali konca matrike."}, "DROP": {"a": "(array, rows, [columns])", "d": "Spusti vrstice ali stolpce z začetka ali konca matrike."}}
{"DATE": {"a": "(год; месяц; день)", "d": "Возвращает число, соответствующее дате в коде даты-времени"}, "DATEDIF": {"a": "(нач_дата;кон_дата;единица)", "d": "Возвращает разницу между двумя датами (начальной и конечной) согласно заданному интервалу (единице)"}, "DATEVALUE": {"a": "(дата_как_текст)", "d": "Преобразует дату из текстового формата в числовой в коде даты-времени"}, "DAY": {"a": "(дата_в_числовом_формате)", "d": "Возвращает число месяца - число от 1 до 31."}, "DAYS": {"a": "(кон_дата; нач_дата)", "d": "Возвращает количество дней между двумя датами"}, "DAYS360": {"a": "(нач_дата; кон_дата; [метод])", "d": "Вычисляет количество дней между двумя датами на основе 360-дневного года (двенадцать месяцев по 30 дней)"}, "EDATE": {"a": "(нач_дата; число_месяцев)", "d": "Возвращает порядковый номер даты, отстоящей на заданное число месяцев вперед или назад от заданной даты (нач_дата)"}, "EOMONTH": {"a": "(нач_дата; число_месяцев)", "d": "Возвращает порядковый номер последнего дня месяца, отстоящего на заданное число месяцев вперед или назад от заданной даты (нач_дата)"}, "HOUR": {"a": "(время_в_числовом_формате)", "d": "Возвращает часы в виде числа от 0 до 23."}, "ISOWEEKNUM": {"a": "(дата)", "d": "Возвращает номер недели в году по стандарту ISO для указанной даты"}, "MINUTE": {"a": "(время_в_числовом_формате)", "d": "Возвращает минуты в виде числа от 0 до 59."}, "MONTH": {"a": "(дата_в_числовом_формате)", "d": "Возвращает месяц - число от 1 (январь) до 12 (декабрь)."}, "NETWORKDAYS": {"a": "(нач_дата; кон_дата; [праздники])", "d": "Возвращает количество полных рабочих дней между двумя датами"}, "NETWORKDAYS.INTL": {"a": "(нач_дата; кон_дата; [выходные]; [праздники])", "d": "Возвращает количество полных рабочих дней между двумя датами с настраиваемыми параметрами выходных"}, "NOW": {"a": "()", "d": "Возвращает текущую дату и время в формате даты и времени."}, "SECOND": {"a": "(время_в_числовом_формате)", "d": "Возвращает секунды в виде числа от 0 до 59."}, "TIME": {"a": "(часы; минуты; секунды)", "d": "Преобразует заданные в виде чисел часы, минуты и секунды в число в коде времени"}, "TIMEVALUE": {"a": "(время_как_текст)", "d": "Преобразует время из текстового формата в число, представляющее время - число от 0 (0:00:00) до 0,999988426 (23:59:59). Введя формулу, задайте для ячейки тип \"Время\""}, "TODAY": {"a": "()", "d": "Возвращает текущую дату в формате даты."}, "WEEKDAY": {"a": "(дата_в_числовом_формате; [тип])", "d": "Возвращает число от 1 до 7, соответствующее номеру дня недели для заданной даты."}, "WEEKNUM": {"a": "(пор_номер_даты; [тип_возвр])", "d": "Возвращает номер недели года"}, "WORKDAY": {"a": "(нач_дата; число_дней; [праздники])", "d": "Возвращает порядковый номер даты, отстоящей на заданное число рабочих дней вперед или назад от начальной даты"}, "WORKDAY.INTL": {"a": "(нач_дата; число_дней; [выходные]; [праздники])", "d": "Возвращает порядковый номер даты, отстоящей на заданное число рабочих дней вперед или назад от начальной даты с настраиваемыми параметрами выходных дней"}, "YEAR": {"a": "(дата_в_числовом_формате)", "d": "Возвращает год - целое число от 1900 до 9999."}, "YEARFRAC": {"a": "(нач_дата; кон_дата; [базис])", "d": "Возвращает долю года, которую составляет количество дней между двумя датами (начальной и конечной)"}, "BESSELI": {"a": "(x; n)", "d": "Возвращает модифицированную функцию Бесселя In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Возвращает функцию Бесселя Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Возвращает функцию Бесселя Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Возвращает функцию Бесселя Yn(x)"}, "BIN2DEC": {"a": "(число)", "d": "Преобразует двоичное число в десятичное"}, "BIN2HEX": {"a": "(число; [разрядность])", "d": "Преобразует двоичное число в шестнадцатеричное"}, "BIN2OCT": {"a": "(число; [разрядность])", "d": "Преобразует двоичное число в восьмеричное"}, "BITAND": {"a": "(число1; число2)", "d": "Возвращает побитовое \"и\" двух чисел"}, "BITLSHIFT": {"a": "(число; сдвиг)", "d": "Возвращает значение числа, сдвинутое влево на число бит, которое задано параметром \"сдвиг\""}, "BITOR": {"a": "(число1; число2)", "d": "Возвращает побитовое \"или\" двух чисел"}, "BITRSHIFT": {"a": "(число; сдвиг)", "d": "Возвращает значение числа, сдвинутое вправо на число бит, которое задано параметром \"сдвиг\""}, "BITXOR": {"a": "(число1; число2)", "d": "Возвращает побитовое \"исключающее или\" двух чисел"}, "COMPLEX": {"a": "(действительная_часть; мнимая_часть; [мнимая_единица])", "d": "Преобразует коэффициенты при вещественной и мнимой частях комплексного числа в комплексное число"}, "CONVERT": {"a": "(число; исх_ед_изм; кон_ед_изм)", "d": "Преобразует значение из одной системы мер в другую"}, "DEC2BIN": {"a": "(число; [разрядность])", "d": "Преобразует десятичное число в двоичное"}, "DEC2HEX": {"a": "(число; [разрядность])", "d": "Преобразует десятичное число в шестнадцатеричное"}, "DEC2OCT": {"a": "(число; [разрядность])", "d": "Преобразует десятичное число в восьмеричное"}, "DELTA": {"a": "(число1; [число2])", "d": "Проверяет равенство двух чисел"}, "ERF": {"a": "(нижний_предел; [верхний_предел])", "d": "Возвращает функцию ошибки"}, "ERF.PRECISE": {"a": "(X)", "d": "Возвращает функцию ошибки"}, "ERFC": {"a": "(x)", "d": "Возвращает дополнительную функцию ошибки"}, "ERFC.PRECISE": {"a": "(X)", "d": "Возвращает дополнительную функцию ошибки"}, "GESTEP": {"a": "(число; [порог])", "d": "Проверяет, превышает ли число пороговое значение"}, "HEX2BIN": {"a": "(число; [разрядность])", "d": "Преобразует шестнадцатеричное число в двоичное"}, "HEX2DEC": {"a": "(число)", "d": "Преобразует шестнадцатеричное число в десятичное"}, "HEX2OCT": {"a": "(число; [разрядность])", "d": "Преобразует шестнадцатеричное число в восьмеричное"}, "IMABS": {"a": "(компл_число)", "d": "Возвращает абсолютную величину (модуль) комплексного числа"}, "IMAGINARY": {"a": "(компл_число)", "d": "Возвращает коэффициент при мнимой части комплексного числа"}, "IMARGUMENT": {"a": "(компл_число)", "d": "Возвращает аргумент q, то есть угол, выраженный в радианах"}, "IMCONJUGATE": {"a": "(компл_число)", "d": "Возвращает комплексно-сопряженное комплексного числа"}, "IMCOS": {"a": "(компл_число)", "d": "Возвращает косинус комплексного числа"}, "IMCOSH": {"a": "(компл_число)", "d": "Возвращает гиперболический косинус комплексного числа"}, "IMCOT": {"a": "(компл_число)", "d": "Возвращает котангенс комплексного числа"}, "IMCSC": {"a": "(компл_число)", "d": "Возвращает косеканс комплексного числа"}, "IMCSCH": {"a": "(компл_число)", "d": "Возвращает гиперболический косеканс комплексного числа"}, "IMDIV": {"a": "(компл_число1; компл_число2)", "d": "Возвращает частное двух комплексных чисел"}, "IMEXP": {"a": "(компл_число)", "d": "Возвращает экспоненту комплексного числа"}, "IMLN": {"a": "(компл_число)", "d": "Возвращает натуральный логарифм комплексного числа"}, "IMLOG10": {"a": "(компл_число)", "d": "Возвращает десятичный логарифм комплексного числа"}, "IMLOG2": {"a": "(компл_число)", "d": "Возвращает двоичный логарифм комплексного числа"}, "IMPOWER": {"a": "(компл_число; число)", "d": "Возвращает комплексное число, возведенное в степень с целочисленным показателем"}, "IMPRODUCT": {"a": "(компл_число1; [компл_число2]; ...)", "d": "Возвращает произведение от 1 до 255 комплексных чисел"}, "IMREAL": {"a": "(компл_число)", "d": "Возвращает коэффициент при вещественной (действительной) части комплексного числа"}, "IMSEC": {"a": "(компл_число)", "d": "Возвращает секанс комплексного числа"}, "IMSECH": {"a": "(компл_число)", "d": "Возвращает гиперболический секанс комплексного числа"}, "IMSIN": {"a": "(компл_число)", "d": "Возвращает синус комплексного числа"}, "IMSINH": {"a": "(компл_число)", "d": "Возвращает гиперболический синус комплексного числа"}, "IMSQRT": {"a": "(компл_число)", "d": "Возвращает значение квадратного корня комплексного числа"}, "IMSUB": {"a": "(компл_число1; компл_число2)", "d": "Возвращает разность двух комплексных чисел"}, "IMSUM": {"a": "(компл_число1; [компл_число2]; ...)", "d": "Возвращает сумму комплексных чисел"}, "IMTAN": {"a": "(компл_число)", "d": "Возвращает тангенс комплексного числа"}, "OCT2BIN": {"a": "(число; [разрядность])", "d": "Преобразует восьмеричное число в двоичное"}, "OCT2DEC": {"a": "(число)", "d": "Преобразует восьмеричное число в десятичное"}, "OCT2HEX": {"a": "(число; [разрядность])", "d": "Преобразует восьмеричное число в шестнадцатеричное"}, "DAVERAGE": {"a": "(база_данных; поле; критерий)", "d": "Возвращает среднее всех значений столбца списка или базы данных, которые удовлетворяют заданным условиям"}, "DCOUNT": {"a": "(база_данных; поле; критерий)", "d": "Подсчитывает количество числовых ячеек в выборке из заданной базы данных по заданному критерию"}, "DCOUNTA": {"a": "(база_данных; поле; критерий)", "d": "Подсчитывает количество непустых ячеек в выборке из заданной базы данных по заданному критерию"}, "DGET": {"a": "(база_данных; поле; критерий)", "d": "Извлекает из базы данных одну запись, удовлетворяющую заданному критерию"}, "DMAX": {"a": "(база_данных; поле; критерий)", "d": "Возвращает максимальное значение поля (столбца) записей базы данных, удовлетворяющих указанным условиям"}, "DMIN": {"a": "(база_данных; поле; критерий)", "d": "Возвращает минимальное значение среди выделенных фрагментов базы данных"}, "DPRODUCT": {"a": "(база_данных; поле; критерий)", "d": "Перемножает значения определенных полей записей базы данных, удовлетворяющих критерию"}, "DSTDEV": {"a": "(база_данных; поле; критерий)", "d": "Оценивает стандартное отклонение по выборке из выделенной части базы данных"}, "DSTDEVP": {"a": "(база_данных; поле; критерий)", "d": "Вычисляет стандартное отклонение по генеральной совокупности из выделенной части базы данных"}, "DSUM": {"a": "(база_данных; поле; критерий)", "d": "Суммирует числа в поле (столбце) записей базы данных, удовлетворяющих условию"}, "DVAR": {"a": "(база_данных; поле; критерий)", "d": "Оценивает дисперсию по выборке из выделенной части базы данных"}, "DVARP": {"a": "(база_данных; поле; критерий)", "d": "Вычисляет дисперсию по генеральной совокупности из выделенной части базы данных"}, "CHAR": {"a": "(число)", "d": "Возвращает символ с заданным кодом"}, "CLEAN": {"a": "(текст)", "d": "Удаляет все непечатаемые знаки из текста"}, "CODE": {"a": "(текст)", "d": "Возвращает числовой код первого символа в текстовой строке"}, "CONCATENATE": {"a": "(текст1; [текст2]; ...)", "d": "Объединяет несколько текстовых строк в одну"}, "CONCAT": {"a": "(текст1; ...)", "d": "Объединяет список или диапазон строк текста"}, "DOLLAR": {"a": "(число; [число_знаков])", "d": "Преобразует число в текст, используя денежный формат"}, "EXACT": {"a": "(текст1; текст2)", "d": "Проверяет идентичность двух строк текста и возвращает значение ИСТИНА или ЛОЖЬ. Прописные и строчные буквы различаются"}, "FIND": {"a": "(искомый_текст; просматриваемый_текст; [нач_позиция])", "d": "Возвращает позицию начала искомой строки текста в содержащей ее строке текста. Прописные и строчные буквы различаются"}, "FINDB": {"a": "(искомый_текст;просматриваемый_текст;[нач_позиция])", "d": "Находит заданную подстроку (искомый_текст) внутри строки (просматриваемый_текст), предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "FIXED": {"a": "(число; [чис<PERSON><PERSON>_знаков]; [без_разделителей])", "d": "Форматирует число и преобразует его в текст с заданным числом десятичных знаков"}, "LEFT": {"a": "(текст; [количество_знаков])", "d": "Возвращает указанное количество знаков с начала строки текста"}, "LEFTB": {"a": "(текст;[число_знаков])", "d": "Извлекает подстроку из заданной строки, начиная с левого символа, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "LEN": {"a": "(текст)", "d": "Возвращает количество знаков в текстовой строке"}, "LENB": {"a": "(текст)", "d": "Анализирует заданную строку и возвращает количество символов, которые она содержит, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "LOWER": {"a": "(текст)", "d": "Делает все буквы в строке текста строчными"}, "MID": {"a": "(текст; начальная_позиция; количество_знаков)", "d": "Возвращает заданное число знаков из строки текста, начиная с указанной позиции"}, "MIDB": {"a": "(текст;начальная_позиция;число_знаков)", "d": "Извлекает символы из заданной строки, начиная с любого места, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "NUMBERVALUE": {"a": "(текст; [десятичный_разделитель]; [разделитель_разрядов])", "d": "Преобразует текст в число без учета языкового стандарта"}, "PROPER": {"a": "(текст)", "d": "Начинает текстовую строку с заглавной буквы; делает прописной первую букву в каждом слове текста, преобразуя все другие буквы в строчные"}, "REPLACE": {"a": "(старый_текст; нач_поз; число_знаков; новый_текст)", "d": "Заменяет часть строки текста на другую строку"}, "REPLACEB": {"a": "(стар_текст;начальная_позиция;число_знаков;нов_текст)", "d": "Заменяет ряд символов на новый, с учетом заданного количества символов и начальной позиции, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "REPT": {"a": "(текст; число_повторений)", "d": "Повторяет текст заданное число раз"}, "RIGHT": {"a": "(текст; [число_знаков])", "d": "Возвращает указанное число знаков с конца строки текста"}, "RIGHTB": {"a": "(текст;[число_знаков])", "d": "Извлекает подстроку из заданной строки, начиная с крайнего правого символа, согласно заданному количеству символов, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "SEARCH": {"a": "(искомый_текст; текст_для_поиска; [нач_позиция])", "d": "Возвращает позицию первого вхождения знака или строки текста при чтении слева направо; прописные и строчные буквы не различаются"}, "SEARCHB": {"a": "(искомый_текст;просматриваемый_текст;[начальная_позиция])", "d": "Возвращает местоположение заданной подстроки в строке, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "SUBSTITUTE": {"a": "(текст; стар_текст; нов_текст; [номер_вхождения])", "d": "Заменяет новым текстом старый текст в текстовой строке"}, "T": {"a": "(значение)", "d": "Проверяет, является ли значение текстовым, и возвращает его текст, если да, либо две кавычки (пустой текст), если нет"}, "TEXT": {"a": "(значение; format_text)", "d": "Преобразует значение в текст в определенном формате"}, "TEXTJOIN": {"a": "(разделитель; пропускать_пустые; текст1; ...)", "d": "Объединяет список или диапазон строк текста с помощью разделителя"}, "TRIM": {"a": "(текст)", "d": "Удаляет из текста лишние пробелы (кроме одиночных пробелов между словами)"}, "UNICHAR": {"a": "(число)", "d": "Возвращает знак Юникода, соответствующий указанному числу"}, "UNICODE": {"a": "(текст)", "d": "Возвращает число (код знака), соответствующее первому знаку в тексте"}, "UPPER": {"a": "(текст)", "d": "Делает все буквы в строке текста прописными"}, "VALUE": {"a": "(текст)", "d": "Преобразует текстовый аргумент в число"}, "AVEDEV": {"a": "(число1; [число2]; ...)", "d": "Возвращает среднее абсолютных значений отклонений точек данных от среднего. Аргументами могут являться числа, имена, массивы или ссылки на числовые значения"}, "AVERAGE": {"a": "(число1; [число2]; ...)", "d": "Возвращает среднее арифметическое своих аргументов, которые могут быть числами, именами, массивами или ссылками на ячейки с числами"}, "AVERAGEA": {"a": "(значение1; [значение2]; ...)", "d": "Возвращает среднее арифметическое указанных аргументов. При этом текстовые и ложные логические значения считаются равными 0, а истинные логические значения считаются равными 1. В качестве аргументов могут быть указаны числа, имена, массивы или ссылки"}, "AVERAGEIF": {"a": "(диапазон; условие; [диапазон_усреднения])", "d": "Вычисляет среднее (арифметическое) для ячеек, заданных указанным условием"}, "AVERAGEIFS": {"a": "(диапазон_усреднения; диапазон_условия; условие; ...)", "d": "Вычисляет среднее (арифметическое) для ячеек, удовлетворяющие заданному набору условий"}, "BETADIST": {"a": "(x; альфа; бета; [А]; [B])", "d": "Возвращает интегральную функцию плотности бета-вероятности"}, "BETAINV": {"a": "(вероятность; альфа; бета; [А]; [B])", "d": "Возвращает обратную функцию к интегральной функции плотности бета-вероятности"}, "BETA.DIST": {"a": "(x; альфа; бета; интегральная; [А]; [B])", "d": "Возвращает функцию плотности бета-вероятности"}, "BETA.INV": {"a": "(вероятность; альфа; бета; [А]; [B])", "d": "Возвращает обратную функцию к интегральной функции плотности бета-вероятности (БЕТА.РАСП)"}, "BINOMDIST": {"a": "(число_успехов; число_испытаний; вероятность_успеха; интегральная)", "d": "Возвращает отдельное значение биномиального распределения"}, "BINOM.DIST": {"a": "(число_успехов; число_испытаний; вероятность_успеха; интегральная)", "d": "Возвращает отдельное значение биномиального распределения"}, "BINOM.DIST.RANGE": {"a": "(испытания; вероятность_успеха; число_успехов; [число_успехов2])", "d": "Возвращает вероятность результата испытания с использованием биномиального распределения"}, "BINOM.INV": {"a": "(число_испытаний; вероятность_успеха; альфа)", "d": "Возвращает наименьшее значение, для которого биномиальная функция распределения больше или равна заданного значения"}, "CHIDIST": {"a": "(x; степени_свободы)", "d": "Возвращает одностороннюю вероятность распределения хи-квадрат"}, "CHIINV": {"a": "(вероятность; степени_свободы)", "d": "Возвращает значение обратное к односторонней вероятности распределения хи-квадрат"}, "CHITEST": {"a": "(фактический_интервал; ожидаемый_интервал)", "d": "Возвращает тест на независимость: значение распределения хи-квадрат для статистического распределения и соответствующего числа степеней свободы"}, "CHISQ.DIST": {"a": "(x; степени_свободы; интегральная)", "d": "Возвращает левостороннюю вероятность распределения хи-квадрат"}, "CHISQ.DIST.RT": {"a": "(x; степени_свободы)", "d": "Возвращает правостороннюю вероятность распределения хи-квадрат"}, "CHISQ.INV": {"a": "(вероятность; степени_свободы)", "d": "Возвращает значение, обратное к левосторонней вероятности распределения хи-квадрат"}, "CHISQ.INV.RT": {"a": "(вероятность; степени_свободы)", "d": "Возвращает значение, обратное к правосторонней вероятности распределения хи-квадрат"}, "CHISQ.TEST": {"a": "(фактический_интервал; ожидаемый_интервал)", "d": "Возвращает тест на независимость: значение распределения хи-квадрат для статистического распределения и соответствующего числа степеней свободы"}, "CONFIDENCE": {"a": "(альфа; станд_откл; размер)", "d": "Возвращает доверительный интервал для среднего генеральной совокупности, используя нормальное распределение"}, "CONFIDENCE.NORM": {"a": "(альфа; станд_откл; размер)", "d": "Возвращает доверительный интервал для среднего генеральной совокупности с использованием нормального распределения"}, "CONFIDENCE.T": {"a": "(альфа; станд_откл; размер)", "d": "Возвращает доверительный интервал для среднего генеральной совокупности с использованием распределения Стьюдента"}, "CORREL": {"a": "(массив1; массив2)", "d": "Возвращает коэффициент корреляции между двумя множествами данных"}, "COUNT": {"a": "(значение1; [значение2]; ...)", "d": "Подсчитывает количество ячеек в диапазоне, который содержит числа"}, "COUNTA": {"a": "(значение1; [значение2]; ...)", "d": "Подсчитывает количество непустых ячеек в диапазоне"}, "COUNTBLANK": {"a": "(диапазон)", "d": "Подсчитывает количество пустых ячеек в диапазоне"}, "COUNTIFS": {"a": "(диапазон_условия; условие; ...)", "d": "Подсчитывает количество ячеек, удовлетворяющих заданному набору условий"}, "COUNTIF": {"a": "(диа<PERSON><PERSON><PERSON><PERSON>н; критерий)", "d": "Подсчитывает количество непустых ячеек в диапазоне, удовлетворяющих заданному условию"}, "COVAR": {"a": "(массив1; массив2)", "d": "Возвращает ковариацию, среднее попарных произведений отклонений"}, "COVARIANCE.P": {"a": "(массив1; массив2)", "d": "Возвращает ковариацию генеральной совокупности, среднее попарных произведений отклонений"}, "COVARIANCE.S": {"a": "(массив1; массив2)", "d": "Возвращает ковариацию выборки, среднее попарных произведений отклонений"}, "CRITBINOM": {"a": "(число_испытаний; вероятность_успеха; альфа)", "d": "Возвращает наименьшее значение, для которого биномиальная функция распределения больше или равна заданного значения"}, "DEVSQ": {"a": "(число1; [число2]; ...)", "d": "Возвращает сумму квадратов отклонений точек данных от среднего по выборке"}, "EXPONDIST": {"a": "(x; лямбда; интегральная)", "d": "Возвращает экспоненциальное распределение"}, "EXPON.DIST": {"a": "(x; лямбда; интегральная)", "d": "Возвращает экспоненциальное распределение"}, "FDIST": {"a": "(x; степени_свободы1; степени_свободы2)", "d": "Возвращает одностороннее F-распределение вероятности (степень отклонения) для двух наборов данных"}, "FINV": {"a": "(вероятность; степени_свободы1; степени_свободы2)", "d": "Возвращает обратное значение для одностороннего F-распределения вероятностей: если p = FРАСП(x,...), то FРАСПОБР(p,...) = x"}, "FTEST": {"a": "(массив1; массив2)", "d": "Возвращает результат F-теста, двустороннюю вероятность сходства двух совокупностей"}, "F.DIST": {"a": "(x; степени_свободы1; степени_свободы2; интегральная)", "d": "Возвращает (левостороннее) F-распределение вероятности (степень отклонения) для двух наборов данных"}, "F.DIST.RT": {"a": "(x; степени_свободы1; степени_свободы2)", "d": "Возвращает (правостороннее) F-распределение вероятности (степень отклонения) для двух наборов данных"}, "F.INV": {"a": "(вероятность; степени_свободы1; степени_свободы2)", "d": "Возвращает обратное значение для (левостороннего) F-распределения вероятностей: если p = F.РАСП(x,...), то F.ОБР(p,...) = x"}, "F.INV.RT": {"a": "(вероятность; степени_свободы1; степени_свободы2)", "d": "Возвращает обратное значение для (правостороннего) F-распределения вероятностей: если p = F.РАСП.ПХ(x,...), то F.ОБР(p,...) = x"}, "F.TEST": {"a": "(массив1; массив2)", "d": "Возвращает результат F-теста, двустороннюю вероятность сходства двух совокупностей"}, "FISHER": {"a": "(x)", "d": "Возвращает преобразование Фишера"}, "FISHERINV": {"a": "(y)", "d": "Возвращает обратное преобразование Фишера: если y = ФИШЕР(x), то ФИШЕРОБР(y) = x"}, "FORECAST": {"a": "(x; известные_значения_y; известные_значения_x)", "d": "Рассчитывает (или прогнозирует) будущее значение на линейном тренде на основании имеющихся значений"}, "FORECAST.ETS": {"a": "(целевая_дата; значения; временная_шкала; [сезонность]; [заполнение_данных]; [агрегирование])", "d": "Возвращает прогнозируемое значение на определенную целевую дату в будущем методом экспоненциального сглаживания."}, "FORECAST.ETS.CONFINT": {"a": "(целевая_дата; значения; временная_шкала; [вероятность]; [сезонность]; [заполнение_данных]; [агрегирование])", "d": "Возвращает доверительный интервал прогнозируемого значения на определенную целевую дату."}, "FORECAST.ETS.SEASONALITY": {"a": "(значения; временная_шкала; [заполнение_данных]; [агрегирование])", "d": "Возвращает продолжительность повторяющегося фрагмента, выявленного приложением в указанном временном ряду."}, "FORECAST.ETS.STAT": {"a": "(значения; временная_шкала; тип_статистики; [сезонность]; [заполнение_данных]; [агрегирование])", "d": "Возвращает запрошенную статистику для прогноза."}, "FORECAST.LINEAR": {"a": "(x; известные_значения_y; известные_значения_x)", "d": "Вычисляет или прогнозирует будущее значение линейного тренда, используя имеющиеся значения"}, "FREQUENCY": {"a": "(массив_данных; массив_интервалов)", "d": "Вычисляет распределение значений по интервалам и возвращает вертикальный массив, содержащий на один элемент больше, чем массив интервалов"}, "GAMMA": {"a": "(x)", "d": "Возвращает значение гамма-функции"}, "GAMMADIST": {"a": "(x; альфа; бета; интегральная)", "d": "Возвращает гамма-распределение"}, "GAMMA.DIST": {"a": "(x; альфа; бета; интегральная)", "d": "Возвращает гамма-распределение"}, "GAMMAINV": {"a": "(вероятность; альфа; бета)", "d": "Возвращает обратное гамма-распределение"}, "GAMMA.INV": {"a": "(вероятность; альфа; бета)", "d": "Возвращает обратное интегральное гамма-распределение: если p = ГАММА.РАСП(x,...), то ГАММА.ОБР(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Возвращает натуральный логарифм гамма-функции"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Возвращает натуральный логарифм гамма-функции"}, "GAUSS": {"a": "(x)", "d": "Возвращает число, на 0,5 меньшее, чем стандартное нормальное интегральное распределение"}, "GEOMEAN": {"a": "(число1; [число2]; ...)", "d": "Возвращает среднее геометрическое для массива или диапазона из положительных чисел"}, "GROWTH": {"a": "(известные_значения_y; [известные_значения_x]; [новые_значения_x]; [конст])", "d": "Возвращает значения в соответствии с экспоненциальным трендом"}, "HARMEAN": {"a": "(число1; [число2]; ...)", "d": "Возвращает среднее гармоническое для множества положительных чисел - величину, обратную среднему арифметическому обратных величин"}, "HYPGEOM.DIST": {"a": "(усп_выб; размер_выб; усп_сов; размер_сов; интегральная)", "d": "Возвращает гипергеометрическое распределение числа успехов в выборке"}, "HYPGEOMDIST": {"a": "(число_успехов_в_выборке; размер_выборки; число_успехов_в_совокупности; размер_совокупности)", "d": "Возвращает гипергеометрическое распределение"}, "INTERCEPT": {"a": "(известные_значения_y; известные_значения_x)", "d": "Возвращает отрезок, отсекаемый на оси линией линейной регрессии"}, "KURT": {"a": "(число1; [число2]; ...)", "d": "Возвращает эксцесс множества данных"}, "LARGE": {"a": "(массив; k)", "d": "Возвращает k-ое наибольшее значение в множестве данных (например, пятое наибольшее)"}, "LINEST": {"a": "(известные_значения_y; [известные_значения_x]; [конст]; [статистика])", "d": "Возвращает параметры линейного приближения по методу наименьших квадратов"}, "LOGEST": {"a": "(известные_значения_y; [известные_значения_x]; [конст]; [статистика])", "d": "Возвращает параметры экспоненциального приближения"}, "LOGINV": {"a": "(вероятность; среднее; стандартное_отклонение)", "d": "Возвращает обратное логарифмическое нормальное распределение, где ln(x) представляет собой нормальное распределение"}, "LOGNORM.DIST": {"a": "(x; среднее; стандартное_откл; интегральная)", "d": "Возвращает логнормальное распределение, где ln(x) представляет собой нормальное распределение"}, "LOGNORM.INV": {"a": "(вероятность; среднее; стандартное_отклонение)", "d": "Возвращает обратное логарифмическое нормальное распределение, где ln(x) представляет собой нормальное распределение"}, "LOGNORMDIST": {"a": "(x; среднее; стандартное_откл)", "d": "Возвращает интегральное логнормальное распределение, где ln(x) представляет собой нормальное распределение"}, "MAX": {"a": "(число1; [число2]; ...)", "d": "Возвращает наибольшее значение из списка аргументов. Логические и текстовые значения игнорируются"}, "MAXA": {"a": "(значение1; [значение2]; ...)", "d": "Возвращает наибольшее значение из набора значений. Учитываются логические и текстовые значения"}, "MAXIFS": {"a": "(максимальный_диапазон; диапазон_условий; условия; ...)", "d": "Возвращает максимальное значение ячеек, заданных набором условий или критериев"}, "MEDIAN": {"a": "(число1; [число2]; ...)", "d": "Возвращает медиану исходных чисел"}, "MIN": {"a": "(число1; [число2]; ...)", "d": "Возвращает наименьшее значение из списка аргументов. Логические и текстовые значения игнорируются"}, "MINA": {"a": "(значение1; [значение2]; ...)", "d": "Возвращает наименьшее значение из набора значений. Учитываются логические и текстовые значения"}, "MINIFS": {"a": "(минимальный_диапазон; диапазон_условий; условия; ...)", "d": "Возвращает минимальное значение ячеек, заданных набором условий или критериев"}, "MODE": {"a": "(число1; [число2]; ...)", "d": "Возвращает значение моды для массива или диапазона значений"}, "MODE.MULT": {"a": "(число1; [число2]; ...)", "d": "Возвращает вертикальный массив наиболее часто встречающихся (повторяющихся) значений в массиве или диапазоне значений. Для горизонтального массива используйте выражение =ТРАНСП(МОДА.НСК(число1,число2,...))"}, "MODE.SNGL": {"a": "(число1; [число2]; ...)", "d": "Возвращает значение моды для массива или диапазона значений"}, "NEGBINOM.DIST": {"a": "(число_неудач; число_успехов; вероятность_успеха; интегральная)", "d": "Возвращает отрицательное биномиальное распределение - вероятность возникновения определенного числа неудач до указанного количества успехов, с данной вероятностью успеха"}, "NEGBINOMDIST": {"a": "(число_неудач; число_успехов; вероятность_успеха)", "d": "Возвращает отрицательное биномиальное распределение - вероятность возникновения определенного числа неудач до указанного количества успехов, с данной вероятностью успеха"}, "NORM.DIST": {"a": "(x; среднее; стандартное_откл; интегральная)", "d": "Возвращает нормальную функцию распределения"}, "NORMDIST": {"a": "(x; среднее; стандартное_откл; интегральная)", "d": "Возвращает нормальную функцию распределения"}, "NORM.INV": {"a": "(вероятность; среднее; стандартное_откл)", "d": "Возвращает обратное нормальное распределение"}, "NORMINV": {"a": "(вероятность; среднее; стандартное_откл)", "d": "Возвращает обратное нормальное распределение"}, "NORM.S.DIST": {"a": "(z; интегральная)", "d": "Возвращает стандартное нормальное интегральное распределение"}, "NORMSDIST": {"a": "(z)", "d": "Возвращает стандартное нормальное интегральное распределение"}, "NORM.S.INV": {"a": "(вероятность)", "d": "Возвращает обратное значение стандартного нормального распределения"}, "NORMSINV": {"a": "(вероятность)", "d": "Возвращает обратное значение стандартного нормального распределения"}, "PEARSON": {"a": "(массив1; массив2)", "d": "Возвращает коэффициент корреляции Пирсона, r"}, "PERCENTILE": {"a": "(массив; k)", "d": "Возвращает k-й процентиль для значений диапазона"}, "PERCENTILE.EXC": {"a": "(массив; k)", "d": "Возвращает k-й процентиль для значений диапазона (k от 0 до 1 не включительно)"}, "PERCENTILE.INC": {"a": "(массив; k)", "d": "Возвращает k-й процентиль для значений диапазона, при k от 0 до 1 включительно"}, "PERCENTRANK": {"a": "(массив; x; [разрядность])", "d": "Возвращает процентную норму значения в множестве данных"}, "PERCENTRANK.EXC": {"a": "(массив; x; [разрядность])", "d": "Возвращает процентную норму значения в множестве данных (от 0 до 1 не включительно)"}, "PERCENTRANK.INC": {"a": "(массив; x; [разрядность])", "d": "Возвращает процентную норму значения в множестве данных (от 0 до 1 включительно)"}, "PERMUT": {"a": "(число; число_выбранных)", "d": "Возвращает количество перестановок  заданного числа объектов, которые выбираются из общего числа объектов"}, "PERMUTATIONA": {"a": "(число; число_выбранных)", "d": "Возвращает количество перестановок для заданного числа объектов (с повторами), которые выбираются из общего числа объектов"}, "PHI": {"a": "(x)", "d": "Возвращает значение плотности стандартного нормального распределения"}, "POISSON": {"a": "(x; среднее; интегральная)", "d": "Возвращает распределение Пуассона"}, "POISSON.DIST": {"a": "(x; среднее; интегральная)", "d": "Возвращает распределение Пуассона"}, "PROB": {"a": "(x_интервал; интервал_вероятностей; нижний_предел; [верхний_предел])", "d": "Возвращает вероятность того, что значения диапазона находятся внутри заданных пределов"}, "QUARTILE": {"a": "(массив; часть)", "d": "Возвращает квартиль множества данных"}, "QUARTILE.INC": {"a": "(массив; часть)", "d": "Возвращает квартиль множества данных по значениям процентиля от 0 до 1 включительно"}, "QUARTILE.EXC": {"a": "(массив; часть)", "d": "Возвращает квартиль множества данных по значениям процентиля от 0 до 1 не включительно"}, "RANK": {"a": "(число; ссылка; [порядок])", "d": "Возвращает ранг числа в списке чисел: его порядковый номер относительно других чисел в списке"}, "RANK.AVG": {"a": "(число; ссылка; [порядок])", "d": "Возвращает ранг числа в списке чисел: его порядковый номер относительно других чисел в списке; если несколько значений имеет одинаковый ранг, возвращается средний ранг"}, "RANK.EQ": {"a": "(число; ссылка; [порядок])", "d": "Возвращает ранг числа в списке чисел: его порядковый номер относительно других чисел в списке; если несколько значений имеет одинаковый ранг, возвращается высший ранг из этого набора значений"}, "RSQ": {"a": "(известные_значения_y; известные_значения_x)", "d": "Возвращает квадрат коэффициента корреляции Пирсона по данным точкам"}, "SKEW": {"a": "(число1; [число2]; ...)", "d": "Возвращает асимметрию распределения относительно среднего"}, "SKEW.P": {"a": "(число1; [число2]; ...)", "d": "Возвращает асимметрию распределения по генеральной совокупности относительно среднего"}, "SLOPE": {"a": "(известные_значения_y; известные_значения_x)", "d": "Возвращает наклон линии линейной регрессии"}, "SMALL": {"a": "(массив; k)", "d": "Возвращает k-ое наименьшее значение в множестве данных (например, пятое наименьшее)"}, "STANDARDIZE": {"a": "(x; среднее; стандартное_откл)", "d": "Возвращает нормализованное значение"}, "STDEV": {"a": "(число1; [число2]; ...)", "d": "Оценивает стандартное отклонение по выборке. Логические и текстовые значения игнорируются"}, "STDEV.P": {"a": "(число1; [число2]; ...)", "d": "Вычисляет стандартное отклонение по генеральной совокупности. Логические и текстовые значения игнорируются"}, "STDEV.S": {"a": "(число1; [число2]; ...)", "d": "Оценивает стандартное отклонение по выборке. Логические и текстовые значения игнорируются"}, "STDEVA": {"a": "(значение1; [значение2]; ...)", "d": "Вычисляет стандартное отклонение по выборке с учетом логических и текстовых значений. При этом текстовые и ложные логические значения считаются равными 0, а истинные логические значения считаются равными 1"}, "STDEVP": {"a": "(число1; [число2]; ...)", "d": "Вычисляет стандартное отклонение по генеральной совокупности. Логические и текстовые значения игнорируются"}, "STDEVPA": {"a": "(значение1; [значение2]; ...)", "d": "Вычисляет стандартное отклонение по генеральной совокупности с учетом логических и текстовых значений. При этом текстовые и ложные логические значения считаются равными 0, а истинные логические значения считаются равными 1"}, "STEYX": {"a": "(известные_значения_y; известные_значения_x)", "d": "Возвращает стандартную ошибку предсказанных значений y для каждого значения x в регрессии"}, "TDIST": {"a": "(x; степени_свободы; хвосты)", "d": "Возвращает t-распределение Стьюдента"}, "TINV": {"a": "(вероятность; степени_свободы)", "d": "Возвращает двустороннее обратное распределение Стьюдента"}, "T.DIST": {"a": "(x; степени_свободы; интегральная)", "d": "Возвращает левостороннее t-распределение Стьюдента"}, "T.DIST.2T": {"a": "(x; степени_свободы)", "d": "Возвращает двустороннее t-распределение Стьюдента"}, "T.DIST.RT": {"a": "(x; степени_свободы)", "d": "Возвращает правостороннее t-распределение Стьюдента"}, "T.INV": {"a": "(вероятность; степени_свободы)", "d": "Возвращает левостороннее обратное распределение Стьюдента"}, "T.INV.2T": {"a": "(вероятность; степени_свободы)", "d": "Возвращает двустороннее обратное распределение Стьюдента"}, "T.TEST": {"a": "(массив1; массив2; хвосты; тип)", "d": "Возвращает вероятность, соответствующую t-тесту Стьюдента"}, "TREND": {"a": "(известные_значения_y; [известные_значения_x]; [новые_значения_x]; [конст])", "d": "Возвращает значения в соответствии с линейной аппроксимацией по методу наименьших квадратов"}, "TRIMMEAN": {"a": "(массив; доля)", "d": "Возвращает среднее внутренней части множества данных"}, "TTEST": {"a": "(массив1; массив2; хвосты; тип)", "d": "Возвращает вероятность, соответствующую t-тесту Стьюдента"}, "VAR": {"a": "(число1; [число2]; ...)", "d": "Оценивает дисперсию по выборке. Логические и текстовые значения игнорируются"}, "VAR.P": {"a": "(число1; [число2]; ...)", "d": "Вычисляет дисперсию для генеральной совокупности. Логические и текстовые значения игнорируются"}, "VAR.S": {"a": "(число1; [число2]; ...)", "d": "Оценивает дисперсию по выборке. Логические и текстовые значения игнорируются"}, "VARA": {"a": "(значение1; [значение2]; ...)", "d": "Вычисляет дисперсию по выборке с учетом логических и текстовых значений. При этом текстовые и ложные логические значения считаются равными 0, а истинные логические значения считаются равными 1"}, "VARP": {"a": "(число1; [число2]; ...)", "d": "Вычисляет дисперсию для генеральной совокупности. Логические и текстовые значения игнорируются"}, "VARPA": {"a": "(значение1; [значение2]; ...)", "d": "Вычисляет дисперсию по генеральной совокупности с учетом логических и текстовых значений. При этом текстовые и ложные логические значения считаются равными 0, а истинные логические значения считаются равными 1"}, "WEIBULL": {"a": "(x; альфа; бета; интегральная)", "d": "Возвращает распределение Вейбулла"}, "WEIBULL.DIST": {"a": "(x; альфа; бета; интегральная)", "d": "Возвращает распределение Вейбулла"}, "Z.TEST": {"a": "(массив; x; [сигма])", "d": "Возвращает одностороннее P-значение z-теста"}, "ZTEST": {"a": "(массив; x; [сигма])", "d": "Возвращает одностороннее P-значение z-теста"}, "ACCRINT": {"a": "(дата_выпуска; первый_доход; дата_согл; ставка; номинал; частота; [базис]; [способ_расчета])", "d": "Возвращает накопленный процент по ценным бумагам с периодической выплатой процентов."}, "ACCRINTM": {"a": "(дата_выпуска; дата_согл; ставка; номинал; [базис])", "d": "Возвращает накопленный процент по ценным бумагам, процент по которым выплачивается в срок погашения"}, "AMORDEGRC": {"a": "(стоимость; дата_приобр; первый_период; остаточная_стоимость; период; ставка; [базис])", "d": "Возвращает величину пропорционально распределенной амортизации актива для каждого учетного периода."}, "AMORLINC": {"a": "(стоимость; дата_приобр; первый_период; остаточная_стоимость; период; ставка; [базис])", "d": "Возвращает величину пропорционально распределенной амортизации актива для каждого учетного периода."}, "COUPDAYBS": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает количество дней от начала действия купона до даты расчета"}, "COUPDAYS": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает количество дней в периоде купона, который содержит дату расчета"}, "COUPDAYSNC": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает количество дней от даты расчета до срока следующего купона"}, "COUPNCD": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает дату следующего купона после даты расчета"}, "COUPNUM": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает количество купонов между датой расчета и сроком погашения ценных бумаг"}, "COUPPCD": {"a": "(дата_согл; дата_вступл_в_силу; частота; [базис])", "d": "Возвращает дату предыдущего купона до даты расчета"}, "CUMIPMT": {"a": "(ставка; кол_пер; нз; нач_период; кон_период; тип)", "d": "Возвращает кумулятивную (нарастающим итогом) величину процентов, выплачиваемых по займу в промежутке между двумя периодами выплат"}, "CUMPRINC": {"a": "(ставка; кол_пер; нз; нач_период; кон_период; тип)", "d": "Возвращает кумулятивную (нарастающим итогом) сумму, выплачиваемую в погашение основной суммы займа в промежутке между двумя периодами"}, "DB": {"a": "(нач_стоимость; ост_стоимость; время_эксплуатации; период; [месяцы])", "d": "Возвращает величину амортизации актива для заданного периода, рассчитанную методом фиксированного уменьшения остатка"}, "DDB": {"a": "(нач_стоимость; ост_стоимость; время_эксплуатации; период; [коэффициент])", "d": "Возвращает значение амортизации актива за данный период, используя метод двойного уменьшения остатка или иной явно указанный метод"}, "DISC": {"a": "(дата_согл; дата_вступл_в_силу; цена; погашение; [базис])", "d": "Возвращает ставку дисконтирования для ценных бумаг"}, "DOLLARDE": {"a": "(дроб_руб; дроб)", "d": "Преобразует цену в рублях, выраженную в виде дроби, в цену в рублях, выраженную десятичным числом"}, "DOLLARFR": {"a": "(дроб_руб; дроб)", "d": "Преобразует цену в рублях, выраженную десятичным числом, в цену в рублях, выраженную в виде дроби"}, "DURATION": {"a": "(дата_согл; дата_вступл_в_силу; купон; доход; частота; [базис])", "d": "Возвращает дюрацию для ценных бумаг, по которым выплачивается периодический процент"}, "EFFECT": {"a": "(номинальная_ставка; кол_пер)", "d": "Возвращает фактическую (эффективную) годовую процентную ставку"}, "FV": {"a": "(ставка; кпер; плт; [пс]; [тип])", "d": "Возвращает будущую стоимость инвестиции на основе периодических постоянных (равных по величине сумм) платежей и постоянной процентной ставки"}, "FVSCHEDULE": {"a": "(первичное; план)", "d": "Возвращает будущее значение первоначальной основной суммы после применения ряда (плана) ставок сложных процентов"}, "INTRATE": {"a": "(дата_согл; дата_вступл_в_силу; инвестиция; погашение; [базис])", "d": "Возвращает процентную ставку для полностью инвестированных ценных бумаг"}, "IPMT": {"a": "(ставка; период; кпер; пс; [бс]; [тип])", "d": "Возвращает сумму платежей процентов по инвестиции за данный период на основе постоянства сумм периодических платежей и процентной ставки"}, "IRR": {"a": "(значения; [предположение])", "d": "Возвращает внутреннюю ставку доходности для ряда потоков денежных средств, представленных численными значениями"}, "ISPMT": {"a": "(ставка; период; кпер; пс)", "d": "Вычисляет проценты, выплачиваемые за определенный инвестиционный период"}, "MDURATION": {"a": "(дата_согл; дата_вступл_в_силу; купон; доход; частота; [базис])", "d": "Возвращает модифицированную дюрацию для ценных бумаг с предполагаемой номинальной стоимостью 100 рублей"}, "MIRR": {"a": "(значения; ставка_финанс; ставка_реинвест)", "d": "Возвращает внутреннюю ставку доходности для ряда периодических денежных потоков, учитывая как затраты на привлечение инвестиции, так и процент, получаемый от реинвестирования денежных средств"}, "NOMINAL": {"a": "(факт_ставка; кол_пер)", "d": "Возвращает номинальную годовую процентную ставку"}, "NPER": {"a": "(ставка; плт; пс; [бс]; [тип])", "d": "Возвращает общее количество периодов выплаты для инвестиции на основе периодических постоянных выплат и постоянной процентной ставки"}, "NPV": {"a": "(ставка; значение1; [значение2]; ...)", "d": "Возвращает величину чистой приведенной стоимости инвестиции, используя ставку дисконтирования и стоимости будущих выплат (отрицательные значения) и поступлений (положительные значения)"}, "ODDFPRICE": {"a": "(дата_согл; дата_вступл_в_силу; дата_выпуска; первый_купон; ставка; доход; погашение; частота; [базис])", "d": "Возвращает цену за 100 рублей номинальной стоимости ценных бумаг с нерегулярным (коротким или длинным) первым периодом купона"}, "ODDFYIELD": {"a": "(дата_согл; дата_вступл_в_силу; дата_выпуска; первый_купон; ставка; цена; погашение; частота; [базис])", "d": "Возвращает доход по ценным бумагам с нерегулярным (коротким или длинным) первым периодом купона"}, "ODDLPRICE": {"a": "(дата_согл; дата_вступл_в_силу; посл_купон; ставка; доход; погашение; частота; [базис])", "d": "Возвращает цену за 100 рублей номинальной стоимости ценных бумаг с нерегулярным (коротким или длинным) последним периодом купона"}, "ODDLYIELD": {"a": "(дата_согл; дата_вступл_в_силу; посл_купон; ставка; цена; погашение; частота; [базис])", "d": "Возвращает доход по ценным бумагам с нерегулярным (коротким или длинным) последним периодом купона"}, "PDURATION": {"a": "(ставка; тс; бс)", "d": "Возвращает число периодов, необходимое для достижения указанной стоимости инвестиции"}, "PMT": {"a": "(ставка; кпер; пс; [бс]; [тип])", "d": "Возвращает сумму периодического платежа для займа на основе постоянства сумм платежей и процентной ставки"}, "PPMT": {"a": "(ставка; период; кпер; пс; [бс]; [тип])", "d": "Возвращает величину платежа в погашение основной суммы по инвестиции за данный период на основе постоянства периодических платежей и процентной ставки"}, "PRICE": {"a": "(дата_согл; дата_вступл_в_силу; ставка; доход; погашение; частота; [базис])", "d": "Возвращает цену за 100 рублей номинальной стоимости ценных бумаг, по которым выплачивается периодический процент"}, "PRICEDISC": {"a": "(дата_согл; дата_вступл_в_силу; дисконт; погашение; [базис])", "d": "Возвращает цену за 100 рублей номинальной стоимости ценных бумаг с дисконтом"}, "PRICEMAT": {"a": "(дата_согл; дата_вступл_в_силу; дата_выпуска; ставка; доходность; [базис])", "d": "Возвращает цену за 100 рублей номинальной стоимости ценных бумаг, по которым процент выплачивается в срок погашения"}, "PV": {"a": "(ставка; кпер; плт; [бс]; [тип])", "d": "Возвращает приведенную (к текущему моменту) стоимость инвестиции — общую сумму, которая на настоящий момент равноценна ряду будущих выплат"}, "RATE": {"a": "(кпер; плт; пс; [бс]; [тип]; [предположение])", "d": "Возвращает процентную ставку по аннуитету за один период. Например, при годовой процентной ставке в 6% для квартальной ставки используется значение 6%/4"}, "RECEIVED": {"a": "(дата_согл; дата_вступл_в_силу; инвестиция; дисконт; [базис])", "d": "Возвращает сумму, полученную к сроку погашения полностью инвестированных ценных бумаг"}, "RRI": {"a": "(кпер; тс; бс)", "d": "Возвращает эквивалентную процентную ставку для заданного роста инвестиции"}, "SLN": {"a": "(нач_стоимость; ост_стоимость; время_эксплуатации)", "d": "Возвращает величину амортизации актива за один период, рассчитанную линейным методом"}, "SYD": {"a": "(нач_стоимость; ост_стоимость; время_эксплуатации; период)", "d": "Возвращает величину амортизации актива за данный период, рассчитанную методом суммы годовых чисел"}, "TBILLEQ": {"a": "(дата_согл; дата_вступл_в_силу; скидка)", "d": "Возвращает эквивалентный облигации доход по казначейскому векселю"}, "TBILLPRICE": {"a": "(дата_согл; дата_вступл_в_силу; скидка)", "d": "Возвращает цену за 100 рублей номинальной стоимости для казначейского векселя"}, "TBILLYIELD": {"a": "(дата_согл; дата_вступл_в_силу; цена)", "d": "Возвращает доходность по казначейскому векселю"}, "VDB": {"a": "(нач_стоимость; ост_стоимость; время_эксплуатации; нач_период; кон_период; [коэффициент]; [без_переключения])", "d": "Возвращает величину амортизации актива для любого выбранного периода, в том числе для частичных периодов, с использованием метода двойного уменьшения остатка или иного указанного метода"}, "XIRR": {"a": "(значения; даты; [предп])", "d": "Возвращает внутреннюю ставку доходности для графика денежных потоков"}, "XNPV": {"a": "(ставка; значения; даты)", "d": "Возвращает чистую приведенную стоимость для графика денежных потоков"}, "YIELD": {"a": "(дата_согл; дата_вступл_в_силу; ставка; цена; погашение; частота; [базис])", "d": "Возвращает доходность ценных бумаг, по которым выплачивается периодический процент"}, "YIELDDISC": {"a": "(дата_согл; дата_вступл_в_силу; цена; погашение; [базис])", "d": "Возвращает годовую доходность по ценным бумагам с дисконтом, например по казначейским векселям"}, "YIELDMAT": {"a": "(дата_согл; дата_вступл_в_силу; дата_выпуска; ставка; цена; [базис])", "d": "Возвращает годовую доходность ценных бумаг, по которым процент выплачивается в срок погашения"}, "ABS": {"a": "(число)", "d": "Возвращает модуль (абсолютную величину) числа"}, "ACOS": {"a": "(число)", "d": "Возвращает арккосинус числа в радианах, в диапазоне от 0 до Пи. Арккосинус числа есть угол, косинус которого равен числу."}, "ACOSH": {"a": "(число)", "d": "Возвращает гиперболический арккосинус числа"}, "ACOT": {"a": "(число)", "d": "Возвращает арккотангенс числа в радианах от 0 до пи"}, "ACOTH": {"a": "(число)", "d": "Возвращает обратный гиперболический котангенс числа"}, "AGGREGATE": {"a": "(номер_функции; параметры; ссылка1; ...)", "d": "Возвращает сводное значение в списке или базе данных"}, "ARABIC": {"a": "(текст)", "d": "Преобразует римское число в арабское"}, "ASC": {"a": "(текст)", "d": "Для языков с двухбайтовой кодировкой (DBCS) преобразует полноширинные (двухбайтовые) знаки в полуширинные (однобайтовые)"}, "ASIN": {"a": "(число)", "d": "Возвращает арксинус числа в радианах, в диапазоне от -Пи/2 до Пи/2"}, "ASINH": {"a": "(число)", "d": "Возвращает гиперболический арксинус числа"}, "ATAN": {"a": "(число)", "d": "Возвращает арктангенс числа в радианах, в диапазоне от -Пи/2 до Пи/2"}, "ATAN2": {"a": "(x; y)", "d": "Возвращает арктангенс для заданных координат x и y, в радианах между -Пи и Пи, исключая -Пи"}, "ATANH": {"a": "(число)", "d": "Возвращает гиперболический арктангенс числа"}, "BASE": {"a": "(число; основание; [мин_длина])", "d": "Преобразует число в текстовое представление в системе счисления с заданным основанием"}, "CEILING": {"a": "(число; точность)", "d": "Округляет число до ближайшего большего по модулю целого, кратного указанному значению"}, "CEILING.MATH": {"a": "(число; [точность]; [режим])", "d": "Округляет число вверх до ближайшего целого или ближайшего кратного указанной точности"}, "CEILING.PRECISE": {"a": "(x;[точность])", "d": "Округляет число вверх до ближайшего целого или до ближайшего кратного указанному значению"}, "COMBIN": {"a": "(число; число_выбранных)", "d": "Возвращает количество комбинаций для заданного числа элементов"}, "COMBINA": {"a": "(число; число_выбранных)", "d": "Возвращает количество комбинаций с повторами для заданного числа элементов"}, "COS": {"a": "(число)", "d": "Возвращает косинус угла"}, "COSH": {"a": "(число)", "d": "Возвращает гиперболический косинус числа"}, "COT": {"a": "(число)", "d": "Возвращает котангенс угла"}, "COTH": {"a": "(число)", "d": "Возвращает гиперболический котангенс угла"}, "CSC": {"a": "(число)", "d": "Возвращает косеканс угла"}, "CSCH": {"a": "(число)", "d": "Возвращает гиперболический косеканс угла"}, "DECIMAL": {"a": "(число; основание)", "d": "Преобразует текстовое представление числа в системе счисления с заданным основанием в десятичное значение"}, "DEGREES": {"a": "(угол)", "d": "Преобразует радианы в градусы"}, "ECMA.CEILING": {"a": "(x;точность)", "d": "Округляет число в большую сторону до ближайшего числа, кратного заданной значимости"}, "EVEN": {"a": "(число)", "d": "Округляет число до ближайшего четного целого. Положительные числа округляются в сторону увеличения, отрицательные - в сторону уменьшения"}, "EXP": {"a": "(число)", "d": "Возвращает экспоненту заданного числа"}, "FACT": {"a": "(число)", "d": "Возвращает факториал числа, равный 1*2*3*..*число"}, "FACTDOUBLE": {"a": "(число)", "d": "Возвращает двойной факториал числа"}, "FLOOR": {"a": "(число; точность)", "d": "Округляет число до ближайшего меньшего по модулю целого, кратного указанному значению"}, "FLOOR.PRECISE": {"a": "(x;[точность])", "d": "Возвращает число, округленное с недостатком до ближайшего целого или до ближайшего кратного разрядности"}, "FLOOR.MATH": {"a": "(число; [точность]; [режим])", "d": "Округляет число вниз до ближайшего целого или ближайшего кратного указанной точности"}, "GCD": {"a": "(число1; [число2]; ...)", "d": "Возвращает наибольший общий делитель"}, "INT": {"a": "(число)", "d": "Округляет число до ближайшего меньшего целого"}, "ISO.CEILING": {"a": "(число;[точность])", "d": "Округляет число вверх до ближайшего целого или до ближайшего кратного указанному значению вне зависимости от его знака; если в качестве точности указан нуль, возвращается нуль"}, "LCM": {"a": "(число1; [число2]; ...)", "d": "Возвращает наименьшее общее кратное"}, "LN": {"a": "(число)", "d": "Возвращает натуральный логарифм числа"}, "LOG": {"a": "(число; [основание])", "d": "Возвращает логарифм числа по заданному основанию"}, "LOG10": {"a": "(число)", "d": "Возвращает десятичный логарифм числа"}, "MDETERM": {"a": "(массив)", "d": "Возвращает определитель матрицы (матрица хранится в массиве)"}, "MINVERSE": {"a": "(массив)", "d": "Возвращает обратную матрицу (матрица хранится в массиве)"}, "MMULT": {"a": "(массив1; массив2)", "d": "Возвращает матричное произведение двух массивов; результат имеет то же число строк, что и первый массив, и то же число столбцов, что и второй массив"}, "MOD": {"a": "(число; делитель)", "d": "Возвращает остаток от деления"}, "MROUND": {"a": "(число; точность)", "d": "Возвращает число, округленное с желаемой точностью"}, "MULTINOMIAL": {"a": "(число1; [число2]; ...)", "d": "Возвращает отношение факториала суммы значений к произведению факториалов значений"}, "MUNIT": {"a": "(размер)", "d": "Возвращает единичную матрицу указанного размера"}, "ODD": {"a": "(число)", "d": "Округляет число до ближайшего нечетного целого: положительное - в сторону увеличения, отрицательное - в сторону уменьшения"}, "PI": {"a": "()", "d": "Возвращает округленное до 15 знаков после запятой число Пи (значение 3,14159265358979)"}, "POWER": {"a": "(число; степень)", "d": "Возвращает результат возведения в степень"}, "PRODUCT": {"a": "(число1; [число2]; ...)", "d": "Возвращает произведение аргументов"}, "QUOTIENT": {"a": "(числитель; знаменатель)", "d": "Возвращает целую часть результата деления с остатком"}, "RADIANS": {"a": "(угол)", "d": "Преобразует градусы в радианы"}, "RAND": {"a": "()", "d": "Возвращает равномерно распределенное случайное число большее или равное 0 и меньшее 1 (изменяется при пересчете)"}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "Возвращает массив случайных чисел"}, "RANDBETWEEN": {"a": "(нижн_граница; верхн_граница)", "d": "Возвращает случайное число между двумя заданными числами"}, "ROMAN": {"a": "(число; [форма])", "d": "Преобразует арабские числа в римские, в текстовом формате"}, "ROUND": {"a": "(число; число_разрядов)", "d": "Округляет число до указанного количества десятичных разрядов"}, "ROUNDDOWN": {"a": "(число; число_разрядов)", "d": "Округляет число до ближайшего меньшего по модулю"}, "ROUNDUP": {"a": "(число; число_разрядов)", "d": "Округляет число до ближайшего большего по модулю"}, "SEC": {"a": "(число)", "d": "Возвращает секанс угла"}, "SECH": {"a": "(число)", "d": "Возвращает гиперболический секанс угла"}, "SERIESSUM": {"a": "(x; n; m; коэффициенты)", "d": "Возвращает сумму степенного ряда, вычисленную по формуле"}, "SIGN": {"a": "(число)", "d": "Возвращает знак числа: 1 - если число положительное, 0 - если оно равно нулю и -1 - если число отрицательное"}, "SIN": {"a": "(число)", "d": "Возвращает синус угла"}, "SINH": {"a": "(число)", "d": "Возвращает гиперболический синус числа"}, "SQRT": {"a": "(число)", "d": "Возвращает значение квадратного корня"}, "SQRTPI": {"a": "(число)", "d": "Возвращает квадратный корень из значения выражения (число * ПИ)"}, "SUBTOTAL": {"a": "(номер_функции; ссылка1; ...)", "d": "Возвращает промежуточные итоги в список или базу данных"}, "SUM": {"a": "(число1; [число2]; ...)", "d": "Суммирует аргументы"}, "SUMIF": {"a": "(диа<PERSON>аз<PERSON><PERSON>; критер<PERSON>; [диапазон_суммирования])", "d": "Суммирует ячейки, заданные указанным условием"}, "SUMIFS": {"a": "(диапазон_суммирования; диапазон_условия; условие; ...)", "d": "Суммир<PERSON><PERSON>т ячейки, удовлетворяющие заданному набору условий"}, "SUMPRODUCT": {"a": "(массив1; [массив2]; [массив3]; ...)", "d": "Возвращает сумму произведений диапазонов или массивов"}, "SUMSQ": {"a": "(число1; [число2]; ...)", "d": "Возвращает сумму квадратов аргументов. Аргументами могут являться числа, массивы, имена или ссылки на числовые значения"}, "SUMX2MY2": {"a": "(массив_x; массив_y)", "d": "Возвращает сумму разностей квадратов соответствующих значений в двух массивах"}, "SUMX2PY2": {"a": "(массив_x; массив_y)", "d": "Возвращает сумму сумм квадратов соответствующих элементов двух массивов"}, "SUMXMY2": {"a": "(массив_x; массив_y)", "d": "Возвращает сумму квадратов разностей соответствующих значений в двух массивах"}, "TAN": {"a": "(число)", "d": "Возвращает тангенс угла"}, "TANH": {"a": "(число)", "d": "Возвращает гиперболический тангенс числа"}, "TRUNC": {"a": "(число; [число_разрядов])", "d": "Отбрасывает дробную часть числа, так что остается целое число"}, "ADDRESS": {"a": "(номер_строки; номер_столбца; [тип_ссылки]; [а1]; [имя_листа])", "d": "Возвращает ссылку на одну ячейку в рабочем листе в виде текста"}, "CHOOSE": {"a": "(номер_индекса; значение1; [значение2]; ...)", "d": "Выбирает значение или действие из списка значений по номеру индекса"}, "COLUMN": {"a": "([ссылка])", "d": "Возвращает номер столбца, на который указывает ссылка"}, "COLUMNS": {"a": "(массив)", "d": "Возвращает количество столбцов в массиве или ссылке"}, "FORMULATEXT": {"a": "(ссылка)", "d": "Возвращает формулу в виде строки"}, "HLOOKUP": {"a": "(искомое_значение; таблица; номер_строки; [интервальный_просмотр])", "d": "Ищет значение в верхней строке таблицы и возвращает значение ячейки, находящейся в указанной строке того же столбца"}, "HYPERLINK": {"a": "(адрес; [имя])", "d": "Создает ссылку, открывающую документ, находящийся на жестком диске, сервере сети или в Интернете."}, "INDEX": {"a": "(массив; номер_строки; [номер_столбца]!ссылка; номер_строки; [номер_столбца]; [номер_области])", "d": "Возвращает значение или ссылку на ячейку на пересечении конкретных строки и столбца, в данном диапазоне"}, "INDIRECT": {"a": "(ссылка_на_ячейку; [а1])", "d": "Возвращает ссылку, заданную текстовой строкой"}, "LOOKUP": {"a": "(искомое_значение; просматриваемый_вектор; [вектор_результатов]!искомое_значение; массив)", "d": "Ищет значения в одной строке, одном столбце или массиве. Включен для обеспечения обратной совместимости"}, "MATCH": {"a": "(искомое_значение; просматриваемый_массив; [тип_сопоставления])", "d": "Возвращает относительную позицию в массиве элемента, соответствующего указанному значению с учетом указанного порядка"}, "OFFSET": {"a": "(ссылка; смещ_по_строкам; смещ_по_столбцам; [высота]; [ширина])", "d": "Возвращает ссылку на диапазон, смещенный относительно заданной ссылки на указанное число строк и столбцов"}, "ROW": {"a": "([ссылка])", "d": "Возвращает номер строки, определяемой ссылкой"}, "ROWS": {"a": "(массив)", "d": "Возвращает количество строк в ссылке или массиве"}, "TRANSPOSE": {"a": "(массив)", "d": "Преобразует вертикальный диапазон ячеек в горизонтальный, или наоборот"}, "UNIQUE": {"a": "(массив; [по_столбцам]; [только_один_раз])", "d": "Возвращает уникальные значения из диапазона или массива."}, "VLOOKUP": {"a": "(искомое_значение; таблица; номер_столбца; [интервальный_просмотр])", "d": "Ищет значение в крайнем левом столбце таблицы и возвращает значение ячейки, находящейся в указанном столбце той же строки. По умолчанию таблица должна быть отсортирована по возрастанию"}, "XLOOKUP": {"a": "(искомое_значение; просматриваемый_массив; возращаемый_массив; [если_ничего_не_найдено]; [режим_сопоставления]; [режим_поиска])", "d": "Ищет совпадение в диапазоне или массиве и возвращает соответствующий элемент из второго диапазона или массива. По умолчанию используется точное соответствие"}, "CELL": {"a": "(тип_сведений; [ссылка])", "d": "Возвращает сведения о форматировании, расположении или содержимом ячейки"}, "ERROR.TYPE": {"a": "(значение_ошибки)", "d": "Возвращает код ошибки, соответствующий ее значению."}, "ISBLANK": {"a": "(значение)", "d": "Проверяет, ссылается ли данная ссылка на пустую ячейку, и возвращает значение ИСТИНА или ЛОЖЬ"}, "ISERR": {"a": "(значение)", "d": "Проверяет, отличается ли значение от #Н/Д, и возвращает значение ИСТИНА или ЛОЖЬ"}, "ISERROR": {"a": "(значение)", "d": "Проверяет, является ли значение ошибкой, и возвращает значение ИСТИНА или ЛОЖЬ"}, "ISEVEN": {"a": "(число)", "d": "Возвращает значение ИСТИНА, если число четное"}, "ISFORMULA": {"a": "(ссылка)", "d": "Проверяет, содержится ли формула в ячейке, на которую указывает ссылка, и возвращает значение ИСТИНА или ЛОЖЬ"}, "ISLOGICAL": {"a": "(значение)", "d": "Проверяет, является ли значение логическим (ИСТИНА или ЛОЖЬ), и возвращает ИСТИНА или ЛОЖЬ"}, "ISNA": {"a": "(значение)", "d": "Проверяет, является ли значение недоступным (#Н/Д), и возвращает значение ИСТИНА или ЛОЖЬ"}, "ISNONTEXT": {"a": "(значение)", "d": "Возвращает ИСТИНА, если значение не является текстовым, и ЛОЖЬ в противном случае. Пустые ячейки не являются текстовыми"}, "ISNUMBER": {"a": "(значение)", "d": "Проверяет, является ли значение числом, и возвращает значение ИСТИНА или ЛОЖЬ"}, "ISODD": {"a": "(число)", "d": "Возвращает значение ИСТИНА, если число нечетное"}, "ISREF": {"a": "(значение)", "d": "Проверяет, является ли значение ссылкой, и возвращает значение ИСТИНА или ЛОЖЬ"}, "ISTEXT": {"a": "(значение)", "d": "Проверяет, является ли значение текстом, и возвращает значение ИСТИНА или ЛОЖЬ"}, "N": {"a": "(значение)", "d": "Преобразует нечисловые значения в числа, даты - в даты, представленные числами, значения ИСТИНА в 1, все остальные значения - в 0 (ноль)"}, "NA": {"a": "()", "d": "Возвращает неопределенное значение #Н/Д (значение недоступно)"}, "SHEET": {"a": "([значение])", "d": "Возвращает номер указанного листа"}, "SHEETS": {"a": "([ссылка])", "d": "Возвращает число листов в ссылке"}, "TYPE": {"a": "(value)", "d": "Возвращает целое число, обозначающее тип данных указанного значения: число = 1; строка = 2; логическое значение = 4; ошибка = 16; массив = 64; составные данные = 128"}, "AND": {"a": "(логическое_значение1; [логическое_значение2]; ...)", "d": "Проверяет, все ли аргументы имеют значение ИСТИНА, и возвращает значение ИСТИНА, если истинны все аргументы"}, "FALSE": {"a": "()", "d": "Возвращает логическое значение ЛОЖЬ"}, "IF": {"a": "(лог_выражение; [значение_если_истина]; [значение_если_ложь])", "d": "Проверяет, выполняется ли условие, и возвращает одно значение, если оно выполняется, и другое значение, если нет"}, "IFS": {"a": "(логическая_проверка; если_значение_истина; ...)", "d": "Проверяет соответствие одному или нескольким условиям и возвращает значение для первого условия со значением ИСТИНА"}, "IFERROR": {"a": "(значение; значение_если_ошибка)", "d": "Возвращает значение_если_ошибка, если выражение ошибочно; в противном случае возвращает само выражение"}, "IFNA": {"a": "(значение; значение_если_нд)", "d": "Возвращает указанное значение, если выражение дает результат #Н/Д; в противном случае возвращает результат выражения"}, "NOT": {"a": "(логическое_значение)", "d": "Изменяет значение ЛОЖЬ на ИСТИНА, а ИСТИНА на ЛОЖЬ"}, "OR": {"a": "(логическое_значение1; [логическое_значение2]; ...)", "d": "Проверяет, имеет ли хотя бы один из аргументов значение ИСТИНА, и возвращает значение ИСТИНА или ЛОЖЬ. Значение ЛОЖЬ возвращается только в том случае, если все аргументы имеют значение ЛОЖЬ"}, "SWITCH": {"a": "(выражение; значение1; результат1; [по_умолчанию_или_значение2]; [результат2]; ...)", "d": "Вычисляет выражение на основе списка значений и возвращает результат, соответствующий первому совпавшему значению. Если совпадение отсутствует, возвращается необязательное значение по умолчанию"}, "TRUE": {"a": "()", "d": "Возвращает логическое значение ИСТИНА"}, "XOR": {"a": "(логическое_значение1; [логическое_значение2]; ...)", "d": "Возвращает логическое \"исключающее или\" всех аргументов"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Возвращает текст перед символами-разделителями."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Возвращает текст после символов-разделителей."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Разбивает текст на строки или столбцы с помощью разделителей."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Переносит вектор строки или столбца после указанного числа значений."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Вертикально собирает массивы в один массив."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Горизонтально собирает массивы в один массив."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Возвращает строки из массива или ссылки."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Возвращает столбцы из массива или ссылки."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Возвращает массив в виде одного столбца."}, "TOROW": {"a": "(мас<PERSON>ив, [игнорировать], [сканировать_по_столбцам])", "d": "Возвращает массив в виде одной строки."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Переносит вектор строки или столбца после указанного числа значений."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Возвращает строки или столбцы из начала или конца массива."}, "DROP": {"a": "(array, rows, [columns])", "d": "Удаляет строки или столбцы из начала или конца массива."}}
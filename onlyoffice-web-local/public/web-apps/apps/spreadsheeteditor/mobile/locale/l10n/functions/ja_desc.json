{"DATE": {"a": "(年; 月; 日)", "d": "日付/時刻コードで指定した日付を表す数値を返します。"}, "DATEDIF": {"a": "(開始日; 終了日; 単位)", "d": "2 つの日付の間の日数、月数、または年数を計算します。"}, "DATEVALUE": {"a": "(日付文字列)", "d": "文字列の形式で表された日付を、組み込みの日付表示形式で数値に変換して返します。"}, "DAY": {"a": "(シリアル値)", "d": "シリアル値に対応する日を 1 から 31 までの整数で返します。"}, "DAYS": {"a": "(終了日; 開始日)", "d": "2 つの日付の間の日数を返します。"}, "DAYS360": {"a": "(開始日; 終了日; [方式])", "d": "1 年を 360 日として、指定した 2 つの日付の間の日数を返します。"}, "EDATE": {"a": "(開始日; 月)", "d": "開始日から起算して、指定した月だけ前あるいは後の日付に対応するシリアル値を計算します。"}, "EOMONTH": {"a": "(開始日; 月)", "d": "開始日から起算して、指定した月だけ前あるいは後の月の最終日に対応するシリアル値を計算します。"}, "HOUR": {"a": "(シリアル値)", "d": "時刻を 0 (午前 0 時) ～ 23 (午後 11 時) の範囲の整数で返します。"}, "ISOWEEKNUM": {"a": "(日付)", "d": "指定された日付のその年における ISO 週番号を返します。"}, "MINUTE": {"a": "(シリアル値)", "d": "分を 0 ～ 59 の範囲の整数で返します。"}, "MONTH": {"a": "(シリアル値)", "d": "月を 1 (1 月) ～ 12 (12 月) の範囲の整数で返します。"}, "NETWORKDAYS": {"a": "(開始日; 終了日; [祭日])", "d": "開始日と終了日の間にある週日の日数を計算します。"}, "NETWORKDAYS.INTL": {"a": "(開始日; 終了日; [週末]; [祭日])", "d": "ユーザー設定の週末パラメーターを使用して、開始日と終了日の間にある週日の日数を計算します。"}, "NOW": {"a": "()", "d": "現在の日付と時刻を表すシリアル値を返します。"}, "SECOND": {"a": "(シリアル値)", "d": "秒を 0 ～ 59 の範囲の整数で返します。"}, "TIME": {"a": "(時; 分; 秒)", "d": "指定した時刻を表すシリアル値 (0:00:00 (午前 12:00:00) から 23:59:59 (午後 11:59:59) までを表す 0 から 0.9999999 の範囲の小数値) を返します。"}, "TIMEVALUE": {"a": "(時刻文字列)", "d": "文字列で表された時刻を、シリアル値 (0 (午前 0 時) から 0.999988426 (午後 11 時 59 分 59 秒) までの数値) に変換します。数式の入力後に、数値を時刻表示形式に設定します。"}, "TODAY": {"a": "()", "d": "現在の日付を表すシリアル値 (日付や時刻の計算で使用されるコード) を返します。"}, "WEEKDAY": {"a": "(シリアル値; [種類])", "d": "日付に対応する曜日を 1 から 7 までの整数で返します。"}, "WEEKNUM": {"a": "(シリアル値; [週の基準])", "d": "日付がその年の第何週目に当たるかを返します。"}, "WORKDAY": {"a": "(開始日; 日数; [祭日])", "d": "開始日から起算して日数で指定した日数だけ前あるいは後の日付に対応するシリアル値を計算します。"}, "WORKDAY.INTL": {"a": "(開始日; 日数; [週末]; [祭日])", "d": "ユーザー定義の週末パラメーターを使用して、指定した日数だけ前あるいは後の日付に対応するシリアル値を計算します。"}, "YEAR": {"a": "(シリアル値)", "d": "年を 1900 ～ 9999 の範囲の整数で返します。"}, "YEARFRAC": {"a": "(開始日; 終了日; [基準])", "d": "開始日から終了日までの間の日数を、年を単位とする数値で表します。"}, "BESSELI": {"a": "(x; n)", "d": "修正ベッセル関数 In(x) を返します"}, "BESSELJ": {"a": "(x; n)", "d": "ベッセル関数 Jn(x) を返します"}, "BESSELK": {"a": "(x; n)", "d": "修正ベッセル関数 Kn(x) を返します"}, "BESSELY": {"a": "(x; n)", "d": "ベッセル関数 Yn(x) を返します"}, "BIN2DEC": {"a": "(数値)", "d": "2 進数を 10 進数に変換します。"}, "BIN2HEX": {"a": "(数値; [桁数])", "d": "2 進数を 16 進数に変換します。"}, "BIN2OCT": {"a": "(数値; [桁数])", "d": "2 進数を 8 進数に変換します。"}, "BITAND": {"a": "(数値1; 数値2)", "d": "2 つの数値のビット単位の 'And' を返します"}, "BITLSHIFT": {"a": "(数値; 移動数)", "d": "左に移動数ビット移動する数値を返します"}, "BITOR": {"a": "(数値1; 数値2)", "d": "2 つの数値のビット単位の 'Or' を返します"}, "BITRSHIFT": {"a": "(数値; 移動数)", "d": "右に移動数ビット移動する数値を返します"}, "BITXOR": {"a": "(数値1; 数値2)", "d": "2 つの数値のビット単位の 'Exclusive Or' を返します"}, "COMPLEX": {"a": "(実数; 虚数; [虚数単位])", "d": "指定した実数係数および虚数係数を複素数に変換します。"}, "CONVERT": {"a": "(数値; 変換前単位; 変換後単位)", "d": "数値の単位を変換します。"}, "DEC2BIN": {"a": "(数値; [桁数])", "d": "10 進数を 2 進数に変換します。"}, "DEC2HEX": {"a": "(数値; [桁数])", "d": "10 進数を 16 進数に変換します。"}, "DEC2OCT": {"a": "(数値; [桁数])", "d": "10 進数を 8 進数に変換します。"}, "DELTA": {"a": "(数値1; [数値2])", "d": "2 つの数値が等しいかどうかを判別します。"}, "ERF": {"a": "(下限; [上限])", "d": "誤差関数の積分値を返します。"}, "ERF.PRECISE": {"a": "(X)", "d": "誤差関数の積分値を返します。"}, "ERFC": {"a": "(x)", "d": "相補誤差関数の積分値を返します。"}, "ERFC.PRECISE": {"a": "(X)", "d": "相補誤差関数の積分値を返します。"}, "GESTEP": {"a": "(数値; [しきい値])", "d": "しきい値より大きいか小さいかの判定をします。"}, "HEX2BIN": {"a": "(数値; [桁数])", "d": "16 進数を 2 進数に変換します。"}, "HEX2DEC": {"a": "(数値)", "d": "16 進数を 10 進数に変換します。"}, "HEX2OCT": {"a": "(数値; [桁数])", "d": "16 進数を 8 進数に変換します。"}, "IMABS": {"a": "(複素数)", "d": "複素数の絶対値を計算します。"}, "IMAGINARY": {"a": "(複素数)", "d": "複素数の虚部の係数を返します。"}, "IMARGUMENT": {"a": "(複素数)", "d": "複素数を極形式で表現した場合の偏角θの値をラジアンを単位として計算します。"}, "IMCONJUGATE": {"a": "(複素数)", "d": "複素数の共役複素数を文字列として返します。"}, "IMCOS": {"a": "(複素数)", "d": "複素数のコサインを返します。"}, "IMCOSH": {"a": "(複素数)", "d": "複素数の双曲線余弦を返します。"}, "IMCOT": {"a": "(複素数)", "d": "複素数の余接を返します。"}, "IMCSC": {"a": "(複素数)", "d": "複素数の余割を返します。"}, "IMCSCH": {"a": "(複素数)", "d": "複素数の双曲線余割を返します。"}, "IMDIV": {"a": "(複素数1; 複素数2)", "d": "2 つの複素数を割り算しその商を返します。"}, "IMEXP": {"a": "(複素数)", "d": "複素数のべき乗を返します。"}, "IMLN": {"a": "(複素数)", "d": "複素数の自然対数 (e を底とする対数) を計算します。"}, "IMLOG10": {"a": "(複素数)", "d": "複素数の 10 を底とする対数を返します。"}, "IMLOG2": {"a": "(複素数)", "d": "複素数の 2 を底とする対数を返します。"}, "IMPOWER": {"a": "(複素数; 数値)", "d": "複素数を底として複素数の整数乗を計算します。"}, "IMPRODUCT": {"a": "(複素数1; [複素数2]; ...)", "d": "1 ～ 255 個の複素数の積を計算します。"}, "IMREAL": {"a": "(複素数)", "d": "複素数の実数係数を返します。"}, "IMSEC": {"a": "(複素数)", "d": "複素数の正割を返します。"}, "IMSECH": {"a": "(複素数)", "d": "複素数の双曲線正割を返します。"}, "IMSIN": {"a": "(複素数)", "d": "複素数のサインを返します。"}, "IMSINH": {"a": "(複素数)", "d": "複素数の双曲線正弦を返します。"}, "IMSQRT": {"a": "(複素数)", "d": "複素数の平方根を返します。"}, "IMSUB": {"a": "(複素数1; 複素数2)", "d": "2 つの複素数の差を返します。"}, "IMSUM": {"a": "(複素数1; [複素数2]; ...)", "d": "2 つ以上の複素数の和を計算します。"}, "IMTAN": {"a": "(複素数)", "d": "複素数の正接を返します。"}, "OCT2BIN": {"a": "(数値; [桁数])", "d": "8 進数を 2 進数に変換します。"}, "OCT2DEC": {"a": "(数値)", "d": "8 進数を 10 進数に変換します。"}, "OCT2HEX": {"a": "(数値; [桁数])", "d": "8 進数を 16 進数に変換します。"}, "DAVERAGE": {"a": "(データベース; フィールド; 条件)", "d": "データベースの指定された列を検索し、条件を満たすレコードの平均値を返します。"}, "DCOUNT": {"a": "(データベース; フィールド; 条件)", "d": "データベースの指定された列を検索し、条件を満たすレコードの中で数値が入力されているセルの個数を返します。"}, "DCOUNTA": {"a": "(データベース; フィールド; 条件)", "d": "条件を満たすレコードの中の空白でないセルの個数を返します。"}, "DGET": {"a": "(データベース; フィールド; 条件)", "d": "データベースの列から指定された条件を満たす 1 つのレコードを抽出します。"}, "DMAX": {"a": "(データベース; フィールド; 条件)", "d": "データベースの指定された列を検索し、条件を満たすレコードの最大値を返します。"}, "DMIN": {"a": "(データベース; フィールド; 条件)", "d": "データベースの指定された列を検索し、条件を満たすレコードの最小値を返します。"}, "DPRODUCT": {"a": "(データベース; フィールド; 条件)", "d": "条件を満たすデータベース レコードの指定したフィールドに入力されている数値の積を返します。"}, "DSTDEV": {"a": "(データベース; フィールド; 条件)", "d": "選択したデータベース レコードの標本を基に標準偏差を返します。"}, "DSTDEVP": {"a": "(データベース; フィールド; 条件)", "d": "条件を満たすレコードを母集団全体と見なして、母集団の標準偏差を返します。"}, "DSUM": {"a": "(データベース; フィールド; 条件)", "d": "データベースの指定された列を検索し、条件を満たすレコードの合計を返します。"}, "DVAR": {"a": "(データベース; フィールド; 条件)", "d": "条件を満たすデータベース レコードの指定したフィールドに入力した値を母集団の標本とみなして、母集団の分散を返します。"}, "DVARP": {"a": "(データベース; フィールド; 条件)", "d": "条件を満たすレコードを母集団全体と見なして、母集団の分散を返します。"}, "CHAR": {"a": "(数値)", "d": "使っているコンピューターの文字セットから、そのコード番号に対応する文字を返します。"}, "CLEAN": {"a": "(文字列)", "d": "印刷できない文字を文字列から削除します。"}, "CODE": {"a": "(文字列)", "d": "文字列の先頭文字を表す数値コードを返します。"}, "CONCATENATE": {"a": "(文字列1; [文字列2]; ...)", "d": "複数の文字列を結合して 1 つの文字列にまとめます。"}, "CONCAT": {"a": "(テキスト1; ...)", "d": " テキスト文字列の一覧または範囲を連結します"}, "DOLLAR": {"a": "(数値; [桁数])", "d": "数値を四捨五入し、通貨書式を設定した文字列に変換します"}, "EXACT": {"a": "(文字列1; 文字列2)", "d": "2 つの文字列を比較し、同じものであれば TRUE、異なれば FALSE を返します。EXACT 関数では、英字の大文字と小文字は区別されます。"}, "FIND": {"a": "(検索文字列; 対象; [開始位置])", "d": "文字列が他の文字列内で最初に現れる位置を検索します。大文字と小文字は区別されます。"}, "FINDB": {"a": "(検索文字列; 対象; [開始位置])", "d": "指定された文字列を他の文字列の中で検索し、その文字列が最初に現れる位置を左端から数え、その番号を返します。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。"}, "FIXED": {"a": "(数値; [桁数]; [桁区切り])", "d": "数値を指定された小数点で四捨五入し、コンマ (,) を使って、または使わずに書式設定した文字列に変換します。"}, "LEFT": {"a": "(文字列; [文字数])", "d": "文字列の先頭から指定された数の文字を返します。"}, "LEFTB": {"a": "(文字列; [文字数])", "d": "関数は、文字列の先頭から指定されたバイト数の文字を返します。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。"}, "LEN": {"a": "(文字列)", "d": "文字列の長さ (文字数) を返します。半角と全角の区別なく、1 文字を 1 として処理します。"}, "LENB": {"a": "(文字列)", "d": "関数は、文字列のバイト数を返します。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。"}, "LOWER": {"a": "(文字列)", "d": "文字列に含まれる英字をすべて小文字に変換します。"}, "MID": {"a": "(文字列; 開始位置; 文字数)", "d": "文字列の指定された位置から、指定された数の文字を返します。半角と全角の区別なく、1 文字を 1 として処理します。"}, "MIDB": {"a": "(文字列; 開始位置; 文字数)", "d": "関数は、文字列の任意の位置から指定されたバイト数の文字を返します。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。"}, "NUMBERVALUE": {"a": "(文字列; [小数点記号]; [桁区切り記号])", "d": "文字列をロケールに依存しない方法で数値に変換します。"}, "PROPER": {"a": "(文字列)", "d": "文字列中の各単語の先頭文字を大文字に変換した結果を返します。"}, "REPLACE": {"a": "(文字列; 開始位置; 文字数; 置換文字列)", "d": "文字列中の指定した位置の文字列を置き換えた結果を返します。半角と全角の区別なく、1 文字を 1 として処理します。"}, "REPLACEB": {"a": "(文字列; 開始位置; 文字数; 置換文字列)", "d": "関数は、文字列中の指定されたバイト数の文字を別の文字に置き換えます。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。"}, "REPT": {"a": "(文字列; 繰り返し回数)", "d": "文字列を指定された回数だけ繰り返して表示します。この関数を使用して、セル幅全体に文字列を表示することができます。"}, "RIGHT": {"a": "(文字列; [文字数])", "d": "文字列の末尾から指定された文字数の文字を返します。"}, "RIGHTB": {"a": "(文字列; [バイト数])", "d": "関数は、文字列の末尾 (右端) から指定されたバイト数の文字を返します。関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。"}, "SEARCH": {"a": "(検索文字列; 対象; [開始位置])", "d": "文字列が最初に現れる位置の文字番号を返します。大文字、小文字は区別されません。"}, "SEARCHB": {"a": "(検索文字列,対象,[開始位置])", "d": "関数は、指定された文字列を他の文字列の中で検索し、その文字列が最初に現れる位置を左端から数え、その番号を返します。 関数は、2 バイト文字セット (DBCS) を使う言語での使用を前提としています。DBCS をサポートする言語には、日本語、中国語、および韓国語があります。"}, "SUBSTITUTE": {"a": "(文字列; 検索文字列; 置換文字列; [置換対象])", "d": "文字列中の指定した文字を新しい文字で置き換えます。"}, "T": {"a": "(値)", "d": "値が文字列を参照する場合はその文字列を返し、文字列以外のデータを参照する場合は、空文字列 (\"\") を返します。"}, "TEXT": {"a": "(値; 表示形式)", "d": "数値に指定した書式を設定し、文字列に変換した結果を返します。"}, "TEXTJOIN": {"a": "(区切り文字; 空のセルは無視; テキスト1; ...)", "d": " 区切り文字を使用してテキスト文字列の一覧または範囲を連結します"}, "TRIM": {"a": "(文字列)", "d": "単語間のスペースを 1 つずつ残して、不要なスペースをすべて削除します。"}, "UNICHAR": {"a": "(数値)", "d": "指定された数値により参照される Unicode 文字を返します。"}, "UNICODE": {"a": "(文字列)", "d": "文字列の最初の文字に対応する番号 (コード ポイント) を返します。"}, "UPPER": {"a": "(文字列)", "d": "文字列に含まれる英字をすべて大文字に変換します。"}, "VALUE": {"a": "(文字列)", "d": "文字列として入力されている数字を数値に変換します。"}, "AVEDEV": {"a": "(数値1; [数値2]; ...)", "d": "データ全体の平均値に対するそれぞれのデータの絶対偏差の平均を返します。引数には、数値、数値を含む名前、配列、セル参照を指定できます。"}, "AVERAGE": {"a": "(数値1; [数値2]; ...)", "d": "引数の平均値を返します。引数には、数値、数値を含む名前、配列、セル参照を指定できます。"}, "AVERAGEA": {"a": "(値1; [値2]; ...)", "d": "引数の平均値を返します。引数の文字列および FALSE は 0、TRUE は 1 と見なします。引数には、数値、名前、配列、参照を含むことができます"}, "AVERAGEIF": {"a": "(範囲; 条件; [平均対象範囲])", "d": "特定の条件に一致する数値の平均 (算術平均) を計算します"}, "AVERAGEIFS": {"a": "(平均対象範囲; 条件範囲; 条件; ...)", "d": "特定の条件に一致する数値の平均 (算術平均) を計算します"}, "BETADIST": {"a": "(x; α; β; [A]; [B])", "d": "累積β確率密度関数を返します"}, "BETAINV": {"a": "(確率; α; β; [A]; [B])", "d": "累積β確率密度関数の逆関数を返します。"}, "BETA.DIST": {"a": "(x; α; β; 関数形式; [A]; [B])", "d": "β確率分布関数を返します"}, "BETA.INV": {"a": "(確率; α; β; [A]; [B])", "d": "累積β確率密度関数の逆関数 (BETA.DIST) を返します。"}, "BINOMDIST": {"a": "(成功数; 試行回数; 成功率; 関数形式)", "d": "二項分布の確率を返します。"}, "BINOM.DIST": {"a": "(成功数; 試行回数; 成功率; 関数形式)", "d": "二項分布の確率を返します。"}, "BINOM.DIST.RANGE": {"a": "(試行回数; 成功率; 成功数; [成功数2])", "d": "二項分布を使用した試行結果の確率を返します。"}, "BINOM.INV": {"a": "(試行回数; 成功率; α)", "d": "累積二項分布の値が基準値以上になるような最小の値を返します。"}, "CHIDIST": {"a": "(x; 自由度)", "d": "カイ 2 乗分布の右側確率の値を返します"}, "CHIINV": {"a": "(確率; 自由度)", "d": "カイ 2 乗分布の右側確率の逆関数の値を返します。"}, "CHITEST": {"a": "(実測値範囲; 期待値範囲)", "d": "統計と自由度に対するカイ 2 乗分布から値を抽出して返します。"}, "CHISQ.DIST": {"a": "(x; 自由度; 関数形式)", "d": "カイ 2 乗分布の左側確率の値を返します"}, "CHISQ.DIST.RT": {"a": "(x; 自由度)", "d": "カイ 2 乗分布の右側確率の値を返します"}, "CHISQ.INV": {"a": "(確率; 自由度)", "d": "カイ 2 乗分布の左側確率の逆関数の値を返します。"}, "CHISQ.INV.RT": {"a": "(確率; 自由度)", "d": "カイ 2 乗分布の右側確率の逆関数の値を返します。"}, "CHISQ.TEST": {"a": "(実測値範囲; 期待値範囲)", "d": "統計と自由度に対するカイ 2 乗分布から値を抽出して返します。"}, "CONFIDENCE": {"a": "(α; 標準偏差; 標本数)", "d": "正規分布を使用して、母集団の平均に対する信頼区間を求めます。"}, "CONFIDENCE.NORM": {"a": "(α; 標準偏差; 標本数)", "d": "正規分布を使用して、母集団の平均に対する信頼区間を求めます。"}, "CONFIDENCE.T": {"a": "(α; 標準偏差; 標本数)", "d": "スチューデントの T 分布を使用して、母集団の平均に対する信頼区間を求めます。"}, "CORREL": {"a": "(配列1; 配列2)", "d": "2 つの配列の相関係数を返します。"}, "COUNT": {"a": "(値1; [値2]; ...)", "d": "範囲内の、数値が含まれるセルの個数を返します。"}, "COUNTA": {"a": "(値1; [値2]; ...)", "d": "範囲内の、空白でないセルの個数を返します。"}, "COUNTBLANK": {"a": "(範囲)", "d": "範囲に含まれる空白セルの個数を返します。"}, "COUNTIF": {"a": "(範囲; 検索条件)", "d": "指定された範囲に含まれるセルのうち、検索条件に一致するセルの個数を返します。"}, "COUNTIFS": {"a": "(検索条件範囲; 検索条件; ...)", "d": "特定の条件に一致するセルの個数を返します"}, "COVAR": {"a": "(配列1; 配列2)", "d": "共分散を返します。共分散とは、2 組の対応するデータ間での標準偏差の積の平均値です。"}, "COVARIANCE.P": {"a": "(配列1; 配列2)", "d": "母集団の共分散を返します。共分散とは、2 組の対応するデータ間での標準偏差の積の平均値です。"}, "COVARIANCE.S": {"a": "(配列1; 配列2)", "d": "標本の共分散を返します。共分散とは、2 組の対応するデータ間での標準偏差の積の平均値です。"}, "CRITBINOM": {"a": "(試行回数; 成功率; α)", "d": "累積二項分布の値が基準値以上になるような最小の値を返します。"}, "DEVSQ": {"a": "(数値1; [数値2]; ...)", "d": "標本の平均値に対する各データの偏差の平方和を返します。"}, "EXPONDIST": {"a": "(x; λ; 関数形式)", "d": "指数分布関数を返します。"}, "EXPON.DIST": {"a": "(x; λ; 関数形式)", "d": "指数分布関数を返します。"}, "FDIST": {"a": "(x; 自由度1; 自由度2)", "d": "2 つのデータ セットの (右側) F 確率分布を返します"}, "FINV": {"a": "(確率; 自由度1; 自由度2)", "d": "F 確率分布の逆関数を返します。つまり、確率 = FDIST(x,...) であるとき、FINV(確率,...) = x となるような x の値を返します。"}, "FTEST": {"a": "(配列1; 配列2)", "d": "F-検定の結果を返します。F-検定により、配列 1 と配列 2 とのデータのばらつきに有意な差が認められない両側確率が返されます。"}, "F.DIST": {"a": "(x; 自由度1; 自由度2; 関数形式)", "d": "(左側) F 確率分布を返します"}, "F.DIST.RT": {"a": "(x; 自由度1; 自由度2)", "d": "(右側) F 確率分布を返します"}, "F.INV": {"a": "(確率; 自由度1; 自由度2)", "d": "(左側) F 確率分布の逆関数を返します。"}, "F.INV.RT": {"a": "(確率; 自由度1; 自由度2)", "d": "(右側) F 確率分布の逆関数を返します。"}, "F.TEST": {"a": "(配列1; 配列2)", "d": "F-検定の結果を返します。F-検定により、配列 1 と配列 2 とのデータのばらつきに有意な差が認められない両側確率が返されます。"}, "FISHER": {"a": "(x)", "d": "フィッシャー変換の結果を返します。"}, "FISHERINV": {"a": "(y)", "d": "フィッシャー変換の逆関数を返します。y = FISHER(x) であるとき、FISHERINV(y) = x という関係が成り立ちます。"}, "FORECAST": {"a": "(x; 既知のy; 既知のx)", "d": "既知の値を使用し、線形トレンドに沿って将来の値を予測します。"}, "FORECAST.ETS": {"a": "(目標期日; 値; タイムライン; [季節性]; [データ補間]; [集計])", "d": "指数平滑化法を使用して、今後の指定の目標期日における予測値を返します。"}, "FORECAST.ETS.CONFINT": {"a": "(目標期日; 値; タイムライン; [信頼レベル]; [季節性]; [データ補間]; [集計])", "d": "指定の目標期日における予測値の信頼区間を返します"}, "FORECAST.ETS.SEASONALITY": {"a": "(値; タイムライン; [データ補間]; [集計])", "d": "アプリが指定の時系列に対して検出する繰り返しパターンの長さを返します。"}, "FORECAST.ETS.STAT": {"a": "(値; タイムライン; 統計の種類; [季節性]; [データ補間]; [集計])", "d": "予測のために要求された統計を返します。"}, "FORECAST.LINEAR": {"a": "(x; 既知のy; 既知のx)", "d": "既知の値を使用し、線形トレンドに沿って将来の値を予測します。"}, "FREQUENCY": {"a": "(データ配列; 区間配列)", "d": "範囲内でのデータの度数分布を、垂直配列で返します。返された配列要素の個数は、区間配列の個数より 1 つだけ多くなります。"}, "GAMMA": {"a": "(x)", "d": "ガンマ関数値を返します。"}, "GAMMADIST": {"a": "(x; α; β; 関数形式)", "d": "γ分布関数の値を返します"}, "GAMMA.DIST": {"a": "(x; α; β; 関数形式)", "d": "γ分布関数の値を返します"}, "GAMMAINV": {"a": "(確率; α; β)", "d": "γ累積分布の逆関数の値を返します。つまり、確率 = GAMMADIST(x,...) であるとき、GAMMAINV(確率,...) = x となるような x の値を返します。"}, "GAMMA.INV": {"a": "(確率; α; β)", "d": "γ累積分布の逆関数の値を返します。つまり、確率 = GAMMA.DIST(x,...) であるとき、GAMMA.INV(確率,...) = x となるような x の値を返します。"}, "GAMMALN": {"a": "(x)", "d": "γ関数 G(x) の自然対数を返します。"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "γ関数 G(x) の自然対数を返します。"}, "GAUSS": {"a": "(x)", "d": "標準正規分布の累積分布関数より小さい 0.5 を返します。"}, "GEOMEAN": {"a": "(数値1; [数値2]; ...)", "d": "正の数からなる配列またはセル範囲のデータの幾何平均を返します。"}, "GROWTH": {"a": "(既知のy; [既知のx]; [新しいx]; [定数])", "d": "既知のデータ ポイントに対応する指数トレンドの数値を返します。"}, "HARMEAN": {"a": "(数値1; [数値2]; ...)", "d": "正の数からなるデータの調和平均を返します。調和平均は、逆数の算術平均 (相加平均) に対する逆数として定義されます。"}, "HYPGEOM.DIST": {"a": "(標本の成功数; 標本数; 母集団の成功数; 母集団の大きさ; 関数形式)", "d": "超幾何分布を返します。"}, "HYPGEOMDIST": {"a": "(標本の成功数; 標本数; 母集団の成功数; 母集団の大きさ)", "d": "超幾何分布を返します。"}, "INTERCEPT": {"a": "(既知のy; 既知のx)", "d": "既知の x と既知の y を通過する線形回帰直線の切片を計算します。"}, "KURT": {"a": "(数値1; [数値2]; ...)", "d": "引数として指定したデータの尖度を返します。"}, "LARGE": {"a": "(配列; 順位)", "d": "データの中から、指定した順位番目に大きな値を返します。"}, "LINEST": {"a": "(既知のy; [既知のx]; [定数]; [補正])", "d": "最小二乗法を使って直線を当てはめることで、既知のデータ ポイントに対応する線形トレンドを表す補正項を計算します。"}, "LOGEST": {"a": "(既知のy; [既知のx]; [定数]; [補正])", "d": "既知のデータ ポイントに対応する指数曲線を表す補正項を計算します。"}, "LOGINV": {"a": "(確率; 平均; 標準偏差)", "d": "x の対数正規型の累積分布関数の逆関数の値を返します。ln(x) は平均と標準偏差を引数にする正規型分布になります。"}, "LOGNORM.DIST": {"a": "(x; 平均; 標準偏差; 関数形式)", "d": "x の対数正規分布の確率を返します。ln(x)は、平均と標準偏差を引数にする正規型分布になります"}, "LOGNORM.INV": {"a": "(確率; 平均; 標準偏差)", "d": "x の対数正規型の累積分布関数の逆関数の値を返します。ln(x) は平均と標準偏差を引数にする正規型分布になります。"}, "LOGNORMDIST": {"a": "(x; 平均; 標準偏差)", "d": "x の対数正規分布の確率を返します。ln(x) は、平均と標準偏差を引数にする正規型分布になります"}, "MAX": {"a": "(数値1; [数値2]; ...)", "d": "引数の最大値を返します。論理値および文字列は無視されます。"}, "MAXA": {"a": "(値1; [値2]; ...)", "d": "引数の最大値を返します。論理値や文字列も対象となります。"}, "MAXIFS": {"a": "(最大範囲; 条件範囲; 条件; ...)", "d": "所定の条件または基準で指定したセル間の最大値を返します"}, "MEDIAN": {"a": "(数値1; [数値2]; ...)", "d": "引数リストに含まれる数値のメジアン (中央値) を返します。"}, "MIN": {"a": "(数値1; [数値2]; ...)", "d": "引数の最小値を返します。論理値および文字列は無視されます。"}, "MINA": {"a": "(値1; [値2]; ...)", "d": "引数の最小値を返します。論理値や文字列も対象となります。"}, "MINIFS": {"a": "(最小範囲; 条件範囲; 条件; ...)", "d": "所定の条件または基準で指定したセル間の最小値を返します"}, "MODE": {"a": "(数値1; [数値2]; ...)", "d": "配列またはセル範囲として指定されたデータの中で、最も頻繁に出現する値 (最頻値) を返します。"}, "MODE.MULT": {"a": "(数値1; [数値2]; ...)", "d": "最も頻繁に出現する垂直配列、または指定の配列かデータ範囲内で反復的に出現する値を返します。水平配列の場合は、=TRANSPOSE(MODE.MULT(数値1,数値2,...)) を使用します。"}, "MODE.SNGL": {"a": "(数値1; [数値2]; ...)", "d": "配列またはセル範囲として指定されたデータの中で、最も頻繁に出現する値 (最頻値) を返します。"}, "NEGBINOM.DIST": {"a": "(失敗数; 成功数; 成功率; 関数形式)", "d": "負の二項分布の確率関数の値を返します。試行の成功率が一定のとき、成功数で指定した回数の試行が成功する前に、失敗数で指定した回数の試行が失敗する確率です。"}, "NEGBINOMDIST": {"a": "(失敗数; 成功数; 成功率)", "d": "負の二項分布の確率関数の値を返します。試行の成功率が一定のとき、成功数で指定した回数の試行が成功する前に、失敗数で指定した回数の試行が失敗する確率です。"}, "NORM.DIST": {"a": "(x; 平均; 標準偏差; 関数形式)", "d": "指定した平均と標準偏差に対する正規分布の値を返します。"}, "NORMDIST": {"a": "(x; 平均; 標準偏差; 関数形式)", "d": "指定した平均と標準偏差に対する正規分布関数の値を返します。"}, "NORM.INV": {"a": "(確率; 平均; 標準偏差)", "d": "指定した平均と標準偏差に対する正規分布の累積分布関数の逆関数の値を返します。"}, "NORMINV": {"a": "(確率; 平均; 標準偏差)", "d": "指定した平均と標準偏差に対する正規分布の累積分布関数の逆関数の値を返します。"}, "NORM.S.DIST": {"a": "(z; 関数形式)", "d": "標準正規分布を返します。この分布は、平均が 0 で標準偏差が 1 である正規分布に対応します。"}, "NORMSDIST": {"a": "(z)", "d": "標準正規分布の累積分布関数の値を返します。この分布は、平均が 0 で標準偏差が 1 である正規分布に対応します。"}, "NORM.S.INV": {"a": "(確率)", "d": "標準正規分布の累積分布関数の逆関数の値を返します。この分布は、平均が 0 で標準偏差が 1 である正規分布に対応します。"}, "NORMSINV": {"a": "(確率)", "d": "標準正規分布の累積分布関数の逆関数の値を返します。この分布は、平均が 0 で標準偏差が 1 である正規分布に対応します。"}, "PEARSON": {"a": "(配列1; 配列2)", "d": "ピアソンの積率相関係数 r の値を返します。"}, "PERCENTILE": {"a": "(配列; 率)", "d": "配列に含まれる値の k 番目の百分位を返します。"}, "PERCENTILE.EXC": {"a": "(配列; 率)", "d": "配列に含まれる値の k 番目の百分位を返します。k には、0 より大きく 1 より小さい値を指定します。"}, "PERCENTILE.INC": {"a": "(配列; 率)", "d": "配列に含まれる値の k 番目の百分位を返します。k には、0 以上 1 以下の値を指定します。"}, "PERCENTRANK": {"a": "(配列; x; [有効桁数])", "d": "値 x の配列内での順位を百分率で表した値を返します。"}, "PERCENTRANK.EXC": {"a": "(配列; x; [有効桁数])", "d": "値 x の配列内での順位を百分率 (0 より大きく 1 より小さい) で表した値を返します。"}, "PERCENTRANK.INC": {"a": "(配列; x; [有効桁数])", "d": "値 x の配列内での順位を百分率 (0 以上 1 以下) で表した値を返します。"}, "PERMUT": {"a": "(標本数; 抜き取り数)", "d": "指定した数の対象から、指定された数だけ抜き取る場合の順列の数を返します。"}, "PERMUTATIONA": {"a": "(数値; 抜き取り数)", "d": "指定した数の対象 (反復あり) から、指定された数だけ抜き取る場合の順列の数を返します。"}, "PHI": {"a": "(x)", "d": "標準正規分布の密度関数の値を返します。"}, "POISSON": {"a": "(イベント数; 平均; 関数形式)", "d": "ポワソン分布の値を返します。"}, "POISSON.DIST": {"a": "(イベント数; 平均; 関数形式)", "d": "ポワソン分布の値を返します。"}, "PROB": {"a": "(x範囲; 確率範囲; 下限; [上限])", "d": "指定した範囲内の値が、上限と下限で指定される範囲に含まれる確率を返します。"}, "QUARTILE": {"a": "(配列; 戻り値)", "d": "配列に含まれるデータから四分位数を返します。"}, "QUARTILE.INC": {"a": "(配列; 戻り値)", "d": "0 以上 1 以下の百分位値に基づいて、配列に含まれるデータから四分位数を返します。"}, "QUARTILE.EXC": {"a": "(配列; 戻り値)", "d": "0 より大きく 1 より小さい百分位値に基づいて、配列に含まれるデータから四分位数を返します。"}, "RANK": {"a": "(数値; 参照; [順序])", "d": "順序に従って範囲内の数値を並べ替えたとき、数値が何番目に位置するかを返します。"}, "RANK.AVG": {"a": "(数値; 参照; [順序])", "d": "順序に従って範囲内の数値を並べ替えたとき、数値が何番目に位置するかを返します。複数の数値が同じ順位にある場合は、順位の平均を返します。"}, "RANK.EQ": {"a": "(数値; 参照; [順序])", "d": "順序に従って範囲内の数値を並べ替えたとき、数値が何番目に位置するかを返します。複数の数値が同じ順位にある場合は、その値の中の最上位を返します。"}, "RSQ": {"a": "(既知のy; 既知のx)", "d": "指定されたデータ ポイントからピアソンの積率相関係数の 2 乗を返します。"}, "SKEW": {"a": "(数値1; [数値2]; ...)", "d": "分布の歪度 (ひずみ) を返します。歪度とは、分布の平均値周辺での両側の非対称度を表す値です。"}, "SKEW.P": {"a": "(数値1; [数値2]; ...)", "d": "人口に基づく分布の歪度 (ひずみ) を返します。歪度とは、分布の平均値周辺での両側の非対称度を表す値です。"}, "SLOPE": {"a": "(既知のy; 既知のx)", "d": "指定されたデータ ポイントから線形回帰直線の傾きを返します。"}, "SMALL": {"a": "(配列; 順位)", "d": "データの中から、指定した順位番目に小さな値を返します。"}, "STANDARDIZE": {"a": "(x; 平均; 標準偏差)", "d": "平均と標準偏差で決定される分布を対象に、正規化された値を返します。"}, "STDEV": {"a": "(数値1; [数値2]; ...)", "d": "標本に基づいて予測した標準偏差を返します。標本内の論理値と文字列は無視されます。"}, "STDEV.P": {"a": "(数値1; [数値2]; ...)", "d": "引数を母集団全体であると見なして、母集団の標準偏差を返します。論理値、および文字列は無視されます。"}, "STDEV.S": {"a": "(数値1; [数値2]; ...)", "d": "標本に基づいて予測した標準偏差を返します。標本内の論理値、および文字列は無視されます。"}, "STDEVA": {"a": "(値1; [値2]; ...)", "d": "論理値や文字列を含む標本に基づいて、予測した標準偏差を返します。文字列および論理値 FALSE は値 0、論理値 TRUE は 1 と見なされます。"}, "STDEVP": {"a": "(数値1; [数値2]; ...)", "d": "引数を母集団全体であると見なして、母集団の標準偏差を返します。論理値、および文字列は無視されます。"}, "STDEVPA": {"a": "(値1; [値2]; ...)", "d": "論理値や文字列を含む引数を母集団全体と見なして、母集団の標準偏差を返します。文字列および論理値 FALSE は値 0、論理値 TRUE は値 1 と見なされます。"}, "STEYX": {"a": "(既知のy; 既知のx)", "d": "回帰において、x に対して予測された値 y の標準誤差を返します。"}, "TDIST": {"a": "(x; 自由度; 分布の指定)", "d": "スチューデントの t-分布を返します"}, "TINV": {"a": "(確率; 自由度)", "d": "スチューデントの t-分布の両側逆関数を返します。"}, "T.DIST": {"a": "(x; 自由度; 関数形式)", "d": "左側のスチューデントの t-分布を返します"}, "T.DIST.2T": {"a": "(x; 自由度)", "d": "両側のスチューデントの t-分布を返します"}, "T.DIST.RT": {"a": "(x; 自由度)", "d": "右側のスチューデントの t-分布を返します"}, "T.INV": {"a": "(確率; 自由度)", "d": "スチューデントの t-分布の左側逆関数を返します。"}, "T.INV.2T": {"a": "(確率; 自由度)", "d": "スチューデントの t-分布の両側逆関数を返します。"}, "T.TEST": {"a": "(配列1; 配列2; 検定の指定; 検定の種類)", "d": "スチューデントの t 検定に関連する確率を返します。"}, "TREND": {"a": "(既知のy; [既知のx]; [新しいx]; [定数])", "d": "最小二乗法を使用することで、既知のデータ ポイントに対応する線形トレンドの数値を返します。"}, "TRIMMEAN": {"a": "(配列; 割合)", "d": "データ全体の上限と下限から一定の割合のデータを切り落とし、残りの項の平均値を返します。"}, "TTEST": {"a": "(配列1; 配列2; 検定の指定; 検定の種類)", "d": "スチューデントの t 検定に関連する確率を返します。"}, "VAR": {"a": "(数値1; [数値2]; ...)", "d": "標本に基づいて母集団の分散の推定値 (不偏分散) を返します。標本内の論理値と文字列は無視されます。"}, "VAR.P": {"a": "(数値1; [数値2]; ...)", "d": "引数を母集団全体と見なし、母集団の分散 (標本分散) を返します。論理値、および文字列は無視されます。"}, "VAR.S": {"a": "(数値1; [数値2]; ...)", "d": "標本に基づいて母集団の分散の推定値 (不偏分散) を返します。標本内の論理値、および文字列は無視されます。"}, "VARA": {"a": "(値1; [値2]; ...)", "d": "標本に基づく、分散の予測値を返します。文字列および論理値 FALSE は値 0、論理値 TRUE は値 1 と見なされます。"}, "VARP": {"a": "(数値1; [数値2]; ...)", "d": "引数を母集団全体と見なし、母集団の分散 (標本分散) を返します。論理値、および文字列は無視されます。"}, "VARPA": {"a": "(値1; [値2]; ...)", "d": "母集団全体に基づく分散を返します。文字列および論理値 FALSE は値 0、論理値 TRUE は 1 と見なされます。"}, "WEIBULL": {"a": "(x; α; β; 関数形式)", "d": "ワイブル分布の値を返します"}, "WEIBULL.DIST": {"a": "(x; α; β; 関数形式)", "d": "ワイブル分布の値を返します"}, "Z.TEST": {"a": "(配列; x; [σ])", "d": "z 検定の片側確率の P 値を返します。"}, "ZTEST": {"a": "(配列; x; [σ])", "d": "z 検定の片側確率の P 値を返します。"}, "ACCRINT": {"a": "(発行日; 最初の利払日; 受渡日; 利率; 額面; 頻度; [基準]; [計算方式])", "d": "利息が定期的に支払われる有価証券に対する未収利息を計算します。"}, "ACCRINTM": {"a": "(発行日; 受渡日; 利率; 額面; [基準])", "d": "利息が満期日に支払われる有価証券に対する未収利息を計算します。"}, "AMORDEGRC": {"a": "(取得価額; 購入日; 開始期; 残存価額; 期; 率; [年の基準])", "d": "各会計期における減価償却費を返します"}, "AMORLINC": {"a": "(取得価額; 購入日; 開始期; 残存価額; 期; 率; [年の基準])", "d": "各会計期における減価償却費を返します"}, "COUPDAYBS": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "利払期間の第 1 日目から受渡日までの日数を計算します。"}, "COUPDAYS": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "受渡日を含む利払期間の日数を計算します。"}, "COUPDAYSNC": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "受渡日から次の利払日までの日数を計算します。"}, "COUPNCD": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "受渡日後の次の利払日を計算します。"}, "COUPNUM": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "受渡日と満期日の間の利息支払回数を計算します。"}, "COUPPCD": {"a": "(受渡日; 満期日; 頻度; [基準])", "d": "受渡日の前の最後の利払日を計算します。"}, "CUMIPMT": {"a": "(利率; 期間; 現在価値; 開始期; 終了期; 支払期日)", "d": "開始から終了までの貸付期間内で支払われる利息の累計額を計算します"}, "CUMPRINC": {"a": "(利率; 期間; 現在価値; 開始期; 終了期; 支払期日)", "d": "開始から終了までの貸付期間内で支払われる元金の累計額を計算します"}, "DB": {"a": "(取得価額; 残存価額; 耐用年数; 期; [月])", "d": "定率法を使って計算した資産の減価償却を返します。"}, "DDB": {"a": "(取得価額; 残存価額; 耐用年数; 期; [率])", "d": "倍率逓減法または指定したその他の方式を使って、計算した資産の減価償却を返します。"}, "DISC": {"a": "(受渡日; 満期日; 現在価値; 償還価額; [基準])", "d": "証券に対する割引率を計算します。"}, "DOLLARDE": {"a": "(整数部と分子部; 分母)", "d": "分数として表現されているドル単位の価格を、10 進数を使った数値に変換します。"}, "DOLLARFR": {"a": "(小数値; 分母)", "d": "10 進数として表現されているドル単位の価格を、分数を使った数値に変換します。"}, "DURATION": {"a": "(受渡日; 満期日; 利率; 利回り; 頻度; [基準])", "d": "定期的に利子が支払われる証券の年間のマコーレー係数を計算します。"}, "EFFECT": {"a": "(名目利率; 複利計算期間)", "d": "実質金利の計算をします。"}, "FV": {"a": "(利率; 期間; 定期支払額; [現在価値]; [支払期日])", "d": "一定利率の支払いが定期的に行われる場合の、投資の将来価値を返します"}, "FVSCHEDULE": {"a": "(元金; 利率配列)", "d": "投資期間内の一連の金利を複利計算することにより、初期投資の元金の将来価値を計算します。"}, "INTRATE": {"a": "(受渡日; 満期日; 投資額; 償還価額; [基準])", "d": "全額投資された証券を対象に、その利率を計算します。"}, "IPMT": {"a": "(利率; 期; 期間; 現在価値; [将来価値]; [支払期日])", "d": "一定利率の支払いが定期的に行われる場合の、投資期間内の指定された期に支払われる金利を返します"}, "IRR": {"a": "(範囲; [推定値])", "d": "一連の定期的なキャッシュ フローに対する内部収益率を返します。"}, "ISPMT": {"a": "(利率; 期; 期間; 現在価値)", "d": "投資期間内の指定された期に支払われる金利を返します"}, "MDURATION": {"a": "(受渡日; 満期日; 利率; 利回り; 頻度; [基準])", "d": "額面価格を $100 と仮定して、証券に対する修正マコーレー係数を計算します。"}, "MIRR": {"a": "(範囲; 安全利率; 危険利率)", "d": "投資原価と現金の再投資に対する受取利率 (危険利率) の両方を考慮して、一連の定期的なキャッシュ フローに対する内部収益率を返します"}, "NOMINAL": {"a": "(実効利率; 複利計算期間)", "d": "預金などの名目上の年利を計算します。"}, "NPER": {"a": "(利率; 定期支払額; 現在価値; [将来価値]; [支払期日])", "d": "一定利率の支払いが定期的に行われる場合の、ローンの支払回数を返します"}, "NPV": {"a": "(割引率; 値1; [値2]; ...)", "d": "投資の正味現在価値を、割引率、将来行われる一連の支払い (負の値)、およびその収益 (正の値) を使って算出します"}, "ODDFPRICE": {"a": "(受渡日; 満期日; 発行日; 初回利払日; 利率; 利回り; 償還価額; 頻度; [基準])", "d": "投資期間の第 1 期が半端な日数のとき、対象となる証券の額面 $100 に対する価格を計算します。"}, "ODDFYIELD": {"a": "(受渡日; 満期日; 発行日; 初回利払日; 利率; 現在価値; 償還価額; 頻度; [基準])", "d": "投資期間の第 1 期が半端な日数のとき、対象となる証券の利回りを計算します。"}, "ODDLPRICE": {"a": "(受渡日; 満期日; 最終利払日; 利率; 利回り; 償還価額; 頻度; [基準])", "d": "投資期間の最終期が半端な日数のとき、対象となる証券の額面 $100 に対する価格を計算します。"}, "ODDLYIELD": {"a": "(受渡日; 満期日; 最終利払日; 利率; 現在価値; 償還価額; 頻度; [基準])", "d": "投資期間の最終期が半端な日数のとき、対象となる証券の利回りを計算します。"}, "PDURATION": {"a": "(利率; 現在価値; 将来価値)", "d": "投資が指定した価値に達するまでの投資期間を返します。"}, "PMT": {"a": "(利率; 期間; 現在価値; [将来価値]; [支払期日])", "d": "一定利率の支払いが定期的に行われる場合の、ローンの定期支払額を算出します"}, "PPMT": {"a": "(利率; 期; 期間; 現在価値; [将来価値]; [支払期日])", "d": "一定利率の支払いが定期的に行われる場合の、投資の指定した期に支払われる元金を返します"}, "PRICE": {"a": "(受渡日; 満期日; 利率; 利回り; 償還価額; 頻度; [基準])", "d": "定期的に利息が支払われる証券の額面 $100 に対する価格を計算します。"}, "PRICEDISC": {"a": "(受渡日; 満期日; 割引率; 償還価額; [基準])", "d": "割引証券の額面 $100 に対する価格を計算します。"}, "PRICEMAT": {"a": "(受渡日; 満期日; 発行日; 利率; 利回り; [基準])", "d": "受渡日に利息が支払われる証券の額面 $100 に対する価格を計算します。"}, "PV": {"a": "(利率; 期間; 定期支払額; [将来価値]; [支払期日])", "d": "投資の現在価値を返します。現在価値とは、将来行われる一連の支払いを、現時点で一括払いした場合の合計金額のことをいいます"}, "RATE": {"a": "(期間; 定期支払額; 現在価値; [将来価値]; [支払期日]; [推定値])", "d": "ローンまたは投資の 1 期間あたりの利率を指定します。たとえば、年率 6% のローンを四半期払いで返済する場合、利率には 6%/4 = 1.5 (%) を指定します"}, "RECEIVED": {"a": "(受渡日; 満期日; 投資額; 割引率; [基準])", "d": "全額投資された証券を対象に、満期日における償還価額を計算します。"}, "RRI": {"a": "(期間; 現在価値; 将来価値)", "d": "投資の成長に対する等価利率を返します。"}, "SLN": {"a": "(取得価額; 残存価額; 耐用年数)", "d": "資産に対する減価償却を定額法を使って計算し、その結果を返します。"}, "SYD": {"a": "(取得価額; 残存価額; 耐用年数; 期)", "d": "資産に対する減価償却を級数法を使って計算し、その結果を返します。"}, "TBILLEQ": {"a": "(受渡日; 満期日; 割引率)", "d": "米国財務省短期証券 (TB) の債券相当の利回りを計算します。"}, "TBILLPRICE": {"a": "(受渡日; 満期日; 割引率)", "d": "米国財務省短期証券 (TB) の額面価格 $100 に対する価格を計算します。"}, "TBILLYIELD": {"a": "(受渡日; 満期日; 現在価格)", "d": "米国財務省短期証券 (TB) の利回りを計算します。"}, "VDB": {"a": "(取得価額; 残存価額; 耐用年数; 開始期; 終了期; [率]; [切り替えなし])", "d": "倍額定率法または指定された方法を使用して、特定の期における資産の減価償却費を返します。"}, "XIRR": {"a": "(範囲; 日付; [推定値])", "d": "一連のキャッシュフロー (投資と収益の金額) に基づいて、投資の内部利益率を計算します"}, "XNPV": {"a": "(割引率; キャッシュフロー; 日付)", "d": "一連のキャッシュフロー (投資と収益の金額) に基づいて、投資の正味現在価値を計算します"}, "YIELD": {"a": "(受渡日; 満期日; 利率; 現在価値; 償還価額; 頻度; [基準])", "d": "定期的に利息が支払われる証券の利回りを計算します。"}, "YIELDDISC": {"a": "(受渡日; 満期日; 現在価値; 償還価額; [基準])", "d": "米国財務省短期証券 (TB) などの割引債の年利回りを計算します。"}, "YIELDMAT": {"a": "(受渡日; 満期日; 発行日; 利率; 現在価値; [基準])", "d": "満期日に利息が支払われる証券を対象に、年利回りを計算します。"}, "ABS": {"a": "(数値)", "d": "数値から符号 (+、-) を除いた絶対値を返します。"}, "ACOS": {"a": "(数値)", "d": "数値のアークコサインを返します。戻り値の角度は、0 (ゼロ)  ～ PI の範囲のラジアンとなります。アークコサインとは、そのコサインが数値であるような角度のことです。"}, "ACOSH": {"a": "(数値)", "d": "数値の双曲線逆余弦を返します。"}, "ACOT": {"a": "(数値)", "d": "数値の逆余接を返します。戻り値の角度は、0 ～ Pi の範囲のラジアンとなります。"}, "ACOTH": {"a": "(数値)", "d": "数値の逆双曲線余接を返します。"}, "AGGREGATE": {"a": "(集計方法; オプション; 参照1; ...)", "d": "リストまたはデータベースの集計値を返します。"}, "ARABIC": {"a": "(文字列)", "d": "ローマ数字をアラビア数字に変換します。"}, "ASC": {"a": "(文字列)", "d": "2 バイト文字セット (DBCS) 言語の場合、全角 (2 バイト) 文字を半角 (1 バイト) 文字に変更します。"}, "ASIN": {"a": "(数値)", "d": "数値のアークサインを返します。戻り値の角度は、-PI/2 ～ PI/2 の範囲のラジアンとなります。"}, "ASINH": {"a": "(数値)", "d": "数値の双曲線逆正弦を返します。"}, "ATAN": {"a": "(数値)", "d": "数値のアークタンジェントを返します。戻り値の角度は、-PI/2 ～ PI/2 の範囲のラジアンとなります。"}, "ATAN2": {"a": "(x座標; y座標)", "d": "指定された x-y 座標のアークタンジェントを返します。戻り値の角度は、-PI から PI (ただし -PI を除く) の範囲のラジアンとなります。"}, "ATANH": {"a": "(数値)", "d": "数値の双曲線逆正接を返します。"}, "BASE": {"a": "(数値; 基数; [最小長])", "d": "数値を特定の基数 (底) を持つテキスト表現に変換します。"}, "CEILING": {"a": "(数値; 基準値)", "d": "指定された基準値の倍数のうち、最も近い値に数値を切り上げます。"}, "CEILING.MATH": {"a": "(数値; [基準値]; [モード])", "d": "数値を最も近い整数、または最も近い基準値の倍数に切り上げます。"}, "CEILING.PRECISE": {"a": "(数値; [基準値])", "d": "最も近い整数に切り上げた値、または、指定された基準値の倍数のうち最も近い値を返します。"}, "COMBIN": {"a": "(総数; 抜き取り数)", "d": "すべての項目から指定された個数を選択するときの組み合わせの数を返します。"}, "COMBINA": {"a": "(数値; 抜き取り数)", "d": "すべての項目から指定された個数を選択するときの組み合わせ (反復あり) の数を返します。"}, "COS": {"a": "(数値)", "d": "角度のコサインを返します。"}, "COSH": {"a": "(数値)", "d": "数値の双曲線余弦を返します。"}, "COT": {"a": "(数値)", "d": "角度の余接を返します。"}, "COTH": {"a": "(数値)", "d": "数値の双曲線余接を返します。"}, "CSC": {"a": "(数値)", "d": "角度の余割を返します。"}, "CSCH": {"a": "(数値)", "d": "角度の双曲線余割を返します。"}, "DECIMAL": {"a": "(数値; 基数)", "d": "指定された底の数値のテキスト表現を 10 進数に変換します。"}, "DEGREES": {"a": "(角度)", "d": "ラジアンで表された角度を度に変更します。"}, "ECMA.CEILING": {"a": "(数値; 基準値)", "d": "指定された基準値の倍数のうち、最も近い値に数値を切り上げます。"}, "EVEN": {"a": "(数値)", "d": "指定した数値をもっとも近い偶数に切り上げた値を返します。"}, "EXP": {"a": "(数値)", "d": "e を底とする数値のべき乗を返します。"}, "FACT": {"a": "(数値)", "d": "数値の階乗を返します。数値の階乗は、1 ～ 数値の範囲にある整数の積です。"}, "FACTDOUBLE": {"a": "(数値)", "d": "数値の二重階乗を計算します。"}, "FLOOR": {"a": "(数値; 基準値)", "d": "指定された基準値の倍数のうち、最も近い値に数値を切り捨てます。"}, "FLOOR.PRECISE": {"a": "(数値; [基準値])", "d": "最も近い整数、または最も近い基準値の倍数に切り捨てる数値を返します。"}, "FLOOR.MATH": {"a": "(数値; [基準値]; [モード])", "d": "数値を最も近い整数、または最も近い基準値の倍数に切り下げます。"}, "GCD": {"a": "(数値1; [数値2]; ...)", "d": "指定した数値の最大公約数を計算します。"}, "INT": {"a": "(数値)", "d": "切り捨てて整数にした数値を返します。"}, "ISO.CEILING": {"a": "(数値, [基準値])", "d": "最も近い整数に切り上げた値、または、指定された基準値の倍数のうち最も近い値を返します。 数値は正負に関係なく切り上げられます。 ただし、数値または基準値が 0 の場合は 0 が返されます。"}, "LCM": {"a": "(数値1; [数値2]; ...)", "d": "指定した整数の最小公倍数を計算します。"}, "LN": {"a": "(数値)", "d": "数値の自然対数を返します。"}, "LOG": {"a": "(数値; [底])", "d": "指定された数を底とする数値の対数を返します。"}, "LOG10": {"a": "(数値)", "d": "引数の常用対数を返します。"}, "MDETERM": {"a": "(配列)", "d": "配列の行列式を返します。"}, "MINVERSE": {"a": "(配列)", "d": "配列の逆行列を返します。"}, "MMULT": {"a": "(配列1; 配列2)", "d": "2 つの配列の積を返します。計算結果は、行数が配列 1 と同じで、列数が配列 2 と同じ配列になります。"}, "MOD": {"a": "(数値; 除数)", "d": "数値を除算した剰余を返します。"}, "MROUND": {"a": "(数値; 倍数)", "d": "指定した値の倍数になるように数値の切り上げあるいは切り捨てを行います。"}, "MULTINOMIAL": {"a": "(数値1; [数値2]; ...)", "d": "指定された数値の和の階乗と、指定された数値の階乗の積との比を計算します。"}, "MUNIT": {"a": "(次元)", "d": "指定された次元の単位行列を返します。"}, "ODD": {"a": "(数値)", "d": "正の数値を切り上げ、負の数値を切り捨てて、最も近い奇数にします。"}, "PI": {"a": "()", "d": "円周率π (3.14159265358979) を返します。"}, "POWER": {"a": "(数値; 指数)", "d": "数値を累乗した値を返します。"}, "PRODUCT": {"a": "(数値1; [数値2]; ...)", "d": "引数の積を返します。"}, "QUOTIENT": {"a": "(分子; 分母)", "d": "除算の商の整数部を返します。"}, "RADIANS": {"a": "(角度)", "d": "度単位で表された角度をラジアンに変換した結果を返します。"}, "RAND": {"a": "()", "d": "0 以上で 1 より小さい乱数を発生させます。再計算されるたびに、新しい乱数が返されます。"}, "RANDARRAY": {"a": "([行]; [列]; [最小]; [最大]; [整数])", "d": "乱数の配列を返します。"}, "RANDBETWEEN": {"a": "(最小値; 最大値)", "d": "指定された範囲で一様に分布する整数の乱数を返します。"}, "ROMAN": {"a": "(数値; [書式])", "d": "アラビア数字を、ローマ数字を表す文字列に変換します。"}, "ROUND": {"a": "(数値; 桁数)", "d": "数値を指定した桁数に四捨五入した値を返します。"}, "ROUNDDOWN": {"a": "(数値; 桁数)", "d": "数値を切り捨てます。"}, "ROUNDUP": {"a": "(数値; 桁数)", "d": "数値を切り上げます。"}, "SEC": {"a": "(数値)", "d": "角度の正割を返します。"}, "SECH": {"a": "(数値)", "d": "角度の双曲線正割を返します。"}, "SERIESSUM": {"a": "(x; n; m; 係数)", "d": "べき級数の和を計算します。"}, "SIGN": {"a": "(数値)", "d": "数値の正負を返します。戻り値は、数値が正の数のときは 1、0 のときは 0、負の数のときは -1 となります。"}, "SIN": {"a": "(数値)", "d": "角度のサインを返します。"}, "SINH": {"a": "(数値)", "d": "数値の双曲サインを返します。"}, "SQRT": {"a": "(数値)", "d": "数値の正の平方根を返します。"}, "SQRTPI": {"a": "(数値)", "d": "数値 x πの平方根の値を計算します。"}, "SUBTOTAL": {"a": "(集計方法; 参照1; ...)", "d": "リストまたはデータベースの集計値を返します。"}, "SUM": {"a": "(数値1; [数値2]; ...)", "d": "セル範囲に含まれる数値をすべて合計します。"}, "SUMIF": {"a": "(範囲; 検索条件; [合計範囲])", "d": "指定した検索条件に一致するセルの値を合計します"}, "SUMIFS": {"a": "(合計対象範囲; 条件範囲; 条件; ...)", "d": "特定の条件に一致する数値の合計を求めます"}, "SUMPRODUCT": {"a": "(配列1; [配列2]; [配列3]; ...)", "d": "範囲または配列の対応する要素の積を合計した結果を返します。"}, "SUMSQ": {"a": "(数値1; [数値2]; ...)", "d": "引数の 2 乗の和 (平方和) を返します。引数には、数値、数値を含む名前、配列、セル参照を指定できます。"}, "SUMX2MY2": {"a": "(配列1; 配列2)", "d": "2 つの配列で対応する配列要素の平方差を合計します。"}, "SUMX2PY2": {"a": "(配列1; 配列2)", "d": "2 つの配列の対応する値の積を合計した結果を返します。"}, "SUMXMY2": {"a": "(配列1; 配列2)", "d": "2 つの配列で対応する配列要素の差を 2 乗し、さらにその合計を返します。"}, "TAN": {"a": "(数値)", "d": "角度のタンジェントを返します。"}, "TANH": {"a": "(数値)", "d": "数値の双曲タンジェントを返します。"}, "TRUNC": {"a": "(数値; [桁数])", "d": "数値の小数部を切り捨てて、整数または指定した桁数に変換します。"}, "ADDRESS": {"a": "(行番号; 列番号; [参照の種類]; [参照形式]; [シート名])", "d": "指定したセルの参照を文字列の形式で返します。"}, "CHOOSE": {"a": "(インデックス; 値1; [値2]; ...)", "d": "インデックスを使って、引数リストから特定の値または動作を 1 つ選択します。"}, "COLUMN": {"a": "([参照])", "d": "参照の列番号を返します。"}, "COLUMNS": {"a": "(配列)", "d": "配列または参照の列数を返します。"}, "FORMULATEXT": {"a": "(参照)", "d": "数式を文字列として返します。"}, "HLOOKUP": {"a": "(検索値; 範囲; 行番号; [検索方法])", "d": "指定したテーブルまたは配列の先頭行で特定の値を検索し、指定した列と同じ行にある値を返します。"}, "HYPERLINK": {"a": "(リンク先; [別名])", "d": "ハード ディスク、ネットワーク サーバー、またはインターネット上に格納されているドキュメントを開くために、ショートカットまたはジャンプを作成します。"}, "INDEX": {"a": "(配列; 行番号; [列番号]!参照; 行番号; [列番号]; [領域番号])", "d": "指定された行と列が交差する位置にある値またはセルの参照を返します。"}, "INDIRECT": {"a": "(参照文字列; [参照形式])", "d": "指定される文字列への参照を返します。"}, "LOOKUP": {"a": "(検査値; 検査範囲; [対応範囲]!検査値; 配列)", "d": "1 行または 1 列のみのセル範囲、または配列に含まれる値を返します。この関数は旧バージョンとの互換性を維持するためのものです。"}, "MATCH": {"a": "(検査値; 検査範囲; [照合の種類])", "d": "指定された照合の種類に従って検査範囲内を検索し、検査値と一致する要素の、配列内での相対的な位置を表す数値を返します。"}, "OFFSET": {"a": "(参照; 行数; 列数; [高さ]; [幅])", "d": "指定した参照から指定した行数、列数の範囲への参照を返します。"}, "ROW": {"a": "([参照])", "d": "参照の行番号を返します。"}, "ROWS": {"a": "(配列)", "d": "参照、または配列に含まれる行数を返します。"}, "TRANSPOSE": {"a": "(配列)", "d": "配列の縦方向と横方向のセル範囲の変換を行います。"}, "UNIQUE": {"a": "(配列; [列の比較]; [回数指定])", "d": "範囲または配列から一意の値を返します"}, "VLOOKUP": {"a": "(検索値; 範囲; 列番号; [検索方法])", "d": "指定された範囲の 1 列目で特定の値を検索し、指定した列と同じ行にある値を返します。テーブルは昇順で並べ替えておく必要があります。"}, "XLOOKUP": {"a": "(検索値; 検索範囲; 戻り範囲; [見つからない場合]; [一致モード]; [検索モード])", "d": "範囲または配列で一致の検索を行い、2 つめの範囲または配列から対応する項目を返します。既定では、完全一致が使用されます"}, "CELL": {"a": "(検査の種類; [対象範囲])", "d": "セルの書式、位置、または内容に関する情報を返します。"}, "ERROR.TYPE": {"a": "(エラー値)", "d": "エラー値に対応する数値を返します。"}, "ISBLANK": {"a": "(テストの対象)", "d": "セルの内容が空白の場合に TRUE を返します。"}, "ISERR": {"a": "(テストの対象)", "d": "セルの内容が #N/A 以外のエラー値の場合に TRUE を返します。"}, "ISERROR": {"a": "(テストの対象)", "d": "セルの内容がエラー値の場合に TRUE を返します。"}, "ISEVEN": {"a": "(数値)", "d": "引き数に指定した数値が偶数のとき TRUE を返し、奇数のとき FALSE を返します。"}, "ISFORMULA": {"a": "(参照)", "d": "参照が数式を含むセルに対するものかどうかを確認し、TRUE または FALSE を返します。"}, "ISLOGICAL": {"a": "(テストの対象)", "d": "セルの内容が論理値 (TRUE または FALSE) の場合に TRUE を返します。"}, "ISNA": {"a": "(テストの対象)", "d": "セルの内容がエラー値 #N/A の場合に TRUE を返します。"}, "ISNONTEXT": {"a": "(テストの対象)", "d": "セルの内容が文字列以外の値 (空白セルも対象) である場合に TRUE を返します。"}, "ISNUMBER": {"a": "(テストの対象)", "d": "セルの内容が数値の場合に TRUE を返します。"}, "ISODD": {"a": "(数値)", "d": "引き数に指定した数値が奇数のとき TRUE を返し、偶数のとき FALSE を返します。"}, "ISREF": {"a": "(テストの対象)", "d": "セルの内容が参照である場合に TRUE を返します。"}, "ISTEXT": {"a": "(テストの対象)", "d": "セルの内容が文字列である場合に TRUE を返します。"}, "N": {"a": "(値)", "d": "非数値を数値に、日付をシリアル値に、TRUE の場合は 1 に、それ以外の場合は 0 に変換します。"}, "NA": {"a": "()", "d": "エラー値 #N/A (値が無効) を返します。"}, "SHEET": {"a": "([値])", "d": "参照されるシートのシート番号を返します。"}, "SHEETS": {"a": "([参照])", "d": "参照内のシート数を返します。"}, "TYPE": {"a": "(値)", "d": "値のデータ型を表す整数 (数値 = 1、文字列 = 2、論理値 = 4、エラー値 = 16、配列 = 64、複合データ = 128) を返します。"}, "AND": {"a": "(論理式1; [論理式2]; ...)", "d": "すべての引数が TRUE のとき、TRUE を返します。"}, "FALSE": {"a": "()", "d": "論理値 FALSE を返します。"}, "IF": {"a": "(論理式; [値が真の場合]; [値が偽の場合])", "d": "論理式の結果 (真または偽) に応じて、指定された値を返します"}, "IFS": {"a": "(論理式; 値が真の場合; ...)", "d": "1 つ以上の条件が満たされるかどうかを確認し、最初の真条件に対応する値を返します"}, "IFERROR": {"a": "(値; エラーの場合の値)", "d": "式がエラーの場合は、エラーの場合の値を返します。エラーでない場合は、式の値自体を返します。"}, "IFNA": {"a": "(値; NAの場合の値)", "d": "式が #N/A に解決される場合に指定する値を返します。それ以外の場合は、式の結果を返します。"}, "NOT": {"a": "(論理式)", "d": "引数が FALSE の場合は TRUE、TRUE の場合は FALSE を返します"}, "OR": {"a": "(論理式1; [論理式2]; ...)", "d": "いずれかの引数が TRUE のとき、TRUE を返します。引数がすべて FALSE である場合は、FALSE を返します。"}, "SWITCH": {"a": "(式; 値1; 結果1; [既定または値2]; [結果2]; ...)", "d": " 値の一覧で式を計算し、最初に一致する値に対応する結果が返されます。一致しない場合は、任意の既定値が返されます"}, "TRUE": {"a": "()", "d": "論理値 TRUE を返します。"}, "XOR": {"a": "(論理式1; [論理式2]; ...)", "d": "すべての引数の排他的論理和を返します。"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "文字を区切る前のテキストを返します。"}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "文字を区切った後のテキストを返します。"}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "区切り記号を使用してテキストを行または列に分割。"}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "指定した数の値の後に行または列ベクトルを折り返します。"}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "垂直方向に配列を 1 つの配列にスタックします。"}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "水平方向に配列を 1 つの配列に水にスタックします。"}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "配列または参照から行を返します。"}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "配列または参照から列を返します。"}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "配列を 1 つの列として返します。"}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "配列を 1 行として返します。"}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "指定した数の値の後に行または列のベクトルをラップする。"}, "TAKE": {"a": "(array, rows, [columns])", "d": "配列の開始または終了から行または列を返します。"}, "DROP": {"a": "(array, rows, [columns])", "d": "配列の先頭または末尾から行または列を削除します。"}}
{"DATE": {"a": "(anno; mese; giorno)", "d": "Restituisce il numero che rappresenta la data in codice data-ora."}, "DATEDIF": {"a": "(data_inizio; data_fine; unit)", "d": "Restituisce la differenza tra le due date in ingresso (data_inizio e data_fine), basato sull'unità (unit) specificata"}, "DATEVALUE": {"a": "(data)", "d": "Converte una data in formato testo in un numero che rappresenta la data nel codice data-ora."}, "DAY": {"a": "(num_seriale)", "d": "Restituisce il giorno del mese, un numero compreso tra 1 e 31."}, "DAYS": {"a": "(data_fine; data_inizio)", "d": "Restituisce il numero di giorni che intercorre tra due date."}, "DAYS360": {"a": "(data_iniziale; data_finale; [metodo])", "d": "Restituisce il numero di giorni compresi tra due date sulla base di un anno di 360 giorni (dodici mesi di 30 giorni)."}, "EDATE": {"a": "(data_iniziale; mesi)", "d": "Restituisce il numero seriale della data il cui mese è precedente o successivo a quello della data iniziale, a seconda del numero indicato dall'argomento mesi."}, "EOMONTH": {"a": "(data_iniziale; mesi)", "d": "Restituisce il numero seriale dell'ultimo giorno del mese precedente o successivo di un numero specificato di mesi."}, "HOUR": {"a": "(num_seriale)", "d": "Restituisce l'ora come numero compreso tra 0 e 23."}, "ISOWEEKNUM": {"a": "(data)", "d": "Restituisce il numero settimana ISO dell'anno per una data specificata"}, "MINUTE": {"a": "(num_seriale)", "d": "Restituisce il minuto, un numero compreso tra 0 e 59."}, "MONTH": {"a": "(num_seriale)", "d": "Restituisce il mese, un numero compreso tra 1 (gennaio) e 12 (dicembre)."}, "NETWORKDAYS": {"a": "(data_iniziale; data_finale; [vacanze])", "d": "Restituisce il numero dei giorni lavorativi compresi tra due date"}, "NETWORKDAYS.INTL": {"a": "(data_iniziale; data_finale; [festivi]; [vacanze])", "d": "Restituisce il numero dei giorni lavorativi compresi tra due date con parametri di giorni festivi personalizzati"}, "NOW": {"a": "()", "d": "Restituisce la data e l'ora correnti nel formato data-ora."}, "SECOND": {"a": "(num_seriale)", "d": "Restituisce il secondo, un numero compreso tra 0 e 59."}, "TIME": {"a": "(ora; minuto; secondo)", "d": "Converte ore, minuti e secondi forniti come numeri in un un numero seriale, formattato in modo appropriato."}, "TIMEval": {"a": "(ora)", "d": "Restituisce il numero seriale del tempo"}, "TODAY": {"a": "()", "d": "Restituisce la data corrente nel formato data."}, "WEEKDAY": {"a": "(num_seriale; [tipo_restituito])", "d": "Restituisce un numero compreso tra 1 e 7 che identifica il giorno della settimana di una data."}, "WEEKNUM": {"a": "(num_seriale; [tipo_restituito])", "d": "Restituisce il numero della settimana dell'anno."}, "WORKDAY": {"a": "(data_iniziale; giorni; [vacanze])", "d": "Restituisce la data, espressa come numero seriale, del giorno precedente o successivo a un numero specificato di giorni lavorativi."}, "WORKDAY.INTL": {"a": "(data_iniziale; giorni; [festivi]; [vacanze])", "d": "Restituisce la data, espressa come numero seriale, del giorno precedente o successivo a un numero specificato di giorni lavorativi con parametri di giorni festivi personalizzati."}, "YEAR": {"a": "(num_seriale)", "d": "Restituisce l'anno di una data, un intero nell'intervallo compreso tra 1900 e 9999."}, "YEARFRAC": {"a": "(data_iniziale; data_finale; [base])", "d": "Restituisce la frazione dell'anno corrispondente al numero dei giorni complessivi compresi tra 'data_iniziale' e 'data_finale'."}, "BESSELI": {"a": "(x; n)", "d": "Restituisce la funzione di Bessel modificata In(x)."}, "BESSELJ": {"a": "(x; n)", "d": "Restituisce la funzione di Bessel Jn(x)."}, "BESSELK": {"a": "(x; n)", "d": "Restituisce la funzione di Bessel modificata Kn(x)."}, "BESSELY": {"a": "(x; n)", "d": "Restituisce la funzione di Bessel Yn(x)."}, "BIN2DEC": {"a": "(num)", "d": "Converte un numero binario in decimale."}, "BIN2HEX": {"a": "(num; [cifre])", "d": "Converte un numero binario in esadecimale."}, "BIN2OCT": {"a": "(num; [cifre])", "d": "Converte un numero binario in ottale."}, "BITAND": {"a": "(num1; num2)", "d": "Restituisce un 'AND' bit per bit di due numeri"}, "BITLSHIFT": {"a": "(num; bit_spostamento)", "d": "Restituisce un numero spostato a sinistra dei bit indicati in bit_spostamento"}, "BITOR": {"a": "(num1; num2)", "d": "Restituisce un 'OR' bit per bit di due numeri"}, "BITRSHIFT": {"a": "(num; bit_spostamento)", "d": "Restituisce un numero spostato a destra dei bit indicati in bit_spostamento"}, "BITXOR": {"a": "(num1; num2)", "d": "Restituisce un 'OR esclusivo' bit per bit di due numeri"}, "COMPLEX": {"a": "(parte_reale; coeff_imm; [suffisso])", "d": "Converte la parte reale e il coefficiente dell'immaginario in un numero complesso."}, "DEC2BIN": {"a": "(num; [cifre])", "d": "Converte un numero decimale in binario."}, "DEC2HEX": {"a": "(num; [cifre])", "d": "Converte un numero decimale in esadecimale."}, "DEC2OCT": {"a": "(num; [cifre])", "d": "Converte un numero decimale in ottale."}, "DELTA": {"a": "(num1; [num2])", "d": "Verifica se due numeri sono uguali"}, "ERF": {"a": "(limite_inf; [limite_sup])", "d": "Restituisce la funzione di errore."}, "ERF.PRECISE": {"a": "(X)", "d": "Restituisce la funzione di errore"}, "ERFC": {"a": "(x)", "d": "Restituisce la funzione di errore complementare."}, "ERFC.PRECISE": {"a": "(X)", "d": "Restituisce la funzione di errore complementare"}, "GESTEP": {"a": "(num; [val_soglia])", "d": "Verifica se un numero è maggiore di un valore soglia"}, "HEX2BIN": {"a": "(num; [cifre])", "d": "Converte un numero esadecimale in binario."}, "HEX2DEC": {"a": "(num)", "d": "Converte un numero esadecimale in decimale."}, "HEX2OCT": {"a": "(num; [cifre])", "d": "Converte un numero esadecimale in ottale."}, "IMABS": {"a": "(num_comp)", "d": "Restituisce il valore assoluto (modulo) di un numero complesso."}, "IMAGINARY": {"a": "(num_comp)", "d": "Restituisce il coefficiente dell'immaginario di un numero complesso."}, "IMARGUMENT": {"a": "(num_comp)", "d": "Restituisce l'argomento teta, un angolo espresso in radianti."}, "IMCONJUGATE": {"a": "(num_comp)", "d": "Restituisce il complesso coniugato di un numero complesso."}, "IMCOS": {"a": "(num_comp)", "d": "Restituisce il coseno di un numero complesso."}, "IMCOSH": {"a": "(num_comp)", "d": "Restituisce il coseno iperbolico di un numero complesso"}, "IMCOST": {"a": "(num_comp)", "d": "Restituisce la cotangente di un numero complesso"}, "IMCSC": {"a": "(num_comp)", "d": "Restituisce la cosecante di un numero complesso"}, "IMCSCH": {"a": "(num_comp)", "d": "Restituisce la cosecante iperbolica di un numero complesso"}, "IMDIV": {"a": "(num_comp1; num_comp2)", "d": "Restituisce il quoziente di due numeri complessi."}, "IMEXP": {"a": "(num_comp)", "d": "Restituisce l'esponenziale di un numero complesso."}, "IMLN": {"a": "(num_comp)", "d": "Restituisce il logaritmo naturale di un numero complesso."}, "IMLOG10": {"a": "(num_comp)", "d": "Restituisce il logaritmo in base 10 di un numero complesso."}, "IMLOG2": {"a": "(num_comp)", "d": "Restituisce il logaritmo in base 2 di un numero complesso."}, "IMPOWER": {"a": "(num_comp; num)", "d": "Restituisce un numero complesso elevato a un esponente intero."}, "IMPRODUCT": {"a": "(num_comp1; [num_comp2]; ...)", "d": "Restituisce il prodotto di numeri complessi."}, "IMREAL": {"a": "(num_comp)", "d": "Restituisce la parte reale di un numero complesso."}, "IMSEC": {"a": "(num_comp)", "d": "Restituisce la secante di un numero complesso"}, "IMSECH": {"a": "(num_comp)", "d": "Restituisce la secante iperbolica di un numero complesso"}, "IMSIN": {"a": "(num_comp)", "d": "Restituisce il seno di un numero complesso."}, "IMSINH": {"a": "(num_comp)", "d": "Restituisce il seno iperbolico di un numero complesso"}, "IMSQRT": {"a": "(num_comp)", "d": "Restituisce la radice quadrata di un numero complesso."}, "IMSUB": {"a": "(num_comp1; num_comp2)", "d": "Restituisce la differenza di due numeri complessi."}, "IMSUM": {"a": "(num_comp1; [num_comp2]; ...)", "d": "Restituisce la somma di numeri complessi."}, "IMTAN": {"a": "(num_comp)", "d": "Restituisce la tangente di un numero complesso"}, "OCT2BIN": {"a": "(num; [cifre])", "d": "Converte un numero ottale in binario."}, "OCT2DEC": {"a": "(num)", "d": "Converte un numero ottale in decimale."}, "OCT2HEX": {"a": "(num; [cifre])", "d": "Converte un numero ottale in esadecimale."}, "DAVERAGE": {"a": "(database; campo; criteri)", "d": "Restituisce la media dei valori di una colonna di un elenco o di un database che soddisfano le condizioni specificate."}, "DCOUNT": {"a": "(database; campo; criteri)", "d": "Conta le celle nel campo (colonna) dei record del database che soddisfano le condizioni specificate."}, "DCOUNTA": {"a": "(database; campo; criteri)", "d": "Conta le celle non vuote nel campo (colonna) dei record del database che soddisfano le condizioni specificate."}, "DGET": {"a": "(database; campo; criteri)", "d": "Estrae da un database un singolo record che soddisfa le condizioni specificate."}, "DMAX": {"a": "(database; campo; criteri)", "d": "Restituisce il valore massimo nel campo (colonna) di record del database che soddisfa le condizioni specificate."}, "DMIN": {"a": "(database; campo; criteri)", "d": "Restituisce il valore minimo nel campo (colonna) di record del database che soddisfa le condizioni specificate."}, "DPRODUCT": {"a": "(database; campo; criteri)", "d": "Moltiplica i valori nel campo (colonna) di record del database che soddisfano le condizioni specificate."}, "DSTDEV": {"a": "(database; campo; criteri)", "d": "Stima la deviazione standard sulla base di un campione di voci del database selezionate."}, "DSTDEVP": {"a": "(database; campo; criteri)", "d": "Calcola la deviazione standard sulla base dell'intera popolazione di voci del database selezionate."}, "DSUM": {"a": "(database; campo; criteri)", "d": "Aggiunge i numeri nel campo (colonna) di record del database che soddisfano le condizioni specificate."}, "DVAR": {"a": "(database; campo; criteri)", "d": "Stima la varianza sulla base di un campione di voci del database selezionate."}, "DVARP": {"a": "(database; campo; criteri)", "d": "Calcola la varianza sulla base dell'intera popolazione di voci del database selezionate."}, "CHAR": {"a": "(num)", "d": "Restituisce il carattere specificato dal numero di codice del set di caratteri del computer."}, "CLEAN": {"a": "(testo)", "d": "Rimuove dal testo tutti i caratteri che non possono essere stampati."}, "CODE": {"a": "(testo)", "d": "Restituisce il codice numerico del primo carattere di una stringa di testo, in base al set di caratteri installato nel sistema."}, "CONCATENATE": {"a": "(testo1; [testo2]; ...)", "d": "Unisce diverse stringhe di testo in una singola stringa."}, "CONCAT": {"a": "(testo1; ...)", "d": "Concatena un elenco o un intervallo di stringhe di testo"}, "DOLLAR": {"a": "(num; [decimali])", "d": "Converte un numero in testo utilizzando un formato valuta."}, "EXACT": {"a": "(testo1; testo2)", "d": "Controlla due stringhe di testo e restituisce il valore VERO se sono identiche e FALSO in caso contrario. Distingue tra maiuscole e minuscole."}, "FIND": {"a": "(testo; stringa; [inizio])", "d": "Trova una stringa di testo all'interno di un'altra stringa e restituisce il numero corrispondente alla posizione iniziale della stringa trovata. La funzione distingue tra maiuscole e minuscole"}, "FINDB": {"a": "(testo-1; stringa; [inizio])", "d": "Trova una stringa di testo all'interno di un'altra stringa e restituisce il numero corrispondente alla posizione iniziale della stringa trovata. Distingue tra maiuscole e minuscole, set (DBSC) per linguaggi come Japanese, Chinese, Korean etc."}, "FIXED": {"a": "(num; [decimali]; [nessun_separatore])", "d": "Arrotonda un numero al numero di cifre decimali specificato e restituisce il risultato come testo."}, "LEFT": {"a": "(testo; [num_caratt])", "d": "Restituisce il carattere o i caratteri più a sinistra di una stringa di testo."}, "LEFTB": {"a": "(testo; [num_caratt])", "d": "Restituisce il carattere o i caratteri più a sinistra di una stringa di testo set (DBCS) per linguaggi come Japanese, Chinese, Korean etc."}, "LEN": {"a": "(testo)", "d": "Restituisce il numero di caratteri in una stringa di testo."}, "LENB": {"a": "(testo)", "d": "Restituisce il numero di caratteri in una stringa di testo set (DBCS) per linguaggi come Japanese, Chinese, Korean etc."}, "LOWER": {"a": "(testo)", "d": "Converte le lettere maiuscole in una stringa di testo in lettere minuscole."}, "MID": {"a": "(testo; inizio; num_caratt)", "d": "Restituisce un numero specifico di caratteri da una stringa di testo iniziando dalla posizione specificata."}, "MIDB": {"a": "(testo; inizio; num_caratt)", "d": "Restituisce un numero specifico di caratteri da una stringa di testo iniziando dalla posizione specificata set (DBCS) per linguaggi come Japanese, Chinese, Korean etc."}, "NUMBERVALUE": {"a": "(testo; [separatore_decimale]; [separatore_gruppo])", "d": "Converte il testo in numero in modo indipendente dalle impostazioni locali"}, "PROPER": {"a": "(testo)", "d": "Converte in maiuscolo la prima lettera di ciascuna parola in una stringa di testo e converte le altre lettere in minuscolo."}, "REPLACE": {"a": "(testo_prec; inizio; num_caratt; nuovo_testo)", "d": "Sostituisce parte di una stringa di testo con un'altra stringa di testo."}, "REPLACEB": {"a": "(testo_prec; inizio; num_caratt; nuovo_testo)", "d": "Sostituisce parte di una stringa di testo con un'altra stringa di testo set (DBCS) per linguaggi come Japanese, Chinese, Korean etc."}, "REPT": {"a": "(testo; volte)", "d": "Ripete un testo per il numero di volte specificato. Utilizzare RIPETI per riempire una cella con il numero di occorrenze di una stringa di testo."}, "RIGHT": {"a": "(testo; [num_caratt])", "d": "Restituisce il carattere o i caratteri più a destra di una stringa di testo."}, "RIGHTB": {"a": "(testo; [num_caratt])", "d": "Restituisce l'ultimo o gli ultimi caratteri di una stringa di testo, in base al numero di byte specificati set (DBCS) per linguaggi come Japanese, Chinese, Korean etc."}, "SEARCH": {"a": "(testo; stringa; [inizio])", "d": "Restituisce il numero corrispondente al carattere o alla stringa di testo trovata in una seconda stringa di testo (non distingue tra maiuscole e minuscole)."}, "SEARCHB": {"a": "(testo; stringa; [inizio])", "d": "Restituisce il numero corrispondente al carattere o alla stringa di testo trovata in una seconda stringa di testo (non distingue tra maiuscole e minuscole) set (DBCS) per linguaggi come Japanese, Chinese, Korean etc."}, "SUBSTITUTE": {"a": "(testo; testo_prec; nuovo_testo; [occorrenza])", "d": "Sostituisce il nuovo testo a quello esistente in una stringa di testo."}, "T": {"a": "(val)", "d": "Controlla se il valore è un testo e, in caso positivo, lo restituisce, altrimenti vengono restituite delle virgolette, ossia testo vuoto"}, "T.TEST": {"a": "(matrice1; matrice2; coda; tipo)", "d": "Restituisce la probabilità associata ad un test t di Student."}, "TEXTJOIN": {"a": "(delimitatore; ignora_vuote; testo1; ...)", "d": "Concatena un elenco o un intervallo di stringhe di testo tramite un delimitatore"}, "TEXT": {"a": "(valore; format_text)", "d": "Converte un valore in testo in un formato di numero specifico"}, "TRIM": {"a": "(testo)", "d": "Rimuove gli spazi da una stringa di testo eccetto gli spazi singoli tra le parole."}, "TREND": {"a": "(y_note; [x_note]; [nuove_x]; [cost])", "d": "Restituisce i numeri in una una tendenza lineare corrispondente a punti dati noti usando il metodo dei minimi quadrati."}, "TRIMMEAN": {"a": "(matrice; percento)", "d": "Restituisce la media della parte interna di un set di valori di dati."}, "TTEST": {"a": "(matrice1; matrice2; coda; tipo)", "d": "Restituisce la probabilità associata ad un test t di Student."}, "UNICHAR": {"a": "(num)", "d": "Restituisce il carattere Unicode corrispondente al valore numerico specificato"}, "UNICODE": {"a": "(testo)", "d": "Restituisce il numero (punto di codice) corrispondente al primo carattere del testo"}, "UPPER": {"a": "(testo)", "d": "Converte una stringa di testo in maiuscolo."}, "VALUE": {"a": "(testo)", "d": "Converte una stringa di testo che rappresenta un numero in una stringa di testo."}, "AVEDEV": {"a": "(num1; [num2]; ...)", "d": "Restituisce la media delle deviazioni assolute delle coordinate rispetto alla media di queste ultime. Gli argomenti possono essere numeri o nomi, matrici o riferimenti contenenti numeri."}, "AVERAGE": {"a": "(num1; [num2]; ...)", "d": "Restituisce la media aritmetica degli argomenti (numeri, nomi o riferimenti contenenti numeri)."}, "AVERAGEA": {"a": "(val1; [val2]; ...)", "d": "Restituisce la media aritmetica degli argomenti. Gli argomenti costituiti da testo o dal valore FALSO vengono valutati come 0, quelli costituiti dal valore VERO come 1. Gli argomenti possono essere numeri, nomi, matrici o riferimenti."}, "AVERAGEIF": {"a": "(intervallo; criterio; [int_media])", "d": "Determina la media aritmetica per le celle specificate da una determinata condizione o criterio."}, "AVERAGEIFS": {"a": "(int_media; int_criteri; criterio; ...)", "d": "Determina la media aritmetica per le celle specificate da un determinato insieme di condizioni o criteri."}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Calcola la funzione densità di probabilità cumulativa beta."}, "BETAINV": {"a": "(probabilità; alfa; beta; [A]; [B])", "d": "Restituisce l'inversa della funzione densità di probabilità cumulativa beta (DISTRIB.BETA)."}, "BETA.DIST": {"a": "(x; alfa; beta; cumulativa; [A]; [B])", "d": "Calcola la funzione di distribuzione probabilità beta."}, "BETA.INV": {"a": "(probabilità; alfa; beta; [A]; [B])", "d": "Restituisce l'inversa della funzione densità di probabilità cumulativa beta (DISTRIB.BETA.N)."}, "BINOMDIST": {"a": "(num_successi; prove; probabilità_s; cumulativo)", "d": "Restituisce la distribuzione binomiale per il termine individuale."}, "BINOM.DIST": {"a": "(num_successi; prove; probabilità_s; cumulativo)", "d": "Restituisce la distribuzione binomiale per il termine individuale."}, "BINOM.DIST.RANGE": {"a": "(prove; probabilità_s; num_s; [num_s2])", "d": "Restituisce la probabilità di un risultato di prova usando una distribuzione binomiale"}, "BINOM.INV": {"a": "(prove; probabilità_s; alfa)", "d": "Restituisce il più piccolo valore per il quale la distribuzione cumulativa binomiale risulta maggiore o uguale ad un valore di criterio."}, "CHIDIST": {"a": "(x; grad_libertà)", "d": "Restituisce la probabilità a una coda destra per la distribuzione del chi quadrato."}, "CHIINV": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della probabilità a una coda destra per la distribuzione del chi quadrato."}, "CHITEST": {"a": "(int_effettivo; int_previsto)", "d": "Restituisce il test per l'indipendenza: il valore della distribuzione del chi quadrato per la statistica e i gradi di libertà appropriati."}, "CHISQ.DIST": {"a": "(x; grad_libertà; cumulativa)", "d": "Restituisce la probabilità a una coda sinistra per la distribuzione del chi quadrato."}, "CHISQ.DIST.RT": {"a": "(x; grad_libertà)", "d": "Restituisce la probabilità ad una coda destra per la distribuzione del chi quadrato."}, "CHISQ.INV": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della probabilità a una coda sinistra della distribuzione del chi quadrato."}, "CHISQ.INV.RT": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della probabilità a una coda destra della distribuzione del chi quadrato."}, "CHISQ.TEST": {"a": "(int_effettivo; int_previsto)", "d": "Restituisce il test per l'indipendenza: il valore dalla distribuzione del chi quadrato per la statistica e i gradi di libertà appropriati."}, "CONFIDENCE": {"a": "(alfa; dev_standard; dimensioni)", "d": "Restituisce l'intervallo di confidenza per una popolazione, utilizzando una distribuzione normale"}, "CONFIDENCE.NORM": {"a": "(alfa; dev_standard; dimensioni)", "d": "Restituisce l'intervallo di confidenza per una popolazione, utilizzando una distribuzione normale."}, "CONFIDENCE.T": {"a": "(alfa; dev_standard; dimensioni)", "d": "Restituisce l'intervallo di confidenza per una popolazione, utilizzando una distribuzione T di Student."}, "CORREL": {"a": "(matrice1; matrice2)", "d": "Restituisce il coefficiente di correlazione tra due set di dati."}, "COUNT": {"a": "(val1; [val2]; ...)", "d": "Conta il numero di celle in un intervallo contenente numeri e i numeri presenti nell'elenco degli argomenti"}, "COUNTA": {"a": "(val1; [val2]; ...)", "d": "Conta il numero delle celle non vuote e i valori presenti nell'elenco degli argomenti."}, "COUNTBLANK": {"a": "(intervallo)", "d": "Conta il numero di celle vuote in uno specificato intervallo."}, "COUNTIF": {"a": "(intervallo; criterio)", "d": "Conta il numero di celle in un intervallo che corrispondono al criterio dato."}, "COUNTIFS": {"a": "(intervallo_criteri; criteri; ...)", "d": "Conta il numero di celle specificate da un determinato insieme di condizioni o criteri."}, "COVAR": {"a": "(matrice1; matrice2)", "d": "Calcola la covarianza, la media dei prodotti delle deviazioni di ciascuna coppia di coordinate in due set di dati."}, "COVARIANCE.P": {"a": "(matrice1; matrice2)", "d": "Calcola la covarianza della popolazione, la media dei prodotti delle deviazioni di ciascuna coppia di coordinate in due set di dati"}, "COVARIANCE.S": {"a": "(matrice1; matrice2)", "d": "Calcola la covarianza del campione, la media dei prodotti delle deviazioni di ciascuna coppia di coordinate in due set di dati"}, "CRITBINOM": {"a": "(prove; probabilità_s; alfa)", "d": "Restituisce il più piccolo valore per il quale la distribuzione cumulativa binomiale risulta maggiore o uguale ad un valore di criterio."}, "DEVSQ": {"a": "(num1; [num2]; ...)", "d": "Restituisce la somma dei quadrati delle deviazioni delle coordinate dalla media di queste ultime sul campione."}, "EXPON.DIST": {"a": "(x; lambda; cumulativo)", "d": "Restituisce la distribuzione esponenziale."}, "EXPONDIST": {"a": "(x; lambda; cumulativo)", "d": "Restituisce la distribuzione esponenziale."}, "FDIST": {"a": "(x; grad_libertà1; grad_libertà2)", "d": "Restituisce la distribuzione di probabilità F (coda destra) (grado di diversità) per due set di dati."}, "FINV": {"a": "(probabilità; grado_libertà1; grado_libertà2)", "d": "Restituisce l'inversa della distribuzione di probabilità F (coda destra): se p = FDIST(x,...), allora FINV(p,...) = x"}, "F.DIST": {"a": "(x; grad_libertà1; grad_libertà2; cumulativa)", "d": "Restituisce la distribuzione di probabilità F (coda sinistra ) (grado di diversità) per due set di dati."}, "F.DIST.RT": {"a": "(x; grad_libertà1; grad_libertà2)", "d": "Restituisce la distribuzione di probabilità F (coda destra) (grado di diversità) per due set di dati."}, "F.INV": {"a": "(probabilità; grado_libertà1; grado_libertà2)", "d": "Restituisce l'inversa della distribuzione di probabilità F (coda sinistra): se p = DISTRIB.F(x,...), allora INVF(p,...) = x"}, "F.INV.RT": {"a": "(probabilità; grado_libertà1; grado_libertà2)", "d": "Restituisce l'inversa della distribuzione di probabilità F (coda destra): se p = DISTRIB.F.DS(x,...), allora INV.F.DS(p,...) = x"}, "FISHER": {"a": "(x)", "d": "Restituisce la trasformazione di Fisher."}, "FISHERINV": {"a": "(y)", "d": "Restituisce l'inversa della trasformazione di Fisher: se y = FISHER(x), allora INV.FISHER(y) = x."}, "FORECAST": {"a": "(x; y_note; x_note)", "d": "<PERSON><PERSON> o prevede un valore futuro lungo una tendenza lineare usando i valori esistenti"}, "FORECAST.LINEAR": {"a": "(x; y_note; x_note)", "d": "<PERSON><PERSON> o prevede un valore futuro lungo una tendenza lineare usando i valori esistenti."}, "FREQUENCY": {"a": "(matrice_dati; matrice_bin)", "d": "Calcola la frequenza con cui si presentano valori compresi in un intervallo e restituisce una matrice verticale di numeri con un elemento in più rispetto a Matrice_bin."}, "GAMMA": {"a": "(x)", "d": "Restituisce il valore della funzione GAMMA"}, "GAMMADIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Restituisce la distribuzione gamma."}, "GAMMA.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Restituisce la distribuzione gamma."}, "GAMMAINV": {"a": "(probabilità; alfa; beta)", "d": "Restituisce l'inversa della distribuzione cumulativa gamma: se p = DISTRIB.GAMMA(x,...), allora INV.GAMMA(p,...) = x"}, "GAMMA.INV": {"a": "(probabilità; alfa; beta)", "d": "Restituisce l'inversa della distribuzione cumulativa gamma: se p = DISTRIB.GAMMA.N(x,...), allora INV.GAMMA.N(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Restituisce il logaritmo naturale della funzione gamma."}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Restituisce il logaritmo naturale della funzione gamma."}, "GAUSS": {"a": "(x)", "d": "Restituisce il valore risultante dalla detrazione di 0,5 dalla distribuzione normale standard cumulativa"}, "GEOMEAN": {"a": "(num1; [num2]; ...)", "d": "Restituisce la media geometrica di una matrice o di un intervallo di dati numerici positivi."}, "GROWTH": {"a": "(y_note; [x_note]; [nuove_x]; [cost])", "d": "Calcola la crescita esponenziale prevista utilizzando coordinate esistenti."}, "HARMEAN": {"a": "(num1; [num2]; ...)", "d": "Calcola la media armonica (il reciproco della media aritmetica dei reciproci) di un set di dati costituiti da numeri positivi."}, "HYPGEOM.DIST": {"a": "(s_campione; num_campione; s_pop; num_pop; cumulativa)", "d": "Restituisce la distribuzione ipergeometrica"}, "HYPGEOMDIST": {"a": "(s_esempio; num_esempio; s_pop; num_pop)", "d": "Restituisce la distribuzione ipergeometrica"}, "INTERCEPT": {"a": "(y_note; x_note)", "d": "Calcola il punto di intersezione della retta con l'asse y tracciando una regressione lineare fra le coordinate note."}, "KURT": {"a": "(num1; [num2]; ...)", "d": "Restituisce la curtosi di un set di dati."}, "LARGE": {"a": "(matrice; k)", "d": "Restituisce il k-esimo valore più grande in un set di dati. Ad esempio, il quinto numero più grande."}, "LINEST": {"a": "(y_note; [x_note]; [cost]; [stat])", "d": "Restituisce statistiche che descrivono una tendenza lineare corrispondente a punti dati noti usando il metodo dei minimi quadrati."}, "LOGEST": {"a": "(y_note; [x_note]; [cost]; [stat])", "d": "Restituisce statistiche che descrivono una curva esponenziale che corrisponde a punti dati noti."}, "LOGINV": {"a": "(probabilità; media; dev_standard)", "d": "Restituisce l'inversa della distribuzione lognormale di x, in cui ln(x) è distribuito normalmente con i parametri Media e Dev_standard."}, "LOGNORM.DIST": {"a": "(x; media; dev_standard; cumulativa)", "d": "Restituisce la distribuzione lognormale di x, in cui ln(x) è distribuito normalmente con i parametri Media e Dev_standard."}, "LOGNORM.INV": {"a": "(probabilità; media; dev_standard)", "d": "Restituisce l'inversa della distribuzione lognormale di x, in cui ln(x) è distribuito normalmente con i parametri Media e Dev_standard."}, "LOGNORMDIST": {"a": "(x; media; dev_standard)", "d": "Restituisce la distribuzione lognormale di x, in cui ln(x) è distribuito normalmente con i parametri Media e Dev_standard."}, "MAX": {"a": "(num1; [num2]; ...)", "d": "Restituisce il valore massimo di un insieme di valori. Ignora i valori logici e il testo."}, "MAXA": {"a": "(val1; [val2]; ...)", "d": "Restituisce il valore massimo di un insieme di valori. Non ignora i valori logici e il testo."}, "MAXIFS": {"a": "(intervallo_max; intervallo_criteri; criteri; ...)", "d": "Restituisce il valore massimo tra le celle specificato da un determinato insieme di condizioni o criteri"}, "MEDIAN": {"a": "(num1; [num2]; ...)", "d": "Restituisce la mediana, ovvero il valore centrale, di un insieme ordinato di numeri specificato."}, "MIN": {"a": "(num1; [num2]; ...)", "d": "Restituisce il valore minimo di un insieme di valori. Ignora i valori logici e il testo."}, "MINA": {"a": "(val1; [val2]; ...)", "d": "Restituisce il valore minimo di un insieme di valori. Non ignora i valori logici e il testo."}, "MINIFS": {"a": "(intervallo_min; intervallo_criteri; criteri; ...)", "d": "Restituisce il valore minimo tra le celle specificato da un determinato insieme di condizioni o criteri"}, "MODE": {"a": "(num1; [num2]; ...)", "d": "Restituisce il valore più ricorrente in una matrice o intervallo di dati."}, "MODE.MULT": {"a": "(num1; [num2]; ...)", "d": "Restituisce il valore più ricorrente in una matrice o intervallo di dati. Per una matrice orizzontale, utilizzare MATR.TRASPOSTA(MODA.MULT(num1,num2,...))"}, "MODE.SNGL": {"a": "(num1; [num2]; ...)", "d": "Restituisce il valore più ricorrente in una matrice o intervallo di dati."}, "NEGBINOM.DIST": {"a": "(num_insuccessi; num_successi; probabilità_s; cumulativa)", "d": "Restituisce la distribuzione binomiale negativa, la probabilità che un numero di insuccessi pari a Num_insuccessi si verifichi prima del successo Num_successi, data la probabilità di successo Probabilità_s."}, "NEGBINOMDIST": {"a": "(num_insuccessi; num_successi; probabilità_s)", "d": "Restituisce la distribuzione binomiale negativa, la probabilità che un numero di insuccessi pari a Num_insuccessi si verifichi prima del successo Num_successi, data la probabilità di successo Probabilità_s."}, "NORM.DIST": {"a": "(x; media; dev_standard; cumulativo)", "d": "Restituisce la distribuzione normale per la media e la deviazione standard specificate."}, "NORMDIST": {"a": "(x; media; dev_standard; cumulativo)", "d": "Restituisce la distribuzione normale cumulativa per la media e la deviazione standard specificate."}, "NORM.INV": {"a": "(probabilità; media; dev_standard)", "d": "Restituisce l'inversa della distribuzione normale cumulativa per la media e la deviazione standard specificate."}, "NORMINV": {"a": "(probabilità; media; dev_standard)", "d": "Restituisce l'inversa della distribuzione normale cumulativa per la media e la deviazione standard specificate."}, "NORM.S.DIST": {"a": "(z; cumulativa)", "d": "Restituisce la distribuzione normale standard cumulativa (ha media pari a zero e deviazione standard pari a 1)."}, "NORMSDIST": {"a": "(z)", "d": "Restituisce la distribuzione normale standard cumulativa (ha media pari a zero e deviazione standard pari a 1)."}, "NORMS.INV": {"a": "(probabilità)", "d": "Restituisce l'inversa della distribuzione normale standard cumulativa( ha media = 0 e dev_standard = 1)"}, "NORMSINV": {"a": "(probabilità)", "d": "Restituisce l'inversa della distribuzione normale standard cumulativa (ha media pari a zero e deviazione standard pari a 1)."}, "PEARSON": {"a": "(matrice1; matrice2)", "d": "Restituisce il prodotto del coefficiente di momento di correlazione di Pearson, r."}, "PERCENTILE": {"a": "(matrice; k)", "d": "Restituisce il k-esimo dato percentile di valori in un intervallo."}, "PERCENTILE.EXC": {"a": "(matrice; k)", "d": "Restituisce il k-esimo dato percentile di valori in un intervallo, dove k è compreso nell'intervallo 0..1, estremi esclusi."}, "PERCENTILE.INC": {"a": "(matrice; k)", "d": "Restituisce il k-esimo dato percentile di valori in un intervallo, dove k è compreso nell'intervallo 0..1, estremi inclusi."}, "PERCENTRANK": {"a": "(matrice; x; [cifre_signific])", "d": "Restituisce il rango di un valore in un set di dati come percentuale del set di dati."}, "PERCENTRANK.EXC": {"a": "(matrice; x; [cifre_signific])", "d": "Restituisce il rango di un valore in un set di dati come percentuale del set di dati come percentuale (0..1, estremi esclusi) del set di dati."}, "PERCENTRANK.INC": {"a": "(matrice; x; [cifre_signific])", "d": "Restituisce il rango di un valore in un set di dati come percentuale del set di dati come percentuale (0..1, estremi inclusi) del set di dati."}, "PERMUT": {"a": "(num; classe)", "d": "Restituisce il numero delle permutazioni per un dato numero di oggetti che possono essere selezionati dagli oggetti totali."}, "PERMUTATIONA": {"a": "(num; classe)", "d": "Restituisce il numero delle permutazioni per un dato numero di oggetti (con ripetizioni) che possono essere selezionati dagli oggetti totali"}, "PHI": {"a": "(x)", "d": "Restituisce il valore della funzione densità per una distribuzione normale standard"}, "POISSON": {"a": "(x; media; cumulativo)", "d": "Calcola la distribuzione di probabilità di Poisson"}, "POISSON.DIST": {"a": "(x; media; cumulativo)", "d": "Calcola la distribuzione di probabilità di Poisson"}, "PROB": {"a": "(int_x; prob_int; limite_inf; [limite_sup])", "d": "Calcola la probabilità che dei valori in un intervallo siano compresi tra due limiti o pari al limite inferiore."}, "QUARTILE": {"a": "(matrice; quarto)", "d": "Restituisce il quartile di un set di dati."}, "QUARTILE.INC": {"a": "(matrice; quarto)", "d": "Restituisce il quartile di un set di dati, in base a valori di percentile compresi nell'intervallo 0..1, estremi inclusi."}, "QUARTILE.EXC": {"a": "(matrice; quarto)", "d": "Restituisce il quartile di un set di dati, in base a valori di percentile compresi nell'intervallo 0..1, estremi esclusi."}, "RANK": {"a": "(num; rif; [ordine])", "d": "Restituisce il rango di un numero in un elenco di numeri: la sua grandezza relativa agli altri valori nell'elenco."}, "RANK.AVG": {"a": "(num; rif; [ordine])", "d": "Restituisce il rango di un numero in un elenco di numeri: la sua grandezza relativa agli altri valori nell'elenco; se più valori hanno lo stesso rango, viene restituito il rango medio."}, "RANK.EQ": {"a": "(num; rif; [ordine])", "d": "Restituisce il rango di un numero in un elenco di numeri: la sua grandezza relativa agli altri valori nell'elenco; se più valori hanno lo stesso rango, viene restituito il rango massimo del set di valori."}, "RSQ": {"a": "(y_note; x_note)", "d": "Restituisce la radice quadrata del coefficiente di momento di correlazione di Pearson in corrispondenza delle coordinate date."}, "SKEW": {"a": "(num1; [num2]; ...)", "d": "Restituisce il grado di asimmetria di una distribuzione, ovvero una caratterizzazione del grado di asimmetria di una distribuzione attorno alla media."}, "SKEW.P": {"a": "(num1; [num2]; ...)", "d": "Restituisce il grado di asimmetria di una distribuzione in base a una popolazione, ovvero una caratterizzazione del grado di asimmetria di una distribuzione attorno alla media"}, "SLOPE": {"a": "(y_note; x_note)", "d": "Restituisce la pendenza della retta di regressione lineare fra le coordinate note."}, "SMALL": {"a": "(matrice; k)", "d": "Restituisce il k-esimo valore più piccolo di un set di dati. Ad esempio il quinto numero più piccolo."}, "STANDARDIZE": {"a": "(x; media; dev_standard)", "d": "Restituisce un valore normalizzato da una distribuzione caratterizzata da una media e da una deviazione standard."}, "STDEV": {"a": "(num1; [num2]; ...)", "d": "Restituisce una stima della deviazione standard sulla base di un campione. Ignora i valori logici e il testo nel campione."}, "STDEV.P": {"a": "(num1; [num2]; ...)", "d": "Calcola la deviazione standard sulla base dell'intera popolazione, passata come argomenti (ignora i valori logici e il testo)."}, "STDEV.S": {"a": "(num1; [num2]; ...)", "d": "Restituisce una stima della deviazione standard sulla base di un campione. Ignora i valori logici e il testo nel campione."}, "STDEVA": {"a": "(val1; [val2]; ...)", "d": "Restituisce una stima della deviazione standard sulla base di un campione, inclusi valori logici e testo. Il testo e il valore FALSO vengono valutati come 0, il valore VERO come 1."}, "STDEVP": {"a": "(num1; [num2]; ...)", "d": "Calcola la deviazione standard sulla base dell'intera popolazione, passata come argomenti (ignora i valori logici e il testo)."}, "STDEVPA": {"a": "(val1; [val2]; ...)", "d": "Calcola la deviazione standard sulla base dell'intera popolazione, inclusi valori logici e testo. Il testo e il valore FALSO vengono valutati come 0, il valore VERO come 1."}, "STEYX": {"a": "(y_note; x_note)", "d": "Restituisce l'errore standard del valore previsto per y per ciascun valore di x nella regressione."}, "TDIST": {"a": "(x; grad_libertà; code)", "d": "Restituisce la distribuzione t di Student."}, "TINV": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della distribuzione t di Student."}, "T.DIST": {"a": "(x; grad_libertà; code)", "d": "Restituisce la distribuzione t di Student a una coda sinistra."}, "T.DIST.2T": {"a": "(x; grad_libertà)", "d": "Restituisce la distribuzione t di Student a due code."}, "T.DIST.RT": {"a": "(x; grad_libertà)", "d": "Restituisce la distribuzione t di Student a una coda destra."}, "T.INV": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della distribuzione t di Student a una coda sinistra."}, "T.INV.2T": {"a": "(probabilità; grado_libertà)", "d": "Restituisce l'inversa della distribuzione t di Student a due code."}, "VAR": {"a": "(num1; [num2]; ...)", "d": "Stima la varianza sulla base di un campione. Ignora i valori logici e il testo nel campione."}, "VAR.P": {"a": "(num1; [num2]; ...)", "d": "Calcola la varianza sulla base dell'intera popolazione. Ignora i valori logici e il testo nella popolazione."}, "VAR.S": {"a": "(num1; [num2]; ...)", "d": "Stima la varianza sulla base di un campione. Ignora i valori logici e il testo nel campione."}, "VARA": {"a": "(val1; [val2]; ...)", "d": "Restituisce una stima della varianza sulla base di un campione, inclusi valori logici e testo. Il testo e il valore FALSO vengono valutati come 0, il valore VERO come 1."}, "VARP": {"a": "(num1; [num2]; ...)", "d": "Calcola la varianza sulla base dell'intera popolazione. Ignora i valori logici e il testo nella popolazione."}, "VARPA": {"a": "(val1; [val2]; ...)", "d": "Calcola la varianza sulla base dell'intera popolazione, inclusi valori logici e testo. Il testo e il valore FALSO vengono valutati come 0, il valore VERO come 1."}, "WEIBULL": {"a": "(x; alfa; beta; cumulativo)", "d": "Restituisce la distribuzione di Weibull."}, "WEIBULL.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Restituisce la distribuzione di Weibull."}, "Z.TEST": {"a": "(matrice; x; [sigma])", "d": "Restituisce il livello di significatività a una coda per un test z."}, "ZTEST": {"a": "(matrice; x; [sigma])", "d": "Restituisce il livello di significatività a una coda per un test z."}, "ACCRINT": {"a": "(emiss; primo_int; liquid; tasso_int; val_nom; num_rate; [base]; [metodo_calc])", "d": "Restituisce l'interesse maturato di un titolo per cui è pagato un interesse periodico."}, "ACCRINTM": {"a": "(emiss; liquid; tasso_int; val_nom; [base])", "d": "Restituisce l'interesse maturato per un titolo i cui interessi vengono pagati alla scadenza."}, "AMORDEGRC": {"a": "(costo; data_acquisto; primo_periodo; valore_residuo; periodo; tasso_int; [base])", "d": "Restituisce l'ammortamento lineare ripartito proporzionalmente di un bene per ogni periodo contabile."}, "AMORLINC": {"a": "(costo; data_acquisto; primo_periodo; valore_residuo; periodo; tasso_int; [base])", "d": "Restituisce l'ammortamento lineare ripartito proporzionalmente di un bene per ogni periodo contabile."}, "COUPDAYBS": {"a": "(liquid; scad; num_rate; [base])", "d": "Calcola il numero dei giorni che intercorrono dalla data di inizio del periodo della cedola alla data di liquidazione."}, "COUPDAYS": {"a": "(liquid; scad; num_rate; [base])", "d": "Calcola il numero dei giorni nel periodo della cedola che contiene la data di liquidazione"}, "COUPDAYSNC": {"a": "(liquid; scad; num_rate; [base])", "d": "Calcola il numero dei giorni che intercorrono dalla data di liquidazione alla data della nuova cedola."}, "COUPNCD": {"a": "(liquid; scad; num_rate; [base])", "d": "Restituisce la data della cedola successiva alla data di liquidazione."}, "COUPNUM": {"a": "(liquid; scad; num_rate; [base])", "d": "Calcola il numero di cedole valide tra la data di liquidazione e la data di scadenza."}, "COUPPCD": {"a": "(liquid; scad; num_rate; [base])", "d": "Restituisce la data della cedola precedente alla data di liquidazione."}, "CUMIPMT": {"a": "(tasso_int; periodi; val_attuale; iniz_per; fine_per; tipo)", "d": "Calcola l'interesse cumulativo pagato tra due periodi."}, "CUMPRINC": {"a": "(tasso_int; per; val_attuale; iniz_per; fine_per; tipo)", "d": "Calcola il capitale cumulativo pagato per estinguere un debito tra due periodi."}, "DB": {"a": "(costo; val_residuo; vita_utile; periodo; [mese])", "d": "Restituisce l'ammortamento di un bene per un periodo specificato utilizzando il metodo a quote fisse proporzionali ai valori residui."}, "DDB": {"a": "(costo; val_residuo; vita_utile; periodo; [fattore])", "d": "Restituisce l'ammortamento di un bene per un periodo specificato utilizzando il metodo a doppie quote proporzionali ai valori residui o un altro metodo specificato."}, "DISC": {"a": "(liquid; scad; prezzo; prezzo_rimb; [base])", "d": "Calcola il tasso di sconto di un titolo."}, "DOLLARDE": {"a": "(valuta_frazione; frazione)", "d": "Converte un prezzo espresso come frazione in un prezzo espresso come numero decimale."}, "DOLLARFR": {"a": "(valuta_decimale; frazione)", "d": "Converte un prezzo espresso come numero decimale in un prezzo espresso come frazione."}, "DURATION": {"a": "(liquid; scad; cedola; rend; num_rate; [base])", "d": "Restituisce la durata annuale per un titolo per cui è pagato un interesse periodico."}, "EFFECT": {"a": "(tasso_nominale; periodi)", "d": "Restituisce il tasso di interesse effettivo annuo."}, "FV": {"a": "(tasso_int; periodi; pagam; [val_attuale]; [tipo])", "d": "Restituisce il valore futuro di un investimento dati pagamenti periodici costanti e un tasso di interesse costante."}, "FVSCHEDULE": {"a": "(capitale; piano_invest)", "d": "Restituisce il valore futuro di un capitale iniziale dopo l'applicazione di una serie di tassi di interesse composto."}, "INTRATE": {"a": "(liquid; scad; invest; prezzo_rimb; [base])", "d": "Restituisce il tasso di interesse per un titolo interamente investito."}, "IPMT": {"a": "(tasso_int; periodo; periodi; val_attuale; [val_futuro]; [tipo])", "d": "Restituisce l'ammontare degli interessi relativi ad un investimento di una certa durata con pagamenti periodici costanti e un tasso di interesse costante."}, "IRR": {"a": "(val; [ipotesi])", "d": "Restituisce il tasso di rendimento interno per una serie di flussi di cassa."}, "ISPMT": {"a": "(tasso_int; periodo; periodi; val_attuale)", "d": "Restituisce il tasso di interesse del prestito a tasso fisso."}, "MDURATION": {"a": "(liquid; scad; cedola; rend; num_rate; [base])", "d": "Calcola la durata Macauley modificata per un titolo con valore nominale presunto di 100 euro."}, "MIRR": {"a": "(val; costo; ritorno)", "d": "Restituisce il tasso di rendimento interno per una serie di flussi di cassa periodici, considerando sia il costo di investimento sia gli interessi da reinvestimento della liquidità."}, "NOMINAL": {"a": "(tasso_effettivo; periodi)", "d": "Restituisce il tasso di interesse nominale annuo."}, "NPER": {"a": "(tasso_int; pagam; val_attuale; [val_futuro]; [tipo])", "d": "Restituisce il numero di periodi relativi ad un investimento con pagamenti periodici costanti e un tasso di interesse costante."}, "NPV": {"a": "(tasso_int; val1; [val2]; ...)", "d": "Restituisce il valore attuale netto di un investimento basato su una serie di uscite (valori negativi) e di entrate (valori positivi) future."}, "ODDFPRICE": {"a": "(liquid; scad; emiss; prima_ced; tasso_int; rend; prezzo_rimb; num_rate; [base])", "d": "Restituisce il prezzo di un titolo con valore nominale di 100 euro avente il primo periodo di durata irregolare."}, "ODDFYIELD": {"a": "(liquid; scad; emiss; prima_ced; tasso_int; prezzo; prezzo_rimb; num_rate; [base])", "d": "Restituisce il rendimento di un titolo avente il primo periodo di durata irregolare."}, "ODDLPRICE": {"a": "(liquid; scad; ultimo_int; tasso_int; rend; prezzo_rimb; num_rate; [base])", "d": "Restituisce il prezzo di un titolo con valore nominale di 100 euro avente l'ultimo periodo di durata irregolare."}, "ODDLYIELD": {"a": "(liquid; scad; ultimo_int; tasso_int; prezzo; prezzo_rimb; num_rate; [base])", "d": "Restituisce il rendimento di un titolo avente l'ultimo periodo di durata irregolare."}, "PMT": {"a": "(tasso_int; periodi; val_attuale; [val_futuro]; [tipo])", "d": "Calcola il pagamento per un prestito in base a pagamenti costanti e a un tasso di interesse costante."}, "PPMT": {"a": "(tasso_int; periodo; periodi; val_attuale; [val_futuro]; [tipo])", "d": "Restituisce il pagamento sul capitale di un investimento per un dato periodo con pagamenti periodici costanti e un tasso di interesse costante"}, "PRICE": {"a": "(liquid; scad; tasso_int; rend; prezzo_rimb; num_rate; [base])", "d": "Restituisce il prezzo di un titolo con valore nominale di 100 euro e interessi periodici."}, "PRICEDISC": {"a": "(liquid; scad; sconto; prezzo_rimb; [base])", "d": "Restituisce il prezzo di un titolo scontato con valore nominale di 100 euro."}, "PRICEMAT": {"a": "(liquid; scad; emiss; tasso_int; rend; [base])", "d": "Restituisce il prezzo di un titolo con valore nominale di 100 euro e i cui interessi vengono pagati alla scadenza."}, "PV": {"a": "(tasso_int; periodi; pagam; [val_futuro]; [tipo])", "d": "Restituisce il valore attuale di un investimento: l'ammontare totale del valore attuale di una serie di pagamenti futuri."}, "RATE": {"a": "(periodi; pagam; val_attuale; [val_futuro]; [tipo]; [ipotesi])", "d": "Restituisce il tasso di interesse per il periodo relativo a un prestito o a un investimento. Ad esempio, usare 6%/4 per pagamenti trimestrali al 6%"}, "RECEIVED": {"a": "(liquid; scad; invest; sconto; [base])", "d": "Calcola l'importo ricevuto alla scadenza di un titolo."}, "RRI": {"a": "(periodi; val_attuale; val_futuro)", "d": "Restituisce un tasso di interesse equivalente per la crescita di un investimento"}, "SLN": {"a": "(costo; val_residuo; vita_utile)", "d": "Restituisce l'ammortamento costante di un bene per un periodo."}, "SYD": {"a": "(costo; val_residuo; vita_utile; periodo)", "d": "Restituisce l'ammortamento americano di un bene per un determinato periodo."}, "TBILLEQ": {"a": "(liquid; scad; sconto)", "d": "Calcola il rendimento equivalente a un'obbligazione per un buono del tesoro."}, "TBILLPRICE": {"a": "(liquid; scad; sconto)", "d": "Calcola il prezzo di un buono del tesoro con valore nominale di 100 euro."}, "TBILLYIELD": {"a": "(liquid; scad; prezzo)", "d": "Calcola il rendimento di un buono del tesoro."}, "VDB": {"a": "(costo; val_residuo; vita_utile; inizio; fine; [fattore]; [nessuna_opzione])", "d": "Restituisce l'ammortamento di un bene per un periodo specificato, anche parziale, utilizzando il metodo a quote proporzionali ai valori residui o un altro metodo specificato."}, "XIRR": {"a": "(valori; date_pagam; [ipotesi])", "d": "Restituisce il tasso di rendimento interno per un impiego di flussi di cassa."}, "XNPV": {"a": "(tasso_int; valori; date_pagam)", "d": "Restituisce il valore attuale netto per un impiego di flussi di cassa."}, "YIELD": {"a": "(liquid; scad; tasso_int; prezzo; prezzo_rimb; num_rate; [base])", "d": "Calcola il rendimento di un titolo per cui è pagato un interesse periodico."}, "YIELDDISC": {"a": "(liquid; scad; prezzo; prezzo_rimb; [base])", "d": "Calcola il rendimento annuale per un titolo scontato, ad esempio un buono del tesoro."}, "YIELDMAT": {"a": "(liquid; scad; emiss; tasso_int; prezzo; [base])", "d": "Calcola il rendimento annuo per un titolo i cui interessi vengono pagati alla scadenza."}, "ABS": {"a": "(num)", "d": "Restituisce il valore assoluto di un numero, il numero privo di segno."}, "ACOS": {"a": "(num)", "d": "Restituisce l'arcocoseno di un numero, espresso in radianti da 0 a pi greco. L'arcocoseno è l'angolo il cui coseno è pari al numero."}, "ACOSH": {"a": "(num)", "d": "Restituisce l'inversa del coseno iperbolico di un numero."}, "ACOT": {"a": "(num)", "d": "Restituisce l'arcocotangente di un numero, espressa in radianti nell'intervallo tra 0 e pi greco."}, "ACOTH": {"a": "(num)", "d": "Restituisce la cotangente iperbolica inversa di un numero"}, "AGGREGATE": {"a": "(num_funzione; opzioni; rif1; ...)", "d": "Restituisce un'aggregazione in un elenco o un database"}, "ARABIC": {"a": "(num)", "d": "Converte un numero romano in arabo"}, "ASC": {"a": "(testo)", "d": "Nelle lingue che utilizzano set di caratteri a byte doppio (DBCS, Double-Byte Character Set), la funzione converte i caratteri latini a byte doppio (DB, Double-Byte) in caratteri a byte singolo (SB, Single-Byte)"}, "ASIN": {"a": "(num)", "d": "Restituisce l'arcoseno di un numero, espresso in radianti nell'intervallo tra -pi greco/2 e pi greco/2."}, "ASINH": {"a": "(num)", "d": "Restituisce l'inversa del seno iperbolico di un numero."}, "ATAN": {"a": "(num)", "d": "Restituisce l'arcotangente di un numero, espressa in radianti nell'intervallo tra -pi greco/2 e pi greco/2."}, "ATAN2": {"a": "(x; y)", "d": "Restituisce l'arcotangente in radianti dalle coordinate x e y specificate, nell'intervallo tra -pi greco e pi greco, escluso -pi greco."}, "ATANH": {"a": "(num)", "d": "Restituisce l'inversa della tangente iperbolica di un numero."}, "BASE": {"a": "(num; radice; [lungh_min])", "d": "Converte un numero in una rappresentazione testuale con la radice (base) specificata"}, "CEILING": {"a": "(num; peso)", "d": "Arrotonda un numero per eccesso al multiplo più vicino a peso."}, "CEILING.MATH": {"a": "(num; [peso]; [modalità])", "d": "Arrotonda un numero per eccesso all'intero più vicino o al multiplo più vicino a peso"}, "CEILING.PRECISE": {"a": "( x; [peso] )", "d": "Arrotonda un numero per eccesso all'intero più vicino o al multiplo più vicino a peso."}, "COMBIN": {"a": "(num; classe)", "d": "Calcola il numero delle combinazioni per un numero assegnato di oggetti."}, "COMBINA": {"a": "(num; classe)", "d": "Restituisce il numero delle combinazioni con ripetizioni per un numero specificato di elementi"}, "COS": {"a": "(num)", "d": "Restituisce il coseno di un numero."}, "COSH": {"a": "(num)", "d": "Restituisce il coseno iperbolico di un numero."}, "COT": {"a": "(num)", "d": "Restituisce la cotangente di un angolo"}, "COTH": {"a": "(num)", "d": "Restituisce la cotangente iperbolica di un numero"}, "CSC": {"a": "(num)", "d": "Restituisce la cosecante di un angolo"}, "CSCH": {"a": "(num)", "d": "Restituisce la cosecante iperbolica di un angolo"}, "DECIMALE": {"a": "( num; radice )", "d": "Converte la rappresentazione di un numero in formato testo di una determinata base in un numero decimale"}, "DEGREES": {"a": "(angolo)", "d": "Converte i radianti in gradi."}, "ECMA.CEILING": {"a": "( x; peso )", "d": "Arrotonda un numero per eccesso al multiplo più vicino a peso"}, "EVEN": {"a": "(num)", "d": "Arrotonda il valore assoluto di un numero per eccesso all'intero pari più vicino. I numeri negativi sono arrotondati per difetto."}, "EXP": {"a": "(num)", "d": "Restituisce il numero e elevato alla potenza di un dato numero."}, "FACT": {"a": "(num)", "d": "Restituisce il fattoriale di un numero, uguale a 1*2*3*...* numero."}, "FACTDOUBLE": {"a": "(num)", "d": "Restituisce il fattoriale doppio di un numero."}, "FLOOR": {"a": "(num; peso)", "d": "Arrotonda un numero per difetto al multiplo più vicino a peso."}, "FLOOR.PRECISE": {"a": "( x; peso )", "d": "Arrotonda un numero per difetto all'intero più vicino o al multiplo più vicino a peso."}, "FLOOR.MATH": {"a": "(num; [peso]; [modalità])", "d": "Arrotonda un numero per difetto all'intero più vicino o al multiplo più vicino a peso"}, "GCD": {"a": "(num1; [num2]; ...)", "d": "Restituisce il massimo comun divisore."}, "INT": {"a": "(num)", "d": "Arrotonda un numero per difetto all'intero più vicino."}, "ISO.CEILING": {"a": "(num; [peso])", "d": "Restituisce un numero arrotondato per eccesso all'intero più vicino o al multiplo più vicino a peso. Indipendentemente dal segno di num, il numero viene arrotondato per eccesso. Se tuttavia num o peso è zero, verrà restituito il valore zero."}, "LCM": {"a": "(num1; [num2]; ...)", "d": "Restituisce il minimo comune multiplo."}, "LN": {"a": "(num)", "d": "Restituisce il logaritmo naturale di un numero."}, "LOG": {"a": "(num; [base])", "d": "Restituisce il logaritmo di un numero nella base specificata."}, "LOG10": {"a": "(num)", "d": "Restituisce il logaritmo in base 10 di un numero."}, "MDETERM": {"a": "(matrice)", "d": "Restituisce il determinante di una matrice."}, "MINVERSE": {"a": "(matrice)", "d": "Restituisce l'inversa di una matrice."}, "MMULT": {"a": "(matrice1; matrice2)", "d": "Restituisce il prodotto di due matrici, una matrice avente un numero di righe pari a Matrice1 e un numero di colonne pari a Matrice2."}, "MOD": {"a": "(dividendo; divisore)", "d": "Restituisce il resto della divisione di due numeri."}, "MROUND": {"a": "(num; multiplo)", "d": "Restituisce un numero arrotondato al multiplo desiderato."}, "MULTINOMIAL": {"a": "(num1; [num2]; ...)", "d": "Restituisce il multinomiale di un insieme di numeri."}, "MUNIT": {"a": "(dimensione)", "d": "Restituisce la matrice unitaria per la dimensione specificata"}, "ODD": {"a": "(num)", "d": "Arrotonda un numero positivo per eccesso al numero intero più vicino e uno negativo per difetto al numero dispari più vicino."}, "PI": {"a": "()", "d": "Restituisce il valore di pi greco 3,14159265358979, approssimato a 15 cifre."}, "POWER": {"a": "(num; potenza)", "d": "Restituisce il risultato di un numero elevato a potenza."}, "PRODUCT": {"a": "(num1; [num2]; ...)", "d": "Moltiplica tutti i numeri passati come argomenti e restituisce il prodotto."}, "QUOTIENT": {"a": "(numeratore; denominatore)", "d": "Restituisce il quoziente di una divisione."}, "RADIANS": {"a": "(angolo)", "d": "Converte gradi in radianti."}, "RAND": {"a": "()", "d": "Restituisce un numero casuale uniformemente distribuito, ossia cambia se viene ricalcolato, e maggiore o uguale a 0 e minore di 1"}, "RANDARRAY": {"a": "([righe]; [colonne]; [min]; [max]; [intero])", "d": "Restituisce una matrice di numeri casuali"}, "RANDBETWEEN": {"a": "(minore; maggiore)", "d": "Restituisce un numero casuale compreso tra i numeri specificati."}, "ROMAN": {"a": "(num; [forma])", "d": "Converte un numero arabo in un numero romano in forma di testo."}, "ROUND": {"a": "(num; num_cifre)", "d": "Arrotonda un numero ad un numero specificato di cifre."}, "ROUNDDOWN": {"a": "(num; num_cifre)", "d": "Arrotonda il valore assoluto di un numero per difetto."}, "ROUNDUP": {"a": "(num; num_cifre)", "d": "Arrotonda il valore assoluto di un numero per eccesso."}, "SEC": {"a": "(num)", "d": "Restituisce la secante di un angolo"}, "SECH": {"a": "(num)", "d": "Restituisce la secante iperbolica di un angolo"}, "SERIESSUM": {"a": "(x; n; m; coefficienti)", "d": "Restituisce la somma di una serie di potenze basata sulla formula."}, "SIGN": {"a": "(num)", "d": "Restituisce il segno di un numero: 1 se il numero è positivo, zero se il numero è zero o -1 se il numero è negativo."}, "SIN": {"a": "(radianti)", "d": "Restituisce il seno di un angolo."}, "SINH": {"a": "(num)", "d": "Restituisce il seno iperbolico di un numero."}, "SQRT": {"a": "(num)", "d": "Restituisce la radice quadrata di un numero."}, "SQRTPI": {"a": "(num)", "d": "Restituisce la radice quadrata di (num *pi greco)."}, "SUBTOTAL": {"a": "(num_funzione; rif1; ...)", "d": "Restituisce un subtotale in un elenco o un database."}, "SUM": {"a": "(num1; [num2]; ...)", "d": "Somma i numeri presenti in un intervallo di celle"}, "SUMIF": {"a": "(intervallo; criterio; [int_somma])", "d": "Somma le celle specificate secondo una condizione o criterio assegnato."}, "SUMIFS": {"a": "(int_somma; intervallo_criteri; criteri; ...)", "d": "Somma le celle specificate da un determinato insieme di condizioni o criteri."}, "SUMPRODUCT": {"a": "(matrice1; [matrice2]; [matrice3]; ...)", "d": "Moltiplica elementi numerici corrispondenti in matrici o intervalli di dati e restituisce la somma dei prodotti."}, "SUMSQ": {"a": "(num1; [num2]; ...)", "d": "Restituisce la somma dei quadrati degli argomenti. Gli argomenti possono essere numeri, nomi, matrici o riferimenti a celle contenenti numeri."}, "SUMX2MY2": {"a": "(matrice_x; matrice_y)", "d": "Calcola la differenza tra i quadrati di numeri corrispondenti di due intervalli o matrici e restituisce la somma delle differenze."}, "SUMX2PY2": {"a": "(matrice_x; matrice_y)", "d": "Calcola la somma dei quadrati di numeri corrispondenti di due intervalli o matrici e restituisce la somma delle somme."}, "SUMXMY2": {"a": "(matrice_x; matrice_y)", "d": "Calcola la differenza tra valori corrispondenti di due intervalli o matrici e restituisce la somma dei quadrati delle differenze."}, "TAN": {"a": "(radianti)", "d": "Restituisce la tangente di un numero."}, "TANH": {"a": "(num)", "d": "Restituisce la tangente iperbolica di un numero."}, "TRUNC": {"a": "(num; [num_cifre])", "d": "Elimina la parte decimale di un numero."}, "ADDRESS": {"a": "(riga; col; [ass]; [a1]; [foglio])", "d": "Dati il numero di riga e di colonna, crea un riferimento di cella in formato testo."}, "CHOOSE": {"a": "(indice; val1; [val2]; ...)", "d": "Seleziona un valore o un'azione da eseguire da un elenco di valori in base a un indice"}, "COLUMN": {"a": "([rif])", "d": "Restituisce il numero di colonna di un riferimento."}, "COLUMNS": {"a": "(matrice)", "d": "Restituisce il numero di colonne in una matrice o riferimento."}, "HLOOKUP": {"a": "(valore; matrice_tabella; indice; [intervallo])", "d": "Cerca un valore nella prima riga di una tabella o di una matrice e restituisce il valore nella stessa colonna da una riga specificata."}, "HYPERLINK": {"a": "(posizione_collegamento; [nome_collegamento])", "d": "Crea un collegamento ipertestuale che apre un documento memorizzato sul disco rigido, su un server di rete o su Internet."}, "INDEX": {"a": "(matrice; riga; [col]!rif; riga; [col]; [area])", "d": "Restituisce un valore o un riferimento della cella all'intersezione di una particolare riga e colonna in un dato intervallo."}, "INDIRECT": {"a": "(rif; [a1])", "d": "Restituisce un riferimento indicato da una stringa di testo."}, "LOOKUP": {"a": "(valore; vettore; [risultato]!valore; matrice)", "d": "Ricerca un valore in un intervallo di una riga o di una colonna o da una matrice. Fornito per compatibilità con versioni precedenti"}, "MATCH": {"a": "(valore; matrice; [corrisp])", "d": "Restituisce la posizione relativa di un elemento di matrice che corrisponde a un valore specificato in un ordine specificato."}, "OFFSET": {"a": "(rif; righe; colonne; [altezza]; [largh])", "d": "Restituisce un riferimento a un intervallo costituito da un numero specificato di righe e colonne da un riferimento dato."}, "ROW": {"a": "([rif])", "d": "Restituisce il numero di riga corrispondente a rif"}, "ROWS": {"a": "(matrice)", "d": "Restituisce il numero di righe in un riferimento o in una matrice."}, "TRANSPOSE": {"a": "(matrice)", "d": "Converte un intervallo verticale in un un intervallo orizzontale o viceversa."}, "UNIQUE": {"a": "(matrice; [by_col]; [exactly_once])", "d": " Restituisce i valori univoci di un intervallo o di una matrice."}, "VLOOKUP": {"a": "(valore; matrice_tabella; indice; [intervallo])", "d": "Cerca un valore nella prima colonna sinistra di una tabella e restituisce un valore nella stessa riga da una colonna specificata. La tabella viene ordinata in ordine crescente per impostazione predefinita."}, "XLOOKUP": {"a": "(valore; matrice_ricerca; matrice_restituita; [se_non_trovato]; [modalità_confronto]; [modalità_ricerca])", "d": "Cerca una corrispondenza in un intervallo o una matrice e restituisce l'elemento corrispondente di una seconda matrice o intervallo. Per impostazione predefinita, viene utilizzata una corrispondenza esatta"}, "CELL": {"a": "(info_type; [reference])", "d": "Restituisce informazioni sulla formattazione, la posizione o il contenuto di una cella"}, "ERROR.TYPE": {"a": "(errore)", "d": "Restituisce un numero corrispondente a uno dei valori di errore."}, "ISBLANK": {"a": "(val)", "d": "Restituisce VERO se il valore è una cella vuota."}, "ISERR": {"a": "(val)", "d": "Controlla se un valore è un errore diverso da #N/A e restituisce VERO o FALSO."}, "ISERROR": {"a": "(val)", "d": "Controlla se un valore è un errore e restituisce VERO o FALSO."}, "ISEVEN": {"a": "(num)", "d": "Restituisce VERO se il numero è pari."}, "ISFORMULA": {"a": "(riferimento)", "d": "Controlla se il riferimento specificato è a una cella contenente una formula e restituisce VERO o FALSO"}, "ISLOGICAL": {"a": "(val)", "d": "Restituisce VERO se Valore è un valore logico, VERO o FALSO."}, "ISNA": {"a": "(val)", "d": "Controlla se un valore è #N/D e restituisce VERO o FALSO."}, "ISNONTEXT": {"a": "(val)", "d": "Restituisce VERO se il valore non è del testo (le celle vuote non sono testo)."}, "ISNUMBER": {"a": "(val)", "d": "Restituisce VERO se il valore è un numero."}, "ISODD": {"a": "(num)", "d": "Restituisce VERO se il numero è dispari."}, "ISREF": {"a": "(val)", "d": "Controlla se il valore è un riferimento e restituisce VERO o FALSO."}, "ISTEXT": {"a": "(val)", "d": "Restituisce VERO se il valore è un testo."}, "N": {"a": "(val)", "d": "Converte stringhe di valori in numeri, date in numeri seriali,  VERO in 1, ogni altro valore in 0 (zero)."}, "NA": {"a": "()", "d": "Restituisce il valore di errore #N/D (valore non disponibile)."}, "SHEET": {"a": "([valore])", "d": "Restituisce il numero del foglio del riferimento"}, "SHEETS": {"a": "([riferimento])", "d": "Restituisce il numero di fogli in un riferimento"}, "TYPE": {"a": "(val)", "d": "Restituisce un numero intero che indica il tipo di dati di un valore: numero = 1; testo = 2; valore logico = 4; valore di errore = 16; matrice = 64; dati composti = 128"}, "AND": {"a": "(logico1; [logico2]; ...)", "d": "Restituisce VERO se tutti gli argomenti hanno valore VERO."}, "FALSE": {"a": "()", "d": "Restituisce il valore logico FALSO."}, "IF": {"a": "(test; [se_vero]; [se_falso])", "d": "Restituisce un valore se una condizione specificata dà come risultato VERO e un altro valore se dà come risultato FALSO."}, "IFNA": {"a": "(valore; valore_se_nd)", "d": "Restituisce il valore specificato se l'espressione restituisce #N/D, in caso contrario restituisce il risultato dell'espressione"}, "IFERROR": {"a": "(valore; valore_se_errore)", "d": "Restituisce valore_se_errore se l'espressione genera un errore, in caso contrario restituisce il valore dell'espressione stessa."}, "NOT": {"a": "(logico)", "d": "Inverte il valore logico dell'argomento: restituisce FALSO per un argomento VERO e VERO per un argomento FALSO."}, "SWITCH": {"a": "(espressione; valore1; risultato1; [predefinito_o_valore2]; [risultato2]; ...)", "d": "Valuta un'espressione rispetto a un elenco di valori e restituisce il risultato associato al primo valore corrispondente. Se non ci sono corrispondenze, viene restituito un valore predefinito facoltativo"}, "OR": {"a": "(logico1; [logico2]; ...)", "d": "Restituisce VERO se un argomento qualsiasi è VERO, FALSO se tutti gli argomenti sono FALSO."}, "TRUE": {"a": "()", "d": "Restituisce il valore logico VERO."}, "XOR": {"a": "(logico1; [logico2]; ...)", "d": "Restituisce un 'OR esclusivo' logico di tutti gli argomenti"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Restituisce il testo che si trova prima dei caratteri di delimitazione."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Restituisce il testo che si trova dopo i caratteri di delimitazione."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Divide il testo in righe o colonne tramite i delimitatori."}, "WRAPROWS": {"a": "(vettore, wrap_count, [pad_with])", "d": "Esegue il wrapping di un vettore di riga o colonna dopo un numero specificato di valori."}, "VSTACK": {"a": "(matrice1, [matrice2], ...)", "d": "Impila verticalmente le matrici in un'unica matrice."}, "HSTACK": {"a": "(matrice1, [matrice2], ...)", "d": "Impila orizzontalmente le matrici in un'unica matrice."}, "CHOOSEROWS": {"a": "(matrice, row_num1, [row_num2], ...)", "d": "Restituisce righe da una matrice o un riferimento."}, "CHOOSECOLS": {"a": "(matrice, col_num1, [col_num2], ...)", "d": "Restituisce colonne specificate da una matrice o un riferimento."}, "TOCOL": {"a": "(matrice, [ignora], [scan_by_column])", "d": "Restituisce la matrice come una colonna. "}, "TOROW": {"a": "(matrice, [ignora], [scan_by_column])", "d": "Restituisce la matrice come una riga."}, "WRAPCOLS": {"a": "(vettore, wrap_count, [pad_with])", "d": "Esegue il wrapping di un vettore di riga o colonna dopo un numero specificato di valori."}, "TAKE": {"a": "(matrice, righe, [colonne])", "d": "Restituisce righe o colonne dall'inizio o dalla fine della matrice."}, "DROP": {"a": "(matrice, righe, [colonne])", "d": "Elimina righe o colonne dall'inizio o dalla fine della matrice."}}
{"DATE": "DATUM", "DATEDIF": "DATEDIF", "DATEVALUE": "DATWERT", "DAY": "TAG", "DAYS": "TAGE", "DAYS360": "TAGE360", "EDATE": "EDATUM", "EOMONTH": "MONATSENDE", "HOUR": "STUNDE", "ISOWEEKNUM": "ISOKALENDERWOCHE", "MINUTE": "MINUTE", "MONTH": "MONAT", "NETWORKDAYS": "NETTOARBEITSTAGE", "NETWORKDAYS.INTL": "NETTOARBEITSTAGE.INTL", "NOW": "JETZT", "SECOND": "SEKUNDE", "TIME": "ZEIT", "TIMEVALUE": "ZEITWERT", "TODAY": "HEUTE", "WEEKDAY": "WOCHENTAG", "WEEKNUM": "KALENDERWOCHE", "WORKDAY": "ARBEITSTAG", "WORKDAY.INTL": "ARBEITSTAG.INTL", "YEAR": "JAHR", "YEARFRAC": "BRTEILJAHRE", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BININDEZ", "BIN2HEX": "BININHEX", "BIN2OCT": "BININOKT", "BITAND": "BITUND", "BITLSHIFT": "BITLVERSCHIEB", "BITOR": "BITODER", "BITRSHIFT": "BITRVERSCHIEB", "BITXOR": "BITXODER", "COMPLEX": "KOMPLEXE", "CONVERT": "UMWANDELN", "DEC2BIN": "DEZINBIN", "DEC2HEX": "DEZINHEX", "DEC2OCT": "DEZINOKT", "DELTA": "DELTA", "ERF": "GAUSSFEHLER", "ERF.PRECISE": "GAUSSF.GENAU", "ERFC": "GAUSSFKOMPL", "ERFC.PRECISE": "GAUSSFKOMPL.GENAU", "GESTEP": "GGANZZAHL", "HEX2BIN": "HEXINBIN", "HEX2DEC": "HEXINDEZ", "HEX2OCT": "HEXINOKT", "IMABS": "IMABS", "IMAGINARY": "IMAGINÄRTEIL", "IMARGUMENT": "IMARGUMENT", "IMCONJUGATE": "IMKONJUGIERTE", "IMCOS": "IMCOS", "IMCOSH": "IMCOSHYP", "IMCOT": "IMCOT", "IMCSC": "IMCOSEC", "IMCSCH": "IMCOSECHYP", "IMDIV": "IMDIV", "IMEXP": "IMEXP", "IMLN": "IMLN", "IMLOG10": "IMLOG10", "IMLOG2": "IMLOG2", "IMPOWER": "IMAPOTENZ", "IMPRODUCT": "IMPRODUKT", "IMREAL": "IMREALTEIL", "IMSEC": "IMSEC", "IMSECH": "IMSECHYP", "IMSIN": "IMSIN", "IMSINH": "IMSINHYP", "IMSQRT": "IMWURZEL", "IMSUB": "IMSUB", "IMSUM": "IMSUMME", "IMTAN": "IMTAN", "OCT2BIN": "OKTINBIN", "OCT2DEC": "OKTINDEZ", "OCT2HEX": "OKTINHEX", "DAVERAGE": "DBMITTELWERT", "DCOUNT": "DBANZAHL", "DCOUNTA": "DBANZAHL2", "DGET": "DBAUSZUG", "DMAX": "DBMAX", "DMIN": "DBMIN", "DPRODUCT": "DBPRODUKT", "DSTDEV": "DBSTDABW", "DSTDEVP": "DBSTDABWN", "DSUM": "DBSUMME", "DVAR": "DBVARIANZ", "DVARP": "DBVARIANZEN", "CHAR": "ZEICHEN", "CLEAN": "SÄUBERN", "CODE": "CODE", "CONCATENATE": "VERKETTEN", "CONCAT": "TEXTKETTE", "DOLLAR": "DM", "EXACT": "IDENTISCH", "FIND": "FINDEN", "FINDB": "FINDENB", "FIXED": "FEST", "LEFT": "LINKS", "LEFTB": "LINKSB", "LEN": "LÄNGE", "LENB": "LENB", "LOWER": "KLEIN", "MID": "TEIL", "MIDB": "TEILB", "NUMBERVALUE": "ZAHLENWERT", "PROPER": "GROSS2", "REPLACE": "ERSETZEN", "REPLACEB": "ERSETZENB", "REPT": "WIEDERHOLEN", "RIGHT": "RECHTS", "RIGHTB": "RECHTSB", "SEARCH": "SUCHEN", "SEARCHB": "SUCHENB", "SUBSTITUTE": "WECHSELN", "T": "T", "T.TEST": "T.TEST", "TEXT": "TEXT", "TEXTJOIN": "TEXTVERKETTEN", "TREND": "TREND", "TRIM": "GLÄTTEN", "TRIMMEAN": "GESTUTZTMITTEL", "TTEST": "TTEST", "UNICHAR": "UNIZEICHEN", "UNICODE": "UNICODE", "UPPER": "GROSS", "VALUE": "WERT", "AVEDEV": "MITTELABW", "AVERAGE": "MITTELWERT", "AVERAGEA": "MITTELWERTA", "AVERAGEIF": "MITTELWERTWENN", "AVERAGEIFS": "MITTELWERTWENNS", "BETADIST": "BETAVERT", "BETAINV": "BETAINV", "BETA.DIST": "BETA.VERT", "BETA.INV": "BETA.INV", "BINOMDIST": "BINOMVERT", "BINOM.DIST": "BINOM.VERT", "BINOM.DIST.RANGE": "BINOM.VERT.BEREICH", "BINOM.INV": "BINOM.INV", "CHIDIST": "CHIVERT", "CHIINV": "CHIINV", "CHITEST": "CHITEST", "CHISQ.DIST": "CHIQU.VERT", "CHISQ.DIST.RT": "CHIQU.VERT.RE", "CHISQ.INV": "CHIQU.INV", "CHISQ.INV.RT": "CHIQU.INV.RE", "CHISQ.TEST": "CHIQU.TEST", "CONFIDENCE": "KONFIDENZ", "CONFIDENCE.NORM": "KONFIDENZ.NORM", "CONFIDENCE.T": "KONFIDENZ.T", "CORREL": "KORREL", "COUNT": "ANZAHL", "COUNTA": "ANZAHL2", "COUNTBLANK": "ANZAHLLEEREZELLEN", "COUNTIF": "ZÄHLENWENN", "COUNTIFS": "ZÄHLENWENNS", "COVAR": "KOVAR", "COVARIANCE.P": "KOVARIANZ.P", "COVARIANCE.S": "KOVARIANZ.S", "CRITBINOM": "KRITBINOM", "DEVSQ": "SUMQUADABW", "EXPON.DIST": "EXPON.VERT", "EXPONDIST": "EXPONVERT", "FDIST": "FVERT", "FINV": "FINV", "FTEST": "FTEST", "F.DIST": "F.VERT", "F.DIST.RT": "F.VERT.RE", "F.INV": "F.INV", "F.INV.RT": "F.INV.RE", "F.TEST": "F.TEST", "FISHER": "FISHER", "FISHERINV": "FISHERINV", "FORECAST": "SCHÄTZER", "FORECAST.ETS": "PROGNOSE.ETS", "FORECAST.ETS.CONFINT": "PROGNOSE.ETS.KONFINT", "FORECAST.ETS.SEASONALITY": "PROGNOSE.ETS.SAISONALITÄT", "FORECAST.ETS.STAT": "PROGNOSE.ETS.STAT", "FORECAST.LINEAR": "PROGNOSE.LINEAR", "FREQUENCY": "HÄUFIGKEIT", "GAMMA": "GAMMA", "GAMMADIST": "GAMMAVERT", "GAMMA.DIST": "GAMMA.VERT", "GAMMAINV": "GAMMAINV", "GAMMA.INV": "GAMMA.INV", "GAMMALN": "GAMMALN", "GAMMALN.PRECISE": "GAMMALN.GENAU", "GAUSS": "GAUSS", "GEOMEAN": "GEOMITTEL", "GROWTH": "VARIATION", "HARMEAN": "HARMITTEL", "HYPGEOM.DIST": "HYPGEOM.VERT", "HYPGEOMDIST": "HYPGEOMVERT", "INTERCEPT": "ACHSENABSCHNITT", "KURT": "KURT", "LARGE": "KGRÖSSTE", "LINEST": "RGP", "LOGEST": "RKP", "LOGINV": "LOGINV", "LOGNORM.DIST": "LOGNORM.VERT", "LOGNORM.INV": "LOGNORM.INV", "LOGNORMDIST": "LOGNORMVERT", "MAX": "MAX", "MAXA": "MAXA", "MAXIFS": "MAXWENNS", "MEDIAN": "MEDIAN", "MIN": "MIN", "MINA": "MINA", "MINIFS": "MINWENNS", "MODE": "MODALWERT", "MODE.MULT": "MODUS.VIELF", "MODE.SNGL": "MODUS.EINF", "NEGBINOM.DIST": "NEGBINOM.VERT", "NEGBINOMDIST": "NEGBINOMVERT", "NORM.DIST": "NORM.VERT", "NORM.INV": "NORM.INV", "NORM.S.DIST": "NORM.S.VERT", "NORM.S.INV": "NORM.S.INV", "NORMDIST": "NORMVERT", "NORMINV": "NORMINV", "NORMSDIST": "STANDNORMVERT", "NORMSINV": "STANDNORMINV", "PEARSON": "PEARSON", "PERCENTILE": "QUANTIL", "PERCENTILE.EXC": "QUANTIL.EXKL", "PERCENTILE.INC": "QUANTIL.INKL", "PERCENTRANK": "QUANTILSRANG", "PERCENTRANK.EXC": "QUANTILSRANG.EXKL", "PERCENTRANK.INC": "QUANTILSRANG.INKL", "PERMUT": "VARIATIONEN", "PERMUTATIONA": "VARIATIONEN2", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "POISSON.VERT", "PROB": "WAHRSCHBEREICH", "QUARTILE": "QUARTILE", "QUARTILE.INC": "QUARTILE.INKL", "QUARTILE.EXC": "QUARTILE.EXKL", "RANK.AVG": "RANG.MITTELW", "RANK.EQ": "RANG.GLEICH", "RANK": "RANG", "RSQ": "BESTIMMTHEITSMASS", "SKEW": "SCHIEFE", "SKEW.P": "SCHIEFE.P", "SLOPE": "STEIGUNG", "SMALL": "KKLEINSTE", "STANDARDIZE": "STANDARDISIERUNG", "STDEV": "STABW", "STDEV.P": "STABW.N", "STDEV.S": "STABW.S", "STDEVA": "STABWA", "STDEVP": "STABWN", "STDEVPA": "STABWNA", "STEYX": "STFEHLERYX", "TDIST": "TVERT", "TINV": "TINV", "T.DIST": "T.VERT", "T.DIST.2T": "T.VERT.2S", "T.DIST.RT": "T.VERT.RE", "T.INV": "T.INV", "T.INV.2T": "T.INV.2S", "VAR": "VARIANZ", "VAR.P": "VAR.P", "VAR.S": "VAR.S", "VARA": "VARIANZA", "VARP": "VARIANZEN", "VARPA": "VARIANZENA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "WEIBULL.VERT", "Z.TEST": "G.TEST", "ZTEST": "GTEST", "ACCRINT": "AUFGELZINS", "ACCRINTM": "AUFGELZINSF", "AMORDEGRC": "AMORDEGRK", "AMORLINC": "AMORLINEARK", "COUPDAYBS": "ZINSTERMTAGVA", "COUPDAYS": "ZINSTERMTAGE", "COUPDAYSNC": "ZINSTERMTAGNZ", "COUPNCD": "ZINSTERMNZ", "COUPNUM": "ZINSTERMZAHL", "COUPPCD": "ZINSTERMVZ", "CUMIPMT": "KUMZINSZ", "CUMPRINC": "KUMKAPITAL", "DB": "GDA2", "DDB": "GDA", "DISC": "DISAGIO", "DOLLARDE": "NOTIERUNGDEZ", "DOLLARFR": "NOTIERUNGBRU", "DURATION": "DURATIONT", "EFFECT": "EFFEKTIV", "FV": "ZW", "FVSCHEDULE": "ZW2", "INTRATE": "ZINSSATZ", "IPMT": "ZINSZ", "IRR": "IKV", "ISPMT": "ISPMT", "MDURATION": "MDURATION", "MIRR": "QIKV", "NOMINAL": "NOMINAL", "NPER": "ZZR", "NPV": "NBW", "ODDFPRICE": "UNREGER.KURS", "ODDFYIELD": "UNREGER.REND", "ODDLPRICE": "UNREGLE.KURS", "ODDLYIELD": "UNREGLE.REND", "PDURATION": "PDURATION", "PMT": "RMZ", "PPMT": "KAPZ", "PRICE": "KURS", "PRICEDISC": "KURSDISAGIO", "PRICEMAT": "KURSFÄLLIG", "PV": "BW", "RATE": "ZINS", "RECEIVED": "AUSZAHLUNG", "RRI": "ZSATZINVEST", "SLN": "LIA", "SYD": "DIA", "TBILLEQ": "TBILLÄQUIV", "TBILLPRICE": "TBILLKURS", "TBILLYIELD": "TBILLRENDITE", "VDB": "VDB", "XIRR": "XINTZINSFUSS", "XNPV": "XKAPITALWERT", "YIELD": "RENDITE", "YIELDDISC": "RENDITEDIS", "YIELDMAT": "RENDITEFÄLL", "ABS": "ABS", "ACOS": "ARCCOS", "ACOSH": "ARCCOSHYP", "ACOT": "ARCCOT", "ACOTH": "ARCCOTHYP", "AGGREGATE": "AGGREGAT", "ARABIC": "ARABISCH", "ASC": "ASC", "ASIN": "ARCSIN", "ASINH": "ARCSINHYP", "ATAN": "ARCTAN", "ATAN2": "ARCTAN2", "ATANH": "ARCTANHYP", "BASE": "BASIS", "CEILING": "OBERGRENZE", "CEILING.MATH": "OBERGRENZE.MATHEMATIK", "CEILING.PRECISE": "OBERGRENZE.GENAU", "COMBIN": "KOMBINATIONEN", "COMBINA": "KOMBINATIONEN2", "COS": "COS", "COSH": "COSHYP", "COT": "COT", "COTH": "COTHYP", "CSC": "COSEC", "CSCH": "COSECHYP", "DECIMAL": "DEZIMAL", "DEGREES": "GRAD", "ECMA.CEILING": "ECMA.OBERGRENZE", "EVEN": "GERADE", "EXP": "EXP", "FACT": "FAKULTÄT", "FACTDOUBLE": "ZWEIFAKULTÄT", "FLOOR": "UNTERGRENZE", "FLOOR.PRECISE": "UNTERGRENZE.GENAU", "FLOOR.MATH": "UNTERGRENZE.MATHEMATIK", "GCD": "GGT", "INT": "GANZZAHL", "ISO.CEILING": "ISO.OBERGRENZE", "LCM": "KGV", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MDET", "MINVERSE": "MINV", "MMULT": "MMULT", "MOD": "REST", "MROUND": "VRUNDEN", "MULTINOMIAL": "POLYNOMIAL", "MUNIT": "MEINHEIT", "ODD": "UNGERADE", "PI": "PI", "POWER": "POTENZ", "PRODUCT": "PRODUKT", "QUOTIENT": "QUOTIENT", "RADIANS": "BOGENMASS", "RAND": "ZUFALLSZAHL", "RANDARRAY": "ZUFALLSMATRIX", "RANDBETWEEN": "ZUFALLSBEREICH", "ROMAN": "RÖMISCH", "ROUND": "RUNDEN", "ROUNDDOWN": "ABRUNDEN", "ROUNDUP": "AUFRUNDEN", "SEC": "SEC", "SECH": "SECHYP", "SERIESSUM": "POTENZREIHE", "SIGN": "VORZEICHEN", "SIN": "SIN", "SINH": "SINHYP", "SQRT": "WURZEL", "SQRTPI": "WURZELPI", "SUBTOTAL": "TEILERGEBNIS", "SUM": "SUMME", "SUMIF": "SUMMEWENN", "SUMIFS": "SUMMEWENNS", "SUMPRODUCT": "SUMMENPRODUKT", "SUMSQ": "QUADRATESUMME", "SUMX2MY2": "SUMMEX2MY2", "SUMX2PY2": "SUMMEX2PY2", "SUMXMY2": "SUMMEXMY2", "TAN": "TAN", "TANH": "TANHYP", "TRUNC": "KÜRZEN", "ADDRESS": "ADRESSE", "CHOOSE": "WAHL", "COLUMN": "SPALTE", "COLUMNS": "SPALTEN", "FORMULATEXT": "FORMELTEXT", "HLOOKUP": "WVERWEIS", "HYPERLINK": "HYPERLINK", "INDEX": "INDEX", "INDIRECT": "INDIREKT", "LOOKUP": "VERWEIS", "MATCH": "VERGLEICH", "OFFSET": "BEREICH.VERSCHIEBEN", "ROW": "ZEILE", "ROWS": "ZEILEN", "TRANSPOSE": "MTRANS", "UNIQUE": "EINDEUTIG", "VLOOKUP": "SVERWEIS", "XLOOKUP": "XVERWEIS", "CELL": "ZELLE", "ERROR.TYPE": "FEHLER.TYP", "ISBLANK": "ISTLEER", "ISERR": "ISTFEHL", "ISERROR": "ISTFEHLER", "ISEVEN": "ISTGERADE", "ISFORMULA": "ISTFORMEL", "ISLOGICAL": "ISTLOG", "ISNA": "ISTNV", "ISNONTEXT": "ISTKTEXT", "ISNUMBER": "ISTZAHL", "ISODD": "ISTUNGERADE", "ISREF": "ISTBEZUG", "ISTEXT": "ISTTEXT", "N": "N", "NA": "NV", "SHEET": "BLATT", "SHEETS": "BLÄTTER", "TYPE": "TYP", "AND": "UND", "FALSE": "FALSCH", "IF": "WENN", "IFS": "WENNS", "IFERROR": "WENNFEHLER", "IFNA": "WENNNV", "NOT": "NICHT", "OR": "ODER", "SWITCH": "ERSTERWERT", "TRUE": "WAHR", "XOR": "XODER", "TEXTBEFORE": "TEXTVOR", "TEXTAFTER": "TEXTNACH", "TEXTSPLIT": "TEXTTEILEN", "WRAPROWS": "ZEILENUMBRUCH", "VSTACK": "VSTAPELN", "HSTACK": "HSTAPELN", "CHOOSEROWS": "ZEILENWAHL", "CHOOSECOLS": "SPALTENWAHL", "TOCOL": "ZUSPALTE", "TOROW": "ZUZEILE", "WRAPCOLS": "SPALTENUMBRUCH", "TAKE": "ÜBERNEHMEN", "DROP": "WEGLASSEN", "LocalFormulaOperands": {"StructureTables": {"h": "Kopfzeilen", "d": "Daten", "a": "Alle", "tr": "<PERSON><PERSON>", "t": "Ergebnisse"}, "CONST_TRUE_FALSE": {"t": "WAHR", "f": "FALSCH"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#WERT!", "ref": "#BEZUG!", "name": "#NAME\\?", "num": "#ZAHL!", "na": "#NV", "getdata": "#DATEN_ABRUFEN", "uf": "#UNSUPPORTED_FUNCTION!"}}}
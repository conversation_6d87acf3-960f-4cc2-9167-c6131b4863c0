{"DATE": "DATO", "DATEDIF": "DATEDIF", "DATEVALUE": "DATOVÆRDI", "DAY": "DAG", "DAYS": "DAGE", "DAYS360": "DAGE360", "EDATE": "EDATO", "EOMONTH": "SLUT.PÅ.MÅNED", "HOUR": "TIME", "ISOWEEKNUM": "ISOUGE.NR", "MINUTE": "MINUT", "MONTH": "MÅNED", "NETWORKDAYS": "ANTAL.ARBEJDSDAGE", "NETWORKDAYS.INTL": "ANTAL.ARBEJDSDAGE.INTL", "NOW": "NU", "SECOND": "SEKUND", "TIME": "TID", "TIMEVALUE": "TIDSVÆRDI", "TODAY": "IDAG", "WEEKDAY": "UGEDAG", "WEEKNUM": "UGE.NR", "WORKDAY": "ARBEJDSDAG", "WORKDAY.INTL": "ARBEJDSDAG.INTL", "YEAR": "ÅR", "YEARFRAC": "ÅR.BRØK", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BIN.TIL.DEC", "BIN2HEX": "BIN.TIL.HEX", "BIN2OCT": "BIN.TIL.OKT", "BITAND": "BITOG", "BITLSHIFT": "BITLSKIFT", "BITOR": "BITELLER", "BITRSHIFT": "BITRSKIFT", "BITXOR": "BITXELLER", "COMPLEX": "KOMPLEKS", "CONVERT": "KONVERTER", "DEC2BIN": "DEC.TIL.BIN", "DEC2HEX": "DEC.TIL.HEX", "DEC2OCT": "DEC.TIL.OKT", "DELTA": "DELTA", "ERF": "FEJLFUNK", "ERF.PRECISE": "ERF.PRECISE", "ERFC": "FEJLFUNK.KOMP", "ERFC.PRECISE": "ERFC.PRECISE", "GESTEP": "GETRIN", "HEX2BIN": "HEX.TIL.BIN", "HEX2DEC": "HEX.TIL.DEC", "HEX2OCT": "HEX.TIL.OKT", "IMABS": "IMAGABS", "IMAGINARY": "IMAGINÆR", "IMARGUMENT": "IMAGARGUMENT", "IMCONJUGATE": "IMAGKONJUGERE", "IMCOS": "IMAGCOS", "IMCOSH": "IMAGCOSH", "IMCOT": "IMAGCOT", "IMCSC": "IMAGCSC", "IMCSCH": "IMAGCSCH", "IMDIV": "IMAGDIV", "IMEXP": "IMAGEKSP", "IMLN": "IMAGLN", "IMLOG10": "IMAGLOG10", "IMLOG2": "IMAGLOG2", "IMPOWER": "IMAGPOTENS", "IMPRODUCT": "IMAGPRODUKT", "IMREAL": "IMAGREELT", "IMSEC": "IMAGSEC", "IMSECH": "IMAGSECH", "IMSIN": "IMAGSIN", "IMSINH": "IMAGSINH", "IMSQRT": "IMAGKVROD", "IMSUB": "IMAGSUB", "IMSUM": "IMAGSUM", "IMTAN": "IMAGTAN", "OCT2BIN": "OKT.TIL.BIN", "OCT2DEC": "OKT.TIL.DEC", "OCT2HEX": "OKT.TIL.HEX", "DAVERAGE": "DMIDDEL", "DCOUNT": "DTÆL", "DCOUNTA": "DTÆLV", "DGET": "DHENT", "DMAX": "DMAKS", "DMIN": "DMIN", "DPRODUCT": "DPRODUKT", "DSTDEV": "DSTDAFV", "DSTDEVP": "DSTDAFVP", "DSUM": "DSUM", "DVAR": "DVARIANS", "DVARP": "DVARIANSP", "CHAR": "TEGN", "CLEAN": "RENS", "CODE": "KODE", "CONCATENATE": "SAMMENKÆDE", "CONCAT": "SAMMENKÆDNING", "DOLLAR": "KR", "EXACT": "EKSAKT", "FIND": "FIND", "FINDB": "FINDB", "FIXED": "FAST", "LEFT": "VENSTRE", "LEFTB": "LEFTB", "LEN": "LÆNGDE", "LENB": "LENB", "LOWER": "SMÅ.BOGSTAVER", "MID": "MIDT", "MIDB": "MIDB", "NUMBERVALUE": "TALVÆRDI", "PROPER": "STORT.FORBOGSTAV", "REPLACE": "ERSTAT", "REPLACEB": "REPLACEB", "REPT": "GENTAG", "RIGHT": "HØJRE", "RIGHTB": "RIGHTB", "SEARCH": "SØG", "SEARCHB": "SEARCHB", "SUBSTITUTE": "UDSKIFT", "T": "T", "T.TEST": "T.TEST", "TEXT": "TEKST", "TEXTJOIN": "TEKST.KOMBINER", "TREND": "TENDENS", "TRIM": "FJERN.OVERFLØDIGE.BLANKE", "TRIMMEAN": "TRIMMIDDELVÆRDI", "TTEST": "TTEST", "UNICHAR": "UNICHAR", "UNICODE": "UNICODE", "UPPER": "STORE.BOGSTAVER", "VALUE": "VÆRDI", "AVEDEV": "MAD", "AVERAGE": "MIDDEL", "AVERAGEA": "MIDDELV", "AVERAGEIF": "MIDDEL.HVIS", "AVERAGEIFS": "MIDDEL.HVISER", "BETADIST": "BETAFORDELING", "BETAINV": "BETAINV", "BETA.DIST": "BETA.FORDELING", "BETA.INV": "BETA.INV", "BINOMDIST": "BINOMIALFORDELING", "BINOM.DIST": "BINOMIAL.FORDELING", "BINOM.DIST.RANGE": "BINOMIAL.DIST.INTERVAL", "BINOM.INV": "BINOMIAL.INV", "CHIDIST": "CHIFORDELING", "CHIINV": "CHIINV", "CHITEST": "CHITEST", "CHISQ.DIST": "CHI2.FORDELING", "CHISQ.DIST.RT": "CHI2.FORD.RT", "CHISQ.INV": "CHI2.INV", "CHISQ.INV.RT": "CHI2.INV.RT", "CHISQ.TEST": "CHI2.TEST", "CONFIDENCE": "KONFIDENSINTERVAL", "CONFIDENCE.NORM": "KONFIDENS.NORM", "CONFIDENCE.T": "KONFIDENST", "CORREL": "KORRELATION", "COUNT": "TÆL", "COUNTA": "TÆLV", "COUNTBLANK": "ANTAL.BLANKE", "COUNTIF": "TÆL.HVIS", "COUNTIFS": "TÆL.HVISER", "COVAR": "KOVARIANS", "COVARIANCE.P": "KOVARIANS.P", "COVARIANCE.S": "KOVARIANS.S", "CRITBINOM": "KRITBINOM", "DEVSQ": "SAK", "EXPON.DIST": "EKSP.FORDELING", "EXPONDIST": "EKSPFORDELING", "FDIST": "FFORDELING", "FINV": "FINV", "FTEST": "FTEST", "F.DIST": "F.FORDELING", "F.DIST.RT": "F.FORDELING.RT", "F.INV": "F.INV", "F.INV.RT": "F.INV.RT", "F.TEST": "F.TEST", "FISHER": "FISHER", "FISHERINV": "FISHERINV", "FORECAST": "PROGNOSE", "FORECAST.ETS": "PROGNOSE.ETS", "FORECAST.ETS.CONFINT": "PROGNOSE.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "PROGNOSE.ETS.SÆSONUDSVING", "FORECAST.ETS.STAT": "PROGNOSE.ETS.STAT", "FORECAST.LINEAR": "PROGNOSE.LINEÆR", "FREQUENCY": "FREKVENS", "GAMMA": "GAMMA", "GAMMADIST": "GAMMAFORDELING", "GAMMA.DIST": "GAMMA.FORDELING", "GAMMAINV": "GAMMAINV", "GAMMA.INV": "GAMMA.INV", "GAMMALN": "GAMMALN", "GAMMALN.PRECISE": "GAMMALN.PRECISE", "GAUSS": "GAUSS", "GEOMEAN": "GEOMIDDELVÆRDI", "GROWTH": "FORØGELSE", "HARMEAN": "HARMIDDELVÆRDI", "HYPGEOM.DIST": "HYPGEO.FORDELING", "HYPGEOMDIST": "HYPGEOFORDELING", "INTERCEPT": "SKÆRING", "KURT": "TOPSTEJL", "LARGE": "STØRSTE", "LINEST": "LINREGR", "LOGEST": "LOGREGR", "LOGINV": "LOGINV", "LOGNORM.DIST": "LOGNORM.FORDELING", "LOGNORM.INV": "LOGNORM.INV", "LOGNORMDIST": "LOGNORMFORDELING", "MAX": "MAKS", "MAXA": "MAKSV", "MAXIFS": "MAKSHVISER", "MEDIAN": "MEDIAN", "MIN": "MIN", "MINA": "MINV", "MINIFS": "MINHVISER", "MODE": "HYPPIGST", "MODE.MULT": "HYPPIGST.FLERE", "MODE.SNGL": "HYPPIGST.ENKELT", "NEGBINOM.DIST": "NEGBINOM.FORDELING", "NEGBINOMDIST": "NEGBINOMFORDELING", "NORM.DIST": "NORMAL.FORDELING", "NORM.INV": "NORM.INV", "NORM.S.DIST": "STANDARD.NORM.FORDELING", "NORM.S.INV": "STANDARD.NORM.INV", "NORMDIST": "NORMFORDELING", "NORMINV": "NORMINV", "NORMSDIST": "STANDARDNORMFORDELING", "NORMSINV": "STANDARDNORMINV", "PEARSON": "PEARSON", "PERCENTILE": "FRAKTIL", "PERCENTILE.EXC": "FRAKTIL.UDELAD", "PERCENTILE.INC": "FRAKTIL.MEDTAG", "PERCENTRANK": "PROCENTPLADS", "PERCENTRANK.EXC": "PROCENTPLADS.UDELAD", "PERCENTRANK.INC": "PROCENTPLADS.MEDTAG", "PERMUT": "PERMUT", "PERMUTATIONA": "PERMUTATIONA", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "POISSON.FORDELING", "PROB": "SANDSYNLIGHED", "QUARTILE": "KVARTIL", "QUARTILE.INC": "KVARTIL.MEDTAG", "QUARTILE.EXC": "KVARTIL.UDELAD", "RANK.AVG": "PLADS.GNSN", "RANK.EQ": "PLADS.LIGE", "RANK": "PLADS", "RSQ": "FORKLARINGSGRAD", "SKEW": "SKÆVHED", "SKEW.P": "SKÆVHED.P", "SLOPE": "STIGNING", "SMALL": "MINDSTE", "STANDARDIZE": "STANDARDISER", "STDEV": "STDAFV", "STDEV.P": "STDAFV.P", "STDEV.S": "STDAFV.S", "STDEVA": "STDAFVV", "STDEVP": "STDAFVP", "STDEVPA": "STDAFVPV", "STEYX": "STFYX", "TDIST": "TFORDELING", "TINV": "TINV", "T.DIST": "T.FORDELING", "T.DIST.2T": "T.FORDELING.2T", "T.DIST.RT": "T.FORDELING.RT", "T.INV": "T.INV", "T.INV.2T": "T.INV.2T", "VAR": "VARIANS", "VAR.P": "VARIANS.P", "VAR.S": "VARIANS.S", "VARA": "VARIANSV", "VARP": "VARIANSP", "VARPA": "VARIANSPV", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "WEIBULL.FORDELING", "Z.TEST": "Z.TEST", "ZTEST": "ZTEST", "ACCRINT": "PÅLØBRENTE", "ACCRINTM": "PÅLØBRENTE.UDLØB", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "KUPONDAGE.SA", "COUPDAYS": "KUPONDAGE.A", "COUPDAYSNC": "KUPONDAGE.ANK", "COUPNCD": "KUPONDAG.NÆSTE", "COUPNUM": "KUPONBETALINGER", "COUPPCD": "KUPONDAG.FORRIGE", "CUMIPMT": "AKKUM.RENTE", "CUMPRINC": "AKKUM.HOVEDSTOL", "DB": "DB", "DDB": "DSA", "DISC": "DISKONTO", "DOLLARDE": "KR.DECIMAL", "DOLLARFR": "KR.<PERSON>", "DURATION": "VARIGHED", "EFFECT": "EFFEKTIV.RENTE", "FV": "FV", "FVSCHEDULE": "FVTABEL", "INTRATE": "RENTEFOD", "IPMT": "<PERSON><PERSON>", "IRR": "IA", "ISPMT": "RP.YDELSE", "MDURATION": "MVARIGHED", "MIRR": "MIA", "NOMINAL": "NOMINEL", "NPER": "NPER", "NPV": "NUTIDSVÆRDI", "ODDFPRICE": "ULIGE.KURS.PÅLYDENDE", "ODDFYIELD": "ULIGE.FØRSTE.AFKAST", "ODDLPRICE": "ULIGE.SIDSTE.KURS", "ODDLYIELD": "ULIGE.SIDSTE.AFKAST", "PDURATION": "PVARIGHED", "PMT": "YDELSE", "PPMT": "H.<PERSON>", "PRICE": "KURS", "PRICEDISC": "KURS.DISKONTO", "PRICEMAT": "KURS.UDLØB", "PV": "NV", "RATE": "RENTE", "RECEIVED": "MODTAGET.VED.UDLØB", "RRI": "RRI", "SLN": "LA", "SYD": "ÅRSAFSKRIVNING", "TBILLEQ": "STATSOBLIGATION", "TBILLPRICE": "STATSOBLIGATION.KURS", "TBILLYIELD": "STATSOBLIGATION.AFKAST", "VDB": "VSA", "XIRR": "INTERN.RENTE", "XNPV": "NETTO.NUTIDSVÆRDI", "YIELD": "AFKAST", "YIELDDISC": "AFKAST.DISKONTO", "YIELDMAT": "AFKAST.UDLØBSDATO", "ABS": "ABS", "ACOS": "ARCCOS", "ACOSH": "ARCCOSH", "ACOT": "ARCCOT", "ACOTH": "ARCCOTH", "AGGREGATE": "SAMLING", "ARABIC": "ARABISK", "ASC": "ASC", "ASIN": "ARCSIN", "ASINH": "ARCSINH", "ATAN": "ARCTAN", "ATAN2": "ARCTAN2", "ATANH": "ARCTANH", "BASE": "BASIS", "CEILING": "AFRUND.LOFT", "CEILING.MATH": "LOFT.MAT", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "KOMBIN", "COMBINA": "KOMBINA", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "DECIMAL", "DEGREES": "GRADER", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "LIGE", "EXP": "EKSP", "FACT": "FAKULTET", "FACTDOUBLE": "DOBBELT.FAKULTET", "FLOOR": "AFRUND.GULV", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "AFRUND.BUND.MAT", "GCD": "STØRSTE.FÆLLES.DIVISOR", "INT": "HELTAL", "ISO.CEILING": "ISO.CEILING", "LCM": "MINDSTE.FÆLLES.MULTIPLUM", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MDETERM", "MINVERSE": "MINVERT", "MMULT": "MPRODUKT", "MOD": "REST", "MROUND": "MAFRUND", "MULTINOMIAL": "MULTINOMIAL", "MUNIT": "MENHED", "ODD": "ULIGE", "PI": "PI", "POWER": "POTENS", "PRODUCT": "PRODUKT", "QUOTIENT": "KVOTIENT", "RADIANS": "RADIANER", "RAND": "SLUMP", "RANDARRAY": "SLUMPMATRIX", "RANDBETWEEN": "SLUMPMELLEM", "ROMAN": "ROMERTAL", "ROUND": "AFRUND", "ROUNDDOWN": "RUND.NED", "ROUNDUP": "RUND.OP", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SERIESUM", "SIGN": "FORTEGN", "SIN": "SIN", "SINH": "SINH", "SQRT": "KVROD", "SQRTPI": "KVRODPI", "SUBTOTAL": "SUBTOTAL", "SUM": "SUM", "SUMIF": "SUM.HVIS", "SUMIFS": "SUM.HVISER", "SUMPRODUCT": "SUMPRODUKT", "SUMSQ": "SUMKV", "SUMX2MY2": "SUMX2MY2", "SUMX2PY2": "SUMX2PY2", "SUMXMY2": "SUMXMY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "AFKORT", "ADDRESS": "ADRESSE", "CHOOSE": "VÆLG", "COLUMN": "KOLONNE", "COLUMNS": "KOLONNER", "FORMULATEXT": "FORMELTEKST", "HLOOKUP": "VOPSLAG", "HYPERLINK": "HYPERLINK", "INDEX": "INDEKS", "INDIRECT": "INDIREKTE", "LOOKUP": "SLÅ.OP", "MATCH": "SAMMENLIGN", "OFFSET": "FORSKYDNING", "ROW": "RÆKKE", "ROWS": "RÆKKER", "TRANSPOSE": "TRANSPONER", "UNIQUE": "ENTYDIGE", "VLOOKUP": "LOPSLAG", "XLOOKUP": "XOPSLAG", "CELL": "CELL", "ERROR.TYPE": "FEJLTYPE", "ISBLANK": "ER.TOM", "ISERR": "ER.FJL", "ISERROR": "ER.FEJL", "ISEVEN": "ER.LIGE", "ISFORMULA": "ER.FORMEL", "ISLOGICAL": "ER.LOGISK", "ISNA": "ER.IKKE.TILGÆNGELIG", "ISNONTEXT": "ER.IKKE.TEKST", "ISNUMBER": "ER.TAL", "ISODD": "ER.ULIGE", "ISREF": "ER.REFERENCE", "ISTEXT": "ER.TEKST", "N": "TAL", "NA": "IKKE.TILGÆNGELIG", "SHEET": "ARK", "SHEETS": "ARK.FLERE", "TYPE": "VÆRDITYPE", "AND": "OG", "FALSE": "FALSK", "IF": "HVIS", "IFS": "HVISER", "IFERROR": "HVIS.FEJL", "IFNA": "HVISIT", "NOT": "IKKE", "OR": "ELLER", "SWITCH": "SKIFT", "TRUE": "SAND", "XOR": "XELLER", "TEXTBEFORE": "TEKSTFØR", "TEXTAFTER": "TEKSTEFTER", "TEXTSPLIT": "TEKSTSPLIT", "WRAPROWS": "FOLDRÆKKER", "VSTACK": "VSTAK", "HSTACK": "HSTAK", "CHOOSEROWS": "VÆLGRÆKKER", "CHOOSECOLS": "VÆLGKOL", "TOCOL": "TILKOLONNE", "TOROW": "TILRÆKKE", "WRAPCOLS": "FOLDKOLONNER", "TAKE": "TAG", "DROP": "UDELAD", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!"}}}
{"DATE": {"a": "(år; måned; dag)", "d": "Returnerer tallet som svarer til datoen i koden for dato og tid"}, "DATEDIF": {"a": "(startdato; sluttdato; enhet)", "d": "<PERSON><PERSON><PERSON><PERSON> antall <PERSON>, m<PERSON><PERSON><PERSON> eller år mellom to datoer"}, "DATEVALUE": {"a": "(dato_tekst)", "d": "Konverterer en dato med tekstformat til et tall som representerer datoen i koden for dato og tid"}, "DAY": {"a": "(serienummer)", "d": "Returnerer en dag i måneden, et tall fra 1 til 31."}, "DAYS": {"a": "(sluttdato; startdato)", "d": "Returnerer antallet dager mellom to datoer."}, "DAYS360": {"a": "(startdato; sluttdato; [metode])", "d": "<PERSON><PERSON><PERSON><PERSON> antall dager mellom to datoer basert på et år med 360 dager (12 måneder à 30 dager)"}, "EDATE": {"a": "(start_dato; m<PERSON><PERSON><PERSON>)", "d": "Returnerer serienum<PERSON>t for datoen som er det viste antallet måneder før eller etter startdatoen"}, "EOMONTH": {"a": "(start_dato; m<PERSON><PERSON><PERSON>)", "d": "Returnerer serienummeret for den siste dagen i måneden før eller etter et angitt antall måneder"}, "HOUR": {"a": "(serienummer)", "d": "Returnerer time på dagen som et tall fra 0 (12:00 AM) til 23 (11:00 PM)."}, "ISOWEEKNUM": {"a": "(dato)", "d": "Returnerer ISO-ukenummeret i året for en gitt dato"}, "MINUTE": {"a": "(serienummer)", "d": "Returnerer minuttet, et tall fra 0 til 59."}, "MONTH": {"a": "(serienummer)", "d": "Returnerer m<PERSON><PERSON><PERSON>, et tall fra 1 (januar) til 12 (desember)."}, "NETWORKDAYS": {"a": "(<PERSON>da<PERSON>; sluttdato; [helligdager])", "d": "Returnerer antallet hele arbeidsdager mellom to datoer"}, "NETWORKDAYS.INTL": {"a": "(startdato; sluttdato; [helg]; [helligdager])", "d": "Returnerer antall hele arbeidsdager mellom to datoer med egendefinerte helgeparametere"}, "NOW": {"a": "()", "d": "Returnerer gjeldende dato og klokkeslett formatert som dato og klokkeslett."}, "SECOND": {"a": "(serienummer)", "d": "Returnerer sekundet, et tall fra 0 til 59."}, "TIME": {"a": "(time; minutt; sekund)", "d": "<PERSON><PERSON><PERSON><PERSON> timer, minutter og sekunder som er gitt som tall, til et serienummer, formatert etter et klokkeslettsformat"}, "TIMEVALUE": {"a": "(tidstekst)", "d": "Konverterer teksttid til et serienummer for et klokkeslett, et tall fra 0 (12:00:00 AM) til 0.999988426 (11:59:59 PM). Formaterer tallet med et klokkeslettformat etter å ha innført formelen"}, "TODAY": {"a": "()", "d": "Returnerer gjeldende dato formatert som dato."}, "WEEKDAY": {"a": "(serienummer; [retur_type])", "d": "Returnerer et tall fra 1 til 7 som representerer ukedagen."}, "WEEKNUM": {"a": "(tall; [returtype])", "d": "Returnerer ukenummeret i et år"}, "WORKDAY": {"a": "(startdato; dager; [ekstra_feriedager])", "d": "returnerer serienummeret for da<PERSON><PERSON> før eller etter et angitt antall arbeidsdager"}, "WORKDAY.INTL": {"a": "(startdato; sluttdato; [helg]; [helligdager])", "d": "Returnerer serienummeret for da<PERSON><PERSON> før eller etter et angitt antall arbeidsdager med egendefinerte helgeparametere"}, "YEAR": {"a": "(serienummer)", "d": "Returnerer årstallet, et heltall i intervallet 1900 - 9999."}, "YEARFRAC": {"a": "(startdato; sluttdato; [basis])", "d": "Returnerer delen av året som representeres av antall hele dager mellom startdato og sluttdato"}, "BESSELI": {"a": "(x; n)", "d": "Returnerer Besselfunksjonen In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Returnerer Besselfunksjonen Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Returnerer den modifiserte Besselfunksjonen Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Returnerer Besselfunksjonen Yn(x)"}, "BIN2DEC": {"a": "(tall)", "d": "Konverterer et binærtall til et heltall i 10-tallsystemet"}, "BIN2HEX": {"a": "(tall; [plasser])", "d": "Konverterer et binærtall til et heksadesimalt tall"}, "BIN2OCT": {"a": "(tall; [plasser])", "d": "Konverterer et binærtall til et oktaltall"}, "BITAND": {"a": "(tall1; tall2)", "d": "Returnerer et bitvis \"Og\" av to tall"}, "BITLSHIFT": {"a": "(tall; stø<PERSON><PERSON>_forskyvning)", "d": "Returnerer et tall som forskyves mot venstre med størrelse_forskyvning biter"}, "BITOR": {"a": "(tall1; tall2)", "d": "Returnerer et bitvis \"Eller\" av to tall"}, "BITRSHIFT": {"a": "(tall; stø<PERSON><PERSON>_forskyvning)", "d": "Returnerer et tall som forskyves mot høyre med størrelse_forskyvning biter"}, "BITXOR": {"a": "(tall1; tall2)", "d": "Returnerer et bitvis \"Utelukkende eller\" av to tall"}, "COMPLEX": {"a": "(reelt_tall; imaginært_tall; [suffiks])", "d": "Konverterer reelle og imaginære koeffisienter til et komplekst tall"}, "CONVERT": {"a": "(tall; fra_enhet; til_enhet)", "d": "Konverterer et tall fra ett målesystem til et annet"}, "DEC2BIN": {"a": "(tall; [plasser])", "d": "Konverterer et heltall i 10-tallsystemet til et binærtall"}, "DEC2HEX": {"a": "(tall; [plasser])", "d": "Konverterer et heltall i 10-tallsystemet til et heksadesimaltall"}, "DEC2OCT": {"a": "(tall; [plasser])", "d": "Konverterer et heltall i 10-tallsystemet til et oktaltall"}, "DELTA": {"a": "(tall1; [tall2])", "d": "<PERSON><PERSON><PERSON><PERSON> om to tall er like"}, "ERF": {"a": "(nedre_grense; [øvre_grense])", "d": "Returnerer feilfunksjonen"}, "ERF.PRECISE": {"a": "(X)", "d": "Returnerer feilfunksjonen"}, "ERFC": {"a": "(x)", "d": "Returnerer den komplementære feilfunksjonen"}, "ERFC.PRECISE": {"a": "(x)", "d": "Returnerer den komplementære feilfunksjonen"}, "GESTEP": {"a": "(tall; [steg])", "d": "<PERSON><PERSON><PERSON><PERSON> om et tall er større enn en terskelverdi"}, "HEX2BIN": {"a": "(tall; [plasser])", "d": "Konverterer et heksadesimalt tall til et binærtall"}, "HEX2DEC": {"a": "(tall)", "d": "Konverterer et heksadesimalt tall til et heltall i 10-tallsystemet"}, "HEX2OCT": {"a": "(tall; [plasser])", "d": "Konverterer et heksadesimaltall til et oktaltall"}, "IMABS": {"a": "(imtall)", "d": "Returnerer absoluttverdien (modulus) til et komplekst tall"}, "IMAGINARY": {"a": "(imtall)", "d": "Returnerer den imaginære koeffisienten til et komplekst tall"}, "IMARGUMENT": {"a": "(imtall)", "d": "Returnerer argumentet q, en vinkel uttrykt i radianer"}, "IMCONJUGATE": {"a": "(imtall)", "d": "Returnerer den komplekskonjugerte til et komplekst tall"}, "IMCOS": {"a": "(imtall)", "d": "Returnerer kosinusen til et komplekst tall"}, "IMCOSH": {"a": "(imtall)", "d": "Returnerer hyperbolsk cosinus til et komplekst tall"}, "IMCOT": {"a": "(imtall)", "d": "Returnerer cotangens til et komplekst tall"}, "IMCSC": {"a": "(imtall)", "d": "Returnerer cosekans til et komplekst tall"}, "IMCSCH": {"a": "(imtall)", "d": "Returnerer hyperbolsk cosekans til et komplekst tall"}, "IMDIV": {"a": "(imtall1; imtall2)", "d": "Returnerer kvotienten av to komplekse tall"}, "IMEXP": {"a": "(imtall)", "d": "Returnerer eksponenten til et komplekst tall"}, "IMLN": {"a": "(imtall)", "d": "Returnerer den naturlige logaritmen til et komplekst tall"}, "IMLOG10": {"a": "(imtall)", "d": "Returnerer den briggske logaritmen (med grunntall 10) til et komplekst tall"}, "IMLOG2": {"a": "(imtall)", "d": "Returnerer logaritmen med grunntall 2 til et komplekst tall"}, "IMPOWER": {"a": "(imtall; tall)", "d": "Returnerer et komplekst tall opphøyd i en heltallspotens"}, "IMPRODUCT": {"a": "(imtall1; [imtall2]; ...)", "d": "Returnerer produktet av 1 til 255 komplekse tall"}, "IMREAL": {"a": "(imtall)", "d": "Returnerer den reelle koeffisienten til et komplekst tall"}, "IMSEC": {"a": "(imtall)", "d": "Returnerer sekans til et komplekst tall"}, "IMSECH": {"a": "(imtall)", "d": "Returnerer hyperbolsk sekans til et komplekst tall"}, "IMSIN": {"a": "(imtall)", "d": "Returnerer sinusen til et komplekst tall"}, "IMSINH": {"a": "(imtall)", "d": "Returnerer hyperbolsk sinus til et komplekst tall"}, "IMSQRT": {"a": "(imtall)", "d": "Returnerer kvadratroten til et komplekst tall"}, "IMSUB": {"a": "(imtall1; imtall2)", "d": "Returnerer differansen mellom to komplekse tall"}, "IMSUM": {"a": "(imtall1; [imtall2]; ...)", "d": "Returnerer summen av to eller flere komplekse tall"}, "IMTAN": {"a": "(imtall)", "d": "Returnerer tangens til et komplekst tall"}, "OCT2BIN": {"a": "(tall; [plasser])", "d": "Konverterer et oktaltall til et binærtall"}, "OCT2DEC": {"a": "(tall)", "d": "Konverterer et oktaltall til et heltall i 10-tallsystemet"}, "OCT2HEX": {"a": "(tall; [plasser])", "d": "Konverterer et oktaltall til et heksadesimalt tall"}, "DAVERAGE": {"a": "(database; felt; vilkår)", "d": "Returnerer gjennomsnittet av verdiene i en kolonne i en liste eller database som oppfyller vilkårene du angir"}, "DCOUNT": {"a": "(database; felt; vilkår)", "d": "Teller cellene som inneholder tall, i feltet (kolonnen) med poster i databasen som oppfyller vilkårene du angir"}, "DCOUNTA": {"a": "(database; felt; vilkår)", "d": "Teller utfylte celler i feltet (kolonnen) med poster i databasen som oppfyller vilkårene du angir"}, "DGET": {"a": "(database; felt; vilkår)", "d": "Trekker ut en post som oppfyller vilkårene du angir, fra en database"}, "DMAX": {"a": "(database; felt; vilkår)", "d": "Returnerer det høyeste tallet i feltet (kolonnen) med poster i databasen som oppfyller vilkårene du angir"}, "DMIN": {"a": "(database; felt; vilkår)", "d": "Returnerer det laveste tallet i feltet (kolonnen) med poster i databasen som oppfyller vilkårene du angir"}, "DPRODUCT": {"a": "(database; felt; vilkår)", "d": "Multipliserer verdiene i et bestemt felt (kolonne) med poster i databasen som oppfyller vilkårene du angir"}, "DSTDEV": {"a": "(database; felt; vilkår)", "d": "Estimerer standardavviket på grunnlag av et utvalg i form av merkede databaseposter"}, "DSTDEVP": {"a": "(database; felt; vilkår)", "d": "Be<PERSON>gner standardavviket basert på at merkede databaseposter utgjør hele populasjonen"}, "DSUM": {"a": "(database; felt; vilkår)", "d": "Legger til tallene i feltet (kolonnen) med poster i databasen som oppfyller vilkårene du angir"}, "DVAR": {"a": "(database; felt; vilkår)", "d": "<PERSON><PERSON><PERSON><PERSON> variansen basert på at merkede databaseposter utgjør et utvalg"}, "DVARP": {"a": "(database; felt; vilkår)", "d": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>en basert på at merkede databaseposter utgjør hele populasjonen"}, "CHAR": {"a": "(tall)", "d": "Returnerer tegnet som svarer til kodenummeret fra tegnsettet på datamaskinen"}, "CLEAN": {"a": "(tekst)", "d": "<PERSON><PERSON><PERSON> alle tegn som ikke kan skrives ut, fra teksten"}, "CODE": {"a": "(tekst)", "d": "Returnerer en numerisk kode for det første tegnet i en tekststreng, i tegnsettet som datamaskinen bruker"}, "CONCATENATE": {"a": "(tekst1; [tekst2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> sammen flere tekststrenger til én tekststreng"}, "CONCAT": {"a": "(tekst1; ...)", "d": "<PERSON><PERSON><PERSON> sammen en liste eller et område med tekststrenger"}, "DOLLAR": {"a": "(tall; [desimaler])", "d": "Konverterer et tall til tekst i valutaformat"}, "EXACT": {"a": "(tekst1; tekst2)", "d": "Kontrollerer om to tekststrenger er helt like, og returnerer SANN eller USANN. EKSAKT skiller mellom små og store bokstaver"}, "FIND": {"a": "(finn; innen_tekst; [startpos])", "d": "Returnerer nummeret for posisjonen for det første tegnet i en tekststreng inne i en annen tekststreng. FINN skiller mellom store og små bokstaver"}, "FINDB": {"a": "(finn; innen_tekst; [startpos])", "d": "Finner en tekststreng inni en annen tekststreng og returnerer nummeret som svarer til startposisjonen for den første tekststrengen, regnet fra det første tegnet i den andre tekststrengen, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk"}, "FIXED": {"a": "(tall; [desimaler]; [ingen_tusenskille])", "d": "Runder av et tall til et angitt antall desimaler og konverter det til tekst med eller uten kommaer"}, "LEFT": {"a": "(tekst; [antall_tegn])", "d": "Returnerer det angitte antall tegn fra begynnelsen av en tekststreng"}, "LEFTB": {"a": "(tekst; [antall_tegn])", "d": "Returnerer det første tegnet eller de første tegnene i en tekststreng, basert på antallet byte du angir, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk"}, "LEN": {"a": "(tekst)", "d": "Returnerer antall tegn i en tekststreng"}, "LENB": {"a": "(tekst)", "d": "Returnerer antallet byte som brukes til å representere tegnene i en tekststreng, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk"}, "LOWER": {"a": "(tekst)", "d": "Konverterer alle bokstaver i en tekststreng til små bokstaver"}, "MID": {"a": "(tekst; startpos; antall_tegn)", "d": "Returnerer tegnene fra midten av en tekststreng, hvis posisjonen for det første tegnet og lengden er oppgitt"}, "MIDB": {"a": "(tekst; startpos; antall_tegn)", "d": "Returnerer et angitt antall tegn fra en tekststreng, regnet fra den posisjonen du angir og basert på antall tegn du angir, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk"}, "NUMBERVALUE": {"a": "(tekst; [desimalskilletegn]; [gruppeskilletegn])", "d": "Konverterer tekst til tall på en måte som er uavhengig av nasjonal innstilling"}, "PROPER": {"a": "(tekst)", "d": "Konverterer en tekststreng med hensyn til små og store bokstaver. Den første bokstaven i hvert ord får stor bokstav, og alle andre bokstaver konverteres til små bokstaver"}, "REPLACE": {"a": "(gammel_tekst; startpos; antall_tegn; ny_tekst)", "d": "Erstatter en del av en tekststreng med en annen tekststreng"}, "REPLACEB": {"a": "(gammel_tekst; startpos; antall_tegn; ny_tekst)", "d": "Bytter ut en del av en tekststreng med en annen tekststreng. Hvilken del som byttes ut, er basert på antall byte du angir, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk"}, "REPT": {"a": "(tekst; antall_ganger)", "d": "Gjentar tekst et angitt antall ganger. Bruk GJENTA for å fylle en celle med et antall forekomster av en tekststreng"}, "RIGHT": {"a": "(tekst; [antall_tegn])", "d": "Returnerer det angitte antall tegn fra slutten av en tekststreng"}, "RIGHTB": {"a": "(tekst; [antall_tegn])", "d": "Returnerer det siste tegnet eller de siste tegnene i en tekststreng, basert på antallet byte du angir, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk"}, "SEARCH": {"a": "(finn; innen_tekst; [startpos])", "d": "Returnerer tallet for tegnet hvor et bestemt tegn eller en tekststreng først blir funnet, lest fra venstre mot høyre (skiller ikke mellom små og store bokstaver)"}, "SEARCHB": {"a": "(finn; innen_tekst; [startpos])", "d": "Finner én tekststreng inni en annen tekststreng og returnerer tallet som svarer til startposisjonen for den første tekststrengen, regnet fra det første tegnet i den andre tekststrengen, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk"}, "SUBSTITUTE": {"a": "(tekst; gammel_tekst; ny_tekst; [forekomst_nr])", "d": "Erstatter eksisterende tekst med ny tekst i en tekststreng"}, "T": {"a": "(verdi)", "d": "Kontrollerer om en verdi er tekst, og returnerer i så fall teksten, hvis ikke returnerer den doble anførselstegn (tom tekst)"}, "TEXT": {"a": "(verdi; format_text)", "d": "Konverterer en verdi til tekst i et bestemt nummer format"}, "TEXTJOIN": {"a": "(skilletegn; ignorer_tom; tekst1; ...)", "d": "<PERSON><PERSON><PERSON> sammen en liste eller et område med tekststrenger ved hjelp av et skilletegn"}, "TRIM": {"a": "(tekst)", "d": "<PERSON><PERSON><PERSON> alle mellomrom fra tekst unntatt enkle mellomrom mellom ord"}, "UNICHAR": {"a": "(nummer)", "d": "Returnerer Unicode-tegnet som den gitte numeriske verdien refererer til"}, "UNICODE": {"a": "(tekst)", "d": "Returnerer tallet (kodepunktet) som tilsvarer det første tegnet i teksten"}, "UPPER": {"a": "(tekst)", "d": "Konverterer en tekststreng til store bokstaver"}, "VALUE": {"a": "(tekst)", "d": "Konverterer en tekststreng som representerer et tall, til et tall"}, "AVEDEV": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer datapunktenes gjennomsnittlige absoluttavvik fra middelverdien. Argumentene kan være tall eller navn, matriser eller referanser som inneholder tall"}, "AVERAGE": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer gjenn<PERSON><PERSON><PERSON><PERSON> av argumentene, som kan være tall, navn, matriser eller referanser som inneholder tall"}, "AVERAGEA": {"a": "(verdi1; [verdi2]; ...)", "d": "Returnerer gjennomsnittet (aritmetisk middelverdi) av argumentene. Evaluerer tekst og USANN i argumenter som 0 og SANN som 1. Argumentene kan være tall, navn, matriser eller referanser"}, "AVERAGEIF": {"a": "(område; kriterium; [gjenn<PERSON>nitt_område])", "d": "<PERSON>er gjennomsnittet (det aritmetiske gjennomsnittet) for cellene som angis av et gitt vilkår eller kriterium"}, "AVERAGEIFS": {"a": "(gjennomsnitt_område; kriterieområde; kriterium; ...)", "d": "Finner gjennomsnittet (aritmetisk middelverdi) for cellene som angis av et gitt sett med vilkår eller kriterier"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Returnerer den kumulative betafordelingsfunksjonen for sannsynlig tetthet"}, "BETAINV": {"a": "(sannsynlighet; alfa; beta; [A]; [B])", "d": "Returnerer den inverse til den kumulative betafordelingsfunksjonen for sannsynlig tetthet (BETA.FORDELING)"}, "BETA.DIST": {"a": "(x; alfa; beta; kumulativ; [A]; [B])", "d": "Returnerer betafunksjonen for sannsynlig fordeling"}, "BETA.INV": {"a": "(sannsynlighet; alfa; beta; [A]; [B])", "d": "Returnerer den inverse av den kumulative betafunksjonen for sannsynlig tetthet (BETA.FORDELING.N)"}, "BINOMDIST": {"a": "(tall_s; forsøk; sannsynlighet_s; kumulativ)", "d": "Returnerer punktsannsynlighet eller kumulativ sannsynlighet"}, "BINOM.DIST": {"a": "(antall_s; forsøk; sannsynlighet_s; kumulativ)", "d": "Returnerer den individuelle binomiske sannsynlighetsfordelingen"}, "BINOM.DIST.RANGE": {"a": "(forsøk; sannsynlighet_s; antall_s; [antall_s2])", "d": "Returnerer sannsynligheten for et forsøksresultat ved hjelp av en binomisk fordeling"}, "BINOM.INV": {"a": "(forsøk; sannsynlighet_s; alfa)", "d": "Returnerer den minste verdien der den kumulative binomiske fordelingen er større enn eller lik en vilkårsverdi"}, "CHIDIST": {"a": "(x; frihetsgrader)", "d": "Returnerer den høyre sannsynligheten til kjikvadratfordelingen"}, "CHIINV": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den inverse av den høyre sannsynligheten til kjikvadratfordelingen"}, "CHITEST": {"a": "(faktisk; forventet)", "d": "Returnerer testen for uavhengighet: verdien fra kjikvadratfordelingen for observatoren og gjeldende frihetsgrader"}, "CHISQ.DIST": {"a": "(x; frihetsgrader; kumulative)", "d": "Returnerer venstre sannsynlighet for den kjikvadrerte fordelingen"}, "CHISQ.DIST.RT": {"a": "(x; frihetsgrader)", "d": "Returnerer den høyre sannsynligheten for den kjikvadrerte fordelingen"}, "CHISQ.INV": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den inverse av den venstre sannsynligheten for den kjikvadrerte fordelingen"}, "CHISQ.INV.RT": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den inverse av den høyre sannsynligheten for den kjikvadrerte fordelingen"}, "CHISQ.TEST": {"a": "(faktisk; forventet)", "d": "Returnerer testen for uavhengighet: verdien fra den kjikvadrerte fordelingen for observatoren og gjeldende frihetsgrader"}, "CONFIDENCE": {"a": "(alfa; standardavvik; størrelse)", "d": "Returnerer konfidensintervallet til populasjonens gjennomsnitt ved å bruke en normalfordeling"}, "CONFIDENCE.NORM": {"a": "(alfa; standardavvik; størrelse)", "d": "Returnerer konfidensintervallet til populasjonens middelverdi"}, "CONFIDENCE.T": {"a": "(alfa; standardavvik; størrelse)", "d": "Returnerer konfidensintervallet til populasjonens middelverdi ved hjelp av en Student T-fordeling"}, "CORREL": {"a": "(matrise1; matrise2)", "d": "Returnerer korrelasjonskoeffisienten mellom to datasett"}, "COUNT": {"a": "(verdi1; [verdi2]; ...)", "d": "Teller antall celler i et område som inneholder tall"}, "COUNTA": {"a": "(verdi1; [verdi2]; ...)", "d": "Teller hvor mange celler i et intervall som ikke er tomme"}, "COUNTBLANK": {"a": "(område)", "d": "Teller antall tomme celler innenfor et område"}, "COUNTIF": {"a": "(områ<PERSON>; vilkår)", "d": "Teller antall celler som oppfyller det gitte vilkåret, i et område"}, "COUNTIFS": {"a": "(kriterieområde; kriterium; ...)", "d": "Teller antall celler som angis av et gitt sett med vilkår eller kriterier"}, "COVAR": {"a": "(matrise1; matrise2)", "d": "Returnerer kova<PERSON><PERSON>, gjennomsnittet av produktene av avvikene for hvert datapunktpar i to datasett"}, "COVARIANCE.P": {"a": "(matrise1; matrise2)", "d": "Returnerer populas<PERSON>ens kovarians, som er gjennomsnittet av produktene av avvikene for hvert datapunktpar i to datasett"}, "COVARIANCE.S": {"a": "(matrise1; matrise2)", "d": "Returnerer utval<PERSON> kovarians, som er gjennomsnittet av produktene av avvikene for hvert datapunktpar i to datasett"}, "CRITBINOM": {"a": "(forsøk; sannsynlighet_s; alfa)", "d": "Returnerer den minste verdien der den kumulative binomiske fordelingen er større enn eller lik en vilkårsverdi"}, "DEVSQ": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer summen av datapunkters kvadrerte avvik fra utvalgsgjennomsnittet"}, "EXPONDIST": {"a": "(x; lambda; kumulativ)", "d": "Returnerer eksponentialfordelingen"}, "EXPON.DIST": {"a": "(x; lambda; kumulativ)", "d": "Returnerer eksponentialfordelingen"}, "FDIST": {"a": "(x; frihetsgrader1; frihetsgrader2)", "d": "Returnerer den høyresidige Fisher-fordelingen (spredningsgraden) for to datasett"}, "FINV": {"a": "(sannsynlighet; frihetsgrader1; frihetsgrader2)", "d": "Returnerer den høyresidige inverse av Fisher-fordelingen. Hvis p = FFORDELING(x...), er FFORDELING.INVERS(p...) = x"}, "FTEST": {"a": "(matrise1; matrise2)", "d": "Returnerer resultatet av en F-test, den tosidige sannsynligheten for at variansene i matrise1 og matrise2 ikke er signifikant forskjellige"}, "F.DIST": {"a": "(x; frihetsgrader1; frihetsgrader2; kumulative)", "d": "Returnerer (den venstresidige) F-sannsynlighetsfordelingen (spredningsgraden) for to datasett"}, "F.DIST.RT": {"a": "(x; frihetsgrader1; frihetsgrader2)", "d": "Returnerer (den høyresidige) F-sannsynlighetsfordelingen (spredningsgraden) for to datasett"}, "F.INV": {"a": "(x; frihetsgrader1; frihetsgrader2)", "d": "Returnerer den inverse av (den venstresidige) F-sannsynlighetsfordelingen. hvis p = F.FORDELING(x,...), sål F.INV(p,...) = x"}, "F.INV.RT": {"a": "(sannsynlighet; frihetsgrader1; frihetsgrader2)", "d": "Returnerer den inverse av (den høyresidige) F-sannsynlighetsfordelingen. hvis p = F.FORDELING.H(x,...), så F.INV.H(p,...) = x"}, "F.TEST": {"a": "(matrise1; matrise2)", "d": "Returnerer resultatet av en F-test, den tosidige sannsynligheten for at variansene i matrise1 og matrise2 ikke er signifikant forskjellige"}, "FISHER": {"a": "(x)", "d": "Returnerer Fisher-<PERSON><PERSON><PERSON><PERSON>"}, "FISHERINV": {"a": "(y)", "d": "Returnerer den inverse av Fisher-transformasjonen. Hvis y = FISHER(x), er FISHERINV(y) = x"}, "FORECAST": {"a": "(x; kjente_y; kjente_x)", "d": "<PERSON><PERSON><PERSON><PERSON> el<PERSON> forutsier en fremtidig verdi langs en lineær trend på grunnlag av eksisterende verdier"}, "FORECAST.ETS": {"a": "(m<PERSON><PERSON><PERSON>; verdier; tidslinje; [sesongavhengighet]; [data<PERSON><PERSON><PERSON>ring]; [aggregasjon])", "d": "Returnerer den prognostiserte verdien for en bestemt fremtidig måldato ved hjelp av eksponentiell glatting."}, "FORECAST.ETS.CONFINT": {"a": "(m<PERSON><PERSON><PERSON>; verdier; tidslinje; [konfidensnivå]; [sesongavhengighet]; [data<PERSON><PERSON><PERSON>ring]; [aggregasjon])", "d": "Returnerer kon<PERSON><PERSON><PERSON><PERSON><PERSON>let for prognoseverdien for den angitte måldatoen."}, "FORECAST.ETS.SEASONALITY": {"a": "(verdier; tidslinje; [data<PERSON><PERSON><PERSON><PERSON>]; [aggregasjon])", "d": "Returnerer hele det gjentakende mønsteret som applikasjon oppdager for den angitte tidsserien."}, "FORECAST.ETS.STAT": {"a": "(verdier; tidslinje; statistikktype; [seasongavhengighet]; [datafullf<PERSON>ring]; [aggregasjon])", "d": "Returnerer den forespurte statistikken for prognosen."}, "FORECAST.LINEAR": {"a": "(x; kjente_y; kjente_x)", "d": "<PERSON><PERSON><PERSON><PERSON> el<PERSON> forutsier en fremtidig verdi langs en lineær trend på grunnlag av eksisterende verdier"}, "FREQUENCY": {"a": "(datamatrise; klassematrise)", "d": "<PERSON><PERSON><PERSON><PERSON> hvor ofte verdier forekommer i et område med verdier, og returnerer en loddrett matrise med tall med ett element mer enn klassematrise"}, "GAMMA": {"a": "(x)", "d": "Returnerer gammafunksjonsverdien"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerer gammafordelingen"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerer gammafordelingen"}, "GAMMAINV": {"a": "(sannsynlighet; alfa; beta)", "d": "Returnerer den inverse av den kumulative gammafordelingen. Hvis p = GAMMAFORDELING(x...), er GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(sannsynlighet; alfa; beta)", "d": "Returnerer den inverse av den kumulative gammafordelingen: hvis p = GAMMA.FORDELING(x,...), så GAMMA.INV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Returnerer den naturlige logaritmen til gammafunksjonen"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Returnerer den naturlige logaritmen til gammafunksjonen"}, "GAUSS": {"a": "(x)", "d": "Returnerer 0,5 mindre enn standard kumulativ normalfordeling"}, "GEOMEAN": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer den geometriske middelverdien for en matrise eller et område med positive numeriske data"}, "GROWTH": {"a": "(kjente_y; [kjente_x]; [nye_x]; [konst])", "d": "Returnerer tall i en eksponentiell veksttrend som samsvarer med kjente datapunkter"}, "HARMEAN": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer den harmoniske middelverdien for et datasett med positive tall, det vil si den resiproke verdien av den aritmetiske middelverdien av de resiproke verdiene"}, "HYPGEOM.DIST": {"a": "(utvalg_s; utvalgsstørrelse; suksesser; populasjonsstørrelse; kumulative)", "d": "Returnerer den hypergeometriske fordelingen"}, "HYPGEOMDIST": {"a": "(utvalg_s; utvalgsstørrelse; suksesser; populasjonsstørrelse)", "d": "Returnerer den hypergeometriske fordelingen"}, "INTERCEPT": {"a": "(kjente_y; kjente_x)", "d": "Be<PERSON>gner punktet hvor en linje skjærer y-aksen ved å bruke en regresjonslinje for beste tilpasning som tegnes gjennom de kjente x- og y-verdiene"}, "KURT": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer kurtosen til et datasett"}, "LARGE": {"a": "(matrise; n)", "d": "Returnerer den n-te høyeste verdien i et datasett, for eks<PERSON>pel det femte høyeste tallet"}, "LINEST": {"a": "(kjente_y; [kjente_x]; [konst]; [statistikk])", "d": "Returnerer statistikk som beskriver en lineær trend som samsvarer med kjente datapunkter, ved å tilpasse en rett linje beregnet med minste kvadraters metode"}, "LOGEST": {"a": "(kjente_y; [kjente_x]; [konst]; [statistikk])", "d": "Returnerer statistikk som beskriver en eksponentiell kurve som samsvarer med kjente datapunkter"}, "LOGINV": {"a": "(sannsynlighet; median; standardavvik)", "d": "Returnerer den inverse av den lognormale fordelingsfunksjonen til x, hvor In(x) har normalfordeling med parameterne median og standardavvik"}, "LOGNORM.DIST": {"a": "(x; gje<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; kumulativ)", "d": "Returnerer den lognormale fordelingen av x, der ln(x) er normalfordelt med parameterne Gjennomsnitt og Standardavvik"}, "LOGNORM.INV": {"a": "(sannsynlighet; middelverdi; standardavvik)", "d": "Returnerer den inverse av den lognormale fordelingsfunksjonen, hvor In(x) har normalfordeling med parameterne middelverdi og standardavvik"}, "LOGNORMDIST": {"a": "(x; median; standardavvik)", "d": "Returnerer den kumulative lognormale fordelingen for x, hvor In(x) har normalfordeling med parameterne median og standardavvik"}, "MAX": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer maksimumsverdien i et verdisett. Ignorerer logiske verdier og tekst"}, "MAXA": {"a": "(verdi1; [verdi2]; ...)", "d": "Returnerer den høyeste verdien i et verdisett. Ignorerer ikke logiske verdier og tekst"}, "MAXIFS": {"a": "(maks_områ<PERSON>; vilkår_område; vilk<PERSON>r; ...)", "d": "Returnerer maksimumsverdien mellom cellene som angis av et gitt sett med betingelser eller vilkår"}, "MEDIAN": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer medianen for settet av angitte verdier, altså den midterste verdien i rekken (eller gjennomsnittet av de to midterste) når verdiene er ordnet i stigende rekkefølge"}, "MIN": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer det laveste tallet i et verdisett. Ignorerer logiske verdier og tekst"}, "MINA": {"a": "(verdi1; [verdi2]; ...)", "d": "Returnerer den laveste verdien i et verdisett. Ignorerer ikke logiske verdier og tekst"}, "MINIFS": {"a": "(min_områ<PERSON>; vilk<PERSON>r_område; vilk<PERSON><PERSON>; ...)", "d": "Returnerer minimumssverdien mellom cellene som angis av et gitt sett med betingelser eller vilkår"}, "MODE": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer den hyppigst forekommende verdien i en matrise eller et dataområde"}, "MODE.MULT": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer en loddrett matrise av de hyppigste verdiene, eller de som gjentas oftest, i en matrise eller dataområde. For en vannrett matrise bruker du =TRANSPONER(MODUS.MULT(tall1,tall2,...))"}, "MODE.SNGL": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer den hyppigst forekommende, eller mest repeterte, verdien i en matrise eller et dataområde"}, "NEGBINOM.DIST": {"a": "(tall_f; tall_s; sannsynlighet_s; kumulativ)", "d": "Returnerer den negative binomiske fordelingen, sannsy<PERSON><PERSON><PERSON><PERSON>n for at det vil være tall_f fiaskoer før tall_s-te suksess, der sannsynlighet_s er sannsynligheten for suksess"}, "NEGBINOMDIST": {"a": "(tall_f; tall_s; sannsynlighet_s)", "d": "Returnerer negativ binomisk fordeling, s<PERSON><PERSON> for at det vil bli tall_f fiaskoer før tall_s-te suksess, med sannsynlighet_s sjanse for suksess, hvor sannsynlighet_s er sannsynligheten for suksess"}, "NORM.DIST": {"a": "(x; median; standardavvik; kumulativ)", "d": "Returnerer normalfordelingen for angitt middelverdi og standardavvik"}, "NORMDIST": {"a": "(x; median; standardavvik; kumulativ)", "d": "Returnerer den kumulative normalfordelingen for angitt median og standardavvik"}, "NORM.INV": {"a": "(sannsynlighet; median; standardavvik)", "d": "Returnerer den inverse av den kumulative normalfordelingen for angitt gjennomsnitt og standardavvik"}, "NORMINV": {"a": "(sannsynlighet; median; standardavvik)", "d": "Returnerer den inverse av den kumulative normalfordelingen for angitt gjennomsnitt og standardavvik"}, "NORM.S.DIST": {"a": "(z; kumulativ)", "d": "Returnerer standard normalfordeling (har en middelverdi lik null og et standardavvik lik én)"}, "NORMSDIST": {"a": "(z)", "d": "Returnerer standard kumulativ normalfordeling (der gjennomsnittet er lik null og standardavviket er én)"}, "NORM.S.INV": {"a": "(sannsynlighet)", "d": "Returnerer den inverse av standard kumulativ normalfordeling (har null som middelverdi og én som standardavvik)"}, "NORMSINV": {"a": "(sannsynlighet)", "d": "Returnerer den inverse av standard kumulativ normalfordeling (har et gjennomsnitt på null og standardavvik på én)"}, "PEARSON": {"a": "(matrise1; matrise2)", "d": "Returnerer produkt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> r"}, "PERCENTILE": {"a": "(matrise; n)", "d": "Returnerer det n-te persentilet av verdiene i et område"}, "PERCENTILE.EXC": {"a": "(matrise; k)", "d": "Returnerer den k-te persentilen av verdiene i et område, der k er i området 0..1, eksklusive"}, "PERCENTILE.INC": {"a": "(matrise; k)", "d": "Returnerer den k-te persentilen av verdiene i et område, der k er i området 0..1, inklusive"}, "PERCENTRANK": {"a": "(matrise; x; [gje<PERSON><PERSON>_sifre])", "d": "Returnerer rangeringen av en verdi i et datasett som en prosent av datasettet"}, "PERCENTRANK.EXC": {"a": "(matrise; x; [signifikans])", "d": "Returnerer rangeringen av en verdi i et datasett som en prosentdel av datasettet som en prosentdel (0..1, eksklusive) av datasettet"}, "PERCENTRANK.INC": {"a": "(matrise; x; [signifikans])", "d": "Returnerer rangeringen av en verdi i et datasett som en prosentdel av datasettet som en prosentdel (0..1, inklusive) av datasettet"}, "PERMUT": {"a": "(antall; valgt_antall)", "d": "Returnerer antall permutasjoner for et gitt antall objekter som kan velges fra det totale antall objekter"}, "PERMUTATIONA": {"a": "(antall; valgt_antall)", "d": "Returnerer antallet permutasjoner for et gitt antall objekter (med repetisjoner) som kan velges fra det totale antallet objekter"}, "PHI": {"a": "(x)", "d": "Returnerer verdien av tetthetsfunksjonen for en standard normalfordeling"}, "POISSON": {"a": "(x; median; kumulativ)", "d": "Returnerer Poisson-fordelingen"}, "POISSON.DIST": {"a": "(x; median; kumulativ)", "d": "Returnerer Poisson-fordelingen"}, "PROB": {"a": "(x_omr<PERSON><PERSON>; utfallsområ<PERSON>; nedre_grense; [øvre_grense])", "d": "Returnerer sann<PERSON><PERSON><PERSON><PERSON><PERSON>n for at en verdi ligger mellom to ytterpunkter i et område eller er lik et nedre ytterpunkt"}, "QUARTILE": {"a": "(matrise; kvar<PERSON>)", "d": "Returnerer kvartilen for et datasett"}, "QUARTILE.INC": {"a": "(matrise; kvart)", "d": "Returnerer kvartilen til et datasett, basert på persentilverdier fra 0..1, inklusive"}, "QUARTILE.EXC": {"a": "(matrise; kvart)", "d": "Returnerer kvartilen til et datasett basert på persentilverdier fra 0..1, eksklusive"}, "RANK": {"a": "(tall; ref; [rekke<PERSON><PERSON><PERSON>])", "d": "Returnerer rangeringen av et tall i en liste over tall. Tallet rangeres etter størrelsen i forhold til andre verdier i listen"}, "RANK.AVG": {"a": "(tall; ref; [rekke<PERSON><PERSON><PERSON>])", "d": "Returnerer rangeringen for et tall i en liste med tall: st<PERSON><PERSON><PERSON> i forhold til andre verdier i listen, hvis mer enn én verdi har samme rangering, returneres gjennomsnittlig rangering"}, "RANK.EQ": {"a": "(tall; ref; [rekke<PERSON><PERSON><PERSON>])", "d": "Returnerer rangeringen for et tall i en liste med tall: st<PERSON><PERSON><PERSON> i forhold til andre verdier i listen. Hvis mer enn én verdi har samme rangering, returneres den høyeste rangeringen for det verdisettet"}, "RSQ": {"a": "(kjente_y; kjente_x)", "d": "Returnerer kvadratet av produktmomentkorrelasjonskoeffisienten Pearsons r gjennom de gitte datapunktene"}, "SKEW": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer skjevheten for en fordeling, et mål for en fordelings asymmetri i forhold til gjennomsnittet"}, "SKEW.P": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer skje<PERSON><PERSON><PERSON> for en fordeling basert på en populasjon, et mål for en fordelingsasymmetri i forhold til gjennomsnittet"}, "SLOPE": {"a": "(kjente_y; kjente_x)", "d": "Returnerer stigningstallet for den lineære regresjonslinjen gjennom de gitte datapunktene"}, "SMALL": {"a": "(matrise; n)", "d": "Returnerer den n-te laveste verdien i et datasett, for eks<PERSON><PERSON> det femte laveste tallet"}, "STANDARDIZE": {"a": "(x; median; standardavvik)", "d": "Returnerer en normalisert verdi fra en fordeling spesifisert ved gjennomsnitt og standardavvik"}, "STDEV": {"a": "(tall1; [tall2]; ...)", "d": "Beregner standardavvik basert på et utvalg (ignorerer logiske verdier og tekst i utvalget)"}, "STDEV.P": {"a": "(tall1; [tall2]; ...)", "d": "Beregner standardavvik basert på hele populasjonen (ignorerer logiske verdier og tekst)"}, "STDEV.S": {"a": "(tall1; [tall2]; ...)", "d": "Beregner standardavvik basert på et utvalg (ignorerer logiske verdier og tekst i utvalget)"}, "STDEVA": {"a": "(verdi1; [verdi2]; ...)", "d": "Estimerer standardavvik basert på et utvalg, inkludert logiske verdier og tekst. Tekst og den logiske verdien USANN har verdien 0, den logiske verdien SANN har verdien 1"}, "STDEVP": {"a": "(tall1; [tall2]; ...)", "d": "Beregner standardavvik basert på hele populasjonen gitt som argumenter (ignorerer logiske verdier og tekst)"}, "STDEVPA": {"a": "(verdi1; [verdi2]; ...)", "d": "Beregner standardavvik basert på at argumentene beskriver hele populasjonen, inkludert logiske verdier og tekst. Tekst og den logiske verdien USANN har verdien 0, den logiske verdien SANN har verdien 1"}, "STEYX": {"a": "(kjente_y; kjente_x)", "d": "Returnerer standardfeilen til den forventede y-verdien for hver x i en regresjon"}, "TDIST": {"a": "(x; frihetsgrader; sider)", "d": "Returnerer Students t-fordeling"}, "TINV": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den tosidige inverse av Students t-fordelingen"}, "T.DIST": {"a": "(x; frihetsgrader; kumulative)", "d": "Returnerer den venstre Students t-fordelingen"}, "T.DIST.2T": {"a": "(x; frihetsgrader)", "d": "Returnerer den tosidige Students t-fordelingen"}, "T.DIST.RT": {"a": "(x; frihetsgrader)", "d": "Returnerer den høyre Students t-fordelingen"}, "T.INV": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den venstre inverse av Students t-fordelingen"}, "T.INV.2T": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den tosidige inverse av Students t-fordelingen"}, "T.TEST": {"a": "(matrise1; matrise2; sider; type)", "d": "Returnerer sannsynligheten knyttet til en Students t-test"}, "TREND": {"a": "(kjente_y; [kjente_x]; [nye_x]; [konst])", "d": "Returnerer tall i en lineær trend som samsvarer med kjente datapunkter, ved hjelp av minste kvadraters metode"}, "TRIMMEAN": {"a": "(matrise; prosent)", "d": "Returnerer det trimmede gjennomsnittet av et sett dataverdier"}, "TTEST": {"a": "(matrise1; matrise2; sider; type)", "d": "Returnerer sannsynligheten knyttet til en Students t-test"}, "VAR": {"a": "(tall1; [tall2]; ...)", "d": "Beregner varians basert på et utvalg (ignorerer logiske verdier og tekst i utvalget)"}, "VAR.P": {"a": "(tall1; [tall2]; ...)", "d": "Beregner varians basert på hele populasjonen (ignorerer logiske verdier og tekst i populasjonen)"}, "VAR.S": {"a": "(tall1; [tall2]; ...)", "d": "Beregner varians basert på et utvalg (ignorerer logiske verdier og tekst i utvalget)"}, "VARA": {"a": "(verdi1; [verdi2]; ...)", "d": "Anslår varians basert på et utvalg, inkludert logiske verdier og tekst. Tekst og den logiske verdien USANN har verdi 0, den logiske verdien SANN har verdi 1"}, "VARP": {"a": "(tall1; [tall2]; ...)", "d": "Beregner varians basert på hele populasjonen (ignorerer logiske verdier og tekst i populasjonen)"}, "VARPA": {"a": "(verdi1; [verdi2]; ...)", "d": "Beregner varians basert på hele populasjonen, inkludert logiske verdier og tekst. Tekst og den logiske verdien USANN har verdien 0, den logiske verdien SANN har verdien 1"}, "WEIBULL": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerer <PERSON><PERSON>-fordelingen"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerer <PERSON><PERSON>-fordelingen"}, "Z.TEST": {"a": "(matrise; x; [sigma])", "d": "Returnerer den ensidige P-verdien i en z-test"}, "ZTEST": {"a": "(matrise; x; [sigma])", "d": "Returnerer den ensidige P-verdien for en z-test"}, "ACCRINT": {"a": "(utstedt_dato; første_renteforfall; betalingsdato; rente; pari; frekvens; [basis]; [beregningsmetode])", "d": "Returnerer påløpte renter for et verdipapir som betaler periodisk rente."}, "ACCRINTM": {"a": "(utstedt_dato; forfallsdato; rente; pari; [basis])", "d": "Returnerer den påløpte renten for et verdipapir som betaler rente ved forfall"}, "AMORDEGRC": {"a": "(kostnad; innkjøpsdato; første_periode; skrapverdi; periode; rente; [basis])", "d": "Returnerer forhold<PERSON><PERSON><PERSON> lineær avskrivning for et aktivum for hver regnskapsperiode."}, "AMORLINC": {"a": "(kostnad; innkjøpsdato; første_periode; skrapverdi; periode; rente; [basis])", "d": "Returnerer forhold<PERSON><PERSON><PERSON> lineær avskrivning for et aktivum for hver regnskapsperiode."}, "COUPDAYBS": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer antall dager fra begynnelsen av den rentebærende perioden til betalingsdatoen"}, "COUPDAYS": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer antall dager i den rentebærende perioden som inneholder betalingsdatoen"}, "COUPDAYSNC": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer antall dager fra betalingsdatoen til neste rentebetalingsdato"}, "COUPNCD": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer neste <PERSON>ato etter beta<PERSON>"}, "COUPNUM": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer antallet rentebetalinger mellom betalingsdatoen og forfallsdatoen"}, "COUPPCD": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer si<PERSON> <PERSON><PERSON> før <PERSON>"}, "CUMIPMT": {"a": "(rente; antall_utbet; nåverdi; startperiode; sluttperiode; type)", "d": "Returnerer den kumulative renten på et lån mellom to perioder"}, "CUMPRINC": {"a": "(rente; antall_utbet; nåverdi; startperiode; sluttperiode; type)", "d": "Returnerer den kumulative hovedstolen som er betalt på et lån mellom to perioder"}, "DB": {"a": "(kostnad; restverdi; levetid; periode; [måned])", "d": "Returnerer avskrivningen for et aktivum for en angitt periode, ved hjelp av fast degressiv avskrivning"}, "DDB": {"a": "(kostnad; restverdi; levetid; periode; [faktor])", "d": "Returnerer avskrivningen for et aktivum for en gitt periode, ved hjelp av dobbel degressiv avskrivning eller en annen metode du selv angir"}, "DISC": {"a": "(betalingsdato; forfallsdato; pris; innløsningsverdi; [basis])", "d": "Returnerer diskontosa<PERSON>en for et verdipapir"}, "DOLLARDE": {"a": "(brøk_valuta; brøk)", "d": "Konverterer en valutapris uttrykt som en brøk til en valutapris uttrykt som et desimaltall"}, "DOLLARFR": {"a": "(desimal_valuta; brøk)", "d": "Konverterer en valutapris som et desimaltall til en valutapris uttrykt som en brøk"}, "DURATION": {"a": "(betalingsdato; forfallsdato; rente; avkastning; frekvens; [basis])", "d": "Returnerer den årlige varigheten for et verdipapir med periodisk rentebetaling"}, "EFFECT": {"a": "(nominell_rente; perioder)", "d": "Returnerer den effektive årlige renten"}, "FV": {"a": "(rente; antall_utbet; utbetaling; [nåverdi]; [type])", "d": "Returnerer den fremtidige verdien av en investering basert på periodiske, konstante utbetalinger og en fast rente"}, "FVSCHEDULE": {"a": "(hovedstol; plan)", "d": "Returnerer sluttverdien av en inngående hovedstol etter å ha brukt en serie med sammensatte rentesatser"}, "INTRATE": {"a": "(betalingsdato; forfallsdato; investering; innløsningsverdi; [basis])", "d": "Returnerer rentefoten av et fullfinansiert verdipapir"}, "IPMT": {"a": "(rente; periode; antall_utbet; nåverdi; [sluttverdi]; [type])", "d": "Returnerer betalte renter på en investering for en gitt periode basert på periodiske, konstante utbetalinger og en fast rentesats"}, "IRR": {"a": "(verdi; [antatt])", "d": "Returnerer internrenten for en serie kontantstrømmer"}, "ISPMT": {"a": "(rente; periode; antall_utbet; nåverdi)", "d": "Returnerer renten som er betalt i løpet av en angitt investeringsperiode"}, "MDURATION": {"a": "(betalingsdato; forfallsdato; rente; avkastning; frekvens; [basis])", "d": "Returnerer <PERSON>leys modifiserte varighet for et verdipapir med en antatt pariverdi på kr 100"}, "MIRR": {"a": "(verdier; kapital<PERSON>e; reinvestering<PERSON>rente)", "d": "Returnerer internrenten for en serie periodiske kontantstrømmer og tar hensyn til både investeringskostnad og rente på reinvestering av kontanter"}, "NOMINAL": {"a": "(effektiv_rente; perioder)", "d": "Returnerer den årlige nominelle rentefoten"}, "NPER": {"a": "(rente; utbetaling; nåverdi; [sluttverdi]; [type])", "d": "Returnerer antallet perioder for en investering basert på periodiske, konstante utbetalinger og en fast rentesats"}, "NPV": {"a": "(rente; verdi1; [verdi2]; ...)", "d": "Returnerer netto nåverdi for en investering basert på en rentesats og en serie fremtidige utbetalinger (negative verdier) og inntekter (positive verdier)"}, "ODDFPRICE": {"a": "(betalingsdato; forfallsdato; utstedelsesdato; første_forfallsdato; rente; avkastning; innløsningsverdi; frekvens; [basis])", "d": "Returnerer prisen per pålydende kr 100 for et verdipapir som har en odde første periode"}, "ODDFYIELD": {"a": "(betalingsdato; forfallsdato; utstedelsesdato; første_forfallsdato; rente; pris; innløsningsverdi; frekvens; [basis])", "d": "Returnerer avkastningen av et verdipapir som har en odde første periode"}, "ODDLPRICE": {"a": "(betalingsdato; forfallsdato; siste_forfallsdato; rente; avkastning; innløsningsverdi; frekvens; [basis])", "d": "Returnerer prisen per pålydende kr 100 for et verdipapir som har en odde siste periode"}, "ODDLYIELD": {"a": "(betalingsdato; forfallsdato; siste_forfallsdato; rente; pris; innløsningsverdi; frekvens; [basis])", "d": "Returnerer avkastningen av et verdipapir som har en odde siste periode"}, "PDURATION": {"a": "(rente; nåverdi; sluttverdi)", "d": "Returnerer antallet perioder som kreves av en investering for å nå en angitt verdi"}, "PMT": {"a": "(rente; antall_utbet; nåverdi; [sluttverdi]; [type])", "d": "<PERSON><PERSON><PERSON><PERSON> utbetalinger for et lån basert på konstante utbetalinger og en fast rentesats"}, "PPMT": {"a": "(rente; periode; antall_utbet; nåverdi; [sluttverdi]; [type])", "d": "Returnerer utbetaling på hovedstolen for en gitt investering basert på periodiske, konstante utbetalinger og en fast rentesats"}, "PRICE": {"a": "(betalingsdato; forfallsdato; rente; avkastning; innløsningsverdi; frekvens; [basis])", "d": "Returnerer prisen per pålydende kr 100 for et verdipapir som betaler periodisk rente"}, "PRICEDISC": {"a": "(betalingsdato; forfallsdato; diskonto; innløsningsverdi; [basis])", "d": "Returnerer prisen per pålydende kr 100 for et diskontert verdipapir"}, "PRICEMAT": {"a": "(betalingsdato; forfallsdato; utstedelsesdato; rente; avkastning; [basis])", "d": "Returnerer prisen per pålydende kr 100 av et verdipapir som betaler rente ved forfall"}, "PV": {"a": "(rente; antall_utbet; utbetaling; [sluttverdi]; [type])", "d": "Returnerer nåverdien for en investering, det totale beløpet som en serie fremtidige utbetalinger er verdt i dag"}, "RATE": {"a": "(antall_innbet; betaling; nåverdi; [sluttverdi]; [type]; [anslag])", "d": "Returnerer rentesatsen per periode for et lån eller en investering. Bruk for eksempel 6 %/4 for kvartalsvise innbetalinger ved 6 % årlig rente"}, "RECEIVED": {"a": "(betalingsdato; forfallsdato; investering; diskonto; [basis])", "d": "Returnerer belø<PERSON> som mottas ved forfallsdato for et fullinvestert verdipapir"}, "RRI": {"a": "(antall_innbet; nåverdi; sluttverdi)", "d": "Returnerer en ekvivalent rentesats for vekst for en investering"}, "SLN": {"a": "(kostnad; restverdi; levetid)", "d": "Returnerer den lineære verdiavskrivningen for et aktivum i en gitt periode"}, "SYD": {"a": "(kostnad; restverdi; levetid; periode)", "d": "Returnerer årsavskrivningen for et aktivum i en angitt periode"}, "TBILLEQ": {"a": "(betalingsdato; forfallsdato; diskonto)", "d": "Returnerer verdipapirekvivalenten til en statsobligasjon"}, "TBILLPRICE": {"a": "(betalingsdato; forfallsdato; diskonto)", "d": "Returnerer prisen per pålydende kr 100 for en statsobligasjon"}, "TBILLYIELD": {"a": "(betalingsdato; forfallsdato; diskonto)", "d": "Returnerer avkastningen for en statsobligasjon"}, "VDB": {"a": "(kostnad; restverdi; levetid; start_periode; slutt_periode; [faktor]; [skift])", "d": "Returnerer avskrivningen på et aktivum for en periode du angir, medre<PERSON><PERSON>, ved hjelp av dobbel degressiv avskrivning eller en annen metode du angir"}, "XIRR": {"a": "(verdier; datoer; [anslag])", "d": "Returnerer internrenten for en serie kontantstrømmer"}, "XNPV": {"a": "(rente; verdier; datoer)", "d": "Returnerer netto nåverdi av planlagte kontantstrømmer"}, "YIELD": {"a": "(betalingsdato; forfallsdato; rente; pris; innløsningsverdi; frekvens; [basis])", "d": "Returnerer avkastningen på et verdipapir som betaler periodisk rente"}, "YIELDDISC": {"a": "(betalingsdato; forfallsdato; pris; innløsningsverdi; [basis])", "d": "Returnerer den årlige avkastningen for et diskontert verdipapir, for eksempel en statsobligasjon"}, "YIELDMAT": {"a": "(betalingsdato; forfallsdato; utstedelsesdato; rente; pris; [basis])", "d": "Returnerer den årlige avkastningen av et verdipapir som betaler rente ved forfallsdatoen"}, "ABS": {"a": "(tall)", "d": "Returnerer absoluttverdien til et tall, et tall uten fortegn"}, "ACOS": {"a": "(tall)", "d": "Returnerer arccosinus til et tall, i radianer i intervallet 0 til pi. Arccosinus er vinkelen som har tall-variabelen som cosinus"}, "ACOSH": {"a": "(tall)", "d": "Returnerer den inverse hyperbolske cosinus til et tall"}, "ACOT": {"a": "(tall)", "d": "Returnerer arccotangens for et tall i radianer i området 0 til pi."}, "ACOTH": {"a": "(tall)", "d": "Returnerer invers hyperbolsk cotangens for et tall"}, "AGGREGATE": {"a": "(funksjonsnummer; alternativer; ref1; ...)", "d": "Returnerer en mengde i en liste eller database"}, "ARABIC": {"a": "(tekst)", "d": "Konverterer et romertall til et arabisk tall"}, "ASC": {"a": "(tekst)", "d": "For språk med dobbeltbyte-te<PERSON>sett (DBCS) endrer funksjonen tegn med full bredde (dobbeltbyte) til tegn med halv bredde (enkeltbyte)"}, "ASIN": {"a": "(tall)", "d": "Returnerer arcsinus til et tall i radianer, i området ‑Pi/2 til Pi/2"}, "ASINH": {"a": "(tall)", "d": "Returnerer den inverse hyperbolske sinus til et tall"}, "ATAN": {"a": "(tall)", "d": "Returnerer arctangens til et tall i radianer, i området -Pi/2 til Pi/2"}, "ATAN2": {"a": "(x; y)", "d": "Returnerer arctangens til x- og y-koordinatene i radianer, i området fra -Pi til Pi, unntatt -Pi"}, "ATANH": {"a": "(tall)", "d": "Returnerer den inverse hyperbolske tangens til et tall"}, "BASE": {"a": "(tall; basis; [minste_lengde])", "d": "Konverterer et tall til en tekstrepresentasjon med den gitte basisen"}, "CEILING": {"a": "(tall; gjeldende_multiplum)", "d": "Runder av et tall oppover til nærmeste multiplum av en faktor"}, "CEILING.MATH": {"a": "(tall; [gjeldende_multiplum]; [modus])", "d": "Runder av et tall oppover til nærmeste heltall eller til nærmeste signifikante multiplum av en faktor"}, "CEILING.PRECISE": {"a": "(tall; [gjeldende_multiplum])", "d": "Returnerer et tall som er avrundet opp til nærmeste heltall, eller til nærmeste gjeldende multiplum"}, "COMBIN": {"a": "(antall; valgt_antall)", "d": "Returnerer antall komb<PERSON>er for et gitt antall elementer"}, "COMBINA": {"a": "(antall; valgt_antall)", "d": "Returnerer antallet kombinasjoner med repetisjoner for et gitt antall elementer"}, "COS": {"a": "(tall)", "d": "Returnerer cosinus for en vinkel"}, "COSH": {"a": "(tall)", "d": "Returnerer den hyperbolske cosinus til et tall"}, "COT": {"a": "(tall)", "d": "Returnerer cotangens for en vinkel"}, "COTH": {"a": "(tall)", "d": "Returnerer hyperbolsk cotangens for et tall"}, "CSC": {"a": "(tall)", "d": "Returnerer cosekans for en vinkel"}, "CSCH": {"a": "(tall)", "d": "Returnerer hyperbolsk cosekans for en vinkel"}, "DECIMAL": {"a": "(tall; basis)", "d": "Konverterer en tekstrepresentasjon av et tall i en gitt basis til et desimaltall"}, "DEGREES": {"a": "(vinkel)", "d": "Konverterer radianer til grader"}, "ECMA.CEILING": {"a": "(tall; gjeldende_multiplum)", "d": "Runder av et tall oppover til nærmeste multiplum av en faktor"}, "EVEN": {"a": "(tall)", "d": "Runder av et positivt tall oppover og et negativt tall nedover til nærmeste heltall som er et partall"}, "EXP": {"a": "(tall)", "d": "Returnerer e opphøyd i en potens du angir"}, "FACT": {"a": "(tall)", "d": "Returnerer fakultet til et tall, med andre ord produktet av 1*2*3*...* Tall"}, "FACTDOUBLE": {"a": "(tall)", "d": "Returnerer et talls doble fakultet"}, "FLOOR": {"a": "(tall; gjeldende_multiplum)", "d": "Runder av et tall nedover til nærmeste signifikante multiplum av en faktor"}, "FLOOR.PRECISE": {"a": "(tall; [gjeldende_multiplum])", "d": "Returnerer et tall som er avrundet ned til nærmeste heltall eller til nærmeste multiplum av gjeldende_multiplum"}, "FLOOR.MATH": {"a": "(tall; [gjeldende_multiplum]; [modus])", "d": "Runder av et tall nedover til nærmeste heltall eller til nærmeste signifikante multiplum av en faktor"}, "GCD": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer den største felles divisor"}, "INT": {"a": "(tall)", "d": "Runder av et tall nedover til nærmeste heltall"}, "ISO.CEILING": {"a": "(tall; [gjeldende_multiplum])", "d": "Returnerer et tall som er avrundet opp til nærmeste heltall, eller til nærmeste gjeldende multiplum. <PERSON><PERSON><PERSON> tallets fortegn, blir tallet avrundet opp. Men hvis tallet eller gjeldende multiplum er null, returneres null."}, "LCM": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer minste felles multiplum"}, "LN": {"a": "(tall)", "d": "Returnerer den naturlige logaritmen for et tall"}, "LOG": {"a": "(tall; [grun<PERSON><PERSON>])", "d": "Returnerer logaritmen til et tall med det grunntallet du angir"}, "LOG10": {"a": "(tall)", "d": "Returnerer logaritmen med grunntall 10 for et tall"}, "MDETERM": {"a": "(matrise)", "d": "Returnerer matrisedeterminanten til en matrise"}, "MINVERSE": {"a": "(matrise)", "d": "Returnerer den inverse matrisen til matrisen som er lagret i en matrise"}, "MMULT": {"a": "(matrise1; matrise2)", "d": "Returnerer matriseproduktet av to matriser, en matrise med samme antall rader som matrise1 og kolonner som matrise2"}, "MOD": {"a": "(tall; divisor)", "d": "Returnerer resten når et tall divideres med en divisor"}, "MROUND": {"a": "(tall; multiplum)", "d": "Returnerer et tall avrundet til det ønskede multiplum"}, "MULTINOMIAL": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer polynomet til et sett med tall"}, "MUNIT": {"a": "(dimensjon)", "d": "Returnerer enhetsmatrisen for den angitte dimensjonen"}, "ODD": {"a": "(tall)", "d": "Runder av et positivt tall oppover og et negativt tall nedover til nærmeste heltall som er et oddetall"}, "PI": {"a": "()", "d": "Returnerer verdien av <PERSON>, 3,14159265358979, med 15 desimalers nøyaktighet"}, "POWER": {"a": "(tall; potens)", "d": "Returnerer resultatet av et tall opphøyd i en potens"}, "PRODUCT": {"a": "(tall1; [tall2]; ...)", "d": "Multiplis<PERSON> alle tall som gis som argumenter"}, "QUOTIENT": {"a": "(teller; nevner)", "d": "Returnerer heltallsdelen av en divisjon"}, "RADIANS": {"a": "(vinkel_i_grader)", "d": "Konverterer grader til radianer"}, "RAND": {"a": "()", "d": "Returnerer et tilfeldig tall som er lik eller større enn 0 og mindre enn 1 (endres ved omberegning)"}, "RANDARRAY": {"a": "([rader]; [kolonner]; [minimum]; [maksimum]; [heltall])", "d": "Returnerer en matrise med tilfeldige tall"}, "RANDBETWEEN": {"a": "(bunn; topp)", "d": "Returnerer et tilfeldig tall mellom tallene du angir"}, "ROMAN": {"a": "(tall; [form])", "d": "Konverterer et arabertall til et romertall, som tekst"}, "ROUND": {"a": "(tall; antall_sifre)", "d": "Runder av et tall til et angitt antall sifre"}, "ROUNDDOWN": {"a": "(tall; antall_sifre)", "d": "Runder av et tall nedover mot null"}, "ROUNDUP": {"a": "(tall; antall_sifre)", "d": "Runder av et tall oppover, bort fra null"}, "SEC": {"a": "(tall)", "d": "Returnerer sekans for en vinkel"}, "SECH": {"a": "(tall)", "d": "Returnerer hyperbolsk sekans for en vinkel"}, "SERIESSUM": {"a": "(x; n; m; koeffisienter)", "d": "Returnerer summen av en geometrisk rekke, basert på formelen"}, "SIGN": {"a": "(tall)", "d": "Returnerer fortegnet for et tall: 1 hvis tallet er er positivt, 0 hvis tallet er null, og -1 hvis tallet er negativt"}, "SIN": {"a": "(tall)", "d": "Returnerer sinus for en vinkel"}, "SINH": {"a": "(tall)", "d": "Returnerer den hyperbolske sinus til et tall"}, "SQRT": {"a": "(tall)", "d": "Returnerer kvadratroten av et tall"}, "SQRTPI": {"a": "(tall)", "d": "Returnerer k<PERSON><PERSON><PERSON><PERSON> av (tall * pi)"}, "SUBTOTAL": {"a": "(<PERSON><PERSON><PERSON>; ref1; ...)", "d": "Returnerer en delsum fra en liste eller database"}, "SUM": {"a": "(tall1; [tall2]; ...)", "d": "<PERSON>er alle tallene i et celleområde"}, "SUMIF": {"a": "(områ<PERSON>; vilkår; [summeringsområde])", "d": "<PERSON>er cellene som tilfredsstiller en gitt betingelse eller et gitt vilkår"}, "SUMIFS": {"a": "(summeringsområde; kriterieområde; kriterium; ...)", "d": "<PERSON><PERSON> sammen cellene som angis av et gitt sett med vilkår eller kriterier"}, "SUMPRODUCT": {"a": "(matrise1; [matrise2]; [matrise3]; ...)", "d": "Returnerer summen av produktene til tilsvarende områder eller matriser"}, "SUMSQ": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer summen av de kvadrerte argumentene. Argumentene kan være tall eller matriser, navn eller referanser til celler som inneholder tall"}, "SUMX2MY2": {"a": "(matrise_x; matrise_y)", "d": "<PERSON>er differansen mellom kvadratene av to tilsvarende områder eller matriser"}, "SUMX2PY2": {"a": "(matrise_x; matrise_y)", "d": "Returnerer totalsummen av summene av kvadratene for tall i to tilsvarende områder eller matriser"}, "SUMXMY2": {"a": "(matrise_x; matrise_y)", "d": "Summerer kvadratene av differansene mellom to tilsvarende områder eller matriser"}, "TAN": {"a": "(tall)", "d": "Returnerer tangens for en vinkel"}, "TANH": {"a": "(tall)", "d": "Returnerer den hyperbolske tangens til et tall"}, "TRUNC": {"a": "(tall; [antall_sifre])", "d": "Avrunder et tall nedover ved å fjerne alle desimaler over et angitt antall (et positivt tall) eller runder et angitt antall sifre til venstre for kommaet ned til null (et negativt tall)"}, "ADDRESS": {"a": "(rad_nr; kolonne_nr; [abs]; [a1]; [regneark])", "d": "Lager en cellereferanse som tekst, når det angis rad- og kolonnenumre"}, "CHOOSE": {"a": "(indeks; verdi1; [verdi2]; ...)", "d": "<PERSON>elger en verdi eller en handling fra en liste med verdier, basert på et indekstall"}, "COLUMN": {"a": "([ref])", "d": "Returnerer kolonnenummeret for en referanse"}, "COLUMNS": {"a": "(matrise)", "d": "Returnerer antall kolonner i en matrise eller referanse"}, "FORMULATEXT": {"a": "(referanse)", "d": "Returnerer en formel som en streng"}, "HLOOKUP": {"a": "(s<PERSON><PERSON><PERSON><PERSON>; matrise; radinde<PERSON>; [omr<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> etter en verdi i den øverste raden i en tabell eller matrise, og returnerer verdien i samme kolonne fra en rad du angir"}, "HYPERLINK": {"a": "(kobling; [egendefinert_navn])", "d": "Lager en snarvei eller et hopp som åpner et dokument som er lagret på harddisken, på en server på nettverket eller på Internett"}, "INDEX": {"a": "(matrise; rad_nr; [kolonne_nr]!ref; rad; [kolonne]; [omr<PERSON><PERSON>])", "d": "Returnerer en verdi eller referanse for cellen i skjæringspunktet av en bestemt rad eller kolonne, i et gitt celleområde"}, "INDIRECT": {"a": "(ref; [a1])", "d": "Returnerer en referanse angitt av en tekststreng"}, "LOOKUP": {"a": "(søkever<PERSON>; søkematrise; [resultatvektor]!søkeverdi; matrise)", "d": "<PERSON><PERSON><PERSON><PERSON> opp en verdi enten fra et enrads- eller enkolonnes-område eller fra en matrise. Angitt for bakoverkompatibilitet"}, "MATCH": {"a": "(søke<PERSON><PERSON>; søkematrise; [type])", "d": "Returnerer den relative posisjonen til et element i en matrise som tilsvarer en angitt verdi i en angitt rekkefølge"}, "OFFSET": {"a": "(ref; rader; kolonner; [hø<PERSON><PERSON>]; [bredde])", "d": "Returnerer en referanse til et område som er et gitt antall rader og kolonner fra en gitt referanse"}, "ROW": {"a": "([ref])", "d": "Returnerer radnummeret for en referanse"}, "ROWS": {"a": "(matrise)", "d": "Returnerer antall rader i en referanse eller en matrise"}, "TRANSPOSE": {"a": "(matrise)", "d": "Konverterer et vertikalt celleområde til et horisontalt celleområde, eller omvendt"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": "Returnerer de unike verdiene fra et område eller en matrise."}, "VLOOKUP": {"a": "(sø<PERSON><PERSON><PERSON>; matrise; kolonneind<PERSON>s; [sø<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> etter en verdi i kolonnen lengst til venstre i en tabell, og returnerer en verdi i samme rad fra en kolonne du angir. Standardinnstilling er at tabellen må være sortert i stigende rekkefølge"}, "XLOOKUP": {"a": "(søkeverdi; søkematrise; returneringsmatrise; [om_ikke_funnet]; [samsvarsmodus]; [søke<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> i et område eller en matrise etter et treff og returnerer det tilsvarende elementet fra et annet område eller en annen matrise. Som standard brukes et eksakt treff"}, "CELL": {"a": "(infotype; [ref])", "d": "Returnerer informasjon om formatering, plassering eller innholdet til en celle"}, "ERROR.TYPE": {"a": "(feil<PERSON><PERSON>)", "d": "Returnerer et tall som svarer til en feilverdi."}, "ISBLANK": {"a": "(verdi)", "d": "Kontrollerer om en referanse er til en tom celle, og returnerer SANN eller USANN"}, "ISERR": {"a": "(verdi)", "d": "Kontrollerer om verdien er en annen feil enn #I/T (ikke tilg<PERSON>), og returnerer SANN eller USANN"}, "ISERROR": {"a": "(verdi)", "d": "Kontrollerer om verdien er en feil, og returnerer SANN eller USANN"}, "ISEVEN": {"a": "(tall)", "d": "Returnerer SANN hvis tallet er et partall"}, "ISFORMULA": {"a": "(referanse)", "d": "Kontrollerer om en referanse er til en celle som inneholder en formel, og returnerer SANN eller USANN"}, "ISLOGICAL": {"a": "(verdi)", "d": "Kontrollerer om en verdi er en logisk verdi (SANN eller USANN), og returnerer SANN eller USANN"}, "ISNA": {"a": "(verdi)", "d": "Kontrollerer om verdien er #I/T (ikke tilg<PERSON>g) og returnerer SANN eller USANN"}, "ISNONTEXT": {"a": "(verdi)", "d": "Kontrollerer om en verdi ikke er tekst (tomme celler er ikke tekst), og returnerer SANN eller USANN"}, "ISNUMBER": {"a": "(verdi)", "d": "Kontrollerer om en verdi er et tall, og returnerer SANN eller USANN"}, "ISODD": {"a": "(tall)", "d": "Returnerer SANN hvis tallet er et oddetall"}, "ISREF": {"a": "(verdi)", "d": "Kontrollerer om verdien er en referanse, og returnerer SANN eller USANN"}, "ISTEXT": {"a": "(verdi)", "d": "Kontrollerer om en verdi er tekst, og returnerer SANN eller USANN"}, "N": {"a": "(verdi)", "d": "Konverterer verdier som ikke er tall, til tall, da<PERSON><PERSON> til serienumre, SANN til 1 og alt annet til 0 (null)"}, "NA": {"a": "()", "d": "Returnerer feilverdien #I/T (ik<PERSON>)"}, "SHEET": {"a": "([verdi])", "d": "Returnerer arknummeret for arket det refereres til"}, "SHEETS": {"a": "([referanse])", "d": "Returnerer antall ark i en referanse"}, "TYPE": {"a": "(verdi)", "d": "Returnerer et heltall som representerer datatypen til en verdi: tall = 1, tekst = 2, logisk verdi =4, feilverdi = 16, matrise = 64, sammensatte data = 128"}, "AND": {"a": "(logisk1; [logisk2]; ...)", "d": "Kontrollerer om alle argumenter er lik SANN, og returnerer SANN hvis alle argumentene er lik SANN"}, "FALSE": {"a": "()", "d": "Returnerer den logiske verdien USANN"}, "IF": {"a": "(logisk_test; [sann]; [usann])", "d": "Kontrollerer om en betingelse er oppfylt, og returnerer en verdi hvis SANN, og en annen verdi hvis USANN"}, "IFS": {"a": "(logisk_test; verdi_hvis_sann; ...)", "d": "Kontrollerer om én eller flere betingelser er oppfylt, og returnerer en verdi som tilsvarer den første betingelsen som er SANN"}, "IFERROR": {"a": "(verdi; verdi_hvis_feil)", "d": "Returnerer verdi_hvis_feil hvis uttrykket er en feil, ellers returneres verdien til selve uttrykket"}, "IFNA": {"a": "(verdi; verdi_hvis_it)", "d": "Returnerer verdien du angir hvis uttrykket løses til #I/T, og ellers returneres resultatet av uttrykket"}, "NOT": {"a": "(logisk)", "d": "<PERSON>rer USANN til SANN eller SANN til USANN"}, "OR": {"a": "(logisk1; [logisk2]; ...)", "d": "Kontrollerer om noen av argumentene er lik SANN, og returnerer SANN eller USANN. Returnerer USANN bare hvis alle argumentene er lik USANN"}, "SWITCH": {"a": "(uttrykk; verdi1; resultat1; [standard_eller_verdi2]; [resultat2]; ...)", "d": "Evaluerer et uttrykk mot en liste med verdier og returnerer resultatet som tilsvarer den første samsvarende verdien. Hvis det ikke er noen samsvarende verdier, returneres en valgfri standardverdi"}, "TRUE": {"a": "()", "d": "Returnerer den logiske verdien SANN"}, "XOR": {"a": "(logisk1; [logisk2]; ...)", "d": "Returnerer et \"Utelukkende eller\" av alle argumenter"}, "TEXTBEFORE": {"a": "(tekst, skilletegn, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnerer tekst som er før tegnskilletegn."}, "TEXTAFTER": {"a": "(tekst, skilletegn, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnerer tekst som er etter skilletegn."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Deler tekst inn i rader eller kolonner ved hjelp av skilletegn."}, "WRAPROWS": {"a": "(vektor, wrap_count, [pad_with])", "d": "B<PERSON>ter en rad- eller kolonnevektor etter et bestemt antall verdier."}, "VSTACK": {"a": "(matrise1, [matrise2], ...)", "d": "<PERSON><PERSON><PERSON> matriser loddrett i én matrise."}, "HSTACK": {"a": "(matrise1, [matrise2], ...)", "d": "<PERSON><PERSON><PERSON> matriser van<PERSON>t i én matrise."}, "CHOOSEROWS": {"a": "(matrise, row_num1, [row_num2], ...)", "d": "Returnerer rader fra en matrise eller referanse."}, "CHOOSECOLS": {"a": "(matrise, col_num1, [col_num2], ...)", "d": "Returnerer kolonner fra en matrise eller referanse."}, "TOCOL": {"a": "(matrise, [ignorer], [scan_by_column])", "d": " Returnerer matrisen som én kolonne."}, "TOROW": {"a": "(matrise, [ignorer], [skann_etter_kolonne])", "d": "Returnerer matrisen som én rad."}, "WRAPCOLS": {"a": "(vektor, wrap_count, [pad_with])", "d": "B<PERSON>ter en rad- eller kolonnevektor etter et bestemt antall verdier."}, "TAKE": {"a": "(matrise, rader, [kolonner])", "d": "Returnerer rader eller kolonner fra matrisestart eller -slutt."}, "DROP": {"a": "(matrise, rader, [kolonner])", "d": "Sletter rader eller kolonner fra matrisestart eller -slutt."}}
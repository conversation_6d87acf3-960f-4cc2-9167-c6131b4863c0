{"DATE": {"a": "(år; måned; dag)", "d": "Returnerer det tal, der repræsenterer datoen i dato- og klokkeslætskoden"}, "DATEDIF": {"a": "(startdato; slutdato; enhed)", "d": "<PERSON><PERSON><PERSON><PERSON> antallet af dage, måneder eller år mellem to datoer"}, "DATEVALUE": {"a": "(datotekst)", "d": "Konverterer en dato i form af tekst til et tal, der repræsenterer datoen i dato/klokkeslætskoden"}, "DAY": {"a": "(serienr)", "d": "Returnerer dagen i måneden, et tal mellem 1 og 31."}, "DAYS": {"a": "(slutdato; startdato)", "d": "Returnerer antal dage mellem de to datoer."}, "DAYS360": {"a": "(startdato; slutdato; [metode])", "d": "<PERSON><PERSON><PERSON><PERSON> antallet af dage mellem to datoer på baggrund af et år på 360 dage (12 måneder à 30 dage)"}, "EDATE": {"a": "(<PERSON><PERSON><PERSON>; må<PERSON>er)", "d": "Returnerer serienum<PERSON>t for da<PERSON><PERSON>, der er det angivne antal måneder før eller efter startdatoen"}, "EOMONTH": {"a": "(<PERSON><PERSON><PERSON>; må<PERSON>er)", "d": "Returnerer serienummeret på den sidste dag i måneden, før eller efter et specificeret antal måneder"}, "HOUR": {"a": "(serienr)", "d": "Returnerer timen som et tal mellem 0 (24:00) og 23 (23:00)."}, "ISOWEEKNUM": {"a": "(dato)", "d": "Returnerer tallet for ISO-ugenummeret i året for en given dato"}, "MINUTE": {"a": "(serienr)", "d": "Returnerer minuttet, et tal mellem 0 og 59."}, "MONTH": {"a": "(serienr)", "d": "Returnerer m<PERSON><PERSON><PERSON>, et tal mellem 1 (januar) og 12 (december)."}, "NETWORKDAYS": {"a": "(startdato; slutdato; [feriedage])", "d": "Returnerer antal hele arbejdsdage mellem to datoer"}, "NETWORKDAYS.INTL": {"a": "(startdato; slutdato; [weekend]; [feriedage])", "d": "Returnerer antallet af hele arbejdsdage mellem to datoer med brugerdefinerede weekendparametre"}, "NOW": {"a": "()", "d": "Returnerer den aktuelle dato og det aktuelle klokkeslæt formateret som dato og klokkeslæt."}, "SECOND": {"a": "(serienr)", "d": "Returnerer sekundet, et tal mellem 0 og 59."}, "TIME": {"a": "(time; minut; sekund)", "d": "<PERSON>n<PERSON><PERSON> timer, minutter og sekunder angivet som tal i et serienummer, der er formateret med et klokkeslætsformat"}, "TIMEVALUE": {"a": "(tid)", "d": "Konverterer et klokkeslæt i form af tekst til et serienummer for et klokkeslæt, et tal mellem 0 (12:00:00 AM) og 0,999988426 (11:59:59 PM). Formatér tallet med et klokkeslætsformat efter angivelse af formlen"}, "TODAY": {"a": "()", "d": "Returnerer dags dato formateret som en dato."}, "WEEKDAY": {"a": "(serienr; [type])", "d": "Returnerer et tal mellem 1 og 7, som repræsenterer ugedagen i datoen."}, "WEEKNUM": {"a": "(serienr; [returtype])", "d": "Konverterer ugenummeret i året"}, "WORKDAY": {"a": "(startdato; dage; [feriedage])", "d": "Returnerer det serielle datotal for dagen før eller efter et specifikt antal arbejdsdage"}, "WORKDAY.INTL": {"a": "(startdato; dage; [weekend]; [feriedage])", "d": "Returnerer det se<PERSON>le da<PERSON><PERSON> for dagen før eller efter det angivne antal arbejdsdage med brugerdefinerede  weekendparametre"}, "YEAR": {"a": "(serienr)", "d": "Returnerer året i en dato, et heltal mellem 1900 og 9999."}, "YEARFRAC": {"a": "(startdato; slutdato; [datotype])", "d": "Returnerer <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, der repræsenterer antal hele dage mellem startdato og slutdato"}, "BESSELI": {"a": "(x; n)", "d": "Returnerer den modificerede Bessel-funktion In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Returner <PERSON><PERSON>-funktionen Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Returnerer den modificerede Bessel-funktion Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Returner <PERSON><PERSON>-funk<PERSON><PERSON> Yn(x)"}, "BIN2DEC": {"a": "(tal)", "d": "Konverterer et binært tal til et decimaltal"}, "BIN2HEX": {"a": "(tal; [pladser])", "d": "Konvertér et binært tal til et hexadecimaltal"}, "BIN2OCT": {"a": "(tal; [pladser])", "d": "Konvertér et binært tal til et oktaltal"}, "BITAND": {"a": "(tal1; tal2)", "d": "Returnerer et bitbaseret 'Og' af to tal"}, "BITLSHIFT": {"a": "(tal; forskydning)", "d": "Returnerer et tal forskudt til venstre med forskydning bit"}, "BITOR": {"a": "(tal1; tal2)", "d": "Returnerer et bitbaseret 'Eller' af to tal"}, "BITRSHIFT": {"a": "(tal; forskydning)", "d": "Returnerer et tal forskudt til højre med forskydning bit"}, "BITXOR": {"a": "(tal1; tal2)", "d": "Returnerer et bitbaseret 'Eksklusivt eller' af to tal"}, "COMPLEX": {"a": "(reel_koefficient; imag_koefficient; [suffiks])", "d": "Konverterer reelle og imaginære koefficienter til komplekse tal"}, "CONVERT": {"a": "(tal; fra_enhed; til_enhed)", "d": "Konverterer et tal fra en måleenhed til en anden"}, "DEC2BIN": {"a": "(tal; [pladser])", "d": "Konverterer et decimaltal til et binært tal"}, "DEC2HEX": {"a": "(tal; [pladser])", "d": "Konverterer et decimaltal til et hexadecimaltal"}, "DEC2OCT": {"a": "(tal; [pladser])", "d": "Konverterer et decimaltal til et oktaltal"}, "DELTA": {"a": "(tal1; [tal2])", "d": "<PERSON><PERSON><PERSON><PERSON>, om to værdier er ens"}, "ERF": {"a": "(nedre_grænse; [øvre_grænse])", "d": "Returnerer fejlfunktionen"}, "ERF.PRECISE": {"a": "(X)", "d": "Returnerer fejlfunktionen"}, "ERFC": {"a": "(x)", "d": "Returnerer den komplementære fejlfunktion"}, "ERFC.PRECISE": {"a": "(X)", "d": "Returnerer den komplementære fejlfunktion"}, "GESTEP": {"a": "(tal; [trin])", "d": "<PERSON><PERSON><PERSON><PERSON>, om et tal er større end en tærskelværdi"}, "HEX2BIN": {"a": "(tal; [pladser])", "d": "Konverterer et hexadecimaltal til et binært tal"}, "HEX2DEC": {"a": "(tal)", "d": "Konverterer et hexadecimaltal til et decimaltal"}, "HEX2OCT": {"a": "(tal; [pladser])", "d": "Konverterer et hexadecimaltal til et oktaltal"}, "IMABS": {"a": "(ital)", "d": "Returnerer den absolutte værdi (modulus) af et komplekst tal"}, "IMAGINARY": {"a": "(ital)", "d": "Returnerer den imaginære koefficient af et komplekst tal"}, "IMARGUMENT": {"a": "(ital)", "d": "Returnerer argumentet q udtrykt i radianer"}, "IMCONJUGATE": {"a": "(ital)", "d": "Returnerer den komplekst konjugerede af et komplekst tal"}, "IMCOS": {"a": "(ital)", "d": "Returnerer et komplekst tals cosinus"}, "IMCOSH": {"a": "(ital)", "d": "Returnerer den hyperbolske cosinus af et komplekst tal"}, "IMCOT": {"a": "(ital)", "d": "Returnerer cotangens af et komplekst tal"}, "IMCSC": {"a": "(ital)", "d": "Returnerer cosekanten af et komplekst tal"}, "IMCSCH": {"a": "(ital)", "d": "Returnerer den hyperbolske cosekant af et komplekst tal"}, "IMDIV": {"a": "(ital1; ital2)", "d": "Returnerer kvo<PERSON>ten af to komplekse tal"}, "IMEXP": {"a": "(ital)", "d": "Returnerer et komplekst tals eksponentialfunktion"}, "IMLN": {"a": "(ital)", "d": "Returnerer et komplekst tals naturlige logaritme"}, "IMLOG10": {"a": "(ital)", "d": "Returnerer et komplekst tals 10-tals logaritme"}, "IMLOG2": {"a": "(ital)", "d": "Returnerer et komplekst tals 2-tals logaritme"}, "IMPOWER": {"a": "(ital; tal)", "d": "Returnerer et komplekst tal opløftet i en heltalspotens"}, "IMPRODUCT": {"a": "(ital1; [ital2]; ...)", "d": "Returnerer produktet af 1 til 255 komplekse tal"}, "IMREAL": {"a": "(ital)", "d": "Returnerer den reelle koefficient af et komplekst tal"}, "IMSEC": {"a": "(ital)", "d": "Returnerer sekanten af et komplekst tal"}, "IMSECH": {"a": "(ital)", "d": "Returnerer den hyperbolske sekant af et komplekst tal"}, "IMSIN": {"a": "(ital)", "d": "Returnerer et komplekst tals sinus"}, "IMSINH": {"a": "(ital)", "d": "Returnerer den hyperbolske sinus af et komplekst tal"}, "IMSQRT": {"a": "(ital)", "d": "Returnerer kvadratroden af et komplekst tal"}, "IMSUB": {"a": "(ital1; ital2)", "d": "Returnerer for<PERSON><PERSON> mellem 2 komplekse tal"}, "IMSUM": {"a": "(ital1; [ital2]; ...)", "d": "Returnerer summen af komplekse tal"}, "IMTAN": {"a": "(ital)", "d": "Returnerer tangens af et komplekst tal"}, "OCT2BIN": {"a": "(tal; [pladser])", "d": "Konverterer et oktaltal til et binært tal"}, "OCT2DEC": {"a": "(tal)", "d": "Konverterer et oktaltal til et decimaltal"}, "OCT2HEX": {"a": "(tal; [pladser])", "d": "Konverterer et oktaltal til et hexadecimaltal"}, "DAVERAGE": {"a": "(database; felt; kriterier)", "d": "Be<PERSON>gner gennemsnittet af værdierne i en kolonne på en liste eller i en database, der svarer til de betingelser, du angiver"}, "DCOUNT": {"a": "(database; felt; kriterier)", "d": "<PERSON><PERSON><PERSON> de <PERSON>, der indeholder tal, i feltet (kolonnen) med dokumenter i den database, der svarer til de angivne kriterier"}, "DCOUNTA": {"a": "(database; felt; kriterier)", "d": "<PERSON><PERSON>ller udfyldte celler i feltet (kolonnen) med poster i den database, der svarer til de kriterier, du angiver"}, "DGET": {"a": "(database; felt; kriterier)", "d": "Uddrager en enkelt post fra en database, der opfylder de angivne betingelser"}, "DMAX": {"a": "(database; felt; kriterier)", "d": "Returnerer den største værdi i feltet (kolonnen) med dokumenter i den database, der svarer til de kriterier, du angiver"}, "DMIN": {"a": "(database; felt; kriterier)", "d": "Returnerer den mindste værdi blandt markerede databaseposter, der svarer til de kriterier, du angiver"}, "DPRODUCT": {"a": "(database; felt; kriterier)", "d": "Multiplicerer værdierne i feltet (kolonnen) med poster i databasen, der opfylder de betingelser, du har angivet"}, "DSTDEV": {"a": "(database; felt; kriterier)", "d": "Beregner et skøn over standardafvigelsen baseret på en stikprøve af markerede databaseposter"}, "DSTDEVP": {"a": "(database; felt; kriterier)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>sen baseret på hele populationen af markerede databaseposter"}, "DSUM": {"a": "(database; felt; kriterier)", "d": "<PERSON><PERSON><PERSON> de tal i feltet (kolonnen) med poster i databasen, der opfylder de angivne betingelser sammen"}, "DVAR": {"a": "(database; felt; kriterier)", "d": "<PERSON><PERSON><PERSON><PERSON> et skøn over variansen baseret på en stikprøve af markerede databaseposter"}, "DVARP": {"a": "(database; felt; kriterier)", "d": "Beregner varians baseret på hele populationen af markerede databaseposter"}, "CHAR": {"a": "(tal)", "d": "Returnerer det tegn fra computerens tegnsæt, som kodenummeret angiver"}, "CLEAN": {"a": "(tekst)", "d": "<PERSON><PERSON><PERSON> alle tegn, der ikke kan udskrives, fra tekst"}, "CODE": {"a": "(tekst)", "d": "Returnerer en numerisk kode fra computerens tegnsæt for det første tegn i en tekststreng"}, "CONCATENATE": {"a": "(tekst1; [tekst2]; ...)", "d": "Sammenkæder flere tekststrenge til én tekststreng"}, "CONCAT": {"a": "(tekst1; ...)", "d": "Sammenkæder en liste eller et område af tekststrenge"}, "DOLLAR": {"a": "(tal; [decimaler])", "d": "Konverterer et tal til tekst vha. valutaformat"}, "EXACT": {"a": "(tekst1; tekst2)", "d": "<PERSON><PERSON><PERSON><PERSON>, om to tekststrenge er helt identiske, og returnerer SAND eller FALSK. EKSAKT skelner mellem store og små bogstaver"}, "FIND": {"a": "(find_tekst; i_tekst; [start_ved])", "d": "Returnerer startpositionen for en tekststreng i en anden tekststreng. FIND skelner mellem store og små bogstaver"}, "FINDB": {"a": "(find_tekst; i_tekst; [start_ved])", "d": "Finder én tekststreng inden i en anden tekststreng og returnerer nummeret på den første strengs startposition fra det første tegn i den anden tekststreng, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk"}, "FIXED": {"a": "(tal; [decimaler]; [ingen_punktummer])", "d": "<PERSON><PERSON><PERSON><PERSON> et tal til det angivne antal decimaler og returnerer resultatet som tekst med eller uden kommaer"}, "LEFT": {"a": "(tekst; [antal_tegn])", "d": "Returnerer det angivne antal tegn fra begyndelsen af en tekststreng"}, "LEFTB": {"a": "(tekst; [antal_tegn])", "d": "Returnerer det eller de første tegn i en tekststreng baseret på det antal byte, du angiver, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk"}, "LEN": {"a": "(tekst)", "d": "Returnerer antallet af tegn i en tekststreng"}, "LENB": {"a": "(tekst)", "d": "Returnerer det antal byte, der bruges til at repræsentere tegnene i en tekststreng, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk"}, "LOWER": {"a": "(tekst)", "d": "Konverterer tekst til små bogstaver"}, "MID": {"a": "(tekst; start_ved; antal_tegn)", "d": "Returnerer tegnene fra midten af en tekststreng ved angivelse af startposition og længde"}, "MIDB": {"a": "(tekst; start_ved; antal_tegn)", "d": "Returnerer et bestemt antal tegn fra en tekststreng fra og med den startposition, du angiver, og på basis af det antal byte, du angiver, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk"}, "NUMBERVALUE": {"a": "(tekst; [decimaltegn]; [gruppeseparator])", "d": "Konverterer tekst til tal ifølge landestandarden"}, "PROPER": {"a": "(tekst)", "d": "Konverterer første bogstav i hvert ord til stort og resten af teksten til små bogstaver"}, "REPLACE": {"a": "(gammel_tekst; start_ved; antal_tegn; ny_tekst)", "d": "Erstatter en del af en tekststreng med en anden tekststreng"}, "REPLACEB": {"a": "(gammel_tekst; start_ved; antal_tegn; ny_tekst)", "d": "Erstatter en del af en tekststreng med en anden tekststreng baseret på det antal byte, du angiver, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk"}, "REPT": {"a": "(tekst; antal_gange)", "d": "Gentager tekst et givet antal gange. Brug GENTAG til at fylde en celle med et antal forekomster af en tekststreng"}, "RIGHT": {"a": "(tekst; [antal_tegn])", "d": "Returnerer det angivne antal tegn fra slutningen af en tekststreng"}, "RIGHTB": {"a": "(tekst; [antal_tegn])", "d": "Returnerer det eller de sidste tegn i en tekststreng baseret på det antal bytes, du angiver, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk"}, "SEARCH": {"a": "(find_tekst; i_tekst; [start_ved])", "d": "Returnerer et tal, der repræsenterer placeringen af et tegn eller en tekststreng i en anden tekststreng, læst fra venstre mod højre (skelner ikke mellem store og små bogstaver)"}, "SEARCHB": {"a": "(find_tekst; i_tekst; [start_ved])", "d": "Finder én tekststreng inden i en anden tekststreng og returnerer nummeret på den første strengs startposition fra det første tegn i den anden tekststreng, er beregnet til brug sammen med sprog, der anvender dobbelt-byte tegnsæt (DBCS) - japansk, kinesisk og koreansk"}, "SUBSTITUTE": {"a": "(tekst; gammel_tekst; ny_tekst; [forekomst])", "d": "Erstatter gammel tekst med ny tekst i en tekststreng"}, "T": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er tekst, og returnerer teksten, hvis dette er tilfældet, eller returnerer dobbelte anførselstegn (en tom streng), hvis det ikke er tilfældet"}, "TEXT": {"a": "(værdi; format)", "d": "Konverterer en værdi til tekst i et specifikt talformat"}, "TEXTJOIN": {"a": "(skilletegn; ignorer_tomme; tekst1; ...)", "d": "Sammenkæder en liste eller et område af tekststrenge ved hjælp af en afgrænser"}, "TRIM": {"a": "(tekst)", "d": "Fjerner alle mellemrum fra en tekststreng, undtagen enkeltmellemrum mellem ord"}, "UNICHAR": {"a": "(tal)", "d": "Returnerer det Unicode-tegn, der refereres til med den givne numeriske værdi"}, "UNICODE": {"a": "(tekst)", "d": "Returnerer det tal (tegnværdi), der svarer til det første tegn i teksten"}, "UPPER": {"a": "(tekst)", "d": "Konverterer tekst til store bogstaver"}, "VALUE": {"a": "(tekst)", "d": "Konverterer en tekststreng til et tal"}, "AVEDEV": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den gennemsnitlige absolutte afvigelse af datapunkter fra deres middelværdi. Argumenter kan være tal, navne, matrixer eller referencer, der indeholder tal"}, "AVERAGE": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer middelvæ<PERSON>en af argumenterne, som kan være tal, nav<PERSON>, matrixer eller referencer, der indeholder tal"}, "AVERAGEA": {"a": "(værdi1; [værdi2]; ...)", "d": "Returnerer middelværdien af argumenterne, hvor tekst og FALSK evalueres som 0, og SAND evalueres som 1. Argumenter kan være tal, navne, matrixer eller referencer"}, "AVERAGEIF": {"a": "(o<PERSON><PERSON><PERSON><PERSON>; kriterier; [middel<PERSON><PERSON><PERSON><PERSON>])", "d": "Finder middel<PERSON><PERSON><PERSON>en af cellerne ud fra en given betingelse eller et givet kriterium"}, "AVERAGEIFS": {"a": "(mid<PERSON><PERSON><PERSON><PERSON><PERSON>; kriterieområde; kriterier; ...)", "d": "<PERSON>er middelvæ<PERSON>en af de celler, der er angivet med et sæt betingelser eller kriterier"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "Returnerer fordelingsfunktionen for betafordelingen"}, "BETAINV": {"a": "(sandsynlighed; alpha; beta; [A]; [B])", "d": "Returnerer den inverse fordelingsfunktion for betafordelingen (BETADIST)"}, "BETA.DIST": {"a": "(x; alpha; beta; kumulativ; [A]; [B])", "d": "Returnerer fordelingsfunktionen for betafordelingen"}, "BETA.INV": {"a": "(sandsynlighed; alpha; beta; [A]; [B])", "d": "Returnerer den inverse fordelingsfunktion for betafordelingen (BETA.FORDELING)"}, "BINOMDIST": {"a": "(tal_s; forsøg; sandsynlighed_s; kumulativ)", "d": "Returnerer punktsandsynligheden for binomialfordelingen"}, "BINOM.DIST": {"a": "(tal_s; forsøg; sandsynligheder; akkumuleret)", "d": "Returnerer punktsandsynligheden for binomialfordelingen"}, "BINOM.DIST.RANGE": {"a": "(forsøg; sandsynlighed_s; tal_s; [tal_s2])", "d": "Returnerer sandsynlighed for et forsøgsresultat ved hjælp af binomial fordeling"}, "BINOM.INV": {"a": "(fors<PERSON>g; sandsynligheder; alpha)", "d": "Returnerer den mindste værdi, for hvilken den akkumulerede binomialfordeling er større end eller lig med en kriterieværdi"}, "CHIDIST": {"a": "(x; frihedsgrader)", "d": "Returnerer den højresidede sandsynlighed for en chi2-fordeling"}, "CHIINV": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den inverse af den højresidede sandsynlighed for chi2-fordelingen"}, "CHITEST": {"a": "(observeret_værdi; forventet_værdi)", "d": "Returnerer testen for u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dvs. værdien fra chi2-fordelingen for den statistiske funktion og det passende antal frihedsgrader"}, "CHISQ.DIST": {"a": "(x; fri<PERSON>sgrader; kumulativ)", "d": "Returnerer den venstresidede sandsynlighed for en chi2-fordeling"}, "CHISQ.DIST.RT": {"a": "(x; frihedsgrader)", "d": "Returnerer frak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for en chi2-fordeling"}, "CHISQ.INV": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den inverse venstresidede sandsynlighed for en chi2-fordeling"}, "CHISQ.INV.RT": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den inverse fraktilsandsynlighed for chi2-fordelingen"}, "CHISQ.TEST": {"a": "(observeret_værdi; forventet_værdi)", "d": "Returnerer testen for u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dvs. værdien fra chi2- fordelingen for den statistiske og den passende uafhængighed"}, "CONFIDENCE": {"a": "(alpha; standardafv; størrelse)", "d": "Returnerer tillidsintervallet for middelværdien i en population"}, "CONFIDENCE.NORM": {"a": "(alpha; standardafv; størrelse)", "d": "Returnerer tillidsintervallet for middelværdien i en population ved hjælp af en normalfordeling"}, "CONFIDENCE.T": {"a": "(alpha; standardafv; størrelse)", "d": "Returnerer tillidsintervallet for middelværdien i en population ved hjælp af t-fordelingen for en student"}, "CORREL": {"a": "(vektor1; vektor2)", "d": "Returnerer korrelationskoefficienten mellem to datasæt"}, "COUNT": {"a": "(værdi1; [værdi2]; ...)", "d": "<PERSON><PERSON><PERSON> antallet af celler i et område, der indeholder tal"}, "COUNTA": {"a": "(værdi1; [værdi2]; ...)", "d": "<PERSON><PERSON><PERSON> antallet af celler i et område der ikke er tomme"}, "COUNTBLANK": {"a": "(område)", "d": "<PERSON><PERSON><PERSON> antallet af tomme celler i et angivet område"}, "COUNTIF": {"a": "(omr<PERSON><PERSON>; kriterier)", "d": "<PERSON><PERSON><PERSON> antallet af celler i et område, der svarer til de givne betinge<PERSON>er"}, "COUNTIFS": {"a": "(kriterie<PERSON><PERSON><PERSON><PERSON>; kriterier; ...)", "d": "<PERSON><PERSON><PERSON> antallet af celler i et givet sæt betingelser eller kriterier"}, "COVAR": {"a": "(vektor1; vektor2)", "d": "<PERSON><PERSON><PERSON><PERSON>, dvs. gennemsnittet af produktet af afvigelser for hvert datapar i to datasæt"}, "COVARIANCE.P": {"a": "(matrix1; matrix2)", "d": "Returnerer kovariansen i populationen, dvs. gennemsnittet af produktet af standardafvigelsen for hvert datapar i to datasæt"}, "COVARIANCE.S": {"a": "(matrix1; matrix2)", "d": "Returnerer kovariansen i stikprøven, dvs. gennemsnittet af produktet af afvigelsen for hvert datapar i to datasæt"}, "CRITBINOM": {"a": "(forsøg; sandsynlighed_s; alpha)", "d": "Returnerer den mindste værdi, som det gælder for, at den kumulative binomiale fordeling er større end eller lig med en kriterieværdi"}, "DEVSQ": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer summen af datapunkternes kvadrerede afvigelser fra stikprø<PERSON>s middelværdi"}, "EXPONDIST": {"a": "(x; lambda; kumulativ)", "d": "Returnerer fordelingsfunktionen for eksponentialfordelingen"}, "EXPON.DIST": {"a": "(x; lambda; kumulativ)", "d": "Returnerer eksponentialfordelingen"}, "FDIST": {"a": "(x; frihedsgrader1; frihedsgrader2)", "d": "Returnerer fraktilsandsynligheden (højresidet) for F-fordelingen (afvigelsesgraden) for to datasæt"}, "FINV": {"a": "(sandsynlighed; frihedsgrader1; frihedsgrader2)", "d": "Returnerer den inverse (højresidede) F-fordeling: hvis p = FFORDELING(x;...), så FINV(p;...) = x"}, "FTEST": {"a": "(vektor1; vektor2)", "d": "Returnerer resultatet af en F-test, dvs. den tosidige sandsynlighed for, at varianserne i Vektor1 og Vektor2 ikke er signifikant forskellige"}, "F.DIST": {"a": "(x; frihedsgrader1; frihedsgrader2; kumulativ)", "d": "Returnerer fraktilsandsynligheden for F-fordelingen (afvigelsesgraden) for to datasæt"}, "F.DIST.RT": {"a": "(x; frihedsgrader1; frihedsgrader2)", "d": "Returnerer fraktilsandsynligheden for F-fordelingen (afvigelsesgraden) for to datasæt"}, "F.INV": {"a": "(sandsynlighed; frihedsgrader1; frihedsgrader2)", "d": "Returnerer den inverse fraktilsandsynlighed for F-fordelingen. Hvis p = F.FORDELING(x,...), så er F.FORDELING(p,...) = x"}, "F.INV.RT": {"a": "(sandsynlighed; frihedsgrader1; frihedsgrader2)", "d": "Returnerer den inverse fraktilsandsynlighed for F-fordeling. Hvis p = F.FORDELING.RT(x,...), så er FINV.RT(p,...) = x"}, "F.TEST": {"a": "(matrix1; matrix2)", "d": "Returnerer resultatet af en F-test, dvs. den tosidige sandsynlighed for at varianserne til Matrix1 og Matrix2 ikke er tydeligt forskellige"}, "FISHER": {"a": "(x)", "d": "Returnerer Fisher-<PERSON>en"}, "FISHERINV": {"a": "(y)", "d": "Returnerer den inverse Fisher-transformation: hvis y = FISHER(x), så FISHERINV(y) = x"}, "FORECAST": {"a": "(x; kendte_y'er; kendte_x'er)", "d": "<PERSON><PERSON><PERSON><PERSON>, eller for<PERSON><PERSON> en fremtidig værdi baseret på lineær regression vha. eksisterende værdier"}, "FORECAST.ETS": {"a": "(mål_dato; væ<PERSON><PERSON>; tidslinje; [sæson<PERSON>ving]; [data_completion]; [sammenlægning])", "d": "Returnerer prognoseværdien for en bestemt fremtidig dato vha. en eksponentiel udjævningsmetode."}, "FORECAST.ETS.CONFINT": {"a": "(mål_dato; væ<PERSON><PERSON>; tidslinje; [till<PERSON>_niveau]; [sæ<PERSON><PERSON><PERSON>]; [data_fuldførelse]; [sammenlægning])", "d": "Returnerer et tillidsinterval for prognoseværdien på den angivne måldato."}, "FORECAST.ETS.SEASONALITY": {"a": "(væ<PERSON><PERSON>; tidslinje; [data_completion]; [sammenlægning])", "d": "Returnerer længden på det gentagne mø<PERSON>, som en applikation registrerer for den angivne tidsserie."}, "FORECAST.ETS.STAT": {"a": "(væ<PERSON><PERSON>; tidslinje; statistik_type; [sæsonudsving]; [data_completion]; [sammenlægning])", "d": "Returnerer den ønskede statistik for prognosen."}, "FORECAST.LINEAR": {"a": "(x; kendte_y'er; kendte_x'er)", "d": "<PERSON><PERSON><PERSON><PERSON> eller forudsiger en fremtidig værdi baseret på lineær regression vha. eksisterende værdier"}, "FREQUENCY": {"a": "(datavektor; intervalvektor)", "d": "<PERSON><PERSON><PERSON>r hvor ofte en værdi forekommer indenfor et værdiområde og returnerer en lodret matrix af tal, der har ét element mere end Intervalmatrix"}, "GAMMA": {"a": "(x)", "d": "Returnerer gammafunktionsværdien"}, "GAMMADIST": {"a": "(x; alpha; beta; kumulativ)", "d": "Returnerer gammafordelingen"}, "GAMMA.DIST": {"a": "(x; alpha; beta; kumulativ)", "d": "Returnerer gammafordelingen"}, "GAMMAINV": {"a": "(sandsynlighed; alpha; beta)", "d": "Returnerer den inverse kumulative fordeling for gammafordelingen: hvis p = GAMMAFORDELING(x,...), så GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(sandsynlighed; alpha; beta)", "d": "Returnerer den inverse fordelingsfunktion for gammafordelingen: if p = GAMMA.FORDELING(x,...), then GAMMA.INV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Returnerer den naturlige logaritme til gammafordelingen"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Returnerer den naturlige logaritme til gammafordelingen"}, "GAUSS": {"a": "(x)", "d": "Returnerer 0,5 mindre end fordelingsfunktionen for standardnormalfordelingen"}, "GEOMEAN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den geometriske middelværdi af en matrix eller et område med positive numeriske data"}, "GROWTH": {"a": "(kendte_y'er; [kendte_x'er]; [nye_x'er]; [konstant])", "d": "Returnerer tal i en eksponentiel væksttendens svarende til kendte datapunkter"}, "HARMEAN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den harmoniske middelværdi af et datasæt bestående af positive tal, dvs. det reciprokke tal til middelværdien af de reciprokke værdier"}, "HYPGEOM.DIST": {"a": "(udfald_s; størrelse; population_s; populationsstørrelse; kumulativ)", "d": "Returnerer punktsandsynligheden i en hypergeometrisk fordeling"}, "HYPGEOMDIST": {"a": "(udfald_s; størrelse; population_s; populationsstørrelse)", "d": "Returnerer den hypergeometriske fordeling"}, "INTERCEPT": {"a": "(kendte_y'er; kendte_x'er)", "d": "<PERSON><PERSON><PERSON><PERSON> det <PERSON>, hvor en linje skærer y-aksen ved hjælp af en tilpasset regressionslinje, der er afbildet gennem kendte x-værdier og y-værdier"}, "KURT": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer kurtosisværdien af et datasæt"}, "LARGE": {"a": "(matrix; k)", "d": "Returnerer den k'te-største værdi i et datasæt. For eksempel det femtestørste tal"}, "LINEST": {"a": "(kendte_y'er; [kendte_x'er]; [konstant]; [statistik])", "d": " Returnerer en statistik, som beskriver en lineær tendens, der svarer til kendte datapunkter ved at placere en lige linje vha. de mindste kvadraters metode"}, "LOGEST": {"a": "(kendte_y'er; [kendte_x'er]; [konstant]; [statistik])", "d": "Returnerer en statistik, der beskriver en eksponentialkurve svarende til kendte datapunkter"}, "LOGINV": {"a": "(sandsynlighed; middelværdi; standardafv)", "d": "Returnerer den inverse kumulative fordelingsfunktion for lognormalfordelingen til x, hvor ln(x) er normalfordelt med parametrene Middelværdi og Standardafv"}, "LOGNORM.DIST": {"a": "(x; middelvæ<PERSON>; standardafv; kumulativ)", "d": "Returnerer fordelingsfunktionen for lognormalfordelingen af x, hvor In(x) normalt distribueres med parametrene Middelværdi og Standardafv"}, "LOGNORM.INV": {"a": "(sandsynlighed; middelværdi; standardafv)", "d": "Returnerer den inverse fordelingsfunktion for lognormalfordelingen af x, hvor In(x) normalt distribueres med parametrene Middelværdi og Standardafv"}, "LOGNORMDIST": {"a": "(x; middelværdi; standardafv)", "d": "Returnerer fordelingsfunktionen for lognormalfordelingen af x, hvor ln(x) er normalfordelt med parametrene Middelværdi og Standardafv"}, "MAX": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den største værdi fra et datasæt. Ignorerer logiske værdier og tekst"}, "MAXA": {"a": "(værdi1; [værdi2]; ...)", "d": "Returnerer den største værdi fra et værdisæt. Ignorerer ikke logiske værdier og tekst"}, "MAXIFS": {"a": "(maks_omr<PERSON><PERSON>; kriterieområde; kriterier; ...)", "d": "Returnerer den største værdi blandt celler, der er angivet med et givet sæt betingelser eller kriterier"}, "MEDIAN": {"a": "(tal1; [tal2]; ...)", "d": "Return<PERSON> median<PERSON>, eller den midterste værdi, for det givne talsæt"}, "MIN": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer det mindste tal fra et værdisæt. Ignorerer logiske værdier og tekst"}, "MINA": {"a": "(værdi1; [værdi2]; ...)", "d": "Returnerer den mindste værdi fra et værdisæt. Logiske værdier og tekst ignoreres ikke"}, "MINIFS": {"a": "(min_omr<PERSON><PERSON>; kriterieområde; kriterier; ...)", "d": "Returnerer den mindste værdi blandt celler, der er angivet med et givet sæt betingelser eller kriterier"}, "MODE": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den hyppigst forekommende værdi i en matrix eller et datainterval"}, "MODE.MULT": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer en lodret matrix med de hyppigst forekommende værdier i en matrix eller et datainterval. Brug = TRANSPOSE(MODE.MULT(tal1,tal2,...)) til en vandret matrix"}, "MODE.SNGL": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den hyppigst forekommende værdi i en matrix eller et datainterval"}, "NEGBINOM.DIST": {"a": "(tal_f; tal_s; sandsynlighed_s; kumulativ)", "d": "Returnerer punktsandsynligheden for den negative binomialfordeling, dvs. sandsynligheden for, at der vil være tal_f mislykkede forsøg, før det lykkes i forsøg tal_s med sandsynligheden sandsynlighed_s for, at et forsøg lykkes"}, "NEGBINOMDIST": {"a": "(tal_f; tal_s; sandsynlighed_s)", "d": "Returnerer punktsandsynligheden for den negative binomialfordeling, dvs. sandsynligheden for, at der vil være tal_f mislykkede forsøg før det lykkedes i forsøg tal_s med sandsynligheden sandsynlighed_s for at et forsøg lykkes"}, "NORM.DIST": {"a": "(x; middelvæ<PERSON>; standardafv; kumulativ)", "d": "Returnerer normalfordelingen for den angivne middelværdi og standardafvigelse"}, "NORMDIST": {"a": "(x; middelvæ<PERSON>; standardafv; kumulativ)", "d": "Returnerer normalfordelingen for den angivne middelværdi og standardafvigelse"}, "NORM.INV": {"a": "(sandsynlighed; middelværdi; standardafv)", "d": "Returnerer normalfordelingen for den angivne middelværdi og standardafvigelse"}, "NORMINV": {"a": "(sandsynlighed; middelværdi; standardafv)", "d": "Returnerer den inverse kumulative fordelingsfunktion for normalfordelingen for den angivne middelværdi og standardafvigelse"}, "NORM.S.DIST": {"a": "(z; kumulativ)", "d": "Returnerer fordelingsfunktionen for standardnormalfordelingen (har en middelværdi på nul og en standardafvigelse på en)"}, "NORMSDIST": {"a": "(z)", "d": "Returnerer den kumulative fordeling for standardnormalfordelingen (har en middelværdi på nul og en standardafvigelse på en)"}, "NORM.S.INV": {"a": "(sandsynlighed)", "d": "Returnerer den inverse fordelingsfunktion for standardnormalfordelingen (den har en middelværdi på nul og en standardafvigelse på en)"}, "NORMSINV": {"a": "(sandsynlighed)", "d": "Returnerer den inverse kumulative fordeling for standardnormalfordelingen (den har en middelværdi på nul og en standardafvigelse på en)"}, "PEARSON": {"a": "(matrix1; matrix2)", "d": "Returnerer <PERSON><PERSON>, r"}, "PERCENTILE": {"a": "(vektor; k)", "d": "Returnerer den k'te fraktil for værdier i et interval"}, "PERCENTILE.EXC": {"a": "(matrix; k)", "d": "Returnerer den k'te fraktil for værdier i et interval, hvor k ligger i intervallet fra 0 til 1"}, "PERCENTILE.INC": {"a": "(matrix; k)", "d": "Returnerer den k'te fraktil for værdier i et interval, hvor k ligger i intervallet fra 0 til og med 1"}, "PERCENTRANK": {"a": "(vektor; x; [signifikans])", "d": "Returnerer den procentuelle rang for en given værdi i et datasæt"}, "PERCENTRANK.EXC": {"a": "(matrix; x; [signifikans])", "d": "Returnerer rangen for en værdi i et datasæt som en procentdel (fra 0 til 1) af datasættet"}, "PERCENTRANK.INC": {"a": "(matrix; x; [signifikans])", "d": "Returnerer rangen for en værdi i et datasæt som en procentdel (fra 0 til og med 1) af datasættet"}, "PERMUT": {"a": "(tal; tal_valgt)", "d": "Returnerer antallet af permutationer for et givet antal af objekter, der kan  vælges fra det totale antal objekter"}, "PERMUTATIONA": {"a": "(tal; tal_valgt)", "d": "Returnerer antal permutationer for et givet antal objekter (med gentagelser), der kan vælges ud fra det samlede antal objekter"}, "PHI": {"a": "(x)", "d": "Returnerer værdien af tæthedsfunktionen for en standardnormalfordeling"}, "POISSON": {"a": "(x; mid<PERSON><PERSON><PERSON><PERSON>; kumulativ)", "d": "Returnerer Poisson-fordelingen"}, "POISSON.DIST": {"a": "(x; mid<PERSON><PERSON><PERSON><PERSON>; kumulativ)", "d": "Returnerer Poisson-fordelingen"}, "PROB": {"a": "(x_<PERSON><PERSON><PERSON><PERSON><PERSON>; sandsyn<PERSON>gheder; nedre_grænse; [øvre_grænse])", "d": "Returnerer sand<PERSON><PERSON><PERSON><PERSON><PERSON>n for at værdier i et interval er mellem to græ<PERSON><PERSON> eller lig med en nedre grænse"}, "QUARTILE": {"a": "(vektor; kvart)", "d": "Returnerer kvartilen i et givet datasæt"}, "QUARTILE.INC": {"a": "(matrix; k<PERSON><PERSON>)", "d": "Returnerer kvartilen for et datasæt baseret på fraktilværdier fra 0..1 inklusive"}, "QUARTILE.EXC": {"a": "(matrix; k<PERSON><PERSON>)", "d": "Returnerer kvartilen for et datasæt baseret på fraktilværdier fra 0..1 eksklusive"}, "RANK": {"a": "(tal; reference; [ræk<PERSON><PERSON><PERSON><PERSON>])", "d": "Returnerer rangen for et tal på en liste med tal, dvs. tallets størrelse i forhold til de andre værdier på listen"}, "RANK.AVG": {"a": "(tal; reference; [ræk<PERSON><PERSON><PERSON><PERSON>])", "d": "Returnerer rangen for et tal i en liste med tal. Dets størrel<PERSON> i forhold til andre væ<PERSON>er på listen. <PERSON>vis mere end et tal har den samme rang, returneres den gennemsnitlige rang"}, "RANK.EQ": {"a": "(tal; reference; [ræk<PERSON><PERSON><PERSON><PERSON>])", "d": "Returnerer rangen for et tal i en liste med tal. Dets størrel<PERSON> i forhold til andre værdier på listen. Hvis mere end en værdi har den samme rang, returneres den øverste rang for dette værdi<PERSON>t"}, "RSQ": {"a": "(kendte_y'er; kendte_x'er)", "d": "Returnerer kvadratet på Pearsons korrelationskoefficient gennem de givne datapunkter"}, "SKEW": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer skævheden af en distribution, dvs. en karakteristik af graden af asymmetri for en distribution omkring dens middelværdi"}, "SKEW.P": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer skævheden af en distribution baseret på en population: en karakteristik af graden af asymmetri for en distribution omkring dens middelværdi"}, "SLOPE": {"a": "(kendte_y'er; kendte_x'er)", "d": "Returnerer estimatet på hældningen fra en lineær regressionslinje gennem de givne datapunkter"}, "SMALL": {"a": "(matrix; k)", "d": "Returnerer den k'te-mindste værdi i et datasæt. For eksempel det femtemindste tal"}, "STANDARDIZE": {"a": "(x; middelværdi; standardafv)", "d": "Returnerer en standardiseret værdi fra en distribution, karakteriseret ved en middelværdi og en standardafvigelse"}, "STDEV": {"a": "(tal1; [tal2]; ...)", "d": "Beregner standardafvigelsen på basis af en stikprøve (ignorerer logiske værdier og tekst i stikprøven)"}, "STDEV.P": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>af<PERSON><PERSON>sen baseret på hele populationen, der er givet som argumenter (ignorerer logiske værdier og tekst)"}, "STDEV.S": {"a": "(tal1; [tal2]; ...)", "d": "Estimerer standardafvigelsen baseret på en stikprøve (ignorerer logiske værdier og tekst i stikprøven)"}, "STDEVA": {"a": "(værdi1; [værdi2]; ...)", "d": "Beregner standardafvigelsen på basis af en stikprøve, herunder logiske værdier og tekst. Tekst og den logiske værdi FALSK har værdien 0, og den logiske værdi SAND har værdien 1"}, "STDEVP": {"a": "(tal1; [tal2]; ...)", "d": "Beregner standardafvigelsen på basis af en hel population givet som argumenter (ignorerer logiske værdier og tekst)"}, "STDEVPA": {"a": "(værdi1; [værdi2]; ...)", "d": "Beregner standardafvigelsen på basis af en hel population, herunder logiske værdier og tekst.  Tekst og den logiske værdi FALSK har værdien 0, og den logiske værdi SAND har værdien 1"}, "STEYX": {"a": "(kendte_y'er; kendte_x'er)", "d": "Returnerer standard<PERSON><PERSON><PERSON> på de estimerede y-værdier for hvert x i en regression"}, "TDIST": {"a": "(x; fri<PERSON>sgrader; haler)", "d": "Returnerer Student t-fordelingen"}, "TINV": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den tosidede inverse fordelingsfunktion for Students t-fordeling"}, "T.DIST": {"a": "(x; fri<PERSON>sgrader; kumulativ)", "d": "Returnerer den venstresidede fordelingsfunktion for Students t-fordeling"}, "T.DIST.2T": {"a": "(x; frihedsgrader)", "d": "Returnerer den tosidede fordelingsfunktion for Students t-fordeling"}, "T.DIST.RT": {"a": "(x; frihedsgrader)", "d": "Returnerer den højresidede fordelingsfunktion for Students t-fordeling"}, "T.INV": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den venstresidede inverse fordelingsfunktion for Students t-fordeling"}, "T.INV.2T": {"a": "(sandsynlighed; frihedsgrader)", "d": "Returnerer den tosidede inverse fordelingsfunktion for Students t-fordeling"}, "T.TEST": {"a": "(matrix1; matrix2; haler; type)", "d": "Returnerer den sandsynlighed, der knytter sig til en Students t-test"}, "TREND": {"a": "(kendte_y'er; [kendte_x'er]; [nye_x'er]; [konstant])", "d": "Returnerer værdier i en lineær tendens svarende til kendte datapunkter med brug af de mindste kvadraters metode"}, "TRIMMEAN": {"a": "(vektor; procent)", "d": "Returnerer det trimmede gennemsnit for et datasæt"}, "TTEST": {"a": "(vektor1; vektor2; haler; type)", "d": "Returnerer den sandsynlighed, der knytter sig til en Student t-test"}, "VAR": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON><PERSON>r variansen baseret på en stikprøve (ignorerer logiske værdier og tekst i stikprøven)"}, "VAR.P": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON>gner variansen baseret på hele populationen (ignorerer logiske værdier og tekst i populationen)"}, "VAR.S": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON><PERSON>r variansen baseret på en stikprøve (ignorerer logiske værdier og tekst i stikprøven)"}, "VARA": {"a": "(værdi1; [værdi2]; ...)", "d": "<PERSON><PERSON><PERSON>r variansen baseret på en stikprøve, herunder logiske værdier og tekst. Tekst og den logiske værdi FALSK har værdien 0, og den logiske værdi SAND har værdien 1"}, "VARP": {"a": "(tal1; [tal2]; ...)", "d": "Beregner variansen baseret på en hel population (ignorerer logiske værdier og tekst i populationen)"}, "VARPA": {"a": "(værdi1; [værdi2]; ...)", "d": "Beregner variansen baseret på en hel population,  herunder logiske værdier og tekst. Tekst og den logiske værdi FALSK har værdien 0, og den logiske værdi SAND har værdien 1"}, "WEIBULL": {"a": "(x; alpha; beta; kumulativ)", "d": "Returnerer <PERSON><PERSON>-fordelingen"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; kumulativ)", "d": "Returnerer <PERSON><PERSON>-fordelingen"}, "Z.TEST": {"a": "(matrix; x; [sigma])", "d": "Returnerer den ensidige P-værdi for en z-test"}, "ZTEST": {"a": "(vektor; x; [sigma])", "d": "Returnerer den tosidede P-værdi til en z-test"}, "ACCRINT": {"a": "(udstedelsesdato; første_rente; afregningsdato; rente; nominel; frekvens; [datotype]; [beregningsmetode])", "d": "Returnerer den påløbne rente for et værdipapir med periodisk renteudbetaling."}, "ACCRINTM": {"a": "(udstedelsesdato; afregningsdato; rente; nominel; [datotype])", "d": "Returnerer den påløbne rente for et værdipapir med renteudbetaling ved udløb"}, "AMORDEGRC": {"a": "(kø<PERSON>p<PERSON>; købsdato; første_periode; restværdi; periode; sats; [datotype])", "d": "Returnerer den forholdsmæssige lineære afskrivning af et aktiv for hver regnskabsperiode"}, "AMORLINC": {"a": "(kø<PERSON>p<PERSON>; købsdato; første_periode; restværdi; periode; sats; [datotype])", "d": "Returnerer den forholdsmæssige lineære afskrivning af et aktiv for hver regnskabsperiode"}, "COUPDAYBS": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer antal dage fra starten af kuponperioden til afregningsdatoen"}, "COUPDAYS": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer antal dage i den kuponperiode, der indeholder udløbsdatoen"}, "COUPDAYSNC": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer antal dage fra afregningsdatoen til næste kupondato"}, "COUPNCD": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer den næste kupondato efter afregningsdatoen"}, "COUPNUM": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer antal kuponbetalinger mellem afregnings- og udløbsdatoen"}, "COUPPCD": {"a": "(afregningsdato; udlø<PERSON><PERSON><PERSON>; hyppighed; [datotype])", "d": "Returnerer den forrige kupondato før afregningsdatoen"}, "CUMIPMT": {"a": "(rente; nper; nv; startperiode; slutperiode; betalingstype)", "d": "Returnerer den akkumulerede rente, betalt mellem to perioder"}, "CUMPRINC": {"a": "(rente; nper; nv; startperiode; slutperiode; betalingstype)", "d": "Returnerer den akkumulerede ho<PERSON>, betalt på et lån mellem to perioder"}, "DB": {"a": "(kø<PERSON><PERSON><PERSON>; restværdi; levetid; periode; [måned])", "d": "Returnerer afskrivningsbeløbet for et aktiv for en given periode vha. saldometoden"}, "DDB": {"a": "(kø<PERSON><PERSON><PERSON>; restværdi; levetid; periode; [faktor])", "d": "Returnerer afskrivningsbeløbet for et aktiv for en given periode vha. dobbeltsaldometoden eller en anden angivet afskrivningsmetode"}, "DISC": {"a": "(afregningsdato; udløbsdato; kurs; indløsningskurs; [datotype])", "d": "Returnerer et værdipapirs diskonto"}, "DOLLARDE": {"a": "(brøkdel_kr; brøkdel)", "d": "Konverterer en kronepris, udtrykt som brøk, til en kronepris udtrykt som decimaltal"}, "DOLLARFR": {"a": "(decimal_kr; brøkdel)", "d": "Konverterer en kronepris, udtrykt som et decimaltal, til en kronepris udtrykt som brøk"}, "DURATION": {"a": "(afregningsdato; udløbsdato; kupon; afkast; hyppighed; [datotype])", "d": "Returnerer den årlige varighed af et værdipapir med periodiske rentebetalinger"}, "EFFECT": {"a": "(nominel_rente; nperår)", "d": "Returnerer den årlige effektive rente"}, "FV": {"a": "(rente; nper; ydelse; [nv]; [type])", "d": "Returnerer den fremtidige værdi af en investering på baggrund af periodiske, konstante ydelser og en konstant rentesats"}, "FVSCHEDULE": {"a": "(hovedstol; tabel)", "d": "Returnerer den fremtidige værdi af en hovedstol efter at have anvendt en række sammensatte renter"}, "INTRATE": {"a": "(afregningsdato; udløbsdato; investering; indløsningskurs; [datotype])", "d": "Returnerer renten på et fuldt ud investeret værdipapir"}, "IPMT": {"a": "(rente; periode; nper; nv; [fv]; [type])", "d": "Returnerer rentedelen af en ydelse for en investering i en given periode, baseret på konstante periodiske ydelser og en konstant rente"}, "IRR": {"a": "(værdier; [gæt])", "d": "Returnerer det interne afkast for en række pengestrømme"}, "ISPMT": {"a": "(rente; periode; nper; nv)", "d": "Returnerer den rente, der er betalt i en angivet investeringsperiode"}, "MDURATION": {"a": "(afregningsdato; udløbsdato; kupon; afkast; hyppighed; [datotype])", "d": "Returnerer <PERSON><PERSON>'s modifice<PERSON>e varighed af et værdipapir med en formodet pari på 100 kr."}, "MIRR": {"a": "(værdier; finansrente; investeringsrente)", "d": "Returnerer den interne forrentningprocent for en række periodiske pengestrømme, hvor både investeringsudgifter og renteindtægter ved geninvestering tages i betragtning"}, "NOMINAL": {"a": "(effektiv_rente; nperår)", "d": "Returnerer den årlige nominelle rente"}, "NPER": {"a": "(rente; ydelse; nv; [fv]; [type])", "d": "Returnerer antallet af perioder for en investering på baggrund af periodiske, konstante ydelser og en konstant rentesats"}, "NPV": {"a": "(rente; værdi1; [værdi2]; ...)", "d": "Returnerer den aktuelle nettoværdi af en investering på baggrund af en diskontosats og en serie fremtidige betalinger (negative værdier) og indkomst (positive værdier)"}, "ODDFPRICE": {"a": "(afregningsdato; udløbsdato; udstedelsesdato; første_kupon; rente; afkast; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer kursen pr. 100 kr. nominel værdi for et værdipapir med en ulige første periode"}, "ODDFYIELD": {"a": "(afregningsdato; udløbsdato; udstedelsesdato; første_kupon; rente; kurs; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer afkastet af et værdipapir med ulige første periode"}, "ODDLPRICE": {"a": "(afregningsdato; udløbsdato; sidste_rente; rente; afkast; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer prisen pr. 100 kr. nominel værdi, for et værdipapir med ulige sidste periode"}, "ODDLYIELD": {"a": "(afregningsdato; udløbsdato; sidste_rente; rente; kurs; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer afkastet for et værdipapir med ulige sidste periode"}, "PDURATION": {"a": "(rente; nv; fv)", "d": "Returnerer det på<PERSON>r<PERSON><PERSON>e antal perioder, før en investering når den angivne værdi"}, "PMT": {"a": "(rente; nper; nv; [fv]; [type])", "d": "<PERSON><PERSON>gner ydelsen på et lån baseret på konstante ydelser og en konstant rentesats"}, "PPMT": {"a": "(rente; periode; nper; nv; [fv]; [type])", "d": "Returnerer afdragsdelen på ydelsen for en given investering baseret på konstante periodiske ydelser og en konstant rentesats"}, "PRICE": {"a": "(afregningsdato; udløbsdato; rente; afkast; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer kursen pr. 100 kr. nominel værdi for et værdipapir med periodiske renteudbetalinger"}, "PRICEDISC": {"a": "(afregningsdato; udløbsdato; diskonto; indløsningskurs; [datotype])", "d": "Returnerer kursen pr. 100 kr. nominel værdi for et diskonteret værdipapir"}, "PRICEMAT": {"a": "(afregningsdato; udløbsdato; udstedelsesdato; rente; afkast; [datotype])", "d": "Returnerer kursen pr. 100 kr. nominel værdi for et værdipapir, der udbetaler rente ved udløb"}, "PV": {"a": "(rente; nper; ydelse; [fv]; [type])", "d": "Returnerer nutidsværdien for en investering: det totale beløb, som en række fremtidige ydelser er værd nu"}, "RATE": {"a": "(nper; ydelse; nv; [fv]; [type]; [gæt])", "d": "Returnerer renten i hver periode for et lån eller en investering. Brug for eksempel 6 %/4 om kvartårlige ydelser på 6 % APR"}, "RECEIVED": {"a": "(afregningsdato; udløbsdato; investering; diskonto; [datotype])", "d": "Returnerer beløbet modtaget ved udløbet af et værdipapir"}, "RRI": {"a": "(nper; nv; fv)", "d": "Returnerer en ækvivalent rente for væksten i en investering"}, "SLN": {"a": "(kø<PERSON><PERSON><PERSON>; restvæ<PERSON>; levetid)", "d": "Returnerer den lineære afskrivning for et aktiv i en enkelt periode"}, "SYD": {"a": "(kø<PERSON><PERSON><PERSON>; restværdi; levetid; periode)", "d": "Returnerer den årlige afskrivning på et aktiv i en bestemt periode"}, "TBILLEQ": {"a": "(afregningsdato; udløbsdato; diskonto)", "d": "Returnerer det obligationsækvivalente afkast for en statsobligation"}, "TBILLPRICE": {"a": "(afregningsdato; udløbsdato; diskonto)", "d": "Returnerer kursen pr. kr. 100 nominel værdi for en statsobligation"}, "TBILLYIELD": {"a": "(afregningsdato; udløbsdato; kurs)", "d": "Returnerer statsobligationens afkast"}, "VDB": {"a": "(kø<PERSON>p<PERSON>; restværdi; levetid; startperiode; slutperiode; [faktor]; [ingen_skift])", "d": "Returnerer afskrivningen på et aktiv i en specificeret periode, herund<PERSON> delper<PERSON>, vha. <PERSON><PERSON><PERSON><PERSON><PERSON>, eller en anden metode, du angiver"}, "XIRR": {"a": "(værdier; datoer; [gæt])", "d": "Returnerer den interne rente for en pengestrømsplan"}, "XNPV": {"a": "(rente; værdier; datoer)", "d": "Returnerer nutidsværdien af en pengestrømsplan"}, "YIELD": {"a": "(afregningsdato; udløbsdato; rente; kurs; indløsningskurs; hyppighed; [datotype])", "d": "Returnerer afkastet for et værdipapir med periodiske renteudbetalinger"}, "YIELDDISC": {"a": "(afregningsdato; udløbsdato; kurs; indløsningskurs; [datotype])", "d": "Returnerer det årlige afkast for et diskonteret værdipapir, f.eks. en statsobligation"}, "YIELDMAT": {"a": "(afregningsdato; udløbsdato; udstedelsesdato; rente; kurs; [datotype])", "d": "Returnerer det årlige afkast for et værdipapir med renteudbetaling ved udløb"}, "ABS": {"a": "(tal)", "d": "Returnerer den absolutte værdi af et tal, dvs. tallet uden fortegn"}, "ACOS": {"a": "(tal)", "d": "Returnerer arcus cosinus til et tal, i radianer i intervallet 0 til pi. Arcus cosinus er den vinkel, hvis cosinus er Tal"}, "ACOSH": {"a": "(tal)", "d": "Returnerer den inverse hyperbolske cosinus til et tal"}, "ACOT": {"a": "(tal)", "d": "Returnerer arcus cotangens til et tal i radianer i intervallet 0 til Pi."}, "ACOTH": {"a": "(tal)", "d": "Returnerer den inverse hyperbolske cotangens af et tal"}, "AGGREGATE": {"a": "(funktion; indstillinger; ref1; ...)", "d": "Returnerer en aggregering på en liste eller en database"}, "ARABIC": {"a": "(tekst)", "d": "Konverterer et romertal til arabisk"}, "ASC": {"a": "(tekst)", "d": "For dobbeltbytetegnsætsprog ændrer funktionen tegn i fuld bredde (dobbeltbyte) til tegn i halv bredde (enkeltbyte)"}, "ASIN": {"a": "(tal)", "d": "Returnerer arcus sinus til et tal, i radianer i intervallet -pi/2 til pi/2"}, "ASINH": {"a": "(tal)", "d": "Returnerer den inverse hyperbolske sinus til et tal"}, "ATAN": {"a": "(tal)", "d": "Returnerer arcus tangens til et tal, i radianer i intervallet -pi/2 til pi/2"}, "ATAN2": {"a": "(x_koordinat; y_koordinat)", "d": "Returnerer de specificerede  x- og y-koordinaters arcus tangens, i radianer mellem -pi og pi, forskellig fra -pi"}, "ATANH": {"a": "(tal)", "d": "Returnerer den inverse hyperbolske tangens til et tal"}, "BASE": {"a": "(tal; radikand; [min_længde])", "d": "Konverterer et tal til en tekstrepræsentation med en given radikand (rod)"}, "CEILING": {"a": "(tal; betydning)", "d": "Runder et tal op til nærmeste multiplum af betydning"}, "CEILING.MATH": {"a": "(tal; [betydning]; [tilstand])", "d": "<PERSON>der et tal op til det nærmeste heltal eller til det nærmeste betydende multiplum"}, "CEILING.PRECISE": {"a": "(tal; [betydning])", "d": "Returnerer et tal, der rundes op til nærmeste heltal eller til nærmeste multiplum af signifikans"}, "COMBIN": {"a": "(tal; tal_valgt)", "d": "Returnerer antallet af kombinationer for et givet antal elementer"}, "COMBINA": {"a": "(tal; tal_valgt)", "d": "Returnerer antal kombinationer med gentagelser for et givet antal elementer"}, "COS": {"a": "(tal)", "d": "Returnerer cosinus til en vinkel"}, "COSH": {"a": "(tal)", "d": "Returnerer den hyperbolske cosinus til et tal"}, "COT": {"a": "(tal)", "d": "Returnerer cotangens af en vinkel"}, "COTH": {"a": "(tal)", "d": "Returnerer den hyperbolske cotagens af et tal"}, "CSC": {"a": "(tal)", "d": "Returnerer cosekanten af en vinkel"}, "CSCH": {"a": "(tal)", "d": "Returnerer den hyperbolske cosekant af en vinkel"}, "DECIMAL": {"a": "(tal; radikand)", "d": "Konverterer tekstrepræsentationen af et tal i en given rod til et decimaltal"}, "DEGREES": {"a": "(vinkel)", "d": "Konverterer radianer til grader"}, "ECMA.CEILING": {"a": "(tal; betydning)", "d": "Runder et tal op til nærmeste multiplum af betydning"}, "EVEN": {"a": "(tal)", "d": "Runder positive tal op og negative tal ned til nærmeste lige heltal"}, "EXP": {"a": "(tal)", "d": "Returnerer e opløftet til en potens af et givet tal"}, "FACT": {"a": "(tal)", "d": "Returnerer et tals fakultet, svar<PERSON><PERSON> til 1*2*3*...* Tal"}, "FACTDOUBLE": {"a": "(tal)", "d": "Returnerer et tals dobbelte fakultet"}, "FLOOR": {"a": "(tal; betydning)", "d": "Runder et tal ned til det nærmeste multiplum af betydning"}, "FLOOR.PRECISE": {"a": "(tal; [betydning])", "d": "Returnerer et tal, der rundes ned til nærmeste heltal eller til nærmeste multiplum af signifikans"}, "FLOOR.MATH": {"a": "(tal; [betydning]; [tilstand])", "d": "<PERSON>der et tal ned til det nærmeste heltal eller til det nærmeste betydende multiplum"}, "GCD": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer den største fælles divisor"}, "INT": {"a": "(tal)", "d": "<PERSON>der et tal ned til nærmeste heltal"}, "ISO.CEILING": {"a": "(tal; [betydning])", "d": "Returnerer et tal, der rundes op til nærmeste heltal eller til nærmeste multiplum af signifikans. U<PERSON>et tallets fortegn, rundes tallet op. Men hvis tallet eller signifikansen er nul, returneres nul."}, "LCM": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer det mindste fælles multiplum"}, "LN": {"a": "(tal)", "d": "Returnerer et tals naturlige logaritme"}, "LOG": {"a": "(tal; [grundtal])", "d": "Returnerer et tals logaritme på grundlag af et angivet grundtal"}, "LOG10": {"a": "(tal)", "d": "Returnerer et tals titals logaritme"}, "MDETERM": {"a": "(matrix)", "d": "Returnerer determinanten for en matrix"}, "MINVERSE": {"a": "(matrix)", "d": "Returnerer den inverse matrix for en matrix"}, "MMULT": {"a": "(matrix1; matrix2)", "d": "Returnerer matrixproduktet af to matrixer, dvs. en matrix med samme antal rækker som matrix1 og samme antal kolonner som matrix2"}, "MOD": {"a": "(tal; divisor)", "d": "Returnerer restværdien ved en division"}, "MROUND": {"a": "(tal; multiplum)", "d": "Returnerer et tal afrundet til det ønskede multiplum"}, "MULTINOMIAL": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer polynomiet af en række tal"}, "MUNIT": {"a": "(dimension)", "d": "Returnerer enhedsmatrixen for den angivne dimension"}, "ODD": {"a": "(tal)", "d": "Runder positive tal op og negative tal ned til nærmeste ulige heltal"}, "PI": {"a": "()", "d": "Returnerer værdien af pi  (3.14159265358979) med 15 decimalers nøjagtighed"}, "POWER": {"a": "(tal; potens)", "d": "Returnerer resultatet af et tal opløftet til en potens"}, "PRODUCT": {"a": "(tal1; [tal2]; ...)", "d": "Multiplice<PERSON> de tal, der er givet som argumenter"}, "QUOTIENT": {"a": "(tæller; nævner)", "d": "Returnerer heltalsdelen af en division"}, "RADIANS": {"a": "(vinkel)", "d": "Konverterer grader til radianer"}, "RAND": {"a": "()", "d": "Returnerer et tilfældigt tal mellem 0 og 1, jæ<PERSON><PERSON> fordelt (ændres ved ny beregning)"}, "RANDARRAY": {"a": "([rækker]; [kolonner]; [min]; [maks]; [heltal])", "d": "Returnerer en matrix af tilfældige tal"}, "RANDBETWEEN": {"a": "(<PERSON><PERSON>; stø<PERSON>)", "d": "Returnerer et tilfældigt tal mellem de tal, der angives"}, "ROMAN": {"a": "(tal; [format])", "d": "Konverterer et arabertal til et romertal, som tekst"}, "ROUND": {"a": "(tal; antal_cifre)", "d": "<PERSON><PERSON><PERSON><PERSON> et tal til et angivet antal decimaler"}, "ROUNDDOWN": {"a": "(tal; antal_cifre)", "d": "<PERSON><PERSON> et tal ned (mod nul)"}, "ROUNDUP": {"a": "(tal; antal_cifre)", "d": "<PERSON>der et tal op (væk fra nul)"}, "SEC": {"a": "(tal)", "d": "Returnerer sekanten af en vinkel"}, "SECH": {"a": "(tal)", "d": "Returnerer den hyperbolske sekant af en vinkel"}, "SERIESSUM": {"a": "(x; n; m; koefficienter)", "d": "Returnerer summen af potensserie, baseret på formlen"}, "SIGN": {"a": "(tal)", "d": "Returnerer et tals fortegn: 1, hvis tallet er positivt, nul, hvis tallet er nul og -1, hvis tallet er negativt"}, "SIN": {"a": "(tal)", "d": "Returnerer sinus til en vinkel"}, "SINH": {"a": "(tal)", "d": "Returnerer den hyperbolske sinus til et tal"}, "SQRT": {"a": "(tal)", "d": "Returnerer kvadratroden af et tal"}, "SQRTPI": {"a": "(tal)", "d": "Returnerer kva<PERSON><PERSON>den af (tal * pi)"}, "SUBTOTAL": {"a": "(funktion; reference1; ...)", "d": "Returnerer en subtotal på en liste eller i en database"}, "SUM": {"a": "(tal1; [tal2]; ...)", "d": "<PERSON><PERSON><PERSON> alle tal i et celleområde sammen"}, "SUMIF": {"a": "(områ<PERSON>; kriterier; [sum_område])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, der er specificeret af en given betingelse eller et givet kriterium"}, "SUMIFS": {"a": "(sum<PERSON><PERSON><PERSON><PERSON>; kriterieomr<PERSON><PERSON>; kriterier; ...)", "d": "<PERSON><PERSON><PERSON> de cell<PERSON>, der er angivet med et givet sæt betingelser eller kriterier"}, "SUMPRODUCT": {"a": "(matrix1; [matrix2]; [matrix3]; ...)", "d": "Returnerer summen af produkterne af tilsvarende områder eller matrixer"}, "SUMSQ": {"a": "(tal1; [tal2]; ...)", "d": "Returnerer summen af kva<PERSON>ter for argumenterne. Argumenterne kan være tal, matrixer, navne eller referencer til celler, der indeholder tal"}, "SUMX2MY2": {"a": "(matrix_x; matrix_y)", "d": "Opsummerer forskellene mellem kvadraterne af to tilsvarende områder eller matrixer"}, "SUMX2PY2": {"a": "(matrix_x; matrix_y)", "d": "Returnerer summen af summen af kvadraterne af værdier i to tilsvarende områder eller matrixer"}, "SUMXMY2": {"a": "(matrix_x; matrix_y)", "d": "Opsummerer kvadraterne af forskellene i to tilsvarende områder eller matrixer"}, "TAN": {"a": "(tal)", "d": "Returnerer tangens til en vinkel"}, "TANH": {"a": "(tal)", "d": "Returnerer hyperbolsk tangens til et tal"}, "TRUNC": {"a": "(tal; [antal_cifre])", "d": "Afkorter et tal til et heltal ved at fjerne decimal- eller procentdelen af tallet"}, "ADDRESS": {"a": "(række; kolonne; [abs_nr]; [a1]; [arknavn])", "d": "Opretter en cellereference som tekst ud fra angivne række- og kolonnenumre"}, "CHOOSE": {"a": "(indeksnr; værdi1; [værdi2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> en værdi eller en handling fra en liste over værdier baseret på et indeksnummer"}, "COLUMN": {"a": "([reference])", "d": "Returnerer kolonnenummeret på en reference"}, "COLUMNS": {"a": "(matrix)", "d": "Returnerer antallet af kolonner i en matrix eller en reference"}, "FORMULATEXT": {"a": "(reference)", "d": "Returnerer en formel som en streng"}, "HLOOKUP": {"a": "(opslagsværdi; tabelmatrix; rækkeindeks; [intervalopslag])", "d": "<PERSON><PERSON><PERSON> efter en bestemt værdi i øverste række af en tabel eller i en matrix og returnerer værdien i den samme kolonne fra en række, som du angiver"}, "HYPERLINK": {"a": "(linkplacering; [fuldt_navn])", "d": "Opretter en genvej eller et jump, der åbner et dokument, der er lagret på harddisken, en netserver eller på internettet"}, "INDEX": {"a": "(matrix; række; [kolonne]!reference; række; [kolonne]; [omr<PERSON><PERSON>])", "d": "Returnerer en værdi fra eller reference til en celle ved skæringspunktet mellem en bestemt række og kolonne i et givet område"}, "INDIRECT": {"a": "(reference; [a1])", "d": "Returnerer den reference, der specificeres af en tekststreng"}, "LOOKUP": {"a": "(opslagsværdi; opslagsvektor; [resultatvektor]!opslagsværdi; matrix)", "d": "<PERSON><PERSON><PERSON> efter værdier i en række, en kolonne eller en matrix. Sikrer kompatibilitet med ældre versioner"}, "MATCH": {"a": "(opslagsværdi; opslagsmatrix; [sammenligningstype])", "d": "Returnerer den relative placering af et element i en matrix, som svarer til en angivet værdi i en angivet rækkefølge"}, "OFFSET": {"a": "(reference; rækker; kolonner; [højde]; [bredde])", "d": "Returnerer en reference til et område, der er et givet antal rækker og kolonner fra en given reference"}, "ROW": {"a": "([reference])", "d": "Returnerer ræk<PERSON><PERSON>meret for en reference"}, "ROWS": {"a": "(matrix)", "d": "Returnerer antallet af rækker i en reference eller en matrix"}, "TRANSPOSE": {"a": "(matrix)", "d": "Konverterer et lodret celleområde til et vandret område eller omvendt"}, "UNIQUE": {"a": "(matrix; [efter_kol]; [præcis_en_gang])", "d": "Returnerer de entydige værdier fra et interval eller en matrix."}, "VLOOKUP": {"a": "(opslagsværdi; tabelmatrix; kolonneindeks_nr; [intervalopslag])", "d": "<PERSON><PERSON><PERSON> efter en værdi i den første kolonne i en tabel og returnerer en værdi i den samme række fra en anden kolonne, du har angivet. Tabellen skal som standard sorteres i stigende rækkefølge"}, "XLOOKUP": {"a": "(opslagsværdi; opslagsmatrix; returmatrix; [hvis_ikke_fundet]; [matchtilstand]; [søgetilstand])", "d": "sø<PERSON> efter et match i et område eller en matrix og returnerer det tilsvarende element fra et andet område eller en anden matrix. Som standard bruges et nøjagtigt match"}, "CELL": {"a": "(info; [reference])", "d": "Returnerer oplysninger om formatering, placering eller indholdet af en celle"}, "ERROR.TYPE": {"a": "(fej<PERSON><PERSON><PERSON><PERSON>)", "d": "Returnerer et tal, der svarer til en fejlværdi."}, "ISBLANK": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en reference refererer til en tom celle, og returnerer SAND eller FALSK"}, "ISERR": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er en fejl foruden #I/T, og returnerer SAND eller FALSK"}, "ISERROR": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er en fejl, og returnerer SAND eller FALSK"}, "ISEVEN": {"a": "(tal)", "d": "Returnerer SAND, hvis tallet er lige"}, "ISFORMULA": {"a": "(reference)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en reference er til en celle, der indeholder en formel, og returnerer SAND eller FALSK"}, "ISLOGICAL": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er en logisk værdi (SAND eller FALSK), og returnerer SAND eller FALSK"}, "ISNA": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er #I/T, og returnerer SAND eller FALSK"}, "ISNONTEXT": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi ikke er tekst (tomme celler er ikke tekst), og returnerer SAND eller FALSK"}, "ISNUMBER": {"a": "(værdi)", "d": "Undersøger om en værdi er et tal, og returnerer SAND eller FALSK"}, "ISODD": {"a": "(tal)", "d": "Returnerer SAND, hvis tallet er ulige"}, "ISREF": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er en reference, og returnerer SAND eller FALSK"}, "ISTEXT": {"a": "(værdi)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en værdi er tekst, og returnerer SAND eller FALSK"}, "N": {"a": "(værdi)", "d": "Konverterer en ikke-numerisk værdi til et tal. Datoer konverteres til serienumre, SAND til 1, og alt andet til 0 (nul)"}, "NA": {"a": "()", "d": "Returnerer fejlværdien #I/T (værdien er ikke tilgængelig)"}, "SHEET": {"a": "([værdi])", "d": "Returnerer arknummeret for det ark, der refereres til"}, "SHEETS": {"a": "([reference])", "d": "Returnerer antal ark i en reference"}, "TYPE": {"a": "(værdi)", "d": "Returnerer et heltal, der repræsenterer datatypen for en værdi: tal = 1; tekst = 2; logisk værdi = 4; fejlværdi = 16; matrix = 64; sammensat data = 128"}, "AND": {"a": "(logisk1; [logisk2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, om alle argumenter er SAND, og returnerer SAND, hvis alle argumenter er SAND"}, "FALSE": {"a": "()", "d": "Returnerer den logiske værdi FALSK"}, "IF": {"a": "(logisk_test; [værdi_hvis_sand]; [værdi_hvis_falsk])", "d": "<PERSON><PERSON><PERSON><PERSON>, om et kriterium er opfyldt, og returnerer en værdi, hvis SAND, og en anden værdi, hvis FALSK"}, "IFS": {"a": "(logisk_test; værdi_hvis_sand; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, om en eller flere betingelser er opfyldt, og returnerer en værdi, der svarer til den første SAND-betingelse"}, "IFERROR": {"a": "(værdi; værdi_hvis_fejl)", "d": "Returnerer udtrykket værdi_hvis_fejl, hvis udtrykket er en fejl, og returnerer ellers værdien af selve udtrykket"}, "IFNA": {"a": "(værdi; værdi_hvis_it)", "d": "Returnerer den angivne værdi, hvis udtrykket evalueres til #I/T. Ellers returneres resultatet af udtrykket"}, "NOT": {"a": "(logisk)", "d": "Ændrer FALSK til SAND eller SAND til FALSK"}, "OR": {"a": "(logisk1; [logisk2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, om nogle af argumenterne er SAND, og returnerer SAND eller FALSK. Returnerer kun FALSK, hvis alle argumenter er FALSK"}, "SWITCH": {"a": "(udtryk; værdi1; resultat1; [standard_eller_værdi2]; [resultat2]; ...)", "d": "Evaluerer et udtryk i forhold til en liste med værdier og returnerer resultatet, der svarer til den første tilsvarende værdi. Hvis der ikke er et match, returneres en valgfri standardværdi"}, "TRUE": {"a": "()", "d": "Returnerer den logiske værdi SAND"}, "XOR": {"a": "(logisk1; [logisk2]; ...)", "d": "Returnerer et logisk 'Eksklusivt eller' for alle argumenterne"}, "TEXTBEFORE": {"a": "(tekst, afgrænser, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnerer tekst, der er før afgrænsende tegn."}, "TEXTAFTER": {"a": "(tekst, afgrænser, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnerer tekst, der er efter afgrænsende tegn."}, "TEXTSPLIT": {"a": "(tekst, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": " Opdeler tekst i rækker eller kolonner ved hjælp af afgrænsere."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ombryd en række- eller kolonnevektor efter et angivet antal værdier."}, "VSTACK": {"a": "(matrix1, [matrix2], ...)", "d": "Stabler matrixer lodret i én matrix."}, "HSTACK": {"a": "(matrix1, [matrix2], ...)", "d": "St<PERSON>r matrixer vandret i én matrix."}, "CHOOSEROWS": {"a": "(matrix, row_num1, [row_num2], ...)", "d": " Returnerer rækker fra en matrix eller reference."}, "CHOOSECOLS": {"a": "(matrix, col_num1, [col_num2], ...)", "d": " Returnerer kolonner fra en matrix eller en reference."}, "TOCOL": {"a": "(matrix, [ignorer], [scan_by_column])", "d": "Returnerer matrixen som én kolonne."}, "TOROW": {"a": "(matrix, [ignorer], [scan_by_column])", "d": "Returnerer matrixen som én række."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ombryd en række- eller kolonnevektor efter et angivet antal værdier."}, "TAKE": {"a": "(matrix, rækker, [kolonne])", "d": "Returnerer rækker eller kolonner fra matrixens start eller slutning."}, "DROP": {"a": "(matrix, rækker, [kolo<PERSON>])", "d": "Sletter rækker eller kolonner fra matrixens start eller slutning."}}
{"DATE": {"a": "(rok; mesiac; deň)", "d": "<PERSON><PERSON><PERSON><PERSON>, ktoré v kóde pre dátum a čas predstavuje dátum"}, "DATEDIF": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dátum; kon<PERSON><PERSON><PERSON>_dátum; j<PERSON><PERSON><PERSON>)", "d": "Vypočíta po<PERSON> dní, mesiacov alebo rokov medzi dvomi d<PERSON>ami"}, "DATEVALUE": {"a": "(text_dátumu)", "d": "Konvertuje dátum v textovom formáte na číslo, ktoré v kóde pre dátum a čas predstavuje dátum"}, "DAY": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "Vráti <PERSON>ň v mesiaci, číslo od 1 do 31."}, "DAYS": {"a": "(end_date; start_date)", "d": "<PERSON><PERSON><PERSON><PERSON> poč<PERSON> dní medzi dvomi dá<PERSON>ami"}, "DAYS360": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dátum; konco<PERSON><PERSON>_dátum; [metóda])", "d": "<PERSON><PERSON><PERSON><PERSON> počet dní medzi dvoma dátumami na základe roka s 360 dňami (12 mesiacov po 30 dní)"}, "EDATE": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_d<PERSON>; mesiace)", "d": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON>, ktor<PERSON> predstavuje vyznačený počet mesiacov pred alebo po počiatočnom dátume"}, "EOMONTH": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_d<PERSON>; mesiace)", "d": "<PERSON><PERSON><PERSON><PERSON> pora<PERSON> č<PERSON> posledného dňa mesiaca pred alebo po zadanom počte mesiacov"}, "HOUR": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> od 0 (12:00 dop.) do 23 (11:00 odp.)."}, "ISOWEEKNUM": {"a": "(date)", "d": "Vráti číslo týždňa v roku pre daný dátum podľa normy ISO. idate"}, "MINUTE": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON>, č<PERSON>lo od 0 do 59."}, "MONTH": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON>, číslo od 1 (január) do 12 (december)."}, "NETWORKDAYS": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_d<PERSON><PERSON>; dá<PERSON>_ukončenia; [sviatky])", "d": "<PERSON><PERSON><PERSON><PERSON> počet celých pracovných dní medzi dvomi d<PERSON>"}, "NETWORKDAYS.INTL": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dá<PERSON>; dá<PERSON>_ukončenia; [vík<PERSON>]; [sviatky])", "d": "<PERSON><PERSON><PERSON><PERSON> počet celých pracovných dní medzi dvoma dátumami s vlastnými parametrami víkendov"}, "NOW": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> aktuálny dátum a čas vo formáte dátumu a času."}, "SECOND": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "<PERSON><PERSON><PERSON><PERSON>, č<PERSON>lo od 0 do 59."}, "TIME": {"a": "(hodina; minúta; sekunda)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>, min<PERSON><PERSON> a sekundy zadané ako čísla na poradové číslo vo formáte času"}, "TIMEVALUE": {"a": "(text_času)", "d": "Konvertuje čas vo formáte textového reťazca na poradové číslo pre čas, číslo od 0 (12:00:00 dop.) do 0,999988426 (11:59:59 odp.). Po zadaní vzorca číslo sformátujte do formátu času"}, "TODAY": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>u<PERSON>lny dátum vo formáte dátumu."}, "WEEKDAY": {"a": "(pora<PERSON><PERSON><PERSON>_č<PERSON>lo; [vr<PERSON><PERSON><PERSON>_číslo])", "d": "Vráti číslo 1 až 7, ktoré v dátume určuje deň v týždni."}, "WEEKNUM": {"a": "(pora<PERSON><PERSON><PERSON>_č<PERSON>lo; [vr<PERSON><PERSON><PERSON>_typ])", "d": "Vráti číselné označenie týždňa v roku"}, "WORKDAY": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dátum; dni; [sviatky])", "d": "<PERSON><PERSON><PERSON><PERSON> pora<PERSON> d<PERSON> pred alebo po zadanom počte pracovných dní"}, "WORKDAY.INTL": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dátum; dni; [vík<PERSON>]; [sviatky])", "d": "<PERSON><PERSON><PERSON><PERSON> pora<PERSON> d<PERSON> pred zadaným počtom pracovných dní alebo po zadanom počte pracovných dní s vlastnými parametrami víkendov"}, "YEAR": {"a": "(pora<PERSON><PERSON><PERSON>_číslo)", "d": "Vráti rok dátumu, celé číslo v rozsahu od 1900 do 9999."}, "YEARFRAC": {"a": "(p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dá<PERSON>; dá<PERSON>_ukončenia; [z<PERSON><PERSON>])", "d": "Vráti zlomok roka predstavujúci počet celých dní medzi počiatočným dátumom a dátumom ukončenia"}, "BESSELI": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON>ovu funkciu In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON> funkciu <PERSON>n(x)"}, "BESSELK": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON> Besselovu funkciu Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON><PERSON>(x)"}, "BIN2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Skonvertuje binárne číslo na desiatkové číslo"}, "BIN2HEX": {"a": "(číslo; [miesta])", "d": "Skonvertuje binárne číslo na šestnástkové číslo"}, "BIN2OCT": {"a": "(číslo; [miesta])", "d": "Skonvertuje binárne číslo na osmičkové číslo"}, "BITAND": {"a": "(number1; number2)", "d": "<PERSON><PERSON><PERSON><PERSON>ý operátor AND dvoch čísel"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "<PERSON><PERSON><PERSON><PERSON>lo posunuté doľava o počet bitov určený argumentom shift_amount"}, "BITOR": {"a": "(number1; number2)", "d": "<PERSON><PERSON><PERSON><PERSON> operátor OR dvoch čísel"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "<PERSON><PERSON><PERSON><PERSON>lo posunuté doprava o počet bitov určený argumentom shift_amount"}, "BITXOR": {"a": "(číslo1; číslo2)", "d": "<PERSON><PERSON><PERSON><PERSON> operátor Exclusive Or dvoch čísel"}, "COMPLEX": {"a": "(re<PERSON><PERSON><PERSON>_č<PERSON>lo; i_číslo; [pr<PERSON><PERSON><PERSON>])", "d": "Skonvertuje reálny a imaginárny koeficient na komplexné číslo"}, "CONVERT": {"a": "(<PERSON><PERSON><PERSON>; z_jednotky; na_jednotku)", "d": "Skonvertuje číslo z jedného systému merania na iný"}, "DEC2BIN": {"a": "(číslo; [miesta])", "d": "Skonvertuje desiatkové číslo na binárne číslo"}, "DEC2HEX": {"a": "(číslo; [miesta])", "d": "Skonvertuje desiatkové číslo na šestnástkové číslo"}, "DEC2OCT": {"a": "(číslo; [miesta])", "d": "Skonvertuje desiatkové číslo na osmičkové číslo"}, "DELTA": {"a": "(číslo1; [číslo2])", "d": "Test<PERSON>je z<PERSON>du d<PERSON> čísel"}, "ERF": {"a": "(dolný_limit; [horný_limit])", "d": "<PERSON><PERSON><PERSON><PERSON> funkciu"}, "ERF.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON><PERSON><PERSON> funkciu"}, "ERFC": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>by"}, "ERFC.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>bovú funkciu"}, "GESTEP": {"a": "(číslo; [krok])", "d": "<PERSON><PERSON><PERSON>, či je číslo väčšie ako prahová hodnota"}, "HEX2BIN": {"a": "(číslo; [miesta])", "d": "Skonvertuje šestnástkové číslo na binárne číslo"}, "HEX2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Skonvertuje šestnástkové číslo na desiatkové číslo"}, "HEX2OCT": {"a": "(číslo; [miesta])", "d": "Skonvertuje šestnástkové číslo na osmičkové číslo"}, "IMABS": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ol<PERSON> hodnot<PERSON> (modul) komplexného čísla"}, "IMAGINARY": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> imaginárny koeficient komplexného č<PERSON>"}, "IMARGUMENT": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vráti argument q, čiže uhol vyjadrený v radiánoch"}, "IMCONJUGATE": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "Vráti komplexne združené číslo ku komplexnému <PERSON>"}, "IMCOS": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> komplexného <PERSON>"}, "IMCOSH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON>ký kosínus komplexného č<PERSON>"}, "IMCOT": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> kotangens komplexného čísla"}, "IMCSC": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> komplexného č<PERSON>"}, "IMCSCH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperbolický kosekans komplexného <PERSON>"}, "IMDIV": {"a": "(ičíslo1; ičíslo2)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> dvoch komplexných čísel"}, "IMEXP": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON>ti exponenciálu komplexného čísla"}, "IMLN": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "V<PERSON><PERSON>ti prirodzený logaritmus komplexného čísla"}, "IMLOG10": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> logaritmus komplexného č<PERSON>"}, "IMLOG2": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>y logaritmus komplexného č<PERSON>"}, "IMPOWER": {"a": "(i<PERSON><PERSON><PERSON>; číslo)", "d": "Vráti komplexné číslo umocnené na celočíselnú mocninu"}, "IMPRODUCT": {"a": "(ič<PERSON>lo1; [ič<PERSON>lo2]; ...)", "d": "Vráti súčin 1 až 255 komplexných čísel"}, "IMREAL": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> koeficient komplexného čísla"}, "IMSEC": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> komplexného <PERSON>"}, "IMSECH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON> sekans komplexného <PERSON>"}, "IMSIN": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> komplexného <PERSON>"}, "IMSINH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON>ký sínus komplexného č<PERSON>"}, "IMSQRT": {"a": "(i<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> odmocninu komplexného č<PERSON>"}, "IMSUB": {"a": "(ičíslo1; ičíslo2)", "d": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>l dvoch komplexných č<PERSON>el"}, "IMSUM": {"a": "(ič<PERSON>lo1; [ič<PERSON>lo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> komplexných čísel"}, "IMTAN": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON> tangens komplexného čísla"}, "OCT2BIN": {"a": "(číslo; [miesta])", "d": "Skonvertuje osmičkové číslo na binárne číslo"}, "OCT2DEC": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Skonvertuje osmičkové číslo na binárne číslo"}, "OCT2HEX": {"a": "(číslo; [miesta])", "d": "Skonvertuje osmičkové číslo na šestnástkové číslo"}, "DAVERAGE": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> hodn<PERSON> v stĺpci zoznamu alebo v databáze, ktoré spĺňajú zadané podmienky"}, "DCOUNT": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Spočíta bunky obsahujúce čísla v poli (stĺpci) záznamov databázy, ktoré spĺňajú zadané podmienky"}, "DCOUNTA": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bunk<PERSON>, ktor<PERSON> nie sú prázdne, v poli (stĺpci) záznamov databázy, ktoré spĺňajú zadané podmienky"}, "DGET": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Vyberie z databázy jeden <PERSON>, ktor<PERSON> spĺňa zadané podmienky"}, "DMAX": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Vráti maximálnu hodnotu v poli (stĺpci) záznamov databázy, ktorá spĺňa zadané podmienky"}, "DMIN": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "<PERSON><PERSON><PERSON>ti minimálnu hodnotu v poli (stĺpci) záznamov databázy, ktorá spĺňa zadané podmienky"}, "DPRODUCT": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Vynásobí hodnoty v poli (stĺpci) záznamov databázy, ktoré spĺňajú zadané podmienky"}, "DSTDEV": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Odhadne smerodajnú odchýlku podľa vzorky vybratých položiek databázy"}, "DSTDEVP": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Vypočíta smerodajnú odchýlku podľa celého súboru vybratých položiek databázy"}, "DSUM": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Spočíta čísla v poli (stĺpci) záznamov databázy, ktoré spĺňajú zadané podmienky"}, "DVAR": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Odhadne rozptyl vzorky vybratých položiek databázy"}, "DVARP": {"a": "(datab<PERSON><PERSON>; pole; kritériá)", "d": "Vypočíta rozptyl podľa celého súboru vybratých položiek databázy"}, "CHAR": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti znak určený číslom kódu z tabuľky znakov používanej v danom počítači"}, "CLEAN": {"a": "(text)", "d": "Odstráni z textu všetky znaky, ktoré nemožno vytlačiť"}, "CODE": {"a": "(text)", "d": "<PERSON><PERSON><PERSON><PERSON>elný kód prvého znaku textového reťazca z tabuľky znakov na danom počítači"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Zlúči niekoľko textových reťazcov do jedného"}, "CONCAT": {"a": "(text1; ...)", "d": "Zreťazí zoznam alebo rozsah textových reťazcov"}, "DOLLAR": {"a": "(č<PERSON>lo; [desatinné_miesta])", "d": "Konvertuje číslo na text vo formáte meny"}, "EXACT": {"a": "(text1; text2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či sú dva textové reťazce identické, a vr<PERSON>ti hodnotu TRUE alebo FALSE. Funkcia EXACT rozlišuje malé a veľké písmená"}, "FIND": {"a": "(nájsť_text; v_texte; [po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_číslo])", "d": "Vráti počiatočnú pozíciu textového reťazca v rámci iného textového reťazca. Táto funkcia rozlišuje malé a veľké písmená"}, "FINDB": {"a": "(nájsť_text; v_texte; [po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_číslo])", "d": "<PERSON><PERSON><PERSON><PERSON> jeden textový reťazec vo vnútri druhého reťazca a vracajú počiatočnú pozíciu prvého reťazca od prvého znaku druhého reťazca, je určená pre jazyky, k<PERSON><PERSON> dvojbajtov<PERSON> tabu<PERSON> (DBCS) - japon<PERSON><PERSON>, čínština a kórejčina"}, "FIXED": {"a": "(č<PERSON>lo; [desatinné_miesta]; [bez_č<PERSON><PERSON>])", "d": "Zaokrúhli číslo na daný počet desatinných miest a vráti výsledok vo forme textu s alebo bez čiarok"}, "LEFT": {"a": "(text; [po<PERSON><PERSON>_z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> zadaný počet znakov od začiatku textového reťazca"}, "LEFTB": {"a": "(text; [po<PERSON><PERSON>_z<PERSON><PERSON>])", "d": "Vráti prvý znak alebo prvé znaky v textovom reťazci, pričom ich počet je určený veľkosťou v bajtoch, je určená pre jazyky, ktor<PERSON> pou<PERSON>jú dvojbajtovú tabuľku z<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON>, čínš<PERSON> a kórejčina"}, "LEN": {"a": "(text)", "d": "<PERSON><PERSON><PERSON><PERSON> počet znakov textového reťazca"}, "LENB": {"a": "(text)", "d": "Vráti počet bajtov použitých pre zobrazenie znakov v textovom reťazci, je určená pre jazyky, ktoré používajú dvojbajtovú tabuľku znakov (DBCS) - japončina, čínština a kórejčina"}, "LOWER": {"a": "(text)", "d": "Konvertuje všetky písmená v textovom reťazci na malé"}, "MID": {"a": "(text; po<PERSON><PERSON><PERSON><PERSON><PERSON>_pozícia; po<PERSON><PERSON>_<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> z<PERSON> z textového reťazca, ak je zadaná počiatočná pozícia a dĺžka"}, "MIDB": {"a": "(text; po<PERSON><PERSON><PERSON><PERSON><PERSON>_pozícia; po<PERSON><PERSON>_<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON>ti č<PERSON>ť textového reťazca od zadanej pozície a podľa zadaného poč<PERSON> baj<PERSON>, je určená pre jazyky, ktor<PERSON> pou<PERSON>j<PERSON> dvojbajtovú tabuľ<PERSON> z<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON>ínš<PERSON> a kórejčina"}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "Konvertuje text na číslo spôsobom, k<PERSON><PERSON> nie je závislý od miestnych nastavení"}, "PROPER": {"a": "(text)", "d": "Písmená v textovom reťazci konvertuje do riadneho formátu, prvé písmeno každého slova na veľké a všetky ostatné písmená na malé"}, "REPLACE": {"a": "(starý_text; počiatočné_číslo; poč<PERSON>_znakov; nový_text)", "d": "Nahradí časť textového reťazce iným textovým reťazcom"}, "REPLACEB": {"a": "(starý_text; počiatočné_číslo; poč<PERSON>_znakov; nový_text)", "d": "Nahradí časť textového reťazca so zadaným počtom bajtov odlišným textovým reťazcom, je určená pre jazyky, ktor<PERSON> pou<PERSON> dvojbajtovú tabuľ<PERSON> z<PERSON> (DBCS) - <PERSON><PERSON><PERSON><PERSON>, <PERSON>ínš<PERSON> a kórejčina"}, "REPT": {"a": "(text; počet_opakovaní)", "d": "Text sa zopakuje podľa zadaného počtu opakovaní. Ak chcete bunku vyplniť určitým počtom opakovaní textového reťazca, použite funkciu REPT"}, "RIGHT": {"a": "(text; [po<PERSON><PERSON>_z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>ý počet znakov od konca textového reťazca"}, "RIGHTB": {"a": "(text; [po<PERSON><PERSON>_z<PERSON><PERSON>])", "d": "Vráti znaky z konca textového reťazca, pričom dĺžka výsledku je zadaná v bajtoch, je určená pre jazyky, ktor<PERSON> použ<PERSON>jú dvojbajtovú tabuľku znakov (DBCS) - <PERSON><PERSON><PERSON><PERSON>, čínština a kórejčina"}, "SEARCH": {"a": "(nájsť_text; v_texte; [pozícia_začiatku])", "d": "<PERSON><PERSON><PERSON><PERSON>lo prvého výskytu daného znaku alebo textového reťazca. Program hľadá zľava doprava a nerozlišuje veľké a malé písmená"}, "SEARCHB": {"a": "(nájsť_text; v_texte; [pozícia_začiatku])", "d": "Vyhľadávajú jeden textový reťazec v rámci druhého textového reťazca a vrátia číslo počiatočnej pozície prvého textového reťazca od prvého znaku druhého textového reťazca, je určená pre jazyky, k<PERSON><PERSON> použ<PERSON>jú dvojbajtov<PERSON> tabuľku znakov (DBCS) - japončina, čínština a kórejčina"}, "SUBSTITUTE": {"a": "(text; starý_text; nový_text; [č<PERSON>lo_inštancie])", "d": "Nahradí existujúci text v textovom reťazci novým textom"}, "T": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument textovou hodnotou. <PERSON><PERSON>, vr<PERSON><PERSON> text, v opačnom prípade vr<PERSON><PERSON> (bez textu)"}, "TEXT": {"a": "(hodnota; formát_text)", "d": "Konvertuje hodnotu na text v špeciálnom číselnom formáte"}, "TEXTJOIN": {"a": "(odd<PERSON><PERSON><PERSON><PERSON>; ignorovať_prázdne; text1; ...)", "d": "Zreťazí zoznam alebo rozsah textových reťazcov použitím oddeľovačov"}, "TRIM": {"a": "(text)", "d": "Odstráni všetky medzery z textového reťazca okrem jednotlivých medzier medzi slovami"}, "UNICHAR": {"a": "(number)", "d": "Vráti znak Unicode, na ktorý odkazuje daná číselná hodnota"}, "UNICODE": {"a": "(text)", "d": "<PERSON><PERSON><PERSON><PERSON> (bod kódu) zodpovedajúce prvému znaku textu"}, "UPPER": {"a": "(text)", "d": "Konvertuje všetky písmená v textovom reťazci na veľké"}, "VALUE": {"a": "(text)", "d": "Konvertuje textový reťazec predstavujúci číslo na číslo"}, "AVEDEV": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON>ráti priemernú hodnotu absolútnych odchýlok údajových bodov od ich priemeru. Argumenty môžu byť čísla alebo názvy, polia alebo odkazy obsahujúce čísla"}, "AVERAGE": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> prie<PERSON> hodnot<PERSON> (aritmetický priemer), pri<PERSON><PERSON> to mô<PERSON><PERSON> byť čísla alebo názvy, polia alebo odkazy obsahuj<PERSON><PERSON> čísla"}, "AVERAGEA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> prie<PERSON> hodnotu (aritmet<PERSON><PERSON> priemer) argumentov. Text a logická hodnota FALSE = 0; TRUE = 1. Argumenty m<PERSON> by<PERSON> č<PERSON>, n<PERSON><PERSON><PERSON>, polia alebo odkazy"}, "AVERAGEIF": {"a": "(rozsah; krit<PERSON><PERSON><PERSON>; [prie<PERSON><PERSON><PERSON>_rozsah])", "d": "Vyhľ<PERSON><PERSON> priemer (aritmetick<PERSON> priemer) b<PERSON><PERSON> stano<PERSON>ch podľa zadanej podmienky alebo kritéria"}, "AVERAGEIFS": {"a": "(prie<PERSON><PERSON><PERSON>_rozsah; rozsah_kritérií; kritéri<PERSON>; ...)", "d": "Vyhľ<PERSON><PERSON> priemer (aritmetick<PERSON> priemer) b<PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON> zadanou množinou podmienok alebo kritérií"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie rozdelenia pravdepodobnosti beta"}, "BETAINV": {"a": "(pravdepodobnosť; alfa; beta; [A]; [B])", "d": "Vráti inverznú hodnotu súčtovej hustoty rozdelenia pravdepodobnosti beta (inverzná funkcia k funkcii BETADIST)"}, "BETA.DIST": {"a": "(x; alfa; beta; kumulatívne; [A]; [B])", "d": "<PERSON><PERSON><PERSON><PERSON> rozdelenia pravdepodobnosti beta"}, "BETA.INV": {"a": "(pravdepodobnosť; alfa; beta; [A]; [B])", "d": "<PERSON><PERSON>áti inverznú hodnotu funkcie kumulatívnej hustoty pravdepodobnosti beta (BETA.DIST)"}, "BINOMDIST": {"a": "(počet_s; pokusy; pravdepodobnosť_s; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu binomického rozdelenia pravdepodobnosti jednotlivých veličín"}, "BINOM.DIST": {"a": "(číslo_s; pokusy; pravdepodobnosť_s; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu binomického rozdelenia pravdepodobnosti jednotlivých veličín"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "<PERSON><PERSON><PERSON><PERSON> pravdepodobnosť skúšobného výsledku pomocou binomického rozdelenia"}, "BINOM.INV": {"a": "(pokusy; pravdepodobnosť_s; alfa)", "d": "<PERSON><PERSON><PERSON><PERSON> na<PERSON><PERSON> hodnot<PERSON>, pre ktorú má distribučná funkcia binomického rozdelenia hodnotu väčšiu alebo rovnajúcu sa hodnote kritéria"}, "CHIDIST": {"a": "(x; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> sprava ohraničenú pravdepodobnosť pre rozdelenie chí-kvadrát"}, "CHIINV": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu funkcie inverznej k sprava ohraničenej pravdepodobnosti rozdelenia chí-kvadrát"}, "CHITEST": {"a": "(skuto<PERSON><PERSON><PERSON>_rozsah; očaká<PERSON>ý_rozsah)", "d": "Počíta test nezávislosti: hodnotu rozdelenia chí-kvadrát pre štatistiku a príslušný počet stupňov voľnosti"}, "CHISQ.DIST": {"a": "(x; stupeň_voľnosti; kumulatívne)", "d": "Vráti zľava ohraničenú pravdepodobnosť rozdelenia chí-kvadrát"}, "CHISQ.DIST.RT": {"a": "(x; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> sprava ohraničenú pravdepodobnosť rozdelenia chí-kvadrát"}, "CHISQ.INV": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "Vráti inverznú hodnotu zľava ohraničenej pravdepodobnosti rozdelenia chí-kvadrát"}, "CHISQ.INV.RT": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON>ti inverznú hodnotu sprava ohraničenej pravdepodobnosti rozdelenia chí-kvadrát"}, "CHISQ.TEST": {"a": "(skuto<PERSON><PERSON><PERSON>_rozsah; očaká<PERSON>ý_rozsah)", "d": "Počíta test nezávislosti: hodnotu rozdelenia chí-kvadrát pre štatistiku a príslušný počet stupňov voľnosti"}, "CONFIDENCE": {"a": "(alfa; smerodajná_odch; veľkosť)", "d": "Vráti interval spoľahlivosti pre strednú hodnotu základného súboru s použitím normálneho rozloženia"}, "CONFIDENCE.NORM": {"a": "(alfa; smerodajná_odch; veľkosť)", "d": "Vráti interval spoľahlivosti pre strednú hodnotu súboru s použitím normálneho rozloženia"}, "CONFIDENCE.T": {"a": "(alfa; smerodajná_odch; veľkosť)", "d": "Vráti interval spoľahlivosti pre strednú hodnotu súboru s použitím Studentovho T-rozdelenia"}, "CORREL": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> koeficient pre dva súbory údajov"}, "COUNT": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Spočíta počet buniek v rozsahu, ktorý obsahuje čísla"}, "COUNTA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Spočíta bunky v rozsahu, ktoré nie sú prázdne"}, "COUNTBLANK": {"a": "(rozsah)", "d": "Vráti počet prázdnych buniek v danom rozsahu"}, "COUNTIF": {"a": "(rozsah; kritériá)", "d": "Spočíta bunky v danom rozsahu, ktoré spĺňajú zadanú podmienku"}, "COUNTIFS": {"a": "(rozsah_kritérií; kritériá; ...)", "d": "Spočíta počet buniek podľa zadanej množiny podmienok alebo kritérií"}, "COVAR": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu k<PERSON>, priemernú hodnotu súčinu odchýlok pre každú dvojicu údajových bodov v dvoch množinách údajov"}, "COVARIANCE.P": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON>, prie<PERSON> sú<PERSON>inov odchýlok pre každý pár údajových bodov v dvoch množinách údajov"}, "COVARIANCE.S": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>, prie<PERSON> súčinov odchýlok pre každý pár údajových bodov v dvoch množinách údajov"}, "CRITBINOM": {"a": "(pokusy; pravdepodobnosť_s; alfa)", "d": "<PERSON><PERSON><PERSON><PERSON> na<PERSON><PERSON> hodnot<PERSON>, pre ktorú má distribučná funkcia binomického rozdelenia hodnotu väčšiu alebo rovnajúcu sa hodnote kritéria"}, "DEVSQ": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> druhých mocnín odchýlok údajových bodov od strednej hodnoty vzorky"}, "EXPONDIST": {"a": "(x; lambda; kumulat<PERSON>vne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty exponenciálneho rozdelenia"}, "EXPON.DIST": {"a": "(x; lambda; kumulat<PERSON>vne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty exponenciálneho rozdelenia"}, "FDIST": {"a": "(x; stupeň_voľnosti1; stupeň_voľnosti2)", "d": "<PERSON><PERSON><PERSON><PERSON> (sprava ohraničeného) rozdelenia pravdepodobnosti F (stupeň odlišnosti) pre dve množiny údajov"}, "FINV": {"a": "(pravdepodobnosť; stupeň_voľnosti1; stupeň_voľnosti2)", "d": "Vráti hodnotu inverznej funkcie k (sprava ohraničenej) distribučnej funkcii rozdelenia pravdepodobnosti F: ak p = FDIST(x,...), potom FINV(p,...) = x"}, "FTEST": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> výsledok F-testu, dvojstrannú pravdepodobnosť, že rozptyly v argumentoch Pole1 a Pole2 nie sú výrazne odlišné"}, "F.DIST": {"a": "(x; stupeň_voľnosti1; stupeň_voľnosti2; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> (zľava ohraničené) F rozdelenie pravdepodobnosti (miera rozličnosti) pre dve množiny údajov"}, "F.DIST.RT": {"a": "(x; stupeň_voľnosti1; stupeň_voľnosti2)", "d": "<PERSON><PERSON><PERSON><PERSON> (sprava ohraničené) F rozdelenie pravdepodobnosti (miera rozličnosti) pre dve množiny údajov"}, "F.INV": {"a": "(pravdepodobnosť; stupeň_voľnosti1; stupeň_voľnosti2)", "d": "<PERSON><PERSON><PERSON><PERSON> in<PERSON> (ľavostranného) rozdelenia pravdepodobnosti F: ak p = F.DIST(x,...), potom F.INV(p,...) = x"}, "F.INV.RT": {"a": "(pravdepodobnosť; stupeň_voľnosti1; stupeň_voľnosti2)", "d": "<PERSON><PERSON><PERSON><PERSON> inverzn<PERSON> hodnotu (sprava ohraničeného) F rozdelenia pravdepodobnosti: ak p = F.DIST.RT(x,...), potom F.INV.RT(p,...) = x"}, "F.TEST": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> výsledok F-testu, dvojstrannú pravdepodobnosť, že rozptyly v argumentoch Pole1 a Pole2 nie sú výrazne odlišné"}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON> Fisherovej transformácie"}, "FISHERINV": {"a": "(y)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu inverznej funkcie k Fisherovej transformácii: ak y = FISHER(x), potom FISHERINV(y) = x"}, "FORECAST": {"a": "(x; známe_ys; známe_xs)", "d": "Vypočíta alebo odhadne bud<PERSON> hodnotu lineárneho trendu pomocou existuj<PERSON><PERSON><PERSON> hodn<PERSON>t"}, "FORECAST.ETS": {"a": "(cie<PERSON><PERSON><PERSON>_d<PERSON><PERSON>; hodnoty; čas<PERSON>_os; [sez<PERSON><PERSON><PERSON>]; [dopĺňanie_údajov]; [agreg<PERSON>cia])", "d": "<PERSON><PERSON><PERSON><PERSON> predpoved<PERSON>ú hodnotu pre konkrétny budúci cieľový dátum pomocou metódy exponenciálneho vyrovnávania."}, "FORECAST.ETS.CONFINT": {"a": "(cie<PERSON><PERSON><PERSON>_d<PERSON><PERSON>; hodnoty; čas<PERSON>_os; [úrove<PERSON>_spoľahlivosti]; [sez<PERSON><PERSON><PERSON>]; [dopĺňanie_údajov]; [agreg<PERSON>cia])", "d": "Vráti interval spoľahlivosti pre predpovedanú hodnotu v zadanom cieľovom dátume."}, "FORECAST.ETS.SEASONALITY": {"a": "(hodnoty; časová_os; [dopĺňanie_údajov]; [agreg<PERSON><PERSON>])", "d": "Vráti dĺžku opakujúceho sa vzoru, ktorý aplikácia zistí pre zadaný časový rad."}, "FORECAST.ETS.STAT": {"a": "(hodnoty; časová_os; typ_štatistiky; [sez<PERSON><PERSON><PERSON>]; [dopĺňanie_údajov]; [agreg<PERSON><PERSON>])", "d": "Vrá<PERSON> p<PERSON>žadovanú štatistiku pre prognózu."}, "FORECAST.LINEAR": {"a": "(x; známe_y; známe_x)", "d": "Vypočíta alebo odhadne bud<PERSON> hodnotu lineárneho trendu pomocou existuj<PERSON><PERSON><PERSON> hodn<PERSON>t"}, "FREQUENCY": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>_pole; <PERSON><PERSON><PERSON>_pole)", "d": "Vytvorí frekvenčnú tabuľku, čiže zvislé pole čísel s počtami výskytov hodnôt v jednotlivých rozsahoch"}, "GAMMA": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnot<PERSON> funkcie Gamma"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty rozdelenia gama"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> gama"}, "GAMMAINV": {"a": "(pravdepodobnosť; alfa; beta)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu inverznej funkcie k distribučnej funkcii gama rozdelenia: ak p = GAMMADIST(x,...), potom GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(pravdepodobnosť; alfa; beta)", "d": "<PERSON><PERSON><PERSON><PERSON> inverznú hodnotu kumulatívneho rozdelenia gama: ak p = GAMMA.DIST(x,...), potom GAMMA.INV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> prirodzený logaritmus funkcie gama"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> prirodzený logaritmus funkcie gama"}, "GAUSS": {"a": "(x)", "d": "Vráti hodnotu o 0,5 ni<PERSON>šiu než štandardné normálne kumulatívne rozdelenie"}, "GEOMEAN": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> priemer poľa alebo rozsahu kladn<PERSON>ch číselných údajov"}, "GROWTH": {"a": "(známe_ys; [známe_xs]; [nové_xs]; [konštanta])", "d": "<PERSON><PERSON><PERSON><PERSON> hodn<PERSON> trendu exponenciálneho rastu, ktor<PERSON> zodpovedá známym údajovým bodom"}, "HARMEAN": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> priemer množiny kladných číselných údajov: prevrátená hodnota aritmetického priemeru prevr<PERSON>ten<PERSON>ch hodnôt"}, "HYPGEOM.DIST": {"a": "(vzorka_s; veľkosť_vzorky; populácia_s; počet_pop; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperge<PERSON><PERSON><PERSON> roz<PERSON>"}, "HYPGEOMDIST": {"a": "(vzorka_s; počet_vzorka; populácia_s; počet_pop)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu hypergeometrického rozdelenia."}, "INTERCEPT": {"a": "(známe_ys; známe_xs)", "d": "Vypočíta súradnice bodu, v ktorom čiara pretína os y. Výpočet sa robí preložením najlepšej regresnej čiary známymi hodnotami x a y"}, "KURT": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> množiny údajov"}, "LARGE": {"a": "(pole; k)", "d": "Vráti k-tu najväčšiu hodnotu v množine údajov, napríklad piate najväčšie číslo"}, "LINEST": {"a": "(známe_ys; [známe_xs]; [kon<PERSON>tanta]; [štatist<PERSON>])", "d": "Vráti štatistiku popisujúcu lineárny trend zodpovedajúci známym údajovým bodom aproximáciou priamky metódou naj<PERSON>ších <PERSON>tvorcov"}, "LOGEST": {"a": "(známe_ys; [známe_xs]; [kon<PERSON>tanta]; [štatist<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> štatistiku popisujúcu exponenciálnu krivku zodpovedajúcu známym údajovým bodom"}, "LOGINV": {"a": "(pravde<PERSON><PERSON>bnosť; stred; smerodajná_odch)", "d": "Vráti inverznú funkciu k distribučnej funkcii lognormálneho rozdelenia hodnôt x, kde funkcia ln(x) má normálne rozdelenie s parametrami Stred a Smerodajná_odch"}, "LOGNORM.DIST": {"a": "(x; stred; smer<PERSON><PERSON><PERSON>_odch; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> rozdelenie prirodzeného logaritmu x, kde ln(x) je normálne rozdelený s parametrami Stred a Smerodajná_odch"}, "LOGNORM.INV": {"a": "(pravde<PERSON><PERSON>bnosť; stred; smerodajná_odch)", "d": "Vráti inverznú funkciu k distribučnej funkcii lognormálneho rozdelenia hodnôt x, kde funkcia ln(x) má normálne rozdelenie s parametrami Stred a Smerodajná_odch"}, "LOGNORMDIST": {"a": "(x; stred; smerodajná_odch)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie lognormálneho rozdelenia hodnôt x, kde funkcia ln(x) má normálne rozdelenie s parametrami Stred a Smerodajná_odch"}, "MAX": {"a": "(číslo1; [číslo2]; ...)", "d": "Vráti najvyš<PERSON> hodnotu z množiny hodnôt. Ignoruje logické hodnoty a text"}, "MAXA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vráti najväčšiu hodnotu v množine hodnôt. Neignoruje logické hodnoty a text"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Vráti maximálnu hodnotu nachádzajúcu sa v bunkách špecifikovaných určenou skupinou podmienok alebo kritérií"}, "MEDIAN": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, čiže hodnotu v strede množiny daných čísel"}, "MIN": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> najnižšie číslo z množiny hodnôt. Ignoruje logické hodnoty a text"}, "MINA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vráti najmenšiu hodnotu v množine hodnôt. Neignoruje logické hodnoty a text"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Vráti minimálnu hodnotu nachádzajúcu sa v bunkách špecifikovaných určenou skupinou podmienok alebo kritérií"}, "MODE": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> modus, <PERSON><PERSON><PERSON><PERSON> hodnotu, ktorá sa v poli alebo rozsahu údajov vyskytuje alebo opakuje najčastejšie"}, "MODE.MULT": {"a": "(číslo1; [číslo2]; ...)", "d": "Vráti z<PERSON> pole najčastejšie sa vyskytujúcich (alebo opakujúcich sa) hodnôt v poli alebo rozsahu údajov. V prípade vodorovného poľa použite funkciu =TRANSPOSE(MODE.MULT(číslo1,číslo2,...))"}, "MODE.SNGL": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> modus, <PERSON><PERSON><PERSON><PERSON> hodnotu, ktorá sa v poli alebo rozsahu údajov vyskytuje alebo opakuje najčastejšie"}, "NEGBINOM.DIST": {"a": "(počet_f; počet_s; pravdepodobnosť_s; kumulatívne)", "d": "Záporné binomi<PERSON> r<PERSON>, pravdepodobnosť počet_f zlyhaní pred počet_s-tym úspechom pri pravdepodobnosti Pravdepodobnosť_s"}, "NEGBINOMDIST": {"a": "(počet_f; počet_s; pravdepodobnosť_s)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu záporného binomického rozdelenia (pravdepodobnosť Počet_f neúspešných pokusov po Počet_s úspešných pri pravdepodobnosti Pravdepodobnosť_s"}, "NORM.DIST": {"a": "(x; stred; smer<PERSON><PERSON><PERSON>_odch; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> rozdelenie pre zadanú strednú hodnotu a štandardnú odchýlku"}, "NORMDIST": {"a": "(x; stred; smer<PERSON><PERSON><PERSON>_odch; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty normálneho rozdelenia pre zadanú strednú hodnotu a smerodajnú odchýlku"}, "NORM.INV": {"a": "(pravde<PERSON><PERSON>bnosť; stred; smerodajná_odch)", "d": "Vráti inverznú funkciu k distribučnej funkcii normálneho rozdelenia pre zadanú strednú hodnotu a smerodajnú odchýlku"}, "NORMINV": {"a": "(pravde<PERSON><PERSON>bnosť; stred; smerodajná_odch)", "d": "Vráti inverznú funkciu k distribučnej funkcii normálneho rozdelenia pre zadanú strednú hodnotu a smerodajnú odchýlku"}, "NORM.S.DIST": {"a": "(z; kumulat<PERSON>vne)", "d": "<PERSON><PERSON><PERSON><PERSON> normálne rozdelenie (so strednou hodnotou nula a štandardnou odchýlk<PERSON> jeden)"}, "NORMSDIST": {"a": "(z)", "d": "Vráti hodnotu distribučnej funkcie štandardného normálneho rozdelenia. Toto rozdelenie má strednú hodnotu 0 a smerodajnú odchýlku 1."}, "NORM.S.INV": {"a": "(pravdepodobnosť)", "d": "Vráti inverznú funkciu k distribučnej funkcii štandardného normálneho rozdelenia. Toto rozdelenie má strednú hodnotu 0 a smerodajnú odchýlku 1."}, "NORMSINV": {"a": "(pravdepodobnosť)", "d": "Vráti inverznú funkciu k distribučnej funkcii štandardného normálneho rozdelenia. Toto rozdelenie má strednú hodnotu 0 a smerodajnú odchýlku 1."}, "PEARSON": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> koe<PERSON> korelácie r"}, "PERCENTILE": {"a": "(pole; k)", "d": "Vráti k-ty percentil hodnôt v rozsahu"}, "PERCENTILE.EXC": {"a": "(pole; k)", "d": "Vráti k-ty percentil hodnôt v rozsahu, kde k je z rozsahu čísel väčších ako 0 a menších ako 1"}, "PERCENTILE.INC": {"a": "(pole; k)", "d": "Vráti k-ty percentil hodnôt v rozsahu, kde k je z rozsahu čísel od 0 do 1 vrátane"}, "PERCENTRANK": {"a": "(pole; x; [význam<PERSON>ť])", "d": "Vráti poradie hodnoty v množine údajov vyjadrené percentuálnou časťou množiny údajov"}, "PERCENTRANK.EXC": {"a": "(pole; x; [význam<PERSON>ť])", "d": "Vráti pozíciu hodnoty v množine údajov ako percentuálnu hodnotu (hodnotu väčšiu ako 0 a menšiu ako 1) množiny údajov"}, "PERCENTRANK.INC": {"a": "(pole; x; [význam<PERSON>ť])", "d": "Vráti pozíciu hodnoty v množine údajov ako percentuálnu hodnotu množiny údajov od 0 do 1 vrátane"}, "PERMUT": {"a": "(počet; vybratý_počet)", "d": "Vracia počet permutácií pre daný počet objektov, ktoré možno vybrať z celkového počtu objektov"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "<PERSON><PERSON><PERSON><PERSON> počet permutácií pre daný počet objektov (s opakovaniami), k<PERSON><PERSON> mô<PERSON> byť vybraté z celkového počtu objektov"}, "PHI": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu funkcie hustoty pre štandardné normálne rozdelenie"}, "POISSON": {"a": "(x; stred; kumula<PERSON><PERSON><PERSON>ne)", "d": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON> Poissonovho rozdelenia"}, "POISSON.DIST": {"a": "(x; stred; kumula<PERSON><PERSON><PERSON>ne)", "d": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON> Poissonovho rozdelenia"}, "PROB": {"a": "(x_rozsah; rozsah_pravdepodobnosti; dolný_limit; [horný_limit])", "d": "Vráti pravdepodobnosť, že hodnoty v rozsahu sú medzi dvoma hranicami, alebo sa rovnajú dolnej hranici"}, "QUARTILE": {"a": "(pole; kvart)", "d": "<PERSON><PERSON><PERSON><PERSON>"}, "QUARTILE.INC": {"a": "(pole; kvart)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> množiny údajov na základe hodnôt percentilov od 0 do 1 vrátane"}, "QUARTILE.EXC": {"a": "(pole; kvart)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>til množiny údajov na základe hodnôt percentilov, ktoré sú väčšie ako 0 a menšie ako 1"}, "RANK": {"a": "(č<PERSON>lo; odkaz; [poradie])", "d": "Vráti relatívnu veľkosť čísla v zozname čísel vzhľadom na hodnoty v zozname"}, "RANK.AVG": {"a": "(č<PERSON>lo; odkaz; [poradie])", "d": "Vráti pozíciu čísla v zozname čísel: jeho veľkosť vo vzťahu k ostatným hodnotám v zozname; ak má viacero hodnôt rovnakú pozíciu, vráti sa priemerná pozícia"}, "RANK.EQ": {"a": "(č<PERSON>lo; odkaz; [poradie])", "d": "Vráti pozíciu čísla v zozname čísel: jeho veľkosť vo vzťahu k ostatným hodnotám v zozname; ak má viacero hodnôt rovnakú pozíciu, vráti sa najvyššia pozícia tejto množiny hodnôt"}, "RSQ": {"a": "(známe_ys; známe_xs)", "d": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> mocninu <PERSON> kore<PERSON>čného koe<PERSON>u dan<PERSON><PERSON> údajov<PERSON> bodov"}, "SKEW": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> rozdelenia: charakteristika stupňa asymetrie rozdelenia okolo strednej hodnoty"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> rozdelenia na základe populácie: charakteristika stupňa asymetrie rozdelenia okolo strednej hodnoty"}, "SLOPE": {"a": "(známe_ys; známe_xs)", "d": "Vráti gradient lineárnej regresnej čiary daných údajových bodov"}, "SMALL": {"a": "(pole; k)", "d": "Vráti k-tu najmenšiu hodnotu v množine údajov, napríklad piate najmenšie číslo"}, "STANDARDIZE": {"a": "(x; stred; smerodajná_odch)", "d": "Vracia normalizovanú hodnotu z rozdelenia určeného strednou hodnotou a smerodajnou odchýlkou "}, "STDEV": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne smerodajnú odchýlku na základe vzorky (ignoruje logické hodnoty a text vo vzorke)"}, "STDEV.P": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočíta s<PERSON> odchý<PERSON> cel<PERSON>, k<PERSON><PERSON> bol zadaný ako argument (ignoruje logické hodnoty a text)"}, "STDEV.S": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne smerodajnú odchýlku na základe vzorky (ignoruje logické hodnoty a text vo vzorke)"}, "STDEVA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Odhadne smerodajnú odchýlku podľa výberového súboru vrátane logických hodnôt a textu. Text a logická hodnota FALSE = 0; logická hodnota TRUE = 1"}, "STDEVP": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočíta s<PERSON> odchý<PERSON> cel<PERSON>, k<PERSON><PERSON> bol zadaný ako argument (ignoruje logické hodnoty a text)"}, "STDEVPA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vypočíta smerodajnú odchýlku základného súboru vrátane logických hodnôt a textu. Text a logická hodnota FALSE = 0; logická hodnota TRUE = 1"}, "STEYX": {"a": "(známe_ys; známe_xs)", "d": "<PERSON><PERSON><PERSON><PERSON> chybu predpokladanej hodnoty y pre každé x regresie"}, "TDIST": {"a": "(x; stupeň_voľnosti; strany)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnot<PERSON>ovho t-rozdelenia"}, "TINV": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> inverznú funkciu k funkcii Studentovho t-rozdelenia"}, "T.DIST": {"a": "(x; stupeň_voľnosti; kumulatívne)", "d": "Vráti zľava ohraničené Studentovo t-rozdelenie"}, "T.DIST.2T": {"a": "(x; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> ohraničené Studentovo t-rozdelenie"}, "T.DIST.RT": {"a": "(x; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> sprava ohraničené Studentovo t-rozdelenie"}, "T.INV": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>ava ohraničenú hodnotu Studentovho t-rozdelenia"}, "T.INV.2T": {"a": "(pravdepodobnosť; stupeň_voľnosti)", "d": "<PERSON><PERSON><PERSON><PERSON> ohraničenú hodnotu Studentovho t-rozdelenia"}, "T.TEST": {"a": "(pole1; pole2; strany; typ)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu Studentovho t-testu"}, "TREND": {"a": "(známe_ys; [známe_xs]; [nové_xs]; [konštanta])", "d": "<PERSON><PERSON><PERSON><PERSON> lineárneho trendu zodpovedajúce známym údajovým bodom pomocou metódy naj<PERSON>š<PERSON>vor<PERSON>v"}, "TRIMMEAN": {"a": "(pole; percento)", "d": "<PERSON>ráti priemernú hodnotu vnútornej časti množiny hodnôt údajov"}, "TTEST": {"a": "(pole1; pole2; strany; typ)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu Studentovho t-testu"}, "VAR": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne rozptyl na základe vzorky (ignoruje logické hodnoty a text vo vzorke)"}, "VAR.P": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočíta rozptyl základného súboru (ignoruje logické hodnoty a text v súbore)"}, "VAR.S": {"a": "(číslo1; [číslo2]; ...)", "d": "Odhadne rozptyl na základe vzorky (vo vzorke ignoruje logické hodnoty a text)"}, "VARA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Odhadne rozptyl podľa výberového súboru vrátane logických hodnôt a textu. Text a logická hodnota FALSE = 0; logická hodnota TRUE = 1"}, "VARP": {"a": "(číslo1; [číslo2]; ...)", "d": "Vypočíta rozptyl základného súboru (ignoruje logické hodnoty a text v súbore)"}, "VARPA": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vypočíta rozptyl základného súboru vrátane logických hodnôt a textu. Text a logická hodnota FALSE = 0; logická hodnota TRUE = 1"}, "WEIBULL": {"a": "(x; alfa; beta; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty Weibullovho rozdelenia"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulatívne)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu distribučnej funkcie alebo hustoty Weibullovho rozdelenia"}, "Z.TEST": {"a": "(pole; x; [sigma])", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>nnú P-hodnotu z-testu"}, "ZTEST": {"a": "(pole; x; [sigma])", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>nnú P-hodnotu z-testu"}, "ACCRINT": {"a": "(emisia; prv<PERSON>_úrok; vyrovnanie; sadzba; por; frekvencia; [z<PERSON><PERSON>]; [met<PERSON><PERSON>_výpo<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>ý úrok cenného papiera, z ktorého sa pravidelne platí úrok."}, "ACCRINTM": {"a": "(emisia; vyrovnanie; sadz<PERSON>; por; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> akumulovaný úrok cenného papiera, za ktorý sa platí úrok k dátumu splatnosti"}, "AMORDEGRC": {"a": "(cena; dá<PERSON>_nákupu; prv<PERSON>_obdobie; zost<PERSON><PERSON>; obdobie; sadzba; [z<PERSON><PERSON>])", "d": "Vráti pomernú časť lineárneho odpisu majetku za každé účtovné obdobie."}, "AMORLINC": {"a": "(cena; dá<PERSON>_nákupu; prv<PERSON>_obdobie; zost<PERSON><PERSON>; obdobie; sadzba; [z<PERSON><PERSON>])", "d": "Vráti pomernú časť lineárneho odpisu majetku za každé účtovné obdobie."}, "COUPDAYBS": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON>rá<PERSON> počet dní od začiatku kupónového obdobia až po dátum vyrovnania"}, "COUPDAYS": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> po<PERSON> dn<PERSON> obdobia, k<PERSON><PERSON> obsahuj<PERSON> dá<PERSON> vyrovnania"}, "COUPDAYSNC": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> počet dní od dátumu vyrovnania po ďalší dátum splatnosti kupónu"}, "COUPNCD": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> dátum splatnosti kupónu dátume vyrovnania"}, "COUPNUM": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> počet kupónových splátok medzi dátumom vyrovnania a dátumom splatnosti"}, "COUPPCD": {"a": "(vyrovnanie; splatnosť; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> dá<PERSON> sp<PERSON>, k<PERSON><PERSON> predch<PERSON><PERSON> dátumu vyrovnania"}, "CUMIPMT": {"a": "(sadz<PERSON>; pobd; sh; po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_obdobie; koncové_obdobie; typ)", "d": "<PERSON><PERSON><PERSON><PERSON> kumulatívny vyplatený úrok medzi dvomi obdobiami"}, "CUMPRINC": {"a": "(sadz<PERSON>; pobd; sh; po<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_obdobie; koncové_obdobie; typ)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> vyplatenú istinu z úveru medzi dvomi obdobiami"}, "DB": {"a": "(cena; zostatok; životnosť; obdobie; [mesiac])", "d": "Vypočíta odpis majetku za zadané obdobie podľa metódy klesajúceho zostatku s pevným koeficientom."}, "DDB": {"a": "(cena; zostatok; životnosť; obdo<PERSON>; [faktor])", "d": "Vypočíta odpis majetku za zadané obdobie podľa metódy dvojnásobného odpisovania z klesajúceho zostatku alebo inej zadanej metódy"}, "DISC": {"a": "(vyrovnanie; splatnosť; ss; vy<PERSON>nie; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON>zbu cenného papiera"}, "DOLLARDE": {"a": "(z<PERSON><PERSON><PERSON><PERSON>_<PERSON>; z<PERSON><PERSON><PERSON>)", "d": "Skonvertuje cenu dolára vyjadrenú zlomkom na cenu dolára vyjadrenú desatinným číslom"}, "DOLLARFR": {"a": "(desatinn<PERSON>_do<PERSON>; <PERSON><PERSON><PERSON><PERSON>)", "d": "Skonvertuje cenu dolára vyjadrenú desatinným číslom na cenu dolára vyjadrenú zlomkom"}, "DURATION": {"a": "(vyrovnanie; splatnosť; kupón; výnos; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> ročné trvanie cenného papiera s pravidelnými splátkami úroku"}, "EFFECT": {"a": "(nominálna_sadzba; pzobd)", "d": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> roč<PERSON>ú <PERSON> mieru"}, "FV": {"a": "(sad<PERSON><PERSON>; počet_období; spl<PERSON>tka; [s<PERSON><PERSON><PERSON><PERSON>_hodnota]; [typ])", "d": "Vypočíta budúcu hodnotu investície pri pravidelných a konštantných platbách a konštantnej úrokovej sadzbe"}, "FVSCHEDULE": {"a": "(istina; plán)", "d": "<PERSON><PERSON><PERSON><PERSON> bud<PERSON><PERSON> hodnotu úvodnej istiny po použití série zložených úrokových mier"}, "INTRATE": {"a": "(vyrovnanie; splatnosť; spl<PERSON>tka; vy<PERSON>nie; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> mieru za úplne investovaný cenný papier"}, "IPMT": {"a": "(sadzba; obd; pobd; sh; [bh]; [typ])", "d": "Vypočíta výšku platby úroku v určitom úrokovom období pri pravidelných konštantných splátkach a konštantnej úrokovej sadzbe"}, "IRR": {"a": "(hodnoty; [odhad])", "d": "Vypočíta vnútornú mieru návratnosti pre sériu hotovostn<PERSON> tokov"}, "ISPMT": {"a": "(sadzba; obd; pobd; sh)", "d": "Vypočíta úrok zaplatený v zadanom období investície"}, "MDURATION": {"a": "(vyrovnanie; splatnosť; kupón; výnos; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> upravené trvanie za cenný papier s predpokladanou nominálnou hodnotou 100 USD"}, "MIRR": {"a": "(hodnoty; finančná_sadzba; reinvestičná_sadzba)", "d": "Vypočíta vnútornú mieru návratnosti pre sériu pravidelných hotovostných tokov. Zohľadňuje pritom náklady na investície, ako aj úrok z opätovnej investície získaných prostriedkov"}, "NOMINAL": {"a": "(účinn<PERSON>_sadzba; pzobd)", "d": "<PERSON><PERSON><PERSON><PERSON> roč<PERSON>ú nominálnu úrokovú mieru"}, "NPER": {"a": "(sadzba; plt; sh; [bh]; [typ])", "d": "Vypočíta počet období pre investíciu pri pravidelných konštantných platbách a konštantnej úrokovej sadzbe"}, "NPV": {"a": "(sadz<PERSON>; hodnota1; [hodnota2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>časnú hodnotu investície vypočítanú na základe diskontnej sadzby, série bud<PERSON><PERSON><PERSON> spl<PERSON> (záporná hodnota) a príjmov (kladná hodnota)"}, "ODDFPRICE": {"a": "(vyrovnanie; splatnosť; emisia; prv<PERSON>_kup<PERSON>; sadzba; výnos; vyplatenie; frekvencia; [z<PERSON><PERSON>])", "d": "Vráti cenu za každých 100 USD nominálnej hodnoty cenného papiera s nepárnym prvým obdobím"}, "ODDFYIELD": {"a": "(vyrovnanie; splatnosť; emisia; prv<PERSON>_kup<PERSON>; úrok; po<PERSON>et; vyplatenie; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> vý<PERSON> cenného papiera s prvým nepárnym obdobím"}, "ODDLPRICE": {"a": "(vyrovnanie; splatnosť; pos<PERSON>n<PERSON>_úrok; sadzba; výnos; vyplatenie; frekvencia; [z<PERSON><PERSON>])", "d": "Vráti cenu za každých 100 USD nominálnej hodnoty cenného papiera s nepárnym posledným obdobím"}, "ODDLYIELD": {"a": "(vyrovnanie; splatnosť; pos<PERSON>n<PERSON>_úrok; sadzba; ss; vyplatenie; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> vý<PERSON> cenného papiera s nepárnym posledným obdobím"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> o<PERSON>, ktoré sú potrebné na dosiahnutie zadanej hodnoty investície"}, "PMT": {"a": "(sadzba; pobd; sh; [bh]; [typ])", "d": "Vypočíta splátku pôžičky pri konštantných platbách a konštantnej úrokovej sadzbe"}, "PPMT": {"a": "(sadzba; obd; pobd; sh; [bh]; [typ])", "d": "Vypočíta hodnotu splátky istiny pre zadanú investíciu pri pravidelných konštantných platbách a konštantnej úrokovej sadzbe"}, "PRICE": {"a": "(vyrovnanie; splatnosť; sadzba; výnos; vy<PERSON>nie; frekvencia; [z<PERSON><PERSON>])", "d": "Vráti cenu za každých 100 USD nominálnej hodnoty cenného papiera, z ktorého sa platí pravidelný úrok"}, "PRICEDISC": {"a": "(vyrovnanie; splatnosť; zľava; vyplatenie; [z<PERSON><PERSON>])", "d": "V<PERSON>á<PERSON> cenu za každých 100 USD diskontovaného cenného papiera"}, "PRICEMAT": {"a": "(vyrovnanie; splatnosť; emisia; sadzba; výnos; [z<PERSON><PERSON>])", "d": "Vráti cenu za každých 100 USD nominálnej hodnoty cenného papiera, z ktorého sa k dátumu splatnosti platí úrok"}, "PV": {"a": "(sadzba; pobd; plt; [bh]; [typ])", "d": "Vypočíta súčasnú hodnotu investície: s<PERSON>č<PERSON>nú celkovú hodnotu série budúcich platieb"}, "RATE": {"a": "(pobd; plt; sh; [bh]; [typ]; [odhad])", "d": "Vypočíta úrokovú sadzbu za obdobie pôžičky alebo investície. Ak chcete napríklad zadať štvrťročné platby pri ročnej percentuálnej sadzbe 6 %, použite zá<PERSON> 6%/4"}, "RECEIVED": {"a": "(vyrovnanie; splatnosť; spl<PERSON>tka; zľava; [z<PERSON><PERSON>])", "d": "Vráti sumu prijatú k dátumu splatnosti za úplne investovaný cenný papier"}, "RRI": {"a": "(nper; pv; fv)", "d": "<PERSON><PERSON><PERSON><PERSON>zbu pre rast investície"}, "SLN": {"a": "(cena; zostatok; životnosť)", "d": "Vypočíta odpis majetku za jedno obdobie pri rovnomernom odpisovaní"}, "SYD": {"a": "(cena; zostatok; životnosť; obd)", "d": "Vypočíta odpis majetku za zadané obdobie podľa metódy odpisovania s faktorom súčtu čísel rokov."}, "TBILLEQ": {"a": "(vyrovnanie; splatnosť; zľava)", "d": "Vráti výnos ekvivalentný obligácii za štátnu pokladničnú poukážku"}, "TBILLPRICE": {"a": "(vyrovnanie; splatnosť; zľava)", "d": "Vráti cenu za každých 100 USD nominálnej hodnoty štátnej pokladničnej poukážky"}, "TBILLYIELD": {"a": "(vyrovnanie; splatnosť; ss)", "d": "Vráti výnos štátnej pokladničnej poukážky"}, "VDB": {"a": "(cena; zostatok; životnosť; počiatočné_obdobie; koncové_obdobie; [faktor]; [bez_z<PERSON>y])", "d": "Vypočíta odpisy majetku za zadané obdobia vrátane neukončených období podľa metódy dvojnásobného odpisovania z klesajúceho zostatku alebo inej zadanej metódy"}, "XIRR": {"a": "(hodnoty; dátumy; [odhad])", "d": "Vráti vnútornú mieru návratnosti pre plán hotovostných tokov"}, "XNPV": {"a": "(sadz<PERSON>; hodnoty; d<PERSON><PERSON>y)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ú hodnotu plánu hotovostn<PERSON>ch tokov"}, "YIELD": {"a": "(vyrovnanie; splatnosť; sadzba; ss; vy<PERSON>nie; frekvencia; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> výnos cenného papiera, z ktorého sa platí pravidelný úrok"}, "YIELDDISC": {"a": "(vyrovnanie; splatnosť; ss; vy<PERSON>nie; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> roč<PERSON>ý výnos diskontovaného cenného papiera. Ide napríklad o štátne pokladničné p<PERSON>"}, "YIELDMAT": {"a": "(vyrovnanie; splatnosť; emisia; sadzba; ss; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> ročný výnos cenného papiera, z ktorého sa platí úrok k dátumu splatnosti"}, "ABS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ol<PERSON> hodn<PERSON>, t.j. <PERSON><PERSON><PERSON> be<PERSON> znamienka + alebo -."}, "ACOS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti hodnotu arkus kosínusu čísla v radiánoch v intervale 0 až pí. Arkus kosínus je uhol, ktorého kosínus je argument Číslo"}, "ACOSH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu hyperbolického arkus kosínusu <PERSON>"}, "ACOT": {"a": "(number)", "d": "Vráti hodnotu arkuskotangensu čísla, v radiánoch v intervale od 0 do pí"}, "ACOTH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON>ti inverzný hyperbolický kotangens čísla"}, "AGGREGATE": {"a": "(č<PERSON><PERSON>_funkcie; možnosti; odk1; ...)", "d": "Vráti agregovanú hodnotu v zozname alebo v databáze"}, "ARABIC": {"a": "(text)", "d": "Konvertuje rímske číslice na arabské"}, "ASC": {"a": "(text)", "d": "Pre jazyky s dvojbajtovými tabuľ<PERSON><PERSON> (DBCS) sa menia znaky s plnou šírkou (dvojbajtové) na znaky s polovičnou šírkou (jednobajtové)"}, "ASIN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti arkus sínus čísla v radiánoch v intervale -pí/2 až pí/2"}, "ASINH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu hyperbolického arkus sínusu <PERSON>"}, "ATAN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti arkus tangens čísla v radiánoch v intervale -pí/2 až pí/2"}, "ATAN2": {"a": "(x_č<PERSON>lo; y_č<PERSON>lo)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu arkus tangens danej x-ovej a y-ovej súradnice. Výsledok je v radiánoch v rozsahu od -pí do pí okrem hodnoty -pí"}, "ATANH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu hyperbolického arkustangens čísla"}, "BASE": {"a": "(number; radix; [min_length])", "d": "Konvertuje číslo na textové vyjadrenie s daným základom sústavy (základ)"}, "CEILING": {"a": "(číslo; významnosť)", "d": "Zaokrúhli číslo smerom nahor na najbližší násobok zadanej hodnoty"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "Zaokrúhli číslo nahor na najbližšie celé číslo alebo na najbližší násobok zadanej hodnoty"}, "CEILING.PRECISE": {"a": "(č<PERSON>lo; [významnosť])", "d": "<PERSON><PERSON><PERSON><PERSON>lo zaokrúhlené na najbližšie celé číslo alebo na najbližší násobok zadanej hodnoty"}, "COMBIN": {"a": "(počet; vybratý_počet)", "d": "Vráti počet kombinácií pre zadaný počet položiek"}, "COMBINA": {"a": "(number; number_chosen)", "d": "Vráti počet kombinácií s opakovaniami pre daný počet položiek"}, "COS": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON>"}, "COSH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperbolický kosínus č<PERSON>"}, "COT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>la"}, "COTH": {"a": "(nuber)", "d": "V<PERSON><PERSON>ti hyperbolický kotangens čísla"}, "CSC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "CSCH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON>k<PERSON> kosekans uhla"}, "DECIMAL": {"a": "(number; radix)", "d": "Konvertuje textové vyjadrenie čísla v danom základe na desatinné číslo"}, "DEGREES": {"a": "(uhol)", "d": "Konvertuje radiány na stupne"}, "ECMA.CEILING": {"a": "(číslo; významnosť)", "d": "Zaokrúhli číslo smerom nahor na najbližší násobok zadanej hodnoty"}, "EVEN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrúhli kladné číslo nahor a záporné číslo smerom nadol na najbližšie párne celé číslo"}, "EXP": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti e (základ prirodzeného logaritmu) umocnené na zadané číslo"}, "FACT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> faktori<PERSON>l čísla. Výsledok sa rovná hodnote 1*2*3*...* Č<PERSON>lo"}, "FACTDOUBLE": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> fak<PERSON><PERSON><PERSON>"}, "FLOOR": {"a": "(číslo; významnosť)", "d": "Zaokrúhli číslo nadol na najbližší násobok zadanej hodnoty"}, "FLOOR.PRECISE": {"a": "(č<PERSON>lo; [významnosť])", "d": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> je zaokrúhlené nadol na najbližšie celé číslo alebo na násobok zadanej hodnoty"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "Zaokrúhli číslo nadol na najbližšie celé číslo alebo na najbližší násobok zadanej hodnoty"}, "GCD": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON>ti najv<PERSON>čší spoločný deliteľ"}, "INT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrúhli číslo nadol na najbližšie celé číslo"}, "ISO.CEILING": {"a": "(č<PERSON>lo; [významnosť])", "d": "Vr<PERSON>ti č<PERSON>lo zaokrúhlené na najbližšie celé číslo alebo na najbližší násobok zadanej hodnoty. Číslo sa bez ohľadu na znamienko zaokrúhli nahor. Ak je však dané číslo alebo násobok nula, vr<PERSON>ti sa nula."}, "LCM": {"a": "(číslo1; [číslo2]; ...)", "d": "Vráti najmenší spoločný násobok"}, "LN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> prirodzený logaritmus daného <PERSON>"}, "LOG": {"a": "(<PERSON><PERSON><PERSON>; [z<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON>ti logaritmus čísla pri určenom základe"}, "LOG10": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON>ý logaritmus čísla"}, "MDETERM": {"a": "(pole)", "d": "<PERSON><PERSON><PERSON><PERSON> determinant matice poľa"}, "MINVERSE": {"a": "(pole)", "d": "Vráti inverznú maticu matice uloženej v poli"}, "MMULT": {"a": "(pole1; pole2)", "d": "<PERSON><PERSON><PERSON><PERSON> mat<PERSON> so s<PERSON><PERSON><PERSON><PERSON> d<PERSON> polí, k<PERSON><PERSON> obsahuje rovnaký počet riadkov ako pole1 a rovnaký počet stĺpcov ako pole2"}, "MOD": {"a": "(č<PERSON>lo; deliteľ)", "d": "Vráti zvyšok po delení čísla deliteľom"}, "MROUND": {"a": "(číslo; násobok)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu zaokrúhlenú na požadovaný násobok"}, "MULTINOMIAL": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> polyno<PERSON> množinu čísel"}, "MUNIT": {"a": "(dimension)", "d": "<PERSON><PERSON><PERSON><PERSON> mat<PERSON>u j<PERSON> pre zadanú dimenziu"}, "ODD": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Zaokrúhli kladné číslo nahor a záporné číslo nadol na najbližšie nepárne celé číslo"}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu pí s presnosťou na 15 číslic. Výsledkom je hodnota 3,14159265358979"}, "POWER": {"a": "(<PERSON><PERSON><PERSON>; mocnina)", "d": "Umocní číslo na zadanú mocninu"}, "PRODUCT": {"a": "(hodnota1; [hodnota2]; ...)", "d": "Vynásobí všetky čísla zadané ako argumenty"}, "QUOTIENT": {"a": "(čitate<PERSON>; menovateľ)", "d": "<PERSON><PERSON><PERSON><PERSON>íselnú časť delenia"}, "RADIANS": {"a": "(uhol)", "d": "Konvertuje stupne na radiány"}, "RAND": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON>, ktoré je väčšie alebo rovné 0 a menšie než 1 (zmení sa pri každom prepočítaní)"}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "<PERSON><PERSON><PERSON><PERSON> náhodných čísiel"}, "RANDBETWEEN": {"a": "(najn<PERSON><PERSON><PERSON><PERSON>; najvyššie)", "d": "<PERSON><PERSON><PERSON><PERSON> náhodne vybraté číslo medzi zadanými číslami"}, "ROMAN": {"a": "(číslo; [forma])", "d": "Konvertuje číslo napísané arabskými číslicami na rímske číslice v textovom formáte"}, "ROUND": {"a": "(č<PERSON>lo; počet_číslic)", "d": "Zaokrúhli číslo na daný počet číslic"}, "ROUNDDOWN": {"a": "(č<PERSON>lo; počet_číslic)", "d": "Zaokrúhli číslo smerom nadol k nule"}, "ROUNDUP": {"a": "(č<PERSON>lo; počet_číslic)", "d": "Zaokrú<PERSON><PERSON>, smerom od nuly"}, "SEC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON>"}, "SECH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON> se<PERSON>la"}, "SERIESSUM": {"a": "(x; n; m; koe<PERSON>y)", "d": "<PERSON><PERSON>áti súčet mocninových radov na základe vzorca"}, "SIGN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Vráti znamienko čísla: 1 pri kladnom čísle, 0 pri nule alebo -1 pri zápornom čísle"}, "SIN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON>"}, "SINH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hyper<PERSON>k<PERSON> sínus <PERSON>"}, "SQRT": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> odmocninu čís<PERSON>"}, "SQRTPI": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> (číslo * pí)"}, "SUBTOTAL": {"a": "(č<PERSON><PERSON>_funkcie; odk1; ...)", "d": "Vráti medzisúčet v zozname alebo v databáze"}, "SUM": {"a": "(číslo1; [číslo2]; ...)", "d": "Spočíta všetky čísla v rozsahu buniek"}, "SUMIF": {"a": "(rozsah; kritéri<PERSON>; [rozsah_súhrnu])", "d": "Spočíta bunky vybraté podľa zadanej podmienky alebo kritéria"}, "SUMIFS": {"a": "(rozsah_súčtu; rozsah_kritérií; kritériá; ...)", "d": "Pripočíta bunky určené podľa zadanej množiny podmienok alebo kritérií"}, "SUMPRODUCT": {"a": "(pole1; [pole2]; [pole3]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> súčinov zodpovedajúcich rozsahov alebo polí"}, "SUMSQ": {"a": "(číslo1; [číslo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> druhých mocnín argumentov. Argumentmi mô<PERSON>u byť čísla, polia, názvy alebo odkazy na bunky obsahujúce čísla"}, "SUMX2MY2": {"a": "(pole_x; pole_y)", "d": "Vypočíta sú<PERSON> rozdielov druh<PERSON>ch mocnín hodn<PERSON>t dvoch zodpovedajúcich rozsahov alebo polí"}, "SUMX2PY2": {"a": "(pole_x; pole_y)", "d": "<PERSON><PERSON><PERSON>ti celkový súčet súčtov druhých mocnín čísel v dvoch zodpovedajúcich rozsahoch alebo poliach"}, "SUMXMY2": {"a": "(pole_x; pole_y)", "d": "Vypočíta súč<PERSON> druhých mocnín roz<PERSON>lov hodn<PERSON>t dvoch zodpovedajúcich rozsahov alebo polí"}, "TAN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> tangens uhla"}, "TANH": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperbolický tangens čísla"}, "TRUNC": {"a": "(číslo; [počet_číslic])", "d": "Skráti číslo na celé číslo odstránením desatinnej alebo zlomkovej časti čísla"}, "ADDRESS": {"a": "(číslo_riadka; číslo_stĺpca; [abs_číslo]; [a1]; [text_hárka])", "d": "Po zadaní čísla riadka a stĺpca vytvorí textový odkaz na bunku"}, "CHOOSE": {"a": "(index_číslo; hodnota1; [hodnota2]; ...)", "d": "Zo zoznamu hodnôt zvolí na základe zadaného čísla hodnotu alebo akciu, ktor<PERSON> sa má vykonať"}, "COLUMN": {"a": "([odkaz])", "d": "<PERSON><PERSON><PERSON><PERSON> stĺpca odkazu"}, "COLUMNS": {"a": "(pole)", "d": "Vráti počet stĺpcov v poli alebo odkaze"}, "FORMULATEXT": {"a": "(reference)", "d": "Vráti vzorec ako reťazec"}, "HLOOKUP": {"a": "(vyhľadávaná_hodnota; pole_tabuľky; číslo_indexu_riadka; [vyhľadávanie_rozsah])", "d": "Prehľadá horný riadok tabuľky alebo pole hodnôt a vr<PERSON>ti hodnotu zo zadaného riadka obsiahnutú v rovnakom stĺpci"}, "HYPERLINK": {"a": "(umiestnenie_prepojenia; [priateľ<PERSON><PERSON>_názov])", "d": "Vytvorí odkaz alebo skok, ktorý otvorí dokument uložený na pevnom disku, sieťovom serveri alebo na Internete"}, "INDEX": {"a": "(pole; číslo_riadka; [číslo_stĺpca]!odkaz; číslo_riadka; [číslo_stĺpca]; [číslo_plochy])", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu alebo odkaz na bunku v určitom riadku a stĺpci v danom rozsahu"}, "INDIRECT": {"a": "(text_odkazu; [a1])", "d": "Vráti odkaz určený textovým reťazcom"}, "LOOKUP": {"a": "(vyhľad<PERSON>vaná_hodnota; vektor_vyhľadávania; [vektor_v<PERSON><PERSON><PERSON><PERSON>]!vyhľadávaná_hodnota; pole)", "d": "Vyhľadá hodnotu z poľa alebo z rozsahu jedného riadka alebo jedného stĺpca. Funkcia je poskytovaná s cieľom spätnej kompatibility"}, "MATCH": {"a": "(vyhľadávaná_hodnota; pole_vyhľadávania; [typ_zhody])", "d": "Vráti relatívnu pozíciu položky poľa, ktorá zodpovedá danej hodnote v danom poradí"}, "OFFSET": {"a": "(odkaz; riadky; stĺpce; [výška]; [šírka])", "d": "Vráti odkaz na rozsah, ktorý predstavuje určený počet riadkov a stĺpcov z daného odkazu"}, "ROW": {"a": "([odkaz])", "d": "<PERSON><PERSON><PERSON><PERSON> riadka od<PERSON>"}, "ROWS": {"a": "(pole)", "d": "Vráti počet riadkov v odkaze alebo poli"}, "TRANSPOSE": {"a": "(pole)", "d": "Konvertuje vodorovný rozsah buniek na zvislý alebo naopak"}, "UNIQUE": {"a": "(pole; [podľa_stĺpca]; [presne_raz])", "d": "<PERSON><PERSON><PERSON><PERSON> jedinečné hodnoty v danom rozsahu alebo poli."}, "VLOOKUP": {"a": "(vyhľadávan<PERSON>_hodnota; pole_tabuľky; číslo_indexu_stĺpca; [vyhľadávanie_rozsahu])", "d": "Hľadá hodnotu v ľavom krajnom stĺpci tabuľky a vr<PERSON>ti hodnotu zo zadaného stĺpca v tom istom riadku. Predvolené zoradenie tabuľky je vzostupné"}, "XLOOKUP": {"a": "(vyhľad<PERSON>vaná_hodnota; pole_vyhľadávania; pole_vrátenia; [ak_sa_ne<PERSON><PERSON>]; [rež<PERSON>_z<PERSON><PERSON>]; [režim_vyhľadávania])", "d": "V danom rozsahu alebo poli hľadá zhodu a vráti zodpovedajúcu položku z druhého rozsahu alebo poľa. Predvolene sa použije presná zhoda"}, "CELL": {"a": "(typ_informácií; [odkaz])", "d": "<PERSON><PERSON><PERSON><PERSON> o formátovaní, umiestnení alebo obsahu bunky"}, "ERROR.TYPE": {"a": "(hodnota_chyby)", "d": "<PERSON><PERSON><PERSON><PERSON>odpovedajúce chybovej hodnote."}, "ISBLANK": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument odkazom na prázdnu bunku a vráti hodnotu TRUE alebo FALSE"}, "ISERR": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je hodnota chybou inou ako #NEDOSTUPNÝ a vr<PERSON>ti hodnotu TRUE alebo FALSE"}, "ISERROR": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je hodnota chybou a vr<PERSON>ti hodnotu TRUE alebo FALSE"}, "ISEVEN": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu TRUE, ak je číslo párne"}, "ISFORMULA": {"a": "(reference)", "d": "Overí, či odkaz smeruje na bunku obsahujúcu vzorec a vráti hodnotu TRUE alebo FALSE"}, "ISLOGICAL": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument logickou hodnotou (TRUE alebo FALSE) a vr<PERSON>ti hodnotu TRUE alebo FALSE"}, "ISNA": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či hodnota je #NEDOSTUPNÝ a vr<PERSON>ti hodnotu TRUE alebo FALSE"}, "ISNONTEXT": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či argument nie je textovou hodnotou (prázdne bunky nie sú text) a vr<PERSON><PERSON> hodnotu TRUE alebo FALSE"}, "ISNUMBER": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument číselnou hodnotou a vr<PERSON>ti hodnotu TRUE alebo FALSE"}, "ISODD": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu TRUE, ak je číslo nepárne"}, "ISREF": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument odkazom a vr<PERSON><PERSON> hodnotu TRUE alebo FALSE"}, "ISTEXT": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je argument textovou hodnotou a vr<PERSON>ti hodnotu TRUE alebo FALSE"}, "N": {"a": "(hodnota)", "d": "Konvertuje nečíselnú hodnotu na číslo, dátumy na poradové čísla, hodnotu TRUE na číslo 1, všetky ostatné výrazy na číslo 0 (nula)"}, "NA": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON> hodnotu #NEDOSTUPNÝ (hodnota nie je dostup<PERSON>)"}, "SHEET": {"a": "([value])", "d": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>"}, "SHEETS": {"a": "([reference])", "d": "Vráti počet hárkov v odkaze"}, "TYPE": {"a": "(hodnota)", "d": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>, k<PERSON><PERSON> predstavuje typ údajov hodnoty: číslo = 1; text = 2; <PERSON><PERSON><PERSON> hodnota = 4; chybo<PERSON><PERSON> hodnota = 16; pole = 64; <PERSON><PERSON><PERSON><PERSON><PERSON> = 128"}, "AND": {"a": "(logická_hodnota1; [logická_hodnota2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či vš<PERSON>ky argumenty majú hodnotu TRUE, a v <PERSON>r<PERSON><PERSON><PERSON>, že to tak je, vr<PERSON><PERSON> hodnotu TRUE"}, "FALSE": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu FALSE"}, "IF": {"a": "(logický_test; [hodnota_ak_pravda]; [hodnota_ak_nepravda])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je podmienka splnená, a vr<PERSON><PERSON> jednu hodnotu, ak je výsledkom TRUE, a inú hodnotu, ak je výsledkom FALSE"}, "IFS": {"a": "(logický_test; hodnota_ak_pravda; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či je splnená minimálne jedna podmienka, a vr<PERSON><PERSON> hodnotu zodpovedajúcu prvej podmienke s výsledkom TRUE"}, "IFERROR": {"a": "(hodnota; hodnota_ak_chyba)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu argumentu hodnota_ak_chyba, ak je zadaný výraz chybou; v opačnom prípade vráti výraz"}, "IFNA": {"a": "(hodnota; hodnota_ak_na)", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu, ak je výsledkom výrazu hodnota #N/A, v opačnom prípade vráti výsledok výrazu"}, "NOT": {"a": "(logická_hodnota)", "d": "Zmení hodnotu FALSE na TRUE alebo hodnotu TRUE na FALSE"}, "OR": {"a": "(logická_hodnota1; [logická_hodnota2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, či existujú argumenty s hodnotou TRUE, a vr<PERSON>ti hodnotu TRUE alebo FALSE. Vr<PERSON>ti hodnotu FALSE len v prípade, že všetky argumenty majú hodnotu FALSE"}, "SWITCH": {"a": "(výraz; hodnota1; výsledok1; [predvolené_alebo_hodnota2]; [výsledok2]; ...)", "d": "Vyhodnotí výraz vzhľadom k zoznamu hodnôt a vráti výsledok, ktorý korešponduje s prvou zodpovedajúcou hodnotou. Ak sa nenájde žiadna zhoda, vráti sa voliteľná predvolená hodnota"}, "TRUE": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> hodnotu TRUE"}, "XOR": {"a": "(logická_hodnota1; [logická_hodnota2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>ký operátor Exclusive Or všetkých argumentov"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON><PERSON><PERSON> text, ktorý sa nachádza pred oddeľovacími znakmi."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON><PERSON><PERSON> text, ktorý sa nachádza za oddeľovacími znakmi."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Rozdelí text do riadkov alebo stĺpcov pomocou oddeľ<PERSON>čov."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Zalomí vektor riadka alebo stĺpca za zadaný počet hodn<PERSON>."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Zvislo navrství polia do jedného poľa."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Vodorovne navrství polia do jedného poľa."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "<PERSON><PERSON><PERSON><PERSON> riadky z poľa alebo odkazu."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Vrá<PERSON> stĺpce z poľa alebo odkazu."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON><PERSON> pole ako jeden stĺpec."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON><PERSON> pole ako jeden ria<PERSON>. "}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Zalomí vektor riadka alebo stĺpca za zadaný počet hodn<PERSON>."}, "TAKE": {"a": "(array, rows, [columns])", "d": "<PERSON><PERSON><PERSON><PERSON> riadky alebo stĺpce zo začiatku alebo konca poľa."}, "DROP": {"a": "(array, rows, [columns])", "d": "Vypustí riadky alebo stĺpce zo začiatku alebo konca poľa."}}
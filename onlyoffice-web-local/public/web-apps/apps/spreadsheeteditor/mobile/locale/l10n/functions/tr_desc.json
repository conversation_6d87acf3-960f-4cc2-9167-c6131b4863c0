{"DATE": {"a": "(yıl; ay; gün)", "d": "Tarih-saat kodundaki tarihi gösteren sayıyı verir."}, "DATEDIF": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; bitiş_tarihi; birim)", "d": "<PERSON>ki tarih a<PERSON> g<PERSON>n, ay veya yıl sayı<PERSON>ını hesaplar"}, "DATEVALUE": {"a": "(tarih_metni)", "d": "Metin formunda bulunan bir tarihi tarih-saat kodunu gösteren bir sayıya dönüştürür"}, "DAY": {"a": "(seri_no)", "d": "1 ile 31 <PERSON><PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dö<PERSON>ü<PERSON>ü<PERSON>."}, "DAYS": {"a": "(bitiş_tarihi; ba<PERSON><PERSON><PERSON><PERSON>_tarihi)", "d": "İki tarih arasındaki gün sayısını döndürür."}, "DAYS360": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; bitiş_tarihi; [yö<PERSON><PERSON>])", "d": "İki tarih arasındaki gün sayısını 360 günlük yılı kullanarak hesaplar (oniki 30 günlük ay)"}, "EDATE": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; ay_sayısı)", "d": "Başlangıç tarihinden önceki veya sonraki ay sayısını belirten tarihin seri numarasını döndürür"}, "EOMONTH": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; ay_sayısı)", "d": "<PERSON><PERSON><PERSON><PERSON> sayıda ay önce veya sonraki ayın son gü<PERSON><PERSON><PERSON><PERSON> belirten seri numarası döndür<PERSON>r"}, "HOUR": {"a": "(seri_no)", "d": "<PERSON><PERSON> ve<PERSON>, bir seri numa<PERSON>ına karşılık gelen 0 (12:00)'dan 23 (11:00)'e kadar bir tamsayı."}, "ISOWEEKNUM": {"a": "(tarih)", "d": "Verilen tarih için yıl içinde ISO hafta numarasını döndürür"}, "MINUTE": {"a": "(seri_no)", "d": "<PERSON>ir seri numarasına karşıl<PERSON><PERSON> gelen, 0-59 a<PERSON><PERSON>nda bir tamsayı olan dakikayı verir."}, "MONTH": {"a": "(seri_no)", "d": "1 (Ocak) ile 12 (Aralık) arasındaki bir sayı ile ifade edilen ayı döndürür."}, "NETWORKDAYS": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; biti<PERSON>_tarihi; [tatiller])", "d": "İki tarih arasındaki tüm işgünlerinin sayısını döndürür"}, "NETWORKDAYS.INTL": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; biti<PERSON>_tarihi; [hafta_sonu]; [tatiller])", "d": "İki tarih arasındaki tam işgünlerinin sayısını özel hafta sonu parametreleriyle verir"}, "NOW": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON> tarihi ve saati, tarih ve saat biçiminde verir."}, "SECOND": {"a": "(seri_no)", "d": "Saniyeyi seri numarasına karşılık gelen 0 ile 59 arasında bir tamsayı cinsinden verir."}, "TIME": {"a": "(saat; dakika; saniye)", "d": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, saniye olarak girilen sayıları zaman biçimindeki seri numarasına dönüştürür"}, "TIMEVALUE": {"a": "(saat_metni)", "d": "Bir metin dizesiyle (saat_metni) gösterilen bir saati 0 (00:00:00) ile 0,999988426 (23:59:59) arasındaki saat seri numarasına çevirir. Formülü girdikten sonra sayıyı saat biçiminde biçimlendirin"}, "TODAY": {"a": "()", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarihi, tarih biçiminde verir."}, "WEEKDAY": {"a": "(seri_no; [dö<PERSON><PERSON><PERSON>_tür])", "d": "Verilen tarih gösteren sayıyı kullanarak haftanın gününü tanımlayan 1 ile 7 arasındaki sayı."}, "WEEKNUM": {"a": "(seri_num; [dö<PERSON><PERSON><PERSON>_türü])", "d": "<PERSON><PERSON>l içinde haftanın numarasını döndürür"}, "WORKDAY": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; gün_sayısı; [tatiller])", "d": "Belirtilen sayıda işgünü önce veya sonraki bir tarihin seri numarasını döndürür"}, "WORKDAY.INTL": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; gün_sayısı; [hafta_sonu]; [tatiller])", "d": "Belirtilen sayıda işgünü önce veya sonraki bir tarihin seri numarasını özel hafta sonu parametreleriyle verir"}, "YEAR": {"a": "(seri_no)", "d": "1900 - 9999 aralığındaki bir tamsayı ile ifade edilen tarihin yılını döndürür."}, "YEARFRAC": {"a": "(ba<PERSON><PERSON><PERSON><PERSON>_tarihi; bitiş_tarihi; [temel])", "d": "Başlangıç ve bitiş tarihleri arasındaki tam gün sayısını gösteren yıl oranını döndürür"}, "BESSELI": {"a": "(x; n)", "d": "In(x) değiştirilmiş Bessel işlevini döndürür"}, "BESSELJ": {"a": "(x; n)", "d": "Jn(x) Bessel işlevini döndürür"}, "BESSELK": {"a": "(x; n)", "d": "Kn(x) değiştirilmiş Bessel işlevini döndürür"}, "BESSELY": {"a": "(x; n)", "d": "Yn(x) Bessel işlevini döndürür"}, "BIN2DEC": {"a": "(sayı)", "d": "İkilik düzendeki bir sayıyı onluk düzene dönüştürür"}, "BIN2HEX": {"a": "(sayı; [basamak])", "d": "İkilik düzendeki bir sayıyı onaltılık düzene dönüştürür"}, "BIN2OCT": {"a": "(sayı; [basamak])", "d": "İkilik düzendeki bir sayıyı sekizlik düzene dönüştürür"}, "BITAND": {"a": "(sayı1; sayı2)", "d": "İki sayının bit tabanlı bir 'Ve' değerini verir"}, "BITLSHIFT": {"a": "(sayı; kaydırma_miktarı)", "d": "kaydırma_miktarı kadar bit sola kaydırılan bir sayıyı verir"}, "BITOR": {"a": "(sayı1; sayı2)", "d": "İki sayının bit tabanlı bir 'Veya' değerini verir"}, "BITRSHIFT": {"a": "(sayı; kaydırma_miktarı)", "d": "kaydırma_miktarı kadar bit sağa kaydırılan bir sayıyı verir"}, "BITXOR": {"a": "(sayı1; sayı2)", "d": "İki sayının bit tabanlı bir 'Özel Veya' değ<PERSON>ni verir"}, "COMPLEX": {"a": "(ger<PERSON><PERSON>_sayı; karm_sayı; [sonek])", "d": "Gerçel ve sanal katsayıları bir karmaşık sayıya dönüştürür"}, "CONVERT": {"a": "(sayı; ilk_birim; son_birim)", "d": "Sayıyı bir ölçü biriminden bir diğerine dönüştürür"}, "DEC2BIN": {"a": "(sayı; [basamak])", "d": "Onluk düzendeki bir sayıyı ikilik düzene dönüştürür"}, "DEC2HEX": {"a": "(sayı; [basamak])", "d": "Onluk düzendeki bir sayıyı onaltılık düzene dönüştürür"}, "DEC2OCT": {"a": "(sayı; [basamak])", "d": "Onluk düzendeki bir sayıyı sekizlik düzene dönüştürür"}, "DELTA": {"a": "(sayı1; [sayı2])", "d": "İki sayının e<PERSON> sınar"}, "ERF": {"a": "(alt_limit; [üst_limit])", "d": "Hata işlevini döndürür"}, "ERF.PRECISE": {"a": "(X)", "d": "Hata işlevini döndürür"}, "ERFC": {"a": "(x)", "d": "Tümleyici hata işlevini döndürür"}, "ERFC.PRECISE": {"a": "(X)", "d": "Tamamlayıcı hata işlevini döndürür"}, "GESTEP": {"a": "(sayı; [sın<PERSON><PERSON>_de<PERSON><PERSON>])", "d": "<PERSON><PERSON> sayının sınır bir değerden büyük olup olmadığını sınar."}, "HEX2BIN": {"a": "(sayı; [basamak])", "d": "Onaltılık düzendeki bir sayıyı ikilik düzene dönüştürür"}, "HEX2DEC": {"a": "(sayı)", "d": "Onaltılık düzendeki bir sayıyı onluk düzene çevirir"}, "HEX2OCT": {"a": "(sayı; [basamak])", "d": "Onaltılık düzendeki bir sayıyı sekizlik düzene dönüştürür"}, "IMABS": {"a": "(karmsayı)", "d": "Bir karmaşık sayının mutlak <PERSON> (modulus) döndürür"}, "IMAGINARY": {"a": "(karmsayı)", "d": "Bir karmaşık sayının sanal katsayısını döndürür"}, "IMARGUMENT": {"a": "(karmsayı)", "d": "Bir karmaşık sayının radyan cinsinden bağ<PERSON><PERSON><PERSON><PERSON> (q) döndürür"}, "IMCONJUGATE": {"a": "(karmsayı)", "d": "Bir karmaşık sayının eşleneğini döndürür"}, "IMCOS": {"a": "(karmsayı)", "d": "Bir karmaşık sayının kosinüs değerini döndürür"}, "IMCOSH": {"a": "(karmsayı)", "d": "Bir karmaşık sayının hiperbolik kosinüs değerini verir"}, "IMCOT": {"a": "(karmsayı)", "d": "Bir karmaşık sayının kotanjant değerini verir"}, "IMCSC": {"a": "(karmsayı)", "d": "Bir karmaşık sayının kosekant değerini verir"}, "IMCSCH": {"a": "(karmsayı)", "d": "Bir karmaşık sayının hiperbolik kosekant değerini verir"}, "IMDIV": {"a": "(karmsayı1; karmsayı2)", "d": "İki karmaşık sayının bölümünü döndürür"}, "IMEXP": {"a": "(karmsayı)", "d": "Bir karmaşık sayının üssel değerini döndürür"}, "IMLN": {"a": "(karmsayı)", "d": "Bir karmaşık sayının doğal logaritmasını döndürür"}, "IMLOG10": {"a": "(karmsayı)", "d": "Bir karmaşık sayının 10 tabanında logaritmasını döndürür"}, "IMLOG2": {"a": "(karmsayı)", "d": "Bir karmaşık sayının 2 tabanında logaritmasını döndürür"}, "IMPOWER": {"a": "(karmsayı; sayı)", "d": "Bir karmaşık sayının tamsayı bir kuvvetini döndürür"}, "IMPRODUCT": {"a": "(isayı1; [isayı2]; ...)", "d": "En az 1 en çok 255 karmaşık sayının çarpımını döndürür"}, "IMREAL": {"a": "(karmsayı)", "d": "Bir karmaşık sayının gerçel katsayısını döndürür"}, "IMSEC": {"a": "(karmsayı)", "d": "Bir karmaşık sayının sekant değerini verir"}, "IMSECH": {"a": "(karmsayı)", "d": "Bir karmaşık sayının hiperbolik sekant değerini verir"}, "IMSIN": {"a": "(karmsayı)", "d": "Bir karmaşık sayının sinüs değerini döndürür"}, "IMSINH": {"a": "(karmsayı)", "d": "Bir karmaşık sayının hiperbolik sinüs değerini verir"}, "IMSQRT": {"a": "(karmsayı)", "d": "Bir karmaşık sayının karekökünü döndürür"}, "IMSUB": {"a": "(karmsayı1; karmsayı2)", "d": "İki karmaşık sayının farkını döndürür"}, "IMSUM": {"a": "(isayı1; [isayı2]; ...)", "d": "Karmaşık sayıların toplamını döndürür"}, "IMTAN": {"a": "(karmsayı)", "d": "Bir karmaşık sayının tanjant değerini verir"}, "OCT2BIN": {"a": "(sayı; [basamak])", "d": "Sekizlik düzendeki bir sayıyı ikilik düzene dönüştürür"}, "OCT2DEC": {"a": "(sayı)", "d": "Sekizlik düzendeki bir sayıyı onluk düzene dönüştürür"}, "OCT2HEX": {"a": "(sayı; [basamak])", "d": "Sekizlik düzendeki bir sayıyı onaltılık düzene dönüştürür"}, "DAVERAGE": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Bir liste ya da veritabanındaki bir sütunda yer alan ve belirttiğiniz koşullara uyan değerlerin ortalamasını verir"}, "DCOUNT": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan sayıları içeren ve belirttiğiniz koşullara uyan hücreleri sayar"}, "DCOUNTA": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan ve belirttiğiniz koşullara uyan boş olmayan hücreleri sayar"}, "DGET": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Belirttiğiniz koşullara uyan tek bir kaydı veritabanından çıkarır"}, "DMAX": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan ve belirttiğiniz koşullara uyan en büyük sayıyı verir."}, "DMIN": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan ve belirttiğiniz koşullara uyan en küçük sayıyı verir"}, "DPRODUCT": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan ve belirttiğiniz koşullara uyan verileri çarpar"}, "DSTDEV": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Seçili veritabanı girdilerinden alınan bir örneğin standart sapmasını tahmin eder"}, "DSTDEVP": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Seçili veritabanı girdilerinden oluşan tüm popülasyonun standart sapmasını hesaplar"}, "DSUM": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Veritabanındaki kayıt alanında (sütun) bulunan ve belirttiğiniz koşullara uyan sayıları toplar"}, "DVAR": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Seçili veritabanı girdilerinden alınan örneğin varyansını tahmin eder"}, "DVARP": {"a": "(veritaban<PERSON>; alan; ölçüt)", "d": "Seçili veritabanı popülasyonunun varyansını hesaplar"}, "CHAR": {"a": "(sayı)", "d": "Bilgisayarınızın karakter kümesindeki kod numarasıyla belirtilen karakteri verir"}, "CLEAN": {"a": "(metin)", "d": "Metinden yazdırılamayan karakterleri kaldırır"}, "CODE": {"a": "(metin)", "d": "Bilgisayarınızın kullandığı karakter kümesinden, metin dizesindeki ilk karakter için sayısal bir kod verir"}, "CONCATENATE": {"a": "(metin1; [metin2]; ...)", "d": "<PERSON><PERSON> fazla metin dizesini bir metin dizesi halinde birle<PERSON>rir"}, "CONCAT": {"a": "(metint1; ...)", "d": "<PERSON><PERSON> di<PERSON> oluşan listeyi veya aralığı birleştirir"}, "DOLLAR": {"a": "(sayı; [onluklar])", "d": "Bir sayıyı para biçimi kullanarak metne dönüştürür"}, "EXACT": {"a": "(metin1; metin2)", "d": "İki metin dizesini ka<PERSON>şılaştırır ve tamamen aynıysalar DOĞRU, başka durumlarda YANLIŞ verir (büyük küçük harf duyarlı)"}, "FIND": {"a": "(bul_metin; metin_içinde; [ba<PERSON><PERSON><PERSON><PERSON>_sayısı])", "d": "Bir metin dizesini diğer bir metin dizesi içinde bulur ve bulunan dizenin başlama konumu numarasını verir (büyük küçük harfe duyarlı)"}, "FINDB": {"a": "(bul_metin; metin_içinde; [ba<PERSON><PERSON><PERSON><PERSON>_sayısı])", "d": "Bir metin dizesi içinde bir metin dizesi bulur ve ikinci metin dizesinin ilk karakterinden ilk metin dizesinin başlangıç konumuna ait sayıyı verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir"}, "FIXED": {"a": "(sayı; [on<PERSON><PERSON>]; [virg<PERSON><PERSON>_yok])", "d": "<PERSON>ir sayıyı belirtilen sayıda ondalıklara yuvarlar ve sonucu virgüllü ya da virgülsüz metin olarak verir"}, "LEFT": {"a": "(metin; [say<PERSON>_ka<PERSON><PERSON><PERSON>])", "d": "Bir metin dizesinin ilk (en solundaki) belirtilen sayıdaki karakter ya da karakterlerini verir"}, "LEFTB": {"a": "(metin; [say<PERSON>_ka<PERSON><PERSON><PERSON>])", "d": "Belirteceğiniz bayt sayısına göre bir metin dizesindeki ilk karakteri veya karakterleri verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir"}, "LEN": {"a": "(metin)", "d": "Bir karakter dizesi içerisindeki karakter sayısını verir"}, "LENB": {"a": "(metin)", "d": "Bir metin dizesinde karakterleri temsil etmek üzere kullanılan bayt sayısını verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir"}, "LOWER": {"a": "(metin)", "d": "<PERSON>in dizesindeki tüm büyük harfleri küçük harfe çevirir"}, "MID": {"a": "(metin; ba<PERSON><PERSON><PERSON><PERSON>_sayısı; sayı_karak<PERSON><PERSON>)", "d": "Belirttiğiniz konumdan başlamak üzere metinden belirli sayıda karakter verir"}, "MIDB": {"a": "(metin; ba<PERSON><PERSON><PERSON><PERSON>_sayısı; sayı_karak<PERSON><PERSON>)", "d": "Bir metin dizesinden, be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yerden başlayarak, be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bayt sayısına göre belirli sayıda karakteri verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir"}, "NUMBERVALUE": {"a": "(metin; [ondalık_ayırıcı]; [grup_ayırıcı])", "d": "<PERSON>ni yerel ba<PERSON><PERSON>ms<PERSON>z durumdaki sayıya dönüştürür"}, "PROPER": {"a": "(metin)", "d": "<PERSON><PERSON> di<PERSON>i her sözcüğün ilk harfini büyük harfe, di<PERSON><PERSON> tüm harfleri de küçük harfe dönüştürür"}, "REPLACE": {"a": "(eski_metin; ba<PERSON><PERSON><PERSON><PERSON>_sayısı; sayı_karak<PERSON><PERSON>; yeni_metin)", "d": "Metin dizesinin bir kısmını başka bir metin dizesiyle <PERSON>"}, "REPLACEB": {"a": "(eski_metin; ba<PERSON><PERSON><PERSON><PERSON>_sayısı; sayı_karak<PERSON><PERSON>; yeni_metin)", "d": "Belirteceğiniz bayt say<PERSON> gö<PERSON>, bir metin dizesinin bir kısmını farklı bir metin dizesi ile değiştirir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir"}, "REPT": {"a": "(metin; sayı_kere)", "d": "Bir metni verilen sayıda yineler. Hücreyi metin dizesindeki birçok örnekle doldurmak için YİNELE'yi kullanın"}, "RIGHT": {"a": "(metin; [say<PERSON>_ka<PERSON><PERSON><PERSON>])", "d": "Bir metin dizesinin son (en sa<PERSON><PERSON><PERSON>) belirtilen sayıdaki karakter ya da karakterlerini verir"}, "RIGHTB": {"a": "(metin; [say<PERSON>_ka<PERSON><PERSON><PERSON>])", "d": "Belirteceğiniz bayt say<PERSON> g<PERSON>, bir metin dizesindeki son karakteri veya karakterleri verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir"}, "SEARCH": {"a": "(bul_metin; metin; [ba<PERSON><PERSON><PERSON><PERSON>_sayısı])", "d": "Özel bir karakter ya da metin dizesinin ilk geçtiği yerin karakter numarasını verir, soldan sağa okuma sırasında (büyük küçük harfe duyarlı değil)"}, "SEARCHB": {"a": "(bul_metin; metin; [ba<PERSON><PERSON><PERSON><PERSON>_sayısı])", "d": "Bir metin dizesi içinde bir metin dizesi bulur ve ikinci metin dizesinin ilk karakterinden ilk metin dizesinin başlangıç konumuna ait sayıyı verir, ise çift baytlık karakter kümesi (DBCS) kullanan dillerle kullanım için tasarlanmıştır - Japonca, Çince ve Korecedir"}, "SUBSTITUTE": {"a": "(metin; eski_metin; yeni_metin; [y<PERSON><PERSON><PERSON>_sayıs<PERSON>])", "d": "<PERSON><PERSON> dizesindeki eski bir metni ye<PERSON>"}, "T": {"a": "(de<PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON>'in başvurduğu metni verir"}, "TEXT": {"a": "(de<PERSON><PERSON>; biç<PERSON>_metni)", "d": "Bir değeri belirli bir sayı biçimindeki metne dönüştürür"}, "TEXTJOIN": {"a": "(sı<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; bo<PERSON><PERSON><PERSON>_yoksay; metin1; ...)", "d": "Sınırlayıcı kullanarak metin dizelerinden oluşan bir listeyi veya aralığı birleştirir"}, "TRIM": {"a": "(metin)", "d": "Bir metin dizesinden sözcükler arasındaki tek boşluklar dışındaki tüm boşlukları kaldırır"}, "UNICHAR": {"a": "(sayı)", "d": "Verilen sayısal değer tarafından başvurulan Unicode karakterini verir"}, "UNICODE": {"a": "(metin)", "d": "Metnin ilk karakterine karşılık gelen sayıyı (kod noktası) döndürür"}, "UPPER": {"a": "(metin)", "d": "<PERSON>ir metni büyük harfe dönüştürür"}, "VALUE": {"a": "(metin)", "d": "Bir sayıyı gösteren bir metin dizesini bir sayıya dönüştürür"}, "AVEDEV": {"a": "(sayı1; [sayı2]; ...)", "d": "Veri noktalarının mutlak sapmalarının aritmetik ortalamasını bu noktaların ortalaması aracılığıyla verir. Bağımsız değişkenler sayı, ad, dizi veya sayı içeren başvurular olabilir"}, "AVERAGE": {"a": "(sayı1; [sayı2]; ...)", "d": "Bağıms<PERSON>z <PERSON> (aritmetik) ortalamasını verir, bunlar sayı ya da sayılar içeren ad, dizi veya başvurular olabilir"}, "AVERAGEA": {"a": "(değer1; [değer2]; ...)", "d": "Bağımsız değişkenlerinin aritmetik ortalamasını verir, metni ve bağımsız değişkenlerdeki YANLIŞ değerini 0; DOĞRU değerini 1 olarak hesaplar. Bağımsız değişkenler sayı, ad, dizi ya da başvuru olabilir"}, "AVERAGEIF": {"a": "(aralık; ölçüt; [aralık_ortalaması])", "d": "Verili bir koşul veya ölçüt tarafından belirtilen hücrelerin ortalamasını (aritmetik ortalama) bulur"}, "AVERAGEIFS": {"a": "(aralık_ortalaması; ölçüt_aralığı; ölçüt; ...)", "d": "Verili bir koşul veya ölçüt kümesi tarafından belirtilen hücrelerin ortalamasını (aritmetik ortalama) bulur"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Kümülatif beta olasılık yoğunluk işlevini verir"}, "BETAINV": {"a": "(olasılık; alfa; beta; [A]; [B])", "d": "Kümülatif beta olasılık yoğunluk işlevinin (BETADAĞ) tersini verir"}, "BETA.DIST": {"a": "(x; alfa; beta; kümülatif; [A]; [B])", "d": "Beta olasılık dağılımı işlevini verir"}, "BETA.INV": {"a": "(olasılık; alfa; beta; [A]; [B])", "d": "Kümülatif beta olasılık yoğunluk işlevinin (BETA.DAĞ) tersini verir"}, "BINOMDIST": {"a": "(başar<PERSON>_sayısı; den<PERSON><PERSON><PERSON>; başar<PERSON>_olasılığı; kümülatif)", "d": "Tek terimli binom dağılımı olasılığını verir"}, "BINOM.DIST": {"a": "(başar<PERSON>_sayısı; den<PERSON><PERSON><PERSON>; başar<PERSON>_olasılığı; kümülatif)", "d": "Tek terimli binom dağılımı olasılığını verir"}, "BINOM.DIST.RANGE": {"a": "(den<PERSON><PERSON><PERSON>; başarı_olasılığı; başarı_sayısı; [başarı_sayısı2])", "d": "Binom dağılımını kullanarak bir deneme sonucunun başarı olasılığını döndürür"}, "BINOM.INV": {"a": "(den<PERSON><PERSON><PERSON>; başarı_olasılığı; alfa)", "d": "Kümülatif binom dağılımının ölçüt değerinden küçük veya ona eşit olduğu en küçük değeri verir"}, "CHIDIST": {"a": "(x; serb_derecesi)", "d": "<PERSON><PERSON><PERSON>ılımının sağ kuyruklu olasılığını verir"}, "CHIINV": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "<PERSON><PERSON><PERSON>ılımının sağ kuyruklu olasılığının tersini verir"}, "CHITEST": {"a": "(etkin_erim; beklenen_erim)", "d": "Bağımsızlık sınaması sonucunu verir: istatistik için kikare dağılımından alınan değer ve uygun serbestlik derecesi"}, "CHISQ.DIST": {"a": "(x; serb_derecesi; kümülatif)", "d": "<PERSON><PERSON><PERSON>ımının sol kuyruklu olasılığını verir"}, "CHISQ.DIST.RT": {"a": "(x; serb_derecesi)", "d": "<PERSON><PERSON><PERSON>ılımının sağ kuyruklu olasılığını verir"}, "CHISQ.INV": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "<PERSON><PERSON><PERSON>ımının sol kuyruklu olasılığının tersini verir"}, "CHISQ.INV.RT": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "<PERSON><PERSON><PERSON>ılımının sağ kuyruklu olasılığının tersini verir"}, "CHISQ.TEST": {"a": "(etkin_erim; beklenen_erim)", "d": "Bağımsızlık sınaması sonucunu verir: istatistik için kikare dağılımından alınan değer ve uygun serbestlik derecesi"}, "CONFIDENCE": {"a": "(alfa; standart_sapma; boyut)", "d": "Popülasyon ortalaması için normal bir dağılım kullanarak güvenilirlik aralığını verir"}, "CONFIDENCE.NORM": {"a": "(alfa; standart_sapma; boyut)", "d": "Popülasyon ortalaması için normal bir dağılım kullanarak güvenilirlik aralığını verir"}, "CONFIDENCE.T": {"a": "(alfa; standart_sapma; boyut)", "d": "Popülasyon ortalaması için bir T-dağılımı kullanarak güvenilirlik aralığını verir"}, "CORREL": {"a": "(dizi1; dizi2)", "d": "İki veri kümesi arasındaki korelasyon katsayısını verir"}, "COUNT": {"a": "(değer1; [değer2]; ...)", "d": "Aralıktaki sayı içeren hücrelerin kaç tane olduğunu sayar"}, "COUNTA": {"a": "(değer1; [değer2]; ...)", "d": "Aralıktaki bo<PERSON> o<PERSON>yan hücre<PERSON>in kaç tane olduğunu sayar"}, "COUNTBLANK": {"a": "(aralık)", "d": "Belirtilen hücre aralığındaki boş hücreleri sayar"}, "COUNTIF": {"a": "(aralık; ölçüt)", "d": "Verilen koşula uyan aralık içindeki hücreleri sayar"}, "COUNTIFS": {"a": "(ölçüt_aralığı; ölçüt; ...)", "d": "Verili bir koşul veya ölçüt kümesi tarafından belirtilen hücreleri sayar"}, "COVAR": {"a": "(dizi1; dizi2)", "d": "Kovaryansı verir; iki veri kümes<PERSON>eki her veri noktası çifti için sapmaların çarpımlarının ortalaması"}, "COVARIANCE.P": {"a": "(dizi1; dizi2)", "d": "Popülasyon kovaryansını verir; iki veri kümesindeki her veri noktası çifti için sapmaların çarpımlarının ortalaması"}, "COVARIANCE.S": {"a": "(dizi1; dizi2)", "d": "Örnek kovaryansı verir; iki veri kümesindeki her veri noktası çifti için sapmaların çarpımlarının ortalaması"}, "CRITBINOM": {"a": "(den<PERSON><PERSON><PERSON>; başarı_olasılığı; alfa)", "d": "Kümülatif binom dağılımının ölçüt değerinden küçük veya ona eşit olduğu en küçük değeri verir"}, "DEVSQ": {"a": "(sayı1; [sayı2]; ...)", "d": "Veri noktalarının kendi örneklerinin ortalamasından sapmaların kareleri toplamını verir"}, "EXPONDIST": {"a": "(x; lambda; kümülatif)", "d": "Üstel dağılımı verir"}, "EXPON.DIST": {"a": "(x; lambda; kümülatif)", "d": "Üstel dağılımı verir"}, "FDIST": {"a": "(x; serb_derecesi1; serb_derecesi2)", "d": "İki veri kümesi için (sağ kuyruklu) F olasılık dağılımını (basıklık derecesi) verir"}, "FINV": {"a": "(olas<PERSON>l<PERSON>k; serb_derecesi1; serb_derecesi2)", "d": "(<PERSON>ğ kuyruk<PERSON>) F olasılık dağılımının tersini verir: p = FDAĞ(x,...) ise, FTERS(p,...) = x"}, "FTEST": {"a": "(dizi1; dizi2)", "d": "Bir F-test sonucu verir; Dizi1 ve Dizi2'nin vary<PERSON>ının çok farklı olmadığı iki kuyruklu olasılıktır"}, "F.DIST": {"a": "(x; serb_derecesi1; serb_derecesi2; kümülatif)", "d": "İki veri kümesi için (sol kuyruklu) F olasılık dağılımını (basıklık derecesi) verir"}, "F.DIST.RT": {"a": "(x; serb_derecesi1; serb_derecesi2)", "d": "İki veri kümesi için (sağ kuyruklu) F olasılık dağılımını (basıklık derecesi) verir"}, "F.INV": {"a": "(olas<PERSON>l<PERSON>k; serb_derecesi1; serb_derecesi2)", "d": "(Sol kuyruklu) F olasılık dağılımının tersini verir: p = F.DAĞ(x,...) ise, F.TERS(p,...) = x"}, "F.INV.RT": {"a": "(olas<PERSON>l<PERSON>k; serb_derecesi1; serb_derecesi2)", "d": "(<PERSON>ğ kuyruk<PERSON>) F olasılık dağılımının tersini verir: p = F.DAĞ.SAĞK(x,...) ise, F.TERS.SAĞK(p,...) = x"}, "F.TEST": {"a": "(dizi1; dizi2)", "d": "Bir F-test sonucu verir; Dizi1 ve Dizi2'nin vary<PERSON>ının çok farklı olmadığı iki kuyruklu olasılıktır"}, "FISHER": {"a": "(x)", "d": "<PERSON> dö<PERSON>üşü<PERSON><PERSON><PERSON><PERSON> verir"}, "FISHERINV": {"a": "(y)", "d": "Fisher dönüşü<PERSON>ünün tersini verir: y = FISHER(x) ise, FISHERTERS(y) = x"}, "FORECAST": {"a": "(x; bilinen_y'ler; bilinen_x'ler)", "d": "Varolan değerleri kullanarak bir gelecek değeri doğrusal bir eğilim boyunca hesaplar ya da tahmin eder"}, "FORECAST.ETS": {"a": "(hede<PERSON>_tari<PERSON>; <PERSON><PERSON><PERSON><PERSON>; z<PERSON>_<PERSON>; [mev<PERSON><PERSON><PERSON><PERSON>]; [veri_tamamlama]; [toplama])", "d": "Üstel düzeltme yöntemini kullanarak belirtilen hedef gelecek tarihi için tahmin edilen bir değer verir."}, "FORECAST.ETS.CONFINT": {"a": "(hede<PERSON>_tari<PERSON>; <PERSON><PERSON><PERSON><PERSON>; z<PERSON>_<PERSON>; [g<PERSON><PERSON>_d<PERSON><PERSON>]; [mev<PERSON><PERSON><PERSON><PERSON>]; [veri_tamamlama]; [toplama])", "d": "Belirtilen hedef tari<PERSON>eki tahmin de<PERSON> için bir güvenilirlik aralığını verir."}, "FORECAST.ETS.SEASONALITY": {"a": "(<PERSON><PERSON><PERSON><PERSON>; z<PERSON>_<PERSON>; [veri_tamamlama]; [toplama])", "d": "Belirtilen zaman dizisi için uygulama tarafından algılanan tekrarlanan desenin uzunluğunu verir."}, "FORECAST.ETS.STAT": {"a": "(<PERSON><PERSON><PERSON><PERSON>; z<PERSON>_<PERSON>; istatistik_türü; [mevsi<PERSON><PERSON>k]; [veri_tamamlama]; [toplama])", "d": "<PERSON><PERSON><PERSON> i<PERSON><PERSON> istenen istatistiği döndürür."}, "FORECAST.LINEAR": {"a": "(x; bilinen_y'ler; bilinen_x'ler)", "d": "Varolan değerleri kullanarak bir gelecek değeri doğrusal bir eğilim boyunca hesaplar ya da tahmin eder"}, "FREQUENCY": {"a": "(veri_dizisi; b<PERSON><PERSON><PERSON>_dizisi)", "d": "Bir değerler aralığındaki değerlerin hangi sıklıkta yer aldığını hesaplar ve bölme_dizisi'nden bir fazla elemana sahip olan bir dikey sayı dizisi verir"}, "GAMMA": {"a": "(x)", "d": "Gama işlevi değerini verir"}, "GAMMADIST": {"a": "(x; alfa; beta; kümülatif)", "d": "Gama dağılımını verir"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kümülatif)", "d": "Gama dağılımını verir"}, "GAMMAINV": {"a": "(olasılık; alfa; beta)", "d": "Gama kümülatif <PERSON>mının tersini verir: p = GAMADAĞ(x,...) ise, GAMATERS(p,...) = x"}, "GAMMA.INV": {"a": "(olasılık; alfa; beta)", "d": "Gama kümülatif <PERSON>ımının tersini verir: p = GAMA.DAĞ(x,...) ise, GAMA.TERS(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Gama fonksiyonunun doğal logaritmasını verir"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Gama işlevinin doğal logaritmasını döndürür"}, "GAUSS": {"a": "(x)", "d": "Standart normal kümülatif dağılımdan 0,5 daha azını verir"}, "GEOMEAN": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir dizi ya da pozitif sayısal veri aralığının geometrik ortalamasını verir"}, "GROWTH": {"a": "(bilinen_y'ler; [bilinen_x'ler]; [yeni_x'ler]; [sabit])", "d": "Bilinen veri noktalarıyla eşleşen üstel büyüme trendi içindeki sayıları döndürür"}, "HARMEAN": {"a": "(sayı1; [sayı2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> olu<PERSON>an bir veri kümesinin harmonik ortalamasını verir: devrik de<PERSON>lerin aritmetik ortalamasının devrik <PERSON>"}, "HYPGEOM.DIST": {"a": "(başar<PERSON>_örnek; örnek_sayısı; başarı_popülasyon; pop_sayısı; kümülatif)", "d": "Hipergeometrik dağılımı verir"}, "HYPGEOMDIST": {"a": "(başar<PERSON>_örnek; sayı_örnek; başar<PERSON>_popülasyon; sayı_pop)", "d": "Hipergeometrik dağılımı verir"}, "INTERCEPT": {"a": "(bilinen_y'ler; bilinen_x'ler)", "d": "Bilinen x ve y-değerleri üzerindeki en uygun regresyon çizgisini kullanarak bir çizginin y-eksenini kestiği noktaları hesaplar"}, "KURT": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir veri kümesinin basıklığını verir"}, "LARGE": {"a": "(dizi; k)", "d": "Bir veri kümesi içindeki en büyük k. değeri verir. <PERSON><PERSON><PERSON><PERSON>, beşinci en büyük sayı"}, "LINEST": {"a": "(bilinen_y'ler; [bilinen_x'ler]; [sabit]; [konum])", "d": "En küçük kareler yöntemiyle hesaplanmış olan verilerinize en iyi uyan doğruyu tanımlayan diziyi verir"}, "LOGEST": {"a": "(bilinen_y'ler; [bilinen_x'ler]; [sabit]; [konum])", "d": "Verilerinize uyması için regresyon çözümlemesi yöntemiyle hesaplanmış olan üstel eğriyi tanımlayan değerler dizisini verir"}, "LOGINV": {"a": "(olasılık; ortalama; standart_sapma)", "d": "ln(x)'in Ortalama ve Standart_sapma parametreleriyle normal dağıldığı durumlarda x'in kümülatif lognormal dağılım işlevinin tersini verir"}, "LOGNORM.DIST": {"a": "(x; ortalama; standart_sapma; kümülatif)", "d": "ln(x)'in normal olarak Ortalama ve Standart_sapma parametreleriyle dağıldığı durumlarda x'in lognormal dağılımını verir"}, "LOGNORM.INV": {"a": "(olasılık; ortalama; standart_sapma)", "d": "ln(x)'in Ortalama ve Standart_sapma parametreleriyle normal dağıldığı durumlarda x'in kümülatif lognormal dağılım işlevinin tersini verir"}, "LOGNORMDIST": {"a": "(x; ortalama; standart_sapma)", "d": "ln(x)'in normal olarak Ortalama ve Standart_sapma parametreleriyle dağıldığı durumlarda x'in kümülatif lognormal dağılımını verir"}, "MAX": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir değerler kümesindeki en büyük değeri verir. Mantıksal değerleri ve metni yoksayar"}, "MAXA": {"a": "(değer1; [değer2]; ...)", "d": "Bir değerler kümesindeki en büyük değeri verir. Mantıksal değerleri ve metni yoksaymaz"}, "MAXIFS": {"a": "(en_büyük_aralık; ölçüt_aralığı; ölçüt; ...)", "d": "Verilen koşul veya ölçüt kümesiyle belirtilen hücrelerdeki en büyük değeri döndürür"}, "MEDIAN": {"a": "(sayı1; [sayı2]; ...)", "d": "Verilen sayılar kümesinin ortancasını ya da bu kümenin ortasındaki sayıyı verir"}, "MIN": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir değerler kümesindeki en küçük değeri verir. Mantıksal değerleri ve metni yoksayar"}, "MINA": {"a": "(değer1; [değer2]; ...)", "d": "Bir değerler kümesindeki en küçük değeri verir. Mantıksal değerleri ve metni yoksaymaz"}, "MINIFS": {"a": "(en_küçük_aralık; ölçüt_aralığı; ölçüt; ...)", "d": "Verilen koşul veya ölçüt kümesiyle belirtilen hücrelerdeki en küçük değeri döndürür"}, "MODE": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir veri dizisi ya da aralığında en sık karşılaşılan ya da en çok yinelenen değeri verir"}, "MODE.MULT": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir veri dizisi veya aralığında en sık karşılaşılan veya en çok yinelenen değerleri içeren dikey bir dizi verir. Yatay bir dizi için, =DEVRİK_DÖNÜŞÜM(ENÇOK_OLAN.ÇOK(sayı1,sayı2,...)) kullanın"}, "MODE.SNGL": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir veri dizisi ya da aralığında en sık karşılaşılan ya da en çok yinelenen değeri verir"}, "NEGBINOM.DIST": {"a": "(hata_sayısı; başarı_sayısı; başarı_olasılığı; kümülatif)", "d": "Bir başarının negatif binom dağılımını, yani Başarı_sayısı kadar başarıdan önce Başarısızlık_s kadar başarısızlık olması olasılığını Başarı_olasılığı kadar olasılıkla verir"}, "NEGBINOMDIST": {"a": "(başarısızlık_s; başarı_sı; başarı_o)", "d": "Bir başarının negatif binom dağılımını, yani Başarı_sı kadar başarıdan önce Başarısızlık_s kadar başarısızlık olması olasılığını ve Başarı_o olarak başarı olasılığını verir"}, "NORM.DIST": {"a": "(x; ortalama; standart_sapma; kümülatif)", "d": "Belirtilen ortalama ve standart sapma için normal dağılımı verir"}, "NORMDIST": {"a": "(x; ortalama; standart_sapma; kümülatif)", "d": "Belirtilen ortalama ve standart sapma için normal kümülatif <PERSON>ğılımı verir"}, "NORM.INV": {"a": "(olasılık; ortalama; standart_sapma)", "d": "Belirtilen ortalama ve standart sapma için normal kümülatif <PERSON>ımın tersini verir"}, "NORMINV": {"a": "(olasılık; ortalama; standart_sapma)", "d": "Belirtilen ortalama ve standart sapma için normal kümülatif <PERSON>ımın tersini verir"}, "NORM.S.DIST": {"a": "(z; kümülatif)", "d": "Standart normal dağılımı (ortalaması sıfır, standart sapması bir) verir"}, "NORMSDIST": {"a": "(z)", "d": "Standart normal kümülatif <PERSON>ımı (ortalaması sıfır, standart sapması bir) verir"}, "NORM.S.INV": {"a": "(olasılık)", "d": "Standart normal kümülat<PERSON> (ortalaması sıfır, standart sapması bir) tersini verir"}, "NORMSINV": {"a": "(olasılık)", "d": "Standart normal kümülat<PERSON> (ortalaması sıfır, standart sapması bir) tersini verir"}, "PEARSON": {"a": "(dizi1; dizi2)", "d": "Pearson çarpım moment korelasyon katsayısı olan r'yi verir"}, "PERCENTILE": {"a": "(dizi; k)", "d": "Bir aralık içerisindeki değerlerin k. yüzdebir toplamını verir"}, "PERCENTILE.EXC": {"a": "(dizi; k)", "d": "Aralıktaki değerlerin k. yüzdebirliğini verir; k, 0..1 aralığındadır (bunlar hariç)"}, "PERCENTILE.INC": {"a": "(dizi; k)", "d": "Aralıktaki değerlerin k. yüzdebirliğini verir; k, 0..1 aralığındadır (bunlar dahil)"}, "PERCENTRANK": {"a": "(dizi; x; [anlam])", "d": "Bir veri kümesindeki bir değerin sı<PERSON>ını, veri kümesinin yüzdesi olarak verir"}, "PERCENTRANK.EXC": {"a": "(dizi; x; [anlam])", "d": "Bir veri kümesindeki değerin derecesini veri kümesinin yüzdesi (0..1, bun<PERSON> hari<PERSON>) olarak verir"}, "PERCENTRANK.INC": {"a": "(dizi; x; [anlam])", "d": "Bir veri kümesindeki değerin derecesini veri kümesinin yüzdesi (0..1, bunlar da<PERSON>) olarak verir"}, "PERMUT": {"a": "(sayı; sayı_seçilen)", "d": "Tüm nesnelerden seçilebilecek olan verilen sayıda nesne için permütasyon sayısını verir"}, "PERMUTATIONA": {"a": "(sayı; sayı_seçilen)", "d": "Tüm nesnelerden seçilebilecek olan verilen sayıda (yinelemelerle) nesne için permütasyon sayısını verir"}, "PHI": {"a": "(x)", "d": "Standart normal dağılımın yoğunluk fonksiyonunun değerini döndürür"}, "POISSON": {"a": "(x; ortalama; kümülatif)", "d": "Poisson dağılımını verir"}, "POISSON.DIST": {"a": "(x; ortalama; kümülatif)", "d": "Poisson dağılımını verir"}, "PROB": {"a": "(x_aralığ<PERSON>; olasılık_aralığı; alt_sınır; [üst_sınır])", "d": "Bir aralıktaki değerlerin iki sınır arasında ya da alt sınıra eşit olması olasılığını verir"}, "QUARTILE": {"a": "(dizi; d<PERSON>rt<PERSON><PERSON>)", "d": "Bir veri kümesinin dörttebirliğini verir"}, "QUARTILE.INC": {"a": "(dizi; d<PERSON>rt<PERSON><PERSON>)", "d": "0..1 (b<PERSON><PERSON> dahil) aralığındaki yüzdebir değerlerini temel alarak veri kümesinin dörttebirliğini verir"}, "QUARTILE.EXC": {"a": "(dizi; d<PERSON>rt<PERSON><PERSON>)", "d": "0..1 (b<PERSON><PERSON> hari<PERSON>) aralığındaki yüzdebir değerlerini temel alarak veri kümesinin dörttebirliğini verir"}, "RANK": {"a": "(sayı; başv; [sıra])", "d": "Bir sayı listesindeki bir sayının derecesini verir: <PERSON><PERSON><PERSON> di<PERSON> değerlere göreli olarak büyüklüğü"}, "RANK.AVG": {"a": "(sayı; başv; [sıra])", "d": "Bir sayı listesindeki bir sayının derecesini verir: <PERSON><PERSON><PERSON> di<PERSON>er değerlere göreli olarak büyüklüğü; birden fazla değer aynı dereceye sahipse, ortalama derece döndürülür"}, "RANK.EQ": {"a": "(sayı; başv; [sıra])", "d": "Bir sayı listesindeki bir sayının derecesini verir: <PERSON><PERSON><PERSON> diğer değerlere göreli olarak büyüklüğü; birden fazla değer aynı dereceye sahipse, de<PERSON>er kümesindeki en yüksek derece döndürülür"}, "RSQ": {"a": "(bilinen_y'ler; bilinen_x'ler)", "d": "Verilen veri noktaları boyunca Pearson çarpım moment korelasyon katsayısının karesini verir"}, "SKEW": {"a": "(sayı1; [sayı2]; ...)", "d": "Dağılımın eğriliğini verir: bir dağılımın ortalaması etrafındaki asimetri derecesini belirtir"}, "SKEW.P": {"a": "(sayı1; [sayı2]; ...)", "d": "Popülasyona bağlı olarak dağılımın eğriliğini verir: bir dağılımın ortalaması etrafındaki asimetri derecesini belirtir"}, "SLOPE": {"a": "(bilinen_y'ler; bilinen_x'ler)", "d": "Verilen veri noktaları boyunca doğrusal regresyon çizgisinin eğimini verir"}, "SMALL": {"a": "(dizi; k)", "d": "Bir veri kümesinde k. en küçük değeri verir. <PERSON><PERSON><PERSON><PERSON>, beşinci en küçük sayı"}, "STANDARDIZE": {"a": "(x; ortalama; standart_sapma)", "d": "Bir ortalama ve standart sapma tarafından temsil edilen bir dağılımdan normalleştirilen değeri verir"}, "STDEV": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir örneğe day<PERSON>rak standart sapmayı tahmin eder (örnekteki mantıksal değerleri ve metni yoksayar)"}, "STDEV.P": {"a": "(sayı1; [sayı2]; ...)", "d": "Bağımsız değişkenler olarak verilen tüm popülasyonu temel alarak standart sapmayı hesaplar (mantıksal değerleri ve metni yoksayar)"}, "STDEV.S": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir örneğe day<PERSON>rak standart sapmayı tahmin eder (örnekteki mantıksal değerleri ve metni yoksayar)"}, "STDEVA": {"a": "(değer1; [değer2]; ...)", "d": "Mantıksal değerler ve metin içeren bir örneğin standart sapmasını tahmin eder. Metin ve YANLIŞ mantıksal değer 0; DOĞRU mantıksal değer ise 1 değerini alır"}, "STDEVP": {"a": "(sayı1; [sayı2]; ...)", "d": "Bağımsız değişkenler olarak verilen tüm popülasyonu temel alarak standart sapmayı hesaplar (mantıksal değerleri ve metni yoksayar)"}, "STDEVPA": {"a": "(değer1; [değer2]; ...)", "d": "Mantıksal değerler ve metin içeren tüm bir popülasyon için standart sapmayı hesaplar. Metin ve YANLIŞ mantıksal değer 0; DOĞRU mantıksal değer ise 1 değerini alır"}, "STEYX": {"a": "(bilinen_y'ler; bilinen_x'ler)", "d": "<PERSON><PERSON> regresyon<PERSON>i her x değeri için tahmin edilen y değerinin standart hatasını verir"}, "TDIST": {"a": "(x; serb<PERSON><PERSON>_der; yazı_say)", "d": "T-dağılımını verir"}, "TINV": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "T-dağılımının iki kuyruklu tersini verir"}, "T.DIST": {"a": "(x; serb_derecesi; kümülatif)", "d": "Sol kuyruklu t-dağılımını verir"}, "T.DIST.2T": {"a": "(x; serb_derecesi)", "d": "İki kuyruklu t-dağılımını verir"}, "T.DIST.RT": {"a": "(x; serb_derecesi)", "d": "Sağ kuyruklu t-dağılımını verir"}, "T.INV": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "T-dağılımının sol kuyruklu tersini verir"}, "T.INV.2T": {"a": "(olasıl<PERSON>k; serb_derecesi)", "d": "T-dağılımının iki kuyruklu tersini verir"}, "T.TEST": {"a": "(dizi1; dizi2; yazı_say; tür)", "d": "Bir t-Test i<PERSON><PERSON> verir"}, "TREND": {"a": "(bilinen_y'ler; [bilinen_x'ler]; [yeni_x'ler]; [sabit])", "d": "Bilinen değerlere en küçük kareler yöntemini uygulayarak değerleri bir doğruya uydurur ve bir doğrusal eğilim boyunca verir"}, "TRIMMEAN": {"a": "(dizi; yüzde)", "d": "Bir veri kümesinin iç kısmının ortalamasını verir"}, "TTEST": {"a": "(dizi1; dizi2; yazı_say; tür)", "d": "Bir t-Test i<PERSON><PERSON> verir"}, "VAR": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir örneğe dayanarak <PERSON>ı tahmin eder (örnekteki mantıksal değerleri ve metni yoksayar)"}, "VAR.P": {"a": "(sayı1; [sayı2]; ...)", "d": "Tüm popülasyonun varyansını hesaplar (popülasyondaki mantıksal değerleri ve metni yoksayar)"}, "VAR.S": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir örneğe dayanarak <PERSON>ı tahmin eder (örnekteki mantıksal değerleri ve metni yoksayar)"}, "VARA": {"a": "(değer1; [değer2]; ...)", "d": "Mantıksal değerler ve metin içeren bir örneğin varyansını tahmin eder. Metin ve YANLIŞ mantıksal değer 0; DOĞRU mantıksal değer ise 1 değerini alır"}, "VARP": {"a": "(sayı1; [sayı2]; ...)", "d": "Tüm popülasyonun varyansını hesaplar (popülasyondaki mantıksal değerleri ve metni yoksayar)"}, "VARPA": {"a": "(değer1; [değer2]; ...)", "d": "Mantıksal değerler ve metin içeren bir popülasyon için varyansı hesaplar. Metin ve YANLIŞ mantıksal değer 0; DOĞRU mantıksal değer ise 1 değerini alır"}, "WEIBULL": {"a": "(x; alfa; beta; kümülatif)", "d": "<PERSON><PERSON> dağılımını verir"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kümülatif)", "d": "<PERSON><PERSON> dağılımını verir"}, "Z.TEST": {"a": "(dizi; x; [sigma])", "d": "Bir z-test'in tek kuyruklu P-değerini verir"}, "ZTEST": {"a": "(dizi; x; [sigma])", "d": "Bir z-test'in tek kuyruklu P-değerini verir"}, "ACCRINT": {"a": "(çıkış; ilk_faiz; mutabakat; oran; nominal; sıklık; [temel]; [hesapl_y<PERSON><PERSON><PERSON>])", "d": "Düzenli faiz ödenen bir menkul kıymetin birikmiş faizini döndürür."}, "ACCRINTM": {"a": "(çıkış; mutabakat; oran; nominal; [temel])", "d": "Vadesinde faiz ödeyen bir menkul kıymet için elde edilen faizi döndürür"}, "AMORDEGRC": {"a": "(maliyet; alı<PERSON>_tarihi; ilk_dönem; hurda; dönem; oran; [temel])", "d": "<PERSON>ir malın her hesap dönemi için doğrusal amortisman eşdağılımını döndürür"}, "AMORLINC": {"a": "(maliyet; alı<PERSON>_tarihi; ilk_dönem; hurda; dönem; oran; [temel])", "d": "<PERSON>ir malın her hesap dönemi için doğrusal amortisman eşdağılımını döndürür"}, "COUPDAYBS": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Kupon döneminin başlangıcından mutabakat tarihine kadar olan gün sayısını döndürür"}, "COUPDAYS": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Mutabakat tarihini içeren kupon dönemindeki gün sayısını döndürür"}, "COUPDAYSNC": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Mutabakat tarihinden bir sonraki kupon tarihine kadar olan gün sayısını döndürür"}, "COUPNCD": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Mutabakat tarihinden sonraki kupon tarihini dö<PERSON>ü<PERSON><PERSON><PERSON>"}, "COUPNUM": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Mutabakat ve vade tarihleri arasındaki ödenebilir kupon sayısını döndürür"}, "COUPPCD": {"a": "(mutabakat; vade; sıklık; [temel])", "d": "Mutabakat tarihinden önceki kupon tarihini döndürür"}, "CUMIPMT": {"a": "(oran; döne<PERSON>_sayısı; bd; ba<PERSON><PERSON><PERSON><PERSON>_döne<PERSON>; bitiş_dönemi; tür)", "d": "İki dönem arasında ödenen bileşik faizi döndürür"}, "CUMPRINC": {"a": "(oran; döne<PERSON>_sayısı; bd; ba<PERSON><PERSON><PERSON><PERSON>_döne<PERSON>; bitiş_dönemi; tür)", "d": "İki dönem arasında borç için ödenen bileşik anaparayı döndürür"}, "DB": {"a": "(mali<PERSON>t; hurda; ömür; dönem; [ay])", "d": "Sabit azalan bakiye yöntemi kullanarak bir varlığın belirtilen dönem içindeki yıpranmasını verir"}, "DDB": {"a": "(mali<PERSON>t; hurda; ömür; dönem; [faktör])", "d": "Çift azalan bakiye yöntemi veya belirttiğiniz diğer bir yöntemle, bir varlığın belirtilen dönem içindeki yıpranmasını verir"}, "DISC": {"a": "(mutabakat; vade; fiyat; itfa; [temel])", "d": "Menkul kıymet için indirim oranını döndürür"}, "DOLLARDE": {"a": "(kesirli_para; payda)", "d": "Ke<PERSON>li olarak gösterilen ücreti ondalık düzene çevirir"}, "DOLLARFR": {"a": "(ondalık_para; payda)", "d": "Ondalık düzende gösterilen ücreti kesir şekline çevirir"}, "DURATION": {"a": "(mutabakat; vade; kupon; getiri; sıklık; [temel])", "d": "Dönemsel faiz ödemeli bir menkul kıymet için yıllık süreyi döndürür"}, "EFFECT": {"a": "(nominal_oran; döne<PERSON>_sayısı)", "d": "Etkin bileşik faiz oranını döndürür"}, "FV": {"a": "(oran; döne<PERSON>_sayısı; dö<PERSON><PERSON>el_ödeme; [bd]; [tür])", "d": "Bir yatır<PERSON><PERSON><PERSON>n gel<PERSON>, dönemsel sabit ödemeler ve sabit faiz oranı kullanarak hesaplar."}, "FVSCHEDULE": {"a": "(anapara; program)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bir seri bileşik faiz uygulandıkt<PERSON>, gelecekteki değerini verir"}, "INTRATE": {"a": "(mutabakat; vade; yatırım; itfa; [temel])", "d": "Tam olarak yatırım yapılan bir menkul kıymetin faiz oranını döndürür"}, "IPMT": {"a": "(oran; dönem; döne<PERSON>_sayısı; bd; [gd]; [tür])", "d": "Dönemsel sabit ödemeli ve sabit faiz oranlı bir yatırımın verilen dönem için faiz ödemesini verir."}, "IRR": {"a": "(<PERSON><PERSON><PERSON><PERSON>; [tahmin])", "d": "Bir dizi nakit akışı için iç verim oranını verir"}, "ISPMT": {"a": "(oran; dönem; döne<PERSON>_sayısı; bd)", "d": "Yatırımın belirli bir döneminde ödenen faizi verir"}, "MDURATION": {"a": "(mutabakat; vade; kupon; getiri; sıklık; [temel])", "d": "100 TL nominal değerli bir menkul kıymet için <PERSON>ley farklılaştırılmış süresini döndürür"}, "MIRR": {"a": "(<PERSON><PERSON><PERSON><PERSON>; finansman_faiz_oranı; tekrar_yatırım_oranı)", "d": "Yatırım maliyetini ve nakit paranın tekrar yatırımından elde edilen faizin getirisini dikkate alarak, dönemsel nakit akışları serisi için iç verim oranını verir"}, "NOMINAL": {"a": "(et<PERSON>_oran; d<PERSON><PERSON><PERSON>_sayısı)", "d": "Yıllık nominal faiz oranını döndürür"}, "NPER": {"a": "(oran; dö<PERSON><PERSON><PERSON>_ödeme; bd; [gd]; [tür])", "d": "Dönemsel sabit ödemeli ve sabit faizli bir yatırımın dönem sayısını verir"}, "NPV": {"a": "(oran; değer1; [değer2]; ...)", "d": "<PERSON><PERSON><PERSON>, gelecekte yapılacak bir dizi ödemeyi (negatif değ<PERSON>ler) ve geliri (pozitif değerler) temel alarak yatırımın bugünkü net değerini verir"}, "ODDFPRICE": {"a": "(mutabakat; vade; çıkış; ilk_kupon; oran; getiri; itfa; sıklık; [temel])", "d": "Tek ilk dönemli bir menkul kıymet için 100 TL nominal değer başına fiyatı döndürür"}, "ODDFYIELD": {"a": "(mutabakat; vade; çıkış; ilk_kupon; oran; fiyat; itfa; sıklık; [temel])", "d": "Tek ilk dönemli bir menkul kıymet için getiriyi döndürür"}, "ODDLPRICE": {"a": "(mutaba<PERSON>; vade; son_faiz; oran; getiri; itfa; sıklık; [temel])", "d": "<PERSON><PERSON> son dö<PERSON><PERSON><PERSON> bir menkul kıymet için 100 TL nominal değer başına fiyatı döndürür"}, "ODDLYIELD": {"a": "(mutaba<PERSON>; vade; son_faiz; oran; fiyat; itfa; sıklık; [temel])", "d": "<PERSON><PERSON> son dö<PERSON><PERSON><PERSON> bir menkul kı<PERSON>tin getirisini dö<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "PDURATION": {"a": "(oran; bd; gd)", "d": "Ya<PERSON><PERSON><PERSON><PERSON>m tarafından belirtilen bir değere ulaşmak için gereken dönem sayısını döndürür"}, "PMT": {"a": "(oran; döne<PERSON>_sayısı; bd; [gd]; [tür])", "d": "Sabit ödemeli ve sabit faizli bir borç için yapılacak ödemeyi hesaplar"}, "PPMT": {"a": "(oran; dönem; döne<PERSON>_sayısı; bd; [gd]; [tür])", "d": "Dönemsel sabit ödemeli ve sabit faizli bir yatırım için yapılacak anapara ödemesi tutarını verir"}, "PRICE": {"a": "(mutabakat; vade; oran; getiri; itfa; sıklık; [temel])", "d": "Dönemsel faiz ödeyen bir menkul kıymet için 100 TL nominal değer başına fiyatı döndürür"}, "PRICEDISC": {"a": "(mutabakat; vade; indirim; itfa; [temel])", "d": "İndirimli bir menkul kıymet için 100 TL nominal değer başına fiyatı döndürür"}, "PRICEMAT": {"a": "(mutabakat; vade; çıkış; oran; getiri; [temel])", "d": "Vadesinde faiz ödeyen bir menkul kıymet için 100 TL nominal değer başına fiyatı döndürür"}, "PV": {"a": "(oran; döne<PERSON>_sayısı; dö<PERSON>msel_ödeme; [gd]; [tür])", "d": "Bir yatırımın bugünkü değerini verir: gelecekte yapılacak bir dizi ödemenin bugünkü toplam değeri"}, "RATE": {"a": "(dö<PERSON><PERSON>_sayısı; dönemsel_ödeme; bd; [gd]; [tür]; [tahmin])", "d": "Bir borç ya da yatırım için dönem başına faiz oranını verir. Örneğin, %6 yıllık faiz oranına karşılık üç aylık ödeme için %6/4 kullanın"}, "RECEIVED": {"a": "(mutabakat; vade; yatırım; indirim; [temel])", "d": "Tam olarak yatırım yapılan bir menkul kıymetin vadesindeki getiri miktarını döndürür"}, "RRI": {"a": "(döne<PERSON>_sayısı; bd; gd)", "d": "Yatırımın büyümesi için eşdeğer bir faiz oranı verir"}, "SLN": {"a": "(maliyet; hurda; ömür)", "d": "Bir malın bir dönem için doğrusal yıpranmasını verir"}, "SYD": {"a": "(mali<PERSON>t; hurda; öm<PERSON>r; dö<PERSON><PERSON>)", "d": "Bir malın belirtilen bir dönem için yıpranmasını verir"}, "TBILLEQ": {"a": "(d<PERSON><PERSON><PERSON><PERSON>; vade; indirim)", "d": "Hazine tahvili için bono eşdeğerini döndürür"}, "TBILLPRICE": {"a": "(d<PERSON><PERSON><PERSON><PERSON>; vade; indirim)", "d": "Hazine tahvili için 100 TL başına yüz değerini döndürür"}, "TBILLYIELD": {"a": "(d<PERSON><PERSON><PERSON><PERSON>; vade; <PERSON><PERSON>t)", "d": "Hazine tahvili i<PERSON><PERSON> dö<PERSON>ürür"}, "VDB": {"a": "(ma<PERSON><PERSON><PERSON>; hurda; <PERSON>m<PERSON><PERSON>; ba<PERSON><PERSON><PERSON><PERSON>_döne<PERSON>; son_döne<PERSON>; [fakt<PERSON><PERSON>]; [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Çift azalan bakiye yöntemini ya da belirttiğiniz başka bir yöntemi kullanarak, kı<PERSON>i dönemleri de içeren belirli bir dönem için bir malın amortismanını verir"}, "XIRR": {"a": "(<PERSON><PERSON><PERSON><PERSON>; ta<PERSON><PERSON>; [tahmin])", "d": "Nakit akışı planı için iç verim oranını döndürür"}, "XNPV": {"a": "(oran; <PERSON><PERSON><PERSON><PERSON>; ta<PERSON><PERSON>)", "d": "Nakit akışı planı için bugünkü net değeri döndürür"}, "YIELD": {"a": "(mutabakat; vade; oran; fiyat; itfa; sıklık; [temel])", "d": "Dönemsel faiz ödeyen bir menkul kıymetin getirisini döndürür"}, "YIELDDISC": {"a": "(mutabakat; vade; fiyat; itfa; [temel])", "d": "İndirimli bir menkul kıymet için yıllık getiriyi döndürür, <PERSON><PERSON><PERSON><PERSON> hazine bonosu"}, "YIELDMAT": {"a": "(mutabakat; vade; çıkış; oran; fiyat; [temel])", "d": "Vadesinde faiz ödeyen bir menkul kıymet için yıllık getiriyi döndürür"}, "ABS": {"a": "(sayı)", "d": "<PERSON><PERSON> sayının mutlak <PERSON> verir, <PERSON><PERSON><PERSON><PERSON> o<PERSON> sayı"}, "ACOS": {"a": "(sayı)", "d": "Bir sayının a<PERSON>ü<PERSON> verir, radyan cinsinde ve 0 - Pi aralığındadır. <PERSON><PERSON><PERSON><PERSON><PERSON>, kosinüsü Sayı olan açıdır"}, "ACOSH": {"a": "(sayı)", "d": "B<PERSON> sayının ters hiperbolik kosinüsünü verir"}, "ACOT": {"a": "(sayı)", "d": "Bir sayının arkkotanjantını 0 ile Pi aralığındaki radyanlar cinsinden verir."}, "ACOTH": {"a": "(sayı)", "d": "B<PERSON> sayının ters hiperbolik kotanjant değerini verir"}, "AGGREGATE": {"a": "(işlev_num; seçenekler; başv1; ...)", "d": "Bir liste veya veritabanından bir toplam verir"}, "ARABIC": {"a": "(metin)", "d": "Bir Roma rakamını Arap rakamına dönüştürür"}, "ASC": {"a": "(metin)", "d": "Çift bayt karakter kü<PERSON> (DBCS) dillerde, i<PERSON><PERSON> tam geni<PERSON> (çift bayt) karakterleri yarı geni<PERSON> (tek bayt) karakterlere dönüştürür"}, "ASIN": {"a": "(sayı)", "d": "Bir sayının radyan cinsinden -Pi/2 ile Pi/2 aralığındaki arksinüsünü verir"}, "ASINH": {"a": "(sayı)", "d": "B<PERSON> sayının ters hiperbolik sinüsünü verir"}, "ATAN": {"a": "(sayı)", "d": "Bir sayının radyan cinsinden -Pi/2 ile Pi/2 aralığındaki arktanjantını verir"}, "ATAN2": {"a": "(x_sayısı; y_sayısı)", "d": "Belirtilen x- ve y- koordinatlarının radyan cinsinden -Pi (-<PERSON> hariç) ile Pi arasındaki arktanjantını verir"}, "ATANH": {"a": "(sayı)", "d": "Bir sayının ters hiperbolik tanjantını verir"}, "BASE": {"a": "(sayı; sayıtabanı; [min_uzunluk])", "d": "Bir sayıyı verilen sayı tabanı (temel) ile bir metin gösterimine dönüştürür"}, "CEILING": {"a": "(sayı; anlam)", "d": "<PERSON><PERSON> sayıyı, yukarı doğru en yakın anlamlı sayı katına yuvarlar"}, "CEILING.MATH": {"a": "(sayı; [anlam]; [mod])", "d": "<PERSON><PERSON> sayıyı, yukarı doğru en yakın tamsayı veya anlamlı sayı katına yuvarlar"}, "CEILING.PRECISE": {"a": "(sayı; [anlam])", "d": "En yakın tam sayıya ya da en yakın katına yuvarlanmış sayıyı verir"}, "COMBIN": {"a": "(sayı; sayı_seçilen)", "d": "Verilen öğelerin sayısı için kombinasyon sayısını verir"}, "COMBINA": {"a": "(sayı; sayı_seçilen)", "d": "Verilen sayıda öğe için yinelemelerle birleşimlerin sayısını verir"}, "COS": {"a": "(sayı)", "d": "Bir açının kosinüsünü verir"}, "COSH": {"a": "(sayı)", "d": "Bir sayının hiperbolik kosinüsünü verir"}, "COT": {"a": "(sayı)", "d": "Bir açının kotanjant değerini verir"}, "COTH": {"a": "(sayı)", "d": "Bir sayının hiperbolik kotanjant değerini verir"}, "CSC": {"a": "(sayı)", "d": "Bir açının kosekant değerini verir"}, "CSCH": {"a": "(sayı)", "d": "Bir açının hiperbolik kosekant değerini verir"}, "DECIMAL": {"a": "(sayı; sayıtabanı)", "d": "Verilen temeldeki bir sayının metin gösterimini ondalık bir sayıya dönüştürür"}, "DEGREES": {"a": "(açı)", "d": "Radyanı dereceye <PERSON>"}, "ECMA.CEILING": {"a": "(sayı; anlam)", "d": "<PERSON><PERSON> sayıyı, yukarı doğru en yakın anlamlı sayı katına yuvarlar"}, "EVEN": {"a": "(sayı)", "d": "<PERSON><PERSON> sayıyı, mutlak değerce kendinden büyük en yakın çift tamsayıya yuvarlar"}, "EXP": {"a": "(sayı)", "d": "Verilen bir sayının üssünün e sayısının üssü olarak kullanılması ile oluşan sonucu verir"}, "FACT": {"a": "(sayı)", "d": "Bir sayının 1*2*3*...*Sayı şeklinde çarpınımını verir"}, "FACTDOUBLE": {"a": "(sayı)", "d": "Verilen bir sayıdan bire kadar ikişer ikişer azalarak oluşan sayıların çarpımını döndürür."}, "FLOOR": {"a": "(sayı; anlam)", "d": "<PERSON><PERSON> sayıyı, anlamlı en yakın katına, aşağ<PERSON> doğru yuvarlar"}, "FLOOR.PRECISE": {"a": "(sayı; [anlam])", "d": "En yakın sayıya veya en yakın anlam katına aşağı yuvarlanmış bir sayı verir"}, "FLOOR.MATH": {"a": "(sayı; [anlam]; [mod])", "d": "<PERSON><PERSON> sayıyı, aşağı doğru en yakın tamsayı veya anlamlı sayı katına yuvarlar"}, "GCD": {"a": "(sayı1; [sayı2]; ...)", "d": "En büyük ortak böleni döndürür"}, "INT": {"a": "(sayı)", "d": "<PERSON><PERSON> sayıyı, sı<PERSON><PERSON>rdan ıraksayarak en yakın tam sayıya yuvarlar"}, "ISO.CEILING": {"a": "(sayı; [anlam])", "d": "En yakın tam sayıya ya da en yakın katına yuvarlanmış sayıyı verir. Sayının işareti dikkate alınmadan sayı yuvarlanır. <PERSON><PERSON><PERSON>, sayı veya anlam sıfırsa, sıfır verilir."}, "LCM": {"a": "(sayı1; [sayı2]; ...)", "d": "En küçük ortak çarpanı döndürür"}, "LN": {"a": "(sayı)", "d": "Bir sayının doğal logaritmasını verir"}, "LOG": {"a": "(sayı; [taban])", "d": "Bir sayının belirttiğiniz tabandaki logaritmasını alır"}, "LOG10": {"a": "(sayı)", "d": "Bir sayının 10 tabanında logaritmasını verir"}, "MDETERM": {"a": "(dizi)", "d": "Bir dizinin determinantını verir"}, "MINVERSE": {"a": "(dizi)", "d": "Bir dizide saklanan bir dizeyin tersini verir"}, "MMULT": {"a": "(dizi1; dizi2)", "d": "İki dizinin dizey çarpımını verir, sonu<PERSON>, dizi1 ile aynı sayıda satıra ve dizi2 ile aynı sayıda sütuna sahip olan bir dizidir"}, "MOD": {"a": "(sayı; bölen)", "d": "<PERSON>ir sayının bir bölen tarafından bölünmesinden sonra kalanı verir"}, "MROUND": {"a": "(sayı; katsayı)", "d": "İstenen katsayıya yuvarlanmış bir sayı döndürür"}, "MULTINOMIAL": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir sayı kümesinin çok terimli değerini döndürür"}, "MUNIT": {"a": "(boyut)", "d": "<PERSON><PERSON><PERSON><PERSON> boyut için birim matris döndür<PERSON>r"}, "ODD": {"a": "(sayı)", "d": "<PERSON><PERSON> sayıyı, mutlak değerce kendinden büyük en yakın tek tamsayıya yuvarlar"}, "PI": {"a": "()", "d": "<PERSON>, 15 r<PERSON><PERSON> kadar y<PERSON><PERSON><PERSON> hali 3,14159265358979'dur"}, "POWER": {"a": "(sayı; üs)", "d": "Üssü alınmış sayının sonucunu verir"}, "PRODUCT": {"a": "(sayı1; [sayı2]; ...)", "d": "Bağımsız değişken olarak verilen tüm sayıları çarpar"}, "QUOTIENT": {"a": "(pay; payda)", "d": "Bir bölmenin tamsayı kısmını döndürür"}, "RADIANS": {"a": "(açı)", "d": "<PERSON><PERSON><PERSON><PERSON> r<PERSON> d<PERSON>"}, "RAND": {"a": "()", "d": "0 ya da 0'dan büyük ve 1'den küçük bir sayıyı eşit dağılımla rastgele verir (yeniden hesaplama sonucunda değişir)"}, "RANDARRAY": {"a": "([satırlar]; [sü<PERSON><PERSON>]; [min]; [maks]; [tamsayı])", "d": "<PERSON><PERSON> rastgele sayı dizisini döndürür"}, "RANDBETWEEN": {"a": "(alt; üst)", "d": "Belirttiğiniz sayılar arasında rastgele bir sayı döndürür"}, "ROMAN": {"a": "(sayı; [form])", "d": "<PERSON>p raka<PERSON>ını metin biçimiyle romen rakamlarına dönüştürür"}, "ROUND": {"a": "(sayı; sayı_raka<PERSON>)", "d": "Sayıyı belirli sayıdaki rakama yuvar<PERSON>"}, "ROUNDDOWN": {"a": "(sayı; sayı_raka<PERSON>)", "d": "Bir sayıyı sıfıra yakınsayarak yuvarlar"}, "ROUNDUP": {"a": "(sayı; sayı_raka<PERSON>)", "d": "Bir sayıyı sıfırdan ıraksayarak yukarı yuvarlar"}, "SEC": {"a": "(sayı)", "d": "Bir açının sekant değerini verir"}, "SECH": {"a": "(sayı)", "d": "Bir açının hiperbolik sekant değerini verir"}, "SERIESSUM": {"a": "(x; n; m; katsay<PERSON><PERSON>)", "d": "Form<PERSON>le dayalı olan kuvvet serisinin toplamını döndürür"}, "SIGN": {"a": "(sayı)", "d": "Bir sayının işaretini verir: sayı pozitif ise 1, sı<PERSON>ır ise sıfır, negatif ise -1"}, "SIN": {"a": "(sayı)", "d": "<PERSON>ir açının <PERSON> verir"}, "SINH": {"a": "(sayı)", "d": "Bir sayının hiperbolik sinüsünü verir"}, "SQRT": {"a": "(sayı)", "d": "<PERSON><PERSON> sayının karekökünü verir"}, "SQRTPI": {"a": "(sayı)", "d": "Sayının Pi sayısıyla çarpımının karekökünü döndürür"}, "SUBTOTAL": {"a": "(iş<PERSON>_sayısı; başv1; ...)", "d": "Bir liste veya veritabanından bir alt toplam verir"}, "SUM": {"a": "(sayı1; [sayı2]; ...)", "d": "Bir hücre aralığındaki tüm sayıları toplar"}, "SUMIF": {"a": "(aralık; ölçüt; [toplam_aralığı])", "d": "Verilen bir koşul ya da ölçüt tarafından belirtilen hücreleri toplar"}, "SUMIFS": {"a": "(aralık_toplamı; ölçüt_aralığı; ölçüt; ...)", "d": "Verili bir koşul veya ölçüt kümesi tarafından belirtilen hücreleri toplar"}, "SUMPRODUCT": {"a": "(dizi1; [dizi2]; [dizi3]; ...)", "d": "Verilen aralık ya da dizilerde birbirine karşılık gelen sayısal bileşenleri çarpar ve bu çarpımların toplamını verir"}, "SUMSQ": {"a": "(sayı1; [sayı2]; ...)", "d": "Bağımsız değişkenlerin karelerinin toplamını verir. Bağımsız değişkenler sayı, ad, dizi, ya da sayı içeren hücre başvuruları olabilir"}, "SUMX2MY2": {"a": "(dizi_x; dizi_y)", "d": "Birbirine karşılık gelen iki aralık ya da dizideki sayıların kareleri arasındaki farkı hesaplar ve sonra da bu farkların toplamını verir"}, "SUMX2PY2": {"a": "(dizi_x; dizi_y)", "d": "Birbirine karşılık gelen iki aralık ya da dizideki sayıların karelerinin toplamlarını hesaplar ve sonra da bu toplamların toplamını verir"}, "SUMXMY2": {"a": "(dizi_x; dizi_y)", "d": "Birbirine karşılık gelen iki aralık ya da dizideki değerlerin farklarını hesaplar ve sonra da bu farkların kareleri toplamını verir"}, "TAN": {"a": "(sayı)", "d": "<PERSON><PERSON> sayının tanjantını verir"}, "TANH": {"a": "(sayı)", "d": "Bir sayının hiperbolik tanjantını verir"}, "TRUNC": {"a": "(sayı; [sayı_raka<PERSON><PERSON>])", "d": "Bir sayıyı ondalık ya da kesir kısmını kaldırarak bir tamsayıya yuvarlar"}, "ADDRESS": {"a": "(satır_num; sütun_num; [mutlak_num]; [a1]; [say<PERSON>_metni])", "d": "<PERSON><PERSON> hü<PERSON> ba<PERSON><PERSON><PERSON><PERSON>, beli<PERSON><PERSON>n satır ve sütun numaraları verilmiş halde metin olarak oluşturur"}, "CHOOSE": {"a": "(dizin_sayısı; değer1; [değer2]; ...)", "d": "Bir dizin numarasını temel alan bir değerler listesinden gerçekleştirmek üzere bir değer ya da eylem seçer"}, "COLUMN": {"a": "([ba<PERSON><PERSON><PERSON>])", "d": "Başvurunun sütun say<PERSON>ını verir"}, "COLUMNS": {"a": "(dizi)", "d": "Bir dizideki ya da başvurudaki sütun sayısını verir"}, "FORMULATEXT": {"a": "(baş<PERSON>ru)", "d": "<PERSON><PERSON><PERSON><PERSON> bir dize olarak verir"}, "HLOOKUP": {"a": "(a<PERSON><PERSON>_<PERSON><PERSON><PERSON>; tablo_dizisi; satır_indis_sayısı; [aralık_bak])", "d": "Tablonun üst satırındaki değeri ya da değerler dizisini arar ve aynı sütunda belirtilen satırdan değeri verir"}, "HYPERLINK": {"a": "(bağ_konumu; [yakın_ad])", "d": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, sun<PERSON><PERSON> ağı ya da İnternet'te depolanmış olan bir belgeyi açmak için kısayol ya da atlama oluşturur"}, "INDEX": {"a": "(dizi; satır_sayısı; [sütun_sayısı]!başv; satır_sayısı; [sütun_sayısı]; [alan_sayısı])", "d": "Bir tablo ya da aralıktan bir değer ya da değere yapılan başvuruyu verir"}, "INDIRECT": {"a": "(başv_metni; [a1])", "d": "Bir metin dizesiyle beli<PERSON>ş başvuruyu verir"}, "LOOKUP": {"a": "(aranan_de<PERSON>er; aranan_vektör; [sonuç_vektör]!aranan_değer; dizi)", "d": "Tek-satırlı ya da tek-sütunlu bir aralıktan ya da bir diziden bir değer verir. Geriye dönük uyumluluk için sağlanmıştır"}, "MATCH": {"a": "(aranan_de<PERSON><PERSON>; aranan_dizi; [e<PERSON><PERSON><PERSON><PERSON><PERSON>_tür])", "d": "Belirli bir sırada belirtilen değerle eşleşen bir öğenin bir dizi içerisindeki göreceli konumunu verir"}, "OFFSET": {"a": "(başv; satırlar; sü<PERSON><PERSON>; [yüks<PERSON><PERSON>]; [g<PERSON><PERSON><PERSON>])", "d": "Bir hücre ya da hücreler aralığında belirtilen satır ve sütun sayısına karşılık gelen bir aralığa yapılan başvuruyu verir"}, "ROW": {"a": "([ba<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> satır numarasını verir"}, "ROWS": {"a": "(dizi)", "d": "Bir başvuru ya da dizideki satır sayısını verir"}, "TRANSPOSE": {"a": "(dizi)", "d": "Düşey bir hücreler aralığını yatay bir aralık olarak verir, ya da tam tersi"}, "UNIQUE": {"a": "(dizi; [by_col]; [exactly_once])", "d": " Bir Aralık veya dizideki benzersiz değerleri döndürür."}, "VLOOKUP": {"a": "(a<PERSON><PERSON>_de<PERSON><PERSON>; tablo_dizisi; sütun_indis_sayısı; [aralık_bak])", "d": "Bir tablonun en sol sütunundaki bir değeri arar ve daha sonra aynı satırda belirttiğiniz sütundan bir değer verir. Varsayılan olarak tablo artan sırada sıralanmalıdır"}, "XLOOKUP": {"a": "(arama_de<PERSON><PERSON>; arama_dizi<PERSON>; d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_dizi; [bulunam<PERSON>yorsa]; [e<PERSON><PERSON><PERSON><PERSON>rme_modu]; [arama_modu])", "d": "Eşleştirme bulmak için bir aralıkta veya dizide arama yapar ve ilgili öğeyi ikinci bir aralıkta ya da dizide döndürür. Varsayılan olarak tam eşleşme kullanılır"}, "CELL": {"a": "(bilgi_türü; [ba<PERSON><PERSON><PERSON>])", "d": "Hücrenin biçimlendirmesi, konumu ve içeriği hakkındaki bilgileri verir"}, "ERROR.TYPE": {"a": "(hat<PERSON>_<PERSON><PERSON><PERSON>)", "d": "Bir hata değerine karşılık gelen bir sayı verir."}, "ISBLANK": {"a": "(de<PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON> boş bir hü<PERSON>ye ba<PERSON><PERSON><PERSON>a bulunuyorsa DOĞRU verir"}, "ISERR": {"a": "(de<PERSON><PERSON>)", "d": "Değerin #YOK dışında bir hata olup olmadığını denetler ve DOĞRU ya da YANLIŞ döndürür"}, "ISERROR": {"a": "(de<PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> bir hata olup olmadığını denetler ve DOĞRU ya da YANLIŞ döndürür"}, "ISEVEN": {"a": "(sayı)", "d": "Sayı bir çift sayı ise DOĞRU döndürür"}, "ISFORMULA": {"a": "(baş<PERSON>ru)", "d": "Başvurunun formül içeren bir hücreye yapılıp yapılmadığını denetler ve DOĞRU ya da YANLIŞ değerini döndürür"}, "ISLOGICAL": {"a": "(de<PERSON><PERSON>)", "d": "Bir değerin mantıksal bir <PERSON> (DOĞRU veya YANLIŞ) olup olmadığını denetler ve DOĞRU veya YANLIŞ değerini döndürür"}, "ISNA": {"a": "(de<PERSON><PERSON>)", "d": "Değerin #YOK olup olmadığını denetler ve DOĞRU ya da YANLIŞ verir"}, "ISNONTEXT": {"a": "(de<PERSON><PERSON>)", "d": "Bir de<PERSON>erin metin olup o<PERSON>ığını denetler (boş hücreler metin değildir) ve metin değilse DOĞRU, metinse YANLIŞ döndürür"}, "ISNUMBER": {"a": "(de<PERSON><PERSON>)", "d": "Bir değerin sayı olup olmadığını denetler ve sayıysa DOĞRU, değilse YANLIŞ döndürür"}, "ISODD": {"a": "(sayı)", "d": "<PERSON>ı bir tek sayı ise DOĞRU döndürür"}, "ISREF": {"a": "(de<PERSON><PERSON>)", "d": "Bir değerin başvuru olup olmadığını denetler ve başvuruysa DOĞRU, değilse YANLIŞ döndürür"}, "ISTEXT": {"a": "(de<PERSON><PERSON>)", "d": "Bir değerin metin olup olmadığını denetler ve metinse DOĞRU, metin değilse YANLIŞ döndürür"}, "N": {"a": "(de<PERSON><PERSON>)", "d": "Bir sayıya dönüştürülmüş değeri verir. <PERSON><PERSON><PERSON>, tari<PERSON> seri numa<PERSON>, DOĞRU 1'e, bun<PERSON><PERSON>n dışındaki şeyler de 0 (sıfır)'a dönüştürülür"}, "NA": {"a": "()", "d": "#YOK hata değeri<PERSON> verir (kullanılabilir değer yok)"}, "SHEET": {"a": "([de<PERSON><PERSON>])", "d": "Başvurulan sayfanın sayfa numarasını döndürür"}, "SHEETS": {"a": "([ba<PERSON><PERSON><PERSON>])", "d": "Bir başvurudaki sayfa sayısını döndürür"}, "TYPE": {"a": "(de<PERSON><PERSON>)", "d": "Değerin veri türünü gösteren sayıyı verir: sayı = 1; metin = 2; man<PERSON><PERSON><PERSON><PERSON> = 4; hata de<PERSON><PERSON> = 16; dizi = 64; birleşik veri = 128"}, "AND": {"a": "(mantıksal1; [mantıksal2]; ...)", "d": "Tüm bağ<PERSON><PERSON><PERSON><PERSON>ken<PERSON>in DOĞRU olup olma<PERSON>ığı<PERSON> denetler, tümü DOĞRU ise DOĞRU döndürür"}, "FALSE": {"a": "()", "d": "YANLIŞ mantıksal değerini verir"}, "IF": {"a": "(mantı<PERSON><PERSON>_sınama; [eğ<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_de<PERSON><PERSON>]; [eğer_yan<PERSON><PERSON><PERSON><PERSON>_değer])", "d": "Belirttiğiniz koşul DOĞRU olarak hesaplanıyorsa bir değer, YANLIŞ olarak hesaplanıyorsa başka bir değer verir"}, "IFS": {"a": "(mantıksal_test; do<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>; ...)", "d": "Bir veya birden fazla koşulun karşılanıp karşılanmadığını denetler ve ilk DOĞRU koşula karşılık gelen bir değer döndürür"}, "IFERROR": {"a": "(de<PERSON><PERSON>; e<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_de<PERSON><PERSON>)", "d": "İfade hatalı olursa eğer_hatalıysa_de<PERSON>er, hatalı olmazsa ifadenin kendi değerini döndürür"}, "IFNA": {"a": "(de<PERSON><PERSON>; de<PERSON><PERSON>_eğer_yok)", "d": "İfade #YOK olarak çözümlenirse belirttiğiniz değeri dö<PERSON>ü<PERSON><PERSON>, aksi durumda ifadenin sonucunu döndürür"}, "NOT": {"a": "(mant<PERSON><PERSON><PERSON>)", "d": "Bağımsız değişkenin mantığını tersine çevirir: DOĞRU bir bağımsız değişken için YANLIŞ, YANLIŞ bir bağımsız değişken için DOĞRU verir"}, "OR": {"a": "(mantıksal1; [mantıksal2]; ...)", "d": "Bağımsız değişkenlerin DOĞRU olup olmadığını denetler ve DOĞRU veya YANLIŞ döndürür. Yalnızca bağımsız değişkenlerin tümü YANLIŞ ise YANLIŞ döndürür"}, "SWITCH": {"a": "(ifade; değer1; sonuç1; [var<PERSON><PERSON><PERSON>_veya_değer2]; [sonuç2]; ...)", "d": "İfadeyi bir değer listesine göre hesaplayarak ilk eşleşen değere karşılık gelen sonucu döndürür. Eşleşme yoksa isteğe bağlı varsayılan bir değer döndürülür"}, "TRUE": {"a": "()", "d": "Mantıksal DOĞRU'yu verir"}, "XOR": {"a": "(mantıksal1; [mantıksal2]; ...)", "d": "<PERSON>ü<PERSON> ba<PERSON><PERSON><PERSON><PERSON>z <PERSON>ğişkenlere mantıksal 'Dışlayıcı Veya' işlecini uygular ve sonucu döndürür"}, "TEXTBEFORE": {"a": "(metin, sın<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [<PERSON><PERSON><PERSON>_say<PERSON><PERSON><PERSON>], [eşleştirme_modu], [eş<PERSON>ştirme_sonu], [bulu<PERSON><PERSON><PERSON><PERSON>])", "d": "Karakterleri sınırlandırmadan önceki metni döndürür."}, "TEXTAFTER": {"a": "(metin, sın<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [<PERSON><PERSON><PERSON>_say<PERSON><PERSON><PERSON>], [eşleştirme_modu], [eş<PERSON>ştirme_sonu], [bulu<PERSON><PERSON><PERSON><PERSON>])", "d": "Karakterleri sınırlandırmadan sonraki metni döndürür."}, "TEXTSPLIT": {"a": "(metin, sütun_sınırlayıcı, [satır_sınırlayıc<PERSON>], [b<PERSON><PERSON><PERSON><PERSON>_yoksay], [e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_modu], [<PERSON><PERSON><PERSON><PERSON>_doldur])", "d": "Sınırlayıcıları kullanarak metni satırlara veya sütunlara böler."}, "WRAPROWS": {"a": "(ve<PERSON><PERSON><PERSON>, sarma_sayısı, [ş<PERSON><PERSON><PERSON>_doldur])", "d": "Belirt<PERSON>n sayıda de<PERSON>en sonra bir satır veya sütun vektörünü sarar."}, "VSTACK": {"a": "(dizi1, [dizi2], ...)", "d": "Dizileri tek bir dizide dikey olarak yığınlar."}, "HSTACK": {"a": "(dizi1, [dizi2], ...)", "d": "Dizileri tek bir dizide yatay olarak yığınlar."}, "CHOOSEROWS": {"a": "(dizi, row_num1, [row_num2], ...)", "d": "Bir diziden veya başvurudan satırları döndürür."}, "CHOOSECOLS": {"a": "(dizi, col_num1, [col_num2], ...)", "d": "Bir diziden veya ba<PERSON><PERSON>rudan sütunları döndürür."}, "TOCOL": {"a": "(dizi, [yoksay], [scan_by_column])", "d": "<PERSON><PERSON><PERSON> bir sütun o<PERSON>ak dö<PERSON>ürür."}, "TOROW": {"a": "(dizi, [yoksay], [scan_by_column])", "d": "<PERSON><PERSON><PERSON> bir satır o<PERSON>ak döndürür."}, "WRAPCOLS": {"a": "(ve<PERSON><PERSON><PERSON>, sarma_sayısı, [ş<PERSON><PERSON><PERSON>_doldur])", "d": "Belirt<PERSON>n sayıda de<PERSON>en sonra bir satır veya sütun vektörünü sarar."}, "TAKE": {"a": "(dizi, satırlar, [s<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> ba<PERSON>langıcından veya sonundan satırları veya sütunları döndürür."}, "DROP": {"a": "(dizi, satırlar, [s<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> ba<PERSON>langıcından veya sonundan satırları veya sütunları bırakır."}}
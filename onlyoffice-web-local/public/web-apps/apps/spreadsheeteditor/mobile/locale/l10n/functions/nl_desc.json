{"DATE": {"a": "(jaar; maand; dag)", "d": "Geeft het getal als resultaat dat de datum aangeeft in code voor de datum/tijd"}, "DATEDIF": {"a": "(begindatum; einddatum; eenheid)", "d": "<PERSON><PERSON>ent het aantal dagen, maanden of jaren tussen twee datums"}, "DATEVALUE": {"a": "(datum_tekst)", "d": "Zet de opgegeven datum in de vorm van tekst om in de code voor de datum/tijd"}, "DAY": {"a": "(serieel-getal)", "d": "<PERSON>ft als resultaat de dag van de <PERSON>, een getal tussen 1 en 31."}, "DAYS": {"a": "(einddatum; begindatum)", "d": "Geeft als resultaat het aantal dagen tussen twee datums"}, "DAYS360": {"a": "(begindatum; einddatum; [methode])", "d": "Berekent het aantal dagen tussen twee datums op basis van een jaar met 360 dagen (12 maanden van 30 dagen)"}, "EDATE": {"a": "(begindatum; aantal_maanden)", "d": "Zet de datum die het opgegeven aantal maanden voor of na de begindatum ligt, om in een serieel getal"}, "EOMONTH": {"a": "(begindatum; aantal_maanden)", "d": "<PERSON>et de laatste dag van de maand die een opgegeven aantal maanden voor of na de begindatum ligt, om in een serieel getal"}, "HOUR": {"a": "(serieel-getal)", "d": "Geeft als resultaat het aantal uren als een getal van 0 (00:00) tot 23 (23:00)."}, "ISOWEEKNUM": {"a": "(datum)", "d": "Geeft als resultaat het ISO-weeknummer van het jaar voor een bepaalde datum"}, "MINUTE": {"a": "(serieel-getal)", "d": "Geeft als resultaat het aantal minuten (een getal van 0 tot en met 59)."}, "MONTH": {"a": "(serieel-getal)", "d": "<PERSON>ft als resultaat de ma<PERSON>, een getal van 1 (januari) tot en met 12 (december)."}, "NETWORKDAYS": {"a": "(begindatum; einddatum; [vakantiedagen])", "d": "Geeft het aantal volledige werkdagen tussen twee datums"}, "NETWORKDAYS.INTL": {"a": "(begindatum; einddatum; [weekend]; [vakantiedagen])", "d": "Geeft het aantal volledige werkdagen tussen twee datums met aangepaste weekendparameters"}, "NOW": {"a": "()", "d": "Geeft als resultaat de huidige datum en tijd in de datum- en tijdnotatie."}, "SECOND": {"a": "(serieel-getal)", "d": "Geeft als resultaat het aantal seconden (een getal van 0 tot en met 59)."}, "TIME": {"a": "(uur; minuut; seconde)", "d": "Converteert u<PERSON>, minuten en seconden die als getallen zijn opgegeven naar seriële getallen in de tijdnotatie"}, "TIMEVALUE": {"a": "(tijd_tekst)", "d": "Converteert een tijd in tekstnotatie naar een serieel getal voor tijd, een getal van 0 (00:00:00) tot 0,999988426 (23:59:59). Pas na het opgeven van de formule een tijdnotatie toe op het getal"}, "TODAY": {"a": "()", "d": "Geeft als resultaat de huidige datum in de datumnotatie."}, "WEEKDAY": {"a": "(serieel-getal; [type_getal])", "d": "Geeft als resultaat een getal van 1 tot 7 dat de dag van de week van een datum aangeeft."}, "WEEKNUM": {"a": "(serieel_getal; [type_resultaat])", "d": "Zet een serieel getal om in een weeknummer"}, "WORKDAY": {"a": "(begindatum; aantal_dagen; [vakantiedagen])", "d": "Geeft het seriële getal van de datum voor of na een opgegeven aantal werkdagen"}, "WORKDAY.INTL": {"a": "(begindatum; dagen; [weekend]; [vakantiedagen])", "d": "Geeft het seriële getal van de datum voor of na een opgegeven aantal werkdagen, met aangep<PERSON>e weekendpar<PERSON><PERSON>"}, "YEAR": {"a": "(serieel-getal)", "d": "<PERSON>ft als resultaat het jaar van een datum, een geheel getal in het bereik 1900 - 9999."}, "YEARFRAC": {"a": "(begindatum; einddatum; [soort_jaar])", "d": "Berekent het gede<PERSON>te van het jaar, uitgedrukt in het aantal dagen tussen begindatum en einddatum"}, "BESSELI": {"a": "(x; n)", "d": "Berekent de gemodificeerde i-functie van Bessel In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Berekent de gemodificeerde j-functie van Bessel Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Berekent de gemodificeerde k-functie van Bessel Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Berekent de gemodificeerde y-functie van Bessel Yn(x)"}, "BIN2DEC": {"a": "(getal)", "d": "Converteert een binair getal naar een decimaal getal"}, "BIN2HEX": {"a": "(getal; [aantal_tekens])", "d": "Converteert een binair getal naar een hexadecimaal getal"}, "BIN2OCT": {"a": "(getal; [aantal_tekens])", "d": "Converteert een binair getal naar een octaal getal"}, "BITAND": {"a": "(getal1; getal2)", "d": "Geeft als resultaat een bitsgewijze 'En' van twee getallen"}, "BITLSHIFT": {"a": "(getal; verschuivingsaantal)", "d": "Geeft als resultaat een getal dat naar links is verschoven met <verschuivingsaantal> bits"}, "BITOR": {"a": "(getal1; getal2)", "d": "Geeft als resultaat een bitsgewijze 'Of' van twee getallen"}, "BITRSHIFT": {"a": "(getal; verschuivingsaantal)", "d": "Geeft als resultaat een getal dat naar rechts is verschoven met <verschuivingsaantal> bits"}, "BITXOR": {"a": "(getal1; getal2)", "d": "Geeft als resultaat een bitsgewij<PERSON> 'Exclusieve of' van twee getallen"}, "COMPLEX": {"a": "(re<PERSON><PERSON>_deel; imaginair_deel; [achter<PERSON><PERSON><PERSON>])", "d": "Converteert reële en imaginaire coëfficiënten naar complexe getallen"}, "CONVERT": {"a": "(getal; van_eenheid; naar_eenheid)", "d": "Converteert een getal in de ene maateenheid naar een getal in een andere maateenheid"}, "DEC2BIN": {"a": "(getal; [aantal_tekens])", "d": "Converteert een decimaal getal naar een binair getal"}, "DEC2HEX": {"a": "(getal; [aantal_tekens])", "d": "Converteert een decimaal getal naar een hexadecimaal getal"}, "DEC2OCT": {"a": "(getal; [aantal_tekens])", "d": "Converteert een decimaal getal naar een octaal getal"}, "DELTA": {"a": "(getal1; [getal2])", "d": "<PERSON><PERSON><PERSON> of twee getallen gelijk zijn"}, "ERF": {"a": "(on<PERSON><PERSON><PERSON>; [boveng<PERSON>s])", "d": "Geeft de foutfunctie weer"}, "ERF.PRECISE": {"a": "(X)", "d": "Geeft de foutfunctie als resultaat"}, "ERFC": {"a": "(x)", "d": "Geeft de bijbehorende foutfunctie"}, "ERFC.PRECISE": {"a": "(X)", "d": "Geeft de bijbehorende foutfunctie als resultaat"}, "GESTEP": {"a": "(getal; [drempel<PERSON><PERSON>e])", "d": "To<PERSON>t of een getal groter is dan de drem<PERSON>e"}, "HEX2BIN": {"a": "(getal; [aantal_tekens])", "d": "Converteert een hexadecimaal getal naar een binair getal"}, "HEX2DEC": {"a": "(getal)", "d": "Converteert een hexadecimaal getal naar een decimaal getal"}, "HEX2OCT": {"a": "(getal; [aantal_tekens])", "d": "Converteert een hexadecimaal getal naar een octaal getal"}, "IMABS": {"a": "(complex_getal)", "d": "Geeft de absolute waarde van een complex getal"}, "IMAGINARY": {"a": "(complex_getal)", "d": "Berekent de imaginaire coëfficiënt van een complex getal"}, "IMARGUMENT": {"a": "(complex_getal)", "d": "Berekent het argument theta, een hoek uitgedrukt in radialen"}, "IMCONJUGATE": {"a": "(complex_getal)", "d": "Berekent de complex toegevoegde van een complex getal"}, "IMCOS": {"a": "(complex_getal)", "d": "<PERSON><PERSON>ent de cosinus van een complex getal"}, "IMCOSH": {"a": "(igetal)", "d": "Geeft als resultaat de cosinus hyperbolicus van een complex getal"}, "IMCOT": {"a": "(igetal)", "d": "Geeft als resultaat de cotangens van een complex getal"}, "IMCSC": {"a": "(igetal)", "d": "Geeft als resultaat de cosecans van een complex getal"}, "IMCSCH": {"a": "(igetal)", "d": "Geeft als resultaat de cosecans hyperbolicus van een complex getal"}, "IMDIV": {"a": "(complex_getal1; complex_getal2)", "d": "<PERSON><PERSON><PERSON> het quotiënt van twee complexe getallen"}, "IMEXP": {"a": "(complex_getal)", "d": "Berekent de exponentiële waarde van een complex getal"}, "IMLN": {"a": "(complex_getal)", "d": "Berekent de natuurlijke logaritme van een complex getal"}, "IMLOG10": {"a": "(complex_getal)", "d": "Be<PERSON>ent de logaritme met grondtal 10 van een complex getal"}, "IMLOG2": {"a": "(complex_getal)", "d": "Berekent de logaritme met grondtal 2 van een complex getal"}, "IMPOWER": {"a": "(complex_getal; getal)", "d": "Verheft een complex getal tot een hele macht"}, "IMPRODUCT": {"a": "(complex_getal1; [complex_getal2]; ...)", "d": "Berekent het product van 1 tot 255 complexe getallen"}, "IMREAL": {"a": "(complex_getal)", "d": "Bepaalt de reële coëfficiënt van een complex getal"}, "IMSEC": {"a": "(igetal)", "d": "Geeft als resultaat de secans van een complex getal"}, "IMSECH": {"a": "(igetal)", "d": "Geeft als resultaat de secans hyperbolicus van een complex getal"}, "IMSIN": {"a": "(complex_getal)", "d": "Berekent de sinus van een complex getal"}, "IMSINH": {"a": "(igetal)", "d": "Geeft als resultaat de sinus hyperbolicus van een complex getal"}, "IMSQRT": {"a": "(complex_getal)", "d": "Berekent de vierkantswortel van een complex getal"}, "IMSUB": {"a": "(complex_getal1; complex_getal2)", "d": "Berekent het verschil tussen twee complexe getallen"}, "IMSUM": {"a": "(complex_getal1; [complex_getal2]; ...)", "d": "Geeft als resultaat de som van complexe getallen"}, "IMTAN": {"a": "(igetal)", "d": "Geeft als resultaat de tangens van een complex getal"}, "OCT2BIN": {"a": "(getal; [aantal_tekens])", "d": "Converteert een octaal getal naar een binair getal"}, "OCT2DEC": {"a": "(getal)", "d": "Converteert een octaal getal naar een decimaal getal"}, "OCT2HEX": {"a": "(getal; [aantal_tekens])", "d": "Converteert een octaal getal naar een hexadecimaal getal"}, "DAVERAGE": {"a": "(database; veld; criteria)", "d": "Berekent het gemid<PERSON><PERSON> van de waarden in een kolom of database die voldoen aan de opgegeven voorwaarden"}, "DCOUNT": {"a": "(database; veld; criteria)", "d": "<PERSON><PERSON> de cellen in de database die getallen bevatten in het veld (kolom) met records die voldoen aan de opgegeven voorwaarden"}, "DCOUNTA": {"a": "(database; veld; criteria)", "d": "Telt in de database de niet-lege cellen in het veld (kolom) met records die overeenkomen met de opgegeven voorwaarden"}, "DGET": {"a": "(database; veld; criteria)", "d": "Haalt één record op uit een database dat voldoet aan de gespecificeerde criteria"}, "DMAX": {"a": "(database; veld; criteria)", "d": "Geeft als resultaat de maximumwaarde in het veld (kolom) met records in de database die overeenkomen met de opgegeven voorwaarden"}, "DMIN": {"a": "(database; veld; criteria)", "d": "Geeft als resultaat de minimumwaarde in het veld (kolom) met records in de database die overeenkomen met de opgegeven voorwaarden"}, "DPRODUCT": {"a": "(database; veld; criteria)", "d": "Vermenigvuldigt de waarden in het veld (kolom) met records in de database die voldoen aan de opgegeven voorwaarden"}, "DSTDEV": {"a": "(database; veld; criteria)", "d": "Maakt een schatting van de standaarddeviatie die is gebaseerd op een steekproef onder geselecteerde databasegegevens"}, "DSTDEVP": {"a": "(database; veld; criteria)", "d": "<PERSON><PERSON>ent de standaarddeviatie die is gebaseerd op de hele populatie van geselecteerde databasegegevens"}, "DSUM": {"a": "(database; veld; criteria)", "d": "Telt de getallen op in het veld (kolom) met records in de database die voldoen aan de opgegeven voorwaarden"}, "DVAR": {"a": "(database; veld; criteria)", "d": "Maakt een schatting van de variantie die is gebaseerd op een steekproef onder geselecteerde databasegegevens"}, "DVARP": {"a": "(database; veld; criteria)", "d": "Berekent de variantie die is gebaseerd op de hele populatie van geselecteerde databasegegevens"}, "CHAR": {"a": "(getal)", "d": "Geeft als resultaat het teken dat hoort bij de opgegeven code voor de tekenset van uw computer"}, "CLEAN": {"a": "(tekst)", "d": "Verwijdert alle niet-afdrukbare tekens uit een tekst"}, "CODE": {"a": "(tekst)", "d": "Geeft als resultaat de numerieke code voor het eerste teken in een tekenreeks voor de tekenset die door uw computer wordt gebruikt"}, "CONCATENATE": {"a": "(tekst1; [tekst2]; ...)", "d": "Voegt verschillende tekenreeksen samen tot één tekenreeks"}, "CONCAT": {"a": "(tekst1; ...)", "d": "Voegt een lijst of bereik met teksttekenreeksen samen"}, "DOLLAR": {"a": "(getal; [decimalen])", "d": "Converteert een getal naar tekst op basis van de valutanotatie"}, "EXACT": {"a": "(tekst1; tekst2)", "d": "Controleert of twee tekenreeksen identiek zijn en geeft als resultaat WAAR of ONWAAR. Er wordt verschil gemaakt tussen hoofdletters en kleine letters"}, "FIND": {"a": "(zoeken_tekst; in_tekst; [begin_getal])", "d": "Geeft als resultaat de beginpositie van een tekenreeks binnen een andere tekenreeks (er wordt onderscheid gemaakt tussen hoofdletters en kleine letters)"}, "FINDB": {"a": "(zoeken_tekst; in_tekst; [begin_getal])", "d": "Wordt naar een tekenreeks binnen een andere tekenreeks gezocht en wordt als resultaat het nummer van de beginpositie van de eerste tekenreeks gegeven, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans."}, "FIXED": {"a": "(getal; [decimalen]; [geen-punten])", "d": "Rondt een getal af op het opgegeven aantal decimalen en geeft het resultaat weer als tekst met of zonder komma's"}, "LEFT": {"a": "(tekst; [aantal-tekens])", "d": "Geeft als resultaat het aantal tekens vanaf het begin van een tekenreeks"}, "LEFTB": {"a": "(tekst; [aantal-tekens])", "d": "Geeft het eerste teken of de eerste tekens in een tekenreeks als resultaat, op basis van het aantal bytes dat u opgeeft, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans"}, "LEN": {"a": "(tekst)", "d": "Geeft als resultaat het aantal tekens in een tekenreeks"}, "LENB": {"a": "(tekst)", "d": "Geeft als resultaat het aantal bytes dat is gebru<PERSON>t voor de tekens in een tekenreeks, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans"}, "LOWER": {"a": "(tekst)", "d": "Zet alle letters in een tekenreeks om in kleine letters"}, "MID": {"a": "(tekst; begin_getal; aantal-tekens)", "d": "Geeft als resultaat het aantal tekens in het midden van een tekenreeks, beginnend op een opgegeven positie en met een opgegeven lengte"}, "MIDB": {"a": "(tekst; begin_getal; aantal-tekens)", "d": "Geeft als resultaat een bepaald aantal tekens uit een tekenreeks, gerekend vanaf de opgegeven positie en op basis van het aantal opgegeven bytes, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans"}, "NUMBERVALUE": {"a": "(tekst; [decimaal_scheidingsteken]; [groep_scheidingsteken])", "d": "Converteert tekst naar getal, onafhan<PERSON><PERSON><PERSON> van landinstellingen"}, "PROPER": {"a": "(tekst)", "d": "<PERSON>et de eerste letter van een tekenreeks om in een hoofdletter en converteert alle andere letters naar kleine letters"}, "REPLACE": {"a": "(oud_tekst; begin_getal; aantal-tekens; nieuw_tekst)", "d": "Vervangt een deel van een tekenreeks door een andere tekenreeks"}, "REPLACEB": {"a": "(oud_tekst; begin_getal; aantal-tekens; nieuw_tekst)", "d": "Wordt een deel van een tekenreeks vervangen door een andere tekenreeks, op basis van het aantal bytes dat u opgeeft, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans"}, "REPT": {"a": "(tekst; aantal-malen)", "d": "<PERSON><PERSON><PERSON>t een tekst een aantal malen. Gebruik HERHALING om een cel een aantal keren te vullen met een tekenreeks"}, "RIGHT": {"a": "(tekst; [aantal-tekens])", "d": "Geeft als resultaat het opgegeven aantal tekens vanaf het einde van een tekenreeks"}, "RIGHTB": {"a": "(tekst; [aantal-tekens])", "d": "Geeft het laatste teken of de laatste tekens in een tekenreeks als resultaat, op basis van het aantal bytes dat u opgeeft, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans"}, "SEARCH": {"a": "(zoeken_tekst; in_tekst; [begin_getal])", "d": "Geeft als resultaat de positie van het teken, lezend van links naar rechts, waar een bepaald teken of een bepaalde tekenreeks de eerste keer wordt gevonden (zonder onderscheid tussen hoofdletters en kleine letters)"}, "SEARCHB": {"a": "(zoeken_tekst; in_tekst; [begin_getal])", "d": "Wordt naar een tekenreeks gezocht binnen een andere tekenreeks en wordt het nummer van de beginpositie van de eerste tekenreeks als resultaat gegeven, berekend vanaf het eerste teken van de tweede tekenreeks, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans"}, "SUBSTITUTE": {"a": "(tekst; oud_tekst; nieuw_tekst; [rang_getal])", "d": "<PERSON><PERSON><PERSON><PERSON> bestaande tekst door nieuwe tekst in een tekenreeks"}, "T": {"a": "(waarde)", "d": "Controleert of een waarde tekst is. Als dit het geval is, wordt de tekst als resultaat gegeven. Als dit niet het geval is, worden er dubbele aanhalingstekens (lege tekst) als resultaat gegeven"}, "TEXT": {"a": "(waarde; notatie_tekst)", "d": "Converteert een waarde naar tekst in een specifieke getalnotatie"}, "TEXTJOIN": {"a": "(scheidingsteken; leeg_negeren; tekst1; ...)", "d": "Voegt een lijst of bereik met teksttekenreeks<PERSON> samen met be<PERSON><PERSON> van een scheidingsteken"}, "TRIM": {"a": "(tekst)", "d": "Verwijdert de spaties uit een tekst, behalve de enkele spaties tussen woorden"}, "UNICHAR": {"a": "(getal)", "d": "Geeft als resultaat het Unicode-teken waarnaar wordt verwezen door de opgegeven numerieke waarde"}, "UNICODE": {"a": "(tekst)", "d": "Geeft als resultaat het getal (codepunt) dat overeen<PERSON><PERSON>t met het eerste teken van de tekst"}, "UPPER": {"a": "(tekst)", "d": "Zet een tekenreeks om in hoofdletters"}, "VALUE": {"a": "(tekst)", "d": "Converteert een tekenreeks die overeenkomt met een getal naar een getal"}, "AVEDEV": {"a": "(getal1; [getal2]; ...)", "d": "Berekent het gemiddelde van de absolute deviaties van gegevenspunten ten opzichte van hun gemiddelde waarde. De argumenten kunnen getallen zijn of namen, matrices of verwij<PERSON>en die getallen bevatten"}, "AVERAGE": {"a": "(getal1; [getal2]; ...)", "d": "Berekent het (rekenkundig) gemiddel<PERSON> van de argumenten. De argumenten kunnen getallen zijn of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten"}, "AVERAGEA": {"a": "(waarde1; [waarde2]; ...)", "d": "Berekent het (meetkundige) gemiddelde van de argumenten. Tekst en ONWAAR worden geëvalueerd als 0, WAAR wordt geëvalueerd als 1. Argumenten kunnen getallen, namen, matrices en verwijzingen zijn"}, "AVERAGEIF": {"a": "(bereik; criteria; [gemiddelde_bereik])", "d": "Zoe<PERSON> het (rekenkundige) gemiddelde voor de cellen die worden gespecificeerd door een gegeven voorwaarde of criterium"}, "AVERAGEIFS": {"a": "(gemiddelde_bereik; criteriumbereik; criteria; ...)", "d": "Zoe<PERSON> het (rekenkundige) gemiddelde voor de cellen die worden gespecificeerd door een gegeven set voorwaarden of criteria"}, "BETADIST": {"a": "(x; alfa; bèta; [A]; [B])", "d": "Berekent de cumulatieve bèta-kansdichtheidsfunctie"}, "BETAINV": {"a": "(kans; alfa; beta; [A]; [B])", "d": "Berekent de inverse van de cumulatieve bèta-kansdichtheidsfunctie (BETAVERD)"}, "BETA.DIST": {"a": "(x; alfa; beta; cumulatief; [A]; [B])", "d": "Berekent de bètakansverdelingsfunctie"}, "BETA.INV": {"a": "(kans; alfa; beta; [A]; [B])", "d": "Berekent de inverse van de cumulatieve bètakansdichtheidsfunctie (BETA.VERD)"}, "BINOMDIST": {"a": "(aantal-gunstig; experimenten; kans-gunstig; cumulatief)", "d": "Geeft als resultaat de binomiale verdeling"}, "BINOM.DIST": {"a": "(aantal-gunstig; experimenten; kans-gunstig; cumulatief)", "d": "Geeft als resultaat de binomiale verdeling"}, "BINOM.DIST.RANGE": {"a": "(proeven; kans_succes; getal_succes; [getal_succes2])", "d": "Geeft als resultaat de kans van een proefresultaat waarvoor een binomiale verdeling wordt gebruikt"}, "BINOM.INV": {"a": "(experimenten; kans-gunstig; alfa)", "d": "Berekent de kleinste waarde waarvoor de cumulatieve binomiale verdeling kleiner is dan of gelijk aan een criteriumwaarde"}, "CHIDIST": {"a": "(x; vri<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Be<PERSON><PERSON> de rechtszijdige kans van de chi-kwadraatverdeling"}, "CHIINV": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berek<PERSON> de inverse van de rechtszijdige kans van de chi-kwadraatverdeling"}, "CHITEST": {"a": "(waarnemingen; verwacht)", "d": "Geeft het resultaat van de onafhankelijkheidstoets: de waarde van de chi-kwadraatverdeling voor de toetsingsgrootheid en de ingestelde vrijheidsgraden"}, "CHISQ.DIST": {"a": "(x; vri<PERSON><PERSON><PERSON>n; cumulatief)", "d": "Berekent de linkszijdige kans van de chi-kwadraatverdeling"}, "CHISQ.DIST.RT": {"a": "(x; vri<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Be<PERSON><PERSON> de rechtszijdige kans van de chi-kwadraatverdeling"}, "CHISQ.INV": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berekent de inverse van de linkszijdige kans van de chi-kwadraatverdeling"}, "CHISQ.INV.RT": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berek<PERSON> de inverse van de rechtszijdige kans van de chi-kwadraatverdeling"}, "CHISQ.TEST": {"a": "(waarnemingen; verwacht)", "d": "Geeft het resultaat van de onafhankelijkheidstoets: de waarde van de chi-kwadraatverdeling voor de toetsingsgrootheid en de ingestelde vrijheidsgraden"}, "CONFIDENCE": {"a": "(alfa; stand<PERSON><PERSON><PERSON>; grootte)", "d": "<PERSON><PERSON><PERSON> de betrouwbaarhe<PERSON>interval van een gemiddelde waarde voor de elementen van een populatie, met een normale verdeling"}, "CONFIDENCE.NORM": {"a": "(alfa; stand<PERSON><PERSON><PERSON>; grootte)", "d": "Berekent het betrouwbaarheidsinterval van een gemiddelde waarde voor de elementen van een populatie met een normale verdeling"}, "CONFIDENCE.T": {"a": "(alfa; stand<PERSON><PERSON><PERSON>; grootte)", "d": "<PERSON><PERSON><PERSON> de betrouwbaar<PERSON><PERSON>interval van een gemiddelde waarde voor de elementen van een populatie, met be<PERSON><PERSON> van een Student T-verdeling"}, "CORREL": {"a": "(matrix1; matrix2)", "d": "Berekent de correlatiecoëfficiënt van twee gegevensverzamelingen"}, "COUNT": {"a": "(waarde1; [waarde2]; ...)", "d": "Telt het aantal cellen in een bereik dat getallen bevat"}, "COUNTA": {"a": "(waarde1; [waarde2]; ...)", "d": "Telt het aantal niet-lege cellen in een bereik"}, "COUNTBLANK": {"a": "(bereik)", "d": "Telt het aantal lege cellen in een bereik"}, "COUNTIF": {"a": "(bereik; criterium)", "d": "Telt het aantal niet-lege cellen in een bereik die voldoen aan het opgegeven criterium"}, "COUNTIFS": {"a": "(criteriumbereik; criteria; ...)", "d": "Telt het aantal cellen dat wordt gespecificeerd door een gegeven set voorwaarden of criteria"}, "COVAR": {"a": "(matrix1; matrix2)", "d": "<PERSON><PERSON>ent de covariantie, het gemid<PERSON><PERSON> van de producten van deviaties voor ieder paar gegevenspunten in twee gegevenssets"}, "COVARIANCE.P": {"a": "(matrix1; matrix2)", "d": "Berekent de covariantie van de populatie, het gemid<PERSON><PERSON> van de producten van deviaties voor ieder paar gegevenspunten in twee gegevenssets"}, "COVARIANCE.S": {"a": "(matrix1; matrix2)", "d": "Berekent de covariantie voor een steekproef, het gemid<PERSON><PERSON> van de producten van deviaties voor ieder paar gegevenspunten in twee gegevenssets"}, "CRITBINOM": {"a": "(experimenten; kans-gunstig; alfa)", "d": "Berekent de kleinste waarde waarvoor de cumulatieve binomiale verdeling kleiner is dan of gelijk aan een criteriumwaarde"}, "DEVSQ": {"a": "(getal1; [getal2]; ...)", "d": "Berek<PERSON> de som van de kwadraten van de deviaties van gegevenspunten ten opzichte van het gemiddelde van de steekproef"}, "EXPONDIST": {"a": "(x; lambda; cumulatief)", "d": "Geeft als resultaat de exponentiële verdeling"}, "EXPON.DIST": {"a": "(x; lambda; cumulatief)", "d": "Geeft als resultaat de exponentiële verdeling"}, "FDIST": {"a": "(x; vrijheidsgraden1; vrijheidsgraden2)", "d": "Geeft als resultaat de (rechtszijdige) F-verdeling (de graad van verscheidenheid) voor twee gegevensverzamelingen"}, "FINV": {"a": "(kans; vrijheidsgraden1; vrijheidsgraden2)", "d": "Berekent de inverse van de (rechtszijdige) F-verdeling: als p = F.VERDELING(x,...), is F.INVERSE(p,...) = x"}, "FTEST": {"a": "(matrix1; matrix2)", "d": "Geeft het resultaat van een F-toets, de tweezijdige kans dat de varianties in matrix1 en matrix2 niet significant verschillen"}, "F.DIST": {"a": "(x; vrijheidsgraden1; vrijheidsgraden2; cumulatief)", "d": "Geeft als resultaat de (linkszijdige) F-kansverdeling (de graad van verscheidenheid) voor twee gegevenssets"}, "F.DIST.RT": {"a": "(x; vrijheidsgraden1; vrijheidsgraden2)", "d": "Geeft als resultaat de (rechtszijdige) F-kansverdeling (de graad van verscheidenheid) voor twee gegevenssets"}, "F.INV": {"a": "(kans; vrijheidsgraden1; vrijheidsgraden2)", "d": "Berekent de inverse van de (linkszijdige) F-kansverdeling: als p = F.VERDELING(x,...), dan F.INVERSE(p,...) = x"}, "F.INV.RT": {"a": "(kans; vrijheidsgraden1; vrijheidsgraden2)", "d": "Berekent de inverse van de (rechtszijdige) F-kansverdeling: als p = F.VERD.RECHTS(x,...), dan F.INV.RECHTS(p,...) = x"}, "F.TEST": {"a": "(matrix1; matrix2)", "d": "Geeft het resultaat van een F-toets, de tweezijdige kans dat de varianties in matrix1 en matrix2 niet significant verschillen"}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON><PERSON> de Fisher-transformatie"}, "FISHERINV": {"a": "(y)", "d": "Berekent de inverse van de Fisher-transformatie: als y=FISHER(x), is FISHER.INV(y) = x"}, "FORECAST": {"a": "(x; known_ys; known_xs)", "d": "Berekent of voorspelt een toekomstige waarde langs een lineaire trend op basis van bestaande waarden"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "Retourneert de voorspelde waarde voor een specifieke doeldatum in de toekomst met de methode voor exponentieel vloeiend maken."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Hier<PERSON> wordt een betrouwbaarheidsinterval geretourneerd voor de voorspelde waarde op de opgegeven doeldatum."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Retourneert de lengte van het herhaalde patroon dat in applicatie wordt gedetecteerd voor de opgegeven tijdreeks."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Retourneert de aangevraagde statistische gegevens voor de voorspelling."}, "FORECAST.LINEAR": {"a": "(x; known_ys; known_xs)", "d": "Berekent of voorspelt aan de hand van bestaande waarden een toekomstige waarde volgens een lineaire trend"}, "FREQUENCY": {"a": "(gegevensmatrix; interval_verw)", "d": "<PERSON><PERSON><PERSON> hoe vaak waarden voorkomen in een waardebereik en geeft als resultaat een verticale matrix met getallen met een element meer dan interval_verw"}, "GAMMA": {"a": "(x)", "d": "Geeft als resultaat de waarde van de functie Gamma"}, "GAMMADIST": {"a": "(x; alfa; bèta; cumulatief)", "d": "Geeft als resultaat de gamma-verdeling"}, "GAMMA.DIST": {"a": "(x; alfa; beta; cumulatief)", "d": "Geeft als resultaat de gamma-verdeling"}, "GAMMAINV": {"a": "(kans; alfa; bèta)", "d": "Berekent de inverse van de cumulatieve gamma-verdeling: als p = GAMMA.VERD(x,...), is GAMMA.INV(p,...) = x"}, "GAMMA.INV": {"a": "(kans; alfa; beta)", "d": "Berekent de inverse van de cumulatieve gamma-verdeling: als p = GAMMA.VERD.N(x,...), is GAMMA.INV.N(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Berekent de natuurlijke logaritme van de gamma-functie"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Berekent de natuurlijke logaritme van de gammafunctie"}, "GAUSS": {"a": "(x)", "d": "Geeft als resultaat 0,5 minder dan de normale cumulatieve standaardverdeling"}, "GEOMEAN": {"a": "(getal1; [getal2]; ...)", "d": "Berekent het meetkundige gemiddelde van positieve numerieke gegevens in een matrix of bereik"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Geeft als resultaat getallen in een exponentiële groeitrend die overeenkomen met bekende gegevenspunten"}, "HARMEAN": {"a": "(getal1; [getal2]; ...)", "d": "Berekent het harmonische gemiddelde van een gegevensverzameling met positieve getallen: de reciproque waarde van het meetkundige gemiddelde van reciproque waarden"}, "HYPGEOM.DIST": {"a": "(steekproef-gunstig; grootte-steekproef; populatie-gunstig; grootte-populatie; cumulatief)", "d": "Geeft als resultaat de hypergeometrische verdeling"}, "HYPGEOMDIST": {"a": "(steekproef-gunstig; grootte-steekproef; populatie-gunstig; grootte-populatie)", "d": "Geeft als resultaat de hypergeometrische verdeling"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "<PERSON><PERSON><PERSON> het snijpunt van een lijn met de y-as aan de hand van een optimale regressielijn die wordt getrokken door de bekende x-waarden en y-waarden"}, "KURT": {"a": "(getal1; [getal2]; ...)", "d": "Berekent de kurtosis van een gegevensverzameling"}, "LARGE": {"a": "(matrix; k)", "d": "Berekent de op k-1 na grootste waarde in een gegevensbereik, bijvoorbeeld het vijfde grootste getal"}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Geeft als resultaat statistieken die een lineaire trend beschrijven en overeenkomen met bekende gegevenspunten. De lijn wordt berekend met de kleinste-kwadratenmethode"}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Geeft als resultaat statistieken die een exponentiële curve beschrijven die past bij de gegevenspunten"}, "LOGINV": {"a": "(kans; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Berekent de inverse van de logaritmische normale verdeling van x, waar<PERSON>j ln(x) normaal wordt verdeeld met de parameters Gemiddelde en Standaarddev"}, "LOGNORM.DIST": {"a": "(x; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>; cumulatief)", "d": "Geeft als resultaat de logaritmische normale verdeling van x, waarbij ln(x) normaal is verdeeld met de parameters Gemiddelde en Standaarddev"}, "LOGNORM.INV": {"a": "(kans; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Berekent de inverse van de logaritmische normale verdeling van x, waar<PERSON>j ln(x) normaal wordt verdeeld met de parameters Gemiddelde en Standaarddev"}, "LOGNORMDIST": {"a": "(x; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Geeft als resultaat de logaritmische normale verdeling van x, waar<PERSON>j ln(x) normaal wordt verdeeld met de parameters Gemiddelde en Standaarddev"}, "MAX": {"a": "(getal1; [getal2]; ...)", "d": "Geeft als resultaat de grootste waarde in een lijst met argumenten. Logische waarden en tekst worden genegeerd"}, "MAXA": {"a": "(waarde1; [waarde2]; ...)", "d": "Geeft als resultaat de grootste waarde in een verzameling waarden. Logische waarden en tekst worden niet genegeerd"}, "MAXIFS": {"a": "(maximumbereik; criteriabereik; criteria; ...)", "d": "Retourneert de maximumwaarde tussen cellen die wordt bepaald door een gegeven set voorwaarden of criteria"}, "MEDIAN": {"a": "(getal1; [getal2]; ...)", "d": "<PERSON><PERSON><PERSON> de mediaan (het getal in het midden van een set) van de gegeven getallen"}, "MIN": {"a": "(getal1; [getal2]; ...)", "d": "Geeft als resultaat het kleinste getal in een lijst met waarden. Logische waarden en tekst worden genegeerd"}, "MINA": {"a": "(waarde1; [waarde2]; ...)", "d": "Geeft als resultaat de kleinste waarde in een lijst met argumenten. Logische waarden en tekst worden niet genegeerd"}, "MINIFS": {"a": "(minimumbereik; criteriabereik; criteria; ...)", "d": "Retourneert de minimumwaarde tussen cellen die wordt bepaald door een gegeven set voorwaarden of criteria"}, "MODE": {"a": "(getal1; [getal2]; ...)", "d": "Geeft als resultaat de meest voorkomende (repeterende) waarde in een matrix of bereik met gegevens"}, "MODE.MULT": {"a": "(getal1; [getal2]; ...)", "d": "Berekent een verticale matrix van de vaakst voorkomende, of herhaalde waarden in een matrix of gegevensbereik. Voor een horizontale matrix gebruikt u =TRANSPONEREN(MODUS.VERM(getal1,getal2,...))"}, "MODE.SNGL": {"a": "(getal1; [getal2]; ...)", "d": "Geeft als resultaat de meest voorkomende (repeterende) waarde in een matrix of bereik met gegevens"}, "NEGBINOM.DIST": {"a": "(aantal-ongunstig; aantal-gunstig; kans-gunstig; cumulatief)", "d": "Geeft als resultaat de negatieve binomiaalverdeling, de kans dat er Aantal-ongunstig ongunstige uitkomsten zijn voor Aantal-gunstig gunstige uitkomsten, met een kans van Kans-gunstig op een gunstige uitkomst"}, "NEGBINOMDIST": {"a": "(aantal-ongunstig; aantal-gunstig; kans-gunstig)", "d": "Geeft als resultaat de negatieve binomiaalverdeling, de kans dat er Aantal-ongunstig ongunstige uitkomsten zijn voor Aantal-gunstig gunstige uitkomsten, met een kans van Kans-gunstig op een gunstige uitkomst"}, "NORM.DIST": {"a": "(x; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>; cumulatieve)", "d": "Resulteert in de normale verdeling voor het opgegeven gemiddelde en de standaarddeviatie"}, "NORMDIST": {"a": "(x; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>; cumulatief)", "d": "Geeft als resultaat de cumulatieve normale verdeling van het opgegeven gemiddelde en de standaarddeviatie"}, "NORM.INV": {"a": "(kans; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Berekent de inverse van de cumulatieve normale verdeling voor het gemiddelde en de standaarddeviatie die u hebt opgegeven"}, "NORMINV": {"a": "(kans; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Berekent de inverse van de cumulatieve normale verdeling voor het gemiddelde en de standaarddeviatie die u hebt opgegeven"}, "NORM.S.DIST": {"a": "(z; cumulatief)", "d": "Geeft als resultaat de normale standaardverdeling (heeft een gemiddelde van nul en een standaarddeviatie van één)"}, "NORMSDIST": {"a": "(z)", "d": "Geeft als resultaat de cumulatieve normale standaardverdeling (met een gemiddelde nul en een standaarddeviatie één)"}, "NORM.S.INV": {"a": "(kans)", "d": "Berekent de inverse van de cumulatieve normale standaardverdeling (met een gemiddelde nul en een standaarddeviatie één)"}, "NORMSINV": {"a": "(kans)", "d": "Berekent de inverse van de cumulatieve normale standaardverdeling (met een gemiddelde nul en een standaarddeviatie één)"}, "PEARSON": {"a": "(matrix1; matrix2)", "d": "Berekent de correlatiecoëfficië<PERSON> <PERSON>"}, "PERCENTILE": {"a": "(matrix; k)", "d": "Berekent het k-percentiel van waarden in een bereik"}, "PERCENTILE.EXC": {"a": "(matrix; k)", "d": "Geeft als resultaat het k-percentiel van waarden in een bereik, waar<PERSON>j k zich in het bereik 0..1, exclusief bevindt"}, "PERCENTILE.INC": {"a": "(matrix; k)", "d": "Geeft als resultaat het k-percentiel van waarden in een bereik, waar<PERSON>j k zich in het bereik 0..1, inclusief bevindt"}, "PERCENTRANK": {"a": "(matrix; x; [significantie])", "d": "Geeft als resultaat de positie, in procenten uitgedrukt, van een waarde in de rangorde van een gegevensbereik"}, "PERCENTRANK.EXC": {"a": "(matrix; x; [significantie])", "d": "Bepaalt de positie van een waarde in een gegevensset als een percentage van de gegevensset als een percentage (0..1, exclusief) van de gegevensset"}, "PERCENTRANK.INC": {"a": "(matrix; x; [significantie])", "d": "Bepaalt de positie van een waarde in een gegevensset als een percentage van de gegevensset als een percentage (0..1, inclusief) van de gegevensset"}, "PERMUT": {"a": "(getal; aantal-gekozen)", "d": "Berekent het aantal permutaties voor een gegeven aantal objecten dat uit het totale aantal objecten geselecteerd kan worden"}, "PERMUTATIONA": {"a": "(getal; aantal_gekozen)", "d": "Geeft als resultaat het aantal permutaties voor een opgegeven aantal objecten (met her<PERSON><PERSON>) dat kan worden geselecteerd in het totale aantal objecten"}, "PHI": {"a": "(x)", "d": "Geeft als resultaat de waarde van de dichtheidsfunctie voor de normale standaardverdeling"}, "POISSON": {"a": "(x; gemid<PERSON><PERSON>; cumulatief)", "d": "Geeft als resultaat de Poisson-verdeling"}, "POISSON.DIST": {"a": "(x; gemid<PERSON><PERSON>; cumulatief)", "d": "Geeft als resultaat de Poisson-verdeling"}, "PROB": {"a": "(x-bereik; kansbereik; ondergrens; [boveng<PERSON>s])", "d": "<PERSON><PERSON><PERSON> de kans dat waarden zich tussen twee grenzen bevinden of gelijk zijn aan een onderlimiet"}, "QUARTILE": {"a": "(matrix; kwartiel)", "d": "Berekent het kwartiel van een gegevensverzameling"}, "QUARTILE.INC": {"a": "(matrix; kwartiel)", "d": "Bepaalt het kwartiel van een gegevensset op basis van percentiele waarden van 0..1, inclusief"}, "QUARTILE.EXC": {"a": "(matrix; kwartiel)", "d": "Bepaalt het kwartiel van een gegevensset op basis van percentiele waarden van 0..1, exclusief"}, "RANK": {"a": "(getal; verw; [volgorde])", "d": "Berekent de rang van een getal in een lijst getallen: de grootte ten opzichte van andere waarden in de lijst"}, "RANK.AVG": {"a": "(getal; verw; [volgorde])", "d": "Berekent de rang van een getal in een lijst getallen: de grootte ten opzichte van andere waarden in de lijst; als meer dan één waarde de<PERSON>fde rang heeft, wordt de gemiddelde rang geretourneerd"}, "RANK.EQ": {"a": "(getal; verw; [volgorde])", "d": "Berekent de rang van een getal in een lijst getallen: de grootte ten opzichte van andere waarden in de lijst; als meerdere waarden dezelfde rang hebben, wordt de bovenste rang van die set met waarden geretourneerd"}, "RSQ": {"a": "(known_ys; known_xs)", "d": "Berekent R-kwadraat van een lineaire regressielijn door de ingevoerde gegevenspunten"}, "SKEW": {"a": "(getal1; [getal2]; ...)", "d": "Berek<PERSON> de mate van asymmetrie van een verdeling: een a<PERSON><PERSON>ing van de mate van asymmetrie van een verdeling rond het gemiddelde"}, "SKEW.P": {"a": "(getal1; [getal2]; ...)", "d": "Geeft als resultaat de scheefheid van een verdeling op basis van een populatie: een ken<PERSON><PERSON> van de mate van asymmetrie van een verdeling rondom het gemiddelde"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Berekent de richtingscoëfficiënt van een lineaire regressielijn door de ingevoerde gegevenspunten"}, "SMALL": {"a": "(matrix; k)", "d": "Geeft als resultaat de op k-1 na kleinste waarde in een gegevensbereik, bijvoorbeeld het vijfde kleinste getal"}, "STANDARDIZE": {"a": "(x; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Berekent een genormaliseerde waarde uit een verdeling die wordt gekenmerkt door een gemiddelde en standaarddeviatie"}, "STDEV": {"a": "(getal1; [getal2]; ...)", "d": "Maakt een schatting van de standaarddeviatie op basis van een steekproef (logische waarden en tekst in de steekproef worden genegeerd)"}, "STDEV.P": {"a": "(getal1; [getal2]; ...)", "d": "Berekent de standaarddeviatie op basis van de volledige populatie die als argumenten wordt gegeven (logische waarden en tekst worden genegeerd)"}, "STDEV.S": {"a": "(getal1; [getal2]; ...)", "d": "Maakt een schatting van de standaarddeviatie op basis van een steekproef (logische waarden en tekst in de steekproef worden genegeerd)"}, "STDEVA": {"a": "(waarde1; [waarde2]; ...)", "d": "Maakt een schatting van de standaarddeviatie op basis van een steekproef, met inbegrip van logische waarden en tekst. Tekst en de logische waarde ONWAAR krijgen de waarde 0, de logische waarde WAAR krijgt de waarde 1"}, "STDEVP": {"a": "(getal1; [getal2]; ...)", "d": "Berekent de standaarddeviatie op basis van de volledige populatie die als argumenten worden gegeven (logische waarden en tekst worden genegeerd)"}, "STDEVPA": {"a": "(waarde1; [waarde2]; ...)", "d": "Berekent de standaarddeviatie op basis van de volledige populatie, inclusief logische waarden en tekst. Tekst en de logische waarde ONWAAR krijgen de waarde 0, de logische waarde WAAR krijgt de waarde 1"}, "STEYX": {"a": "(known_ys; known_xs)", "d": "<PERSON><PERSON><PERSON> de standaardfout in de voorspelde y-waarde voor elke x in een regressie"}, "TDIST": {"a": "(x; vri<PERSON><PERSON><PERSON><PERSON><PERSON>; zij<PERSON>)", "d": "<PERSON><PERSON><PERSON> de Student T-verdeling"}, "TINV": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berekent de tweezijdige inverse van de Student T-verdeling"}, "T.DIST": {"a": "(x; vri<PERSON><PERSON><PERSON>n; cumulatief)", "d": "Deze eigenschap retourneert de linkszijdige Student T-verdeling"}, "T.DIST.2T": {"a": "(x; vri<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Berekent de tweezijdige Student T-verdeling"}, "T.DIST.RT": {"a": "(x; vri<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Berekent de rechtszijdige Student T-verdeling"}, "T.INV": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berekent de linkszijdige inverse van de Student T-verdeling"}, "T.INV.2T": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berekent de tweezijdige inverse van de Student T-verdeling"}, "T.TEST": {"a": "(matrix1; matrix2; zijden; type_getal)", "d": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON> T-toets"}, "TREND": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Geeft als resultaat getallen in een lineaire trend die overeenkomen met bekende gegevenspunten, be<PERSON><PERSON> met de kleinste-kwadratenmethode"}, "TRIMMEAN": {"a": "(matrix; percentage)", "d": "Berekent het gemiddelde van waarden in een gegevensverzameling, waarbij de extreme waarden in de berekening worden uitgesloten"}, "TTEST": {"a": "(matrix1; matrix2; zijden; type_getal)", "d": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON> T-toets"}, "VAR": {"a": "(getal1; [getal2]; ...)", "d": "Maakt een schatting van de variantie op basis van een steekproef (logische waarden en tekst in de steekproef worden genegeerd)"}, "VAR.P": {"a": "(getal1; [getal2]; ...)", "d": "Berekent de variantie op basis van de volledige populatie (logische waarden en tekst in de populatie worden genegeerd)"}, "VAR.S": {"a": "(getal1; [getal2]; ...)", "d": "Maakt een schatting van de variantie op basis van een steekproef (logische waarden en tekst in de steekproef worden genegeerd)"}, "VARA": {"a": "(waarde1; [waarde2]; ...)", "d": "Maakt een schatting van de variantie op basis van een steekproef, inclusief logische waarden en tekst. Tekst en de logische waarde ONWAAR krijgen de waarde 0, de logische waarde WAAR krijgt de waarde 1"}, "VARP": {"a": "(getal1; [getal2]; ...)", "d": "Berekent de variantie op basis van de volledige populatie (logische waarden en tekst in de populatie worden genegeerd)"}, "VARPA": {"a": "(waarde1; [waarde2]; ...)", "d": "Berekent de variantie op basis van de volledige populatie, inclusief logische waarden en tekst. Tekst en de logische waarde ONWAAR krijgen de waarde 0, de logische waarde WAAR krijgt de waarde 1"}, "WEIBULL": {"a": "(x; alfa; bèta; cumulatief)", "d": "Geeft als resultaa<PERSON> de <PERSON>-verdeling"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; cumulatief)", "d": "Geeft als resultaa<PERSON> de <PERSON>-verdeling"}, "Z.TEST": {"a": "(matrix; x; [sigma])", "d": "Berekent de eenzijdige P-waarde voor een Z-toets"}, "ZTEST": {"a": "(matrix; x; [sigma])", "d": "Berekent de eenzijdige P-waarde voor een Z-toets"}, "ACCRINT": {"a": "(uitgifte; eerste_rente; vervaldatum; rente; nominale_waarde; frequentie; [soort_jaar]; [berek.methode])", "d": "Berekent de samengestelde rente voor een waardepapier waarvan de rente periodiek wordt uitgekeerd"}, "ACCRINTM": {"a": "(uitgifte; vervaldatum; rente; nominale_waarde; [soort_jaar])", "d": "Berekent de samengestelde rente voor een waardepapier waarvan de rente op de vervaldatum wordt uitgekeerd"}, "AMORDEGRC": {"a": "(kosten; aankoopdatum; eerste_termijn; restwaarde; termijn; snelheid; [basis])", "d": "<PERSON><PERSON>ent de evenredig verdeelde lineaire afschrijving van activa over el<PERSON> boekhoudperiode."}, "AMORLINC": {"a": "(kosten; aankoopdatum; eerste_termijn; restwaarde; termijn; snelheid; [basis])", "d": "<PERSON><PERSON>ent de evenredig verdeelde lineaire afschrijving van activa over el<PERSON> boekhoudperiode."}, "COUPDAYBS": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Berekent het aantal dagen vanaf het begin van de couponperiode tot de stortingsdatum"}, "COUPDAYS": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Bepaalt het aantal dagen in de couponperiode waarin de stortingsdatum valt"}, "COUPDAYSNC": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Berekent het aantal dagen vanaf de stortingsdatum tot de volgende coupondatum"}, "COUPNCD": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Bepaalt de volgende coupondatum na de stortingsdatum"}, "COUPNUM": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Berekent het aantal coupons dat uitbetaald moet worden tussen de stortingsdatum en de vervaldatum"}, "COUPPCD": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Berekent de laatste coupondatum voor de stortingsdatum"}, "CUMIPMT": {"a": "(rente; aantal_termijnen; hw; begin_periode; einde_periode; type_getal)", "d": "Berekent de cumulatieve rente over een bepaalde periode"}, "CUMPRINC": {"a": "(rente; aantal_termijnen; hw; begin_periode; einde_periode; type_getal)", "d": "Berekent de cumulatieve terugbetaalde hoofdsom voor een lening over een bepaalde periode"}, "DB": {"a": "(kosten; restwaarde; duur; termijn; [maand])", "d": "<PERSON><PERSON><PERSON> de afschrijving van activa over een op<PERSON><PERSON><PERSON> termijn, met be<PERSON><PERSON> van de 'fixed declining balance'-methode"}, "DDB": {"a": "(kosten; restwaarde; duur; termijn; [factor])", "d": "<PERSON><PERSON><PERSON> de afschrijving van activa over een opgegeven termijn met de 'double declining balance'-methode of met een methode die u zelf bepaalt"}, "DISC": {"a": "(stortingsdatum; vervaldatum; prijs; aflossingsprijs; [soort_jaar])", "d": "Berekent het discontopercentage voor waardepapier"}, "DOLLARDE": {"a": "(breuk; noemer)", "d": "Converteert een prijs in euro's, uitgedrukt in een breuk, naar een prijs in euro's, uitgedrukt in een decimaal getal"}, "DOLLARFR": {"a": "(decimaal; noemer)", "d": "Converteert een prijs in euro's, uitgedrukt in een decimaal getal, naar een prijs in euro's, uitgedrukt in een breuk"}, "DURATION": {"a": "(stortingsdatum; vervaldatum; coupon; rendem; frequentie; [soort_jaar])", "d": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> van een waardepap<PERSON> met periodieke rentebetalingen"}, "EFFECT": {"a": "(nominale_rente; termijnen_per_jaar)", "d": "Berekent het jaarlijkse effectieve rentepercentage"}, "FV": {"a": "(rente; aantal-termijnen; bet; [hw]; [type_getal])", "d": "<PERSON><PERSON><PERSON> de toekomstige waarde van een investering, geb<PERSON><PERSON> op periodieke, constante betalingen en een constant rentepercentage"}, "FVSCHEDULE": {"a": "(hoofdsom; rente_waarden)", "d": "Berekent de toekomstige waarde van een aanvangshoofdsom nadat de samengestelde rente eraan is toegevoegd"}, "INTRATE": {"a": "(stortingsdatum; vervaldatum; investering; aflossingsprijs; [soort_jaar])", "d": "Berekent het rentepercentage voor een volgestort waardepapier"}, "IPMT": {"a": "(rente; termijn; aantal-termijnen; hw; [tw]; [type_getal])", "d": "Berekent de te betalen rente voor een investering over een bepaalde termijn, op basis van periodieke, constante betalingen en een constant rentepercentage"}, "IRR": {"a": "(waarden; [schatting])", "d": "Berekent de interne rentabiliteit voor een reeks cashflows"}, "ISPMT": {"a": "(rente; termijn; aantal-termijnen; hw)", "d": "Berekent de betaalde rente voor een bepaalde termijn van een investering"}, "MDURATION": {"a": "(stortingsdatum; vervaldatum; coupon; rendem; frequentie; [soort_jaar])", "d": "<PERSON><PERSON><PERSON> de aangepaste <PERSON>ley-duur van een waardepapier, aangenomen dat de nominale waarde 100 euro bedraagt"}, "MIRR": {"a": "(waarden; financieringsrente; herinvesteringsrente)", "d": "Berekent de interne rentabiliteit voor een serie periodieke cashflows, rekening ho<PERSON><PERSON> met zow<PERSON> beleggingskosten als de rente op het herinvesteren van geld"}, "NOMINAL": {"a": "(effectieve_rente; termijnen_per_jaar)", "d": "Berekent de jaarlijkse nominale rente"}, "NPER": {"a": "(rente; bet; hw; [tw]; [type_getal])", "d": "Berekent het aantal termijnen van een investering, op <PERSON> van periodieke, constante betalingen en een constant rentepercentage"}, "NPV": {"a": "(rente; waarde1; [waarde2]; ...)", "d": "Berekent de netto huidige waarde van een investering op basis van een discontopercentage en een reeks toekomstige betalingen (negatieve waarden) en inkomsten (positieve waarden)"}, "ODDFPRICE": {"a": "(stortingsdatum; vervaldatum; uitgifte; eerste_coupon; rente; rendem; aflossingsprijs; frequentie; [soort_jaar])", "d": "Bepaalt de prijs per 100 euro nominale waarde voor een waardepapier met een afwijkende eerste termijn"}, "ODDFYIELD": {"a": "(stortingsdatum; vervaldatum; uitgifte; eerste_coupon; rente; prijs; aflossingsprijs; frequentie; [soort_jaar])", "d": "<PERSON><PERSON><PERSON> het rendement voor een waardepapier met een afwijkende eerste termijn"}, "ODDLPRICE": {"a": "(stortingsdatum; vervaldatum; laatste_rente; rente; rendem; aflossingsprijs; frequentie; [soort_jaar])", "d": "Bepaalt de prijs per 100 euro nominale waarde voor een waardepapier met een afwijkende laatste termijn"}, "ODDLYIELD": {"a": "(stortingsdatum; vervaldatum; laatste_rente; rente; prijs; aflossingsprijs; frequentie; [soort_jaar])", "d": "<PERSON><PERSON><PERSON> het rendement van een waardepapier met een afwijkende laatste termijn"}, "PDURATION": {"a": "(percentage; hw; tw)", "d": "Geeft als resultaat het aantal perioden dat is vereist voor een investering om een opgegeven waarde te bereiken"}, "PMT": {"a": "(rente; aantal-termijnen; hw; [tw]; [type_getal])", "d": "Berekent de periodieke betaling voor een lening op basis van constante betalingen en een constant rentepercentage"}, "PPMT": {"a": "(rente; termijn; aantal-termijnen; hw; [tw]; [type_getal])", "d": "Berekent de afbetaling op de hoofdsom voor een gegeven investering, op basis van constante betalingen en een constant rentepercentage"}, "PRICE": {"a": "(stortingsdatum; vervaldatum; rente; rendem; aflossingsprijs; frequentie; [soort_jaar])", "d": "Bepaalt de prijs per 100 euro nominale waarde voor een waardepapier waarvan de rente periodiek wordt uitgekeerd"}, "PRICEDISC": {"a": "(stortingsdatum; vervaldatum; disc; aflossingsprijs; [soort_jaar])", "d": "Bepaalt de prijs per 100 euro nominale waarde voor een verdisconteerd waardepapier"}, "PRICEMAT": {"a": "(stortingsdatum; vervaldatum; uitgifte; rente; rendem; [soort_jaar])", "d": "Bepaalt de prijs per 100 euro nominale waarde voor een waardepapier waarvan de rente op de vervaldatum wordt uitgekeerd"}, "PV": {"a": "(rente; aantal-termijnen; bet; [tw]; [type_getal])", "d": "Berekent de huidige waarde van een investering: het totale bedrag dat een reeks toekomstige betalingen momenteel waard is"}, "RATE": {"a": "(aantal-termijnen; bet; hw; [tw]; [type_getal]; [schatting])", "d": "Berekent het periodieke rentepercentage voor een lening of een investering. Gebruik bijvoorbeeld 6%/4 voor kwartaalbetalingen met een rentepercentage van 6%"}, "RECEIVED": {"a": "(stortingsdatum; vervaldatum; investering; disc; [soort_jaar])", "d": "Berekent het bedrag dat op de vervaldatum wordt uitgekeerd voor volgestort waardepapier"}, "RRI": {"a": "(perioden; hw; tw)", "d": "Geeft als resultaat een equivalent rentepercentage voor de groei van een investering"}, "SLN": {"a": "(kosten; restwaarde; duur)", "d": "Berekent de lineaire afschrijving van activa over <PERSON><PERSON> bepaalde termijn"}, "SYD": {"a": "(kosten; restwaarde; duur; termijn)", "d": "<PERSON><PERSON><PERSON> de afschrijving van activa over een bepa<PERSON>e termijn met be<PERSON><PERSON>-Of-The-Years-Digit'-methode"}, "TBILLEQ": {"a": "(stortingsdatum; vervaldatum; disc)", "d": "Berekent het rendement op schatkistpapier op dezelfde manier waarop het rendement op obligaties wordt berekend"}, "TBILLPRICE": {"a": "(stortingsdatum; vervaldatum; disc)", "d": "Bepaalt de prijs per 100 euro nominale waarde voor schatkistpapier"}, "TBILLYIELD": {"a": "(stortingsdatum; vervaldatum; prijs)", "d": "<PERSON><PERSON><PERSON> het rendement van schatkistpapier"}, "VDB": {"a": "(kosten; restwaarde; duur; begin-periode; einde-periode; [factor]; [geen-omschakeling])", "d": "<PERSON><PERSON><PERSON> de afschrijving van activa over een opgegeven periode, ook delen van perioden, met be<PERSON><PERSON> van de 'variable declining balance'-methode of met een andere methode die u opgeeft"}, "XIRR": {"a": "(waarden; datums; [schatting])", "d": "Berekent de interne rentabiliteit voor een geplande serie van cashflows"}, "XNPV": {"a": "(rente; waarden; datums)", "d": "Berekent de netto huidige waarde van een geplande serie cashflows"}, "YIELD": {"a": "(stortingsdatum; vervaldatum; rente; prijs; aflossingsprijs; frequentie; [soort_jaar])", "d": "Berekent het rendement van een waardepapier waarvan de rente periodiek wordt uitgekeerd"}, "YIELDDISC": {"a": "(stortingsdatum; vervaldatum; prijs; aflossingsprijs; [soort_jaar])", "d": "Berekent het jaarlijkse rendement van verdisconteerd waardepapier, bij<PERSON><PERSON><PERSON><PERSON> schatkistpapier"}, "YIELDMAT": {"a": "(stortingsdatum; vervaldatum; uitgifte; rente; prijs; [soort_jaar])", "d": "Berekent het jaarlijkse rendement van waardepapier waarvan de rente op de vervaldatum wordt uitgekeerd"}, "ABS": {"a": "(getal)", "d": "Geeft als resultaat de absolute waarde van een getal. Dit is het getal zonder het teken"}, "ACOS": {"a": "(getal)", "d": "Geeft als resultaat de boogcosinus van een getal in radialen, in het bereik 0 tot pi. De boogcosinus is de hoek waarvan de cosinus Getal is"}, "ACOSH": {"a": "(getal)", "d": "Berekent de inverse cosinus hyperbolicus van een getal"}, "ACOT": {"a": "(getal)", "d": "Geeft als resultaat de boogcotangens van een getal in het bereik 0 tot Pi radialen."}, "ACOTH": {"a": "(getal)", "d": "Geeft als resultaat de inverse cotangens hyperbolicus van een getal"}, "AGGREGATE": {"a": "(functie_getal; opties; verw1; ...)", "d": "Geeft als resultaat een statistische waarde in een lijst of database"}, "ARABIC": {"a": "(tekst)", "d": "Converteert een Romeins cijfer naar een Arabisch cijfer"}, "ASC": {"a": "(tekst)", "d": "Voor talen met DBCS-tekensets (Double Byte Character Set). W<PERSON><PERSON>zigt de functie tekens met volledige breedte (dubbel-byte) in tekens met halve breedte (enkel-byte)"}, "ASIN": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de boogs<PERSON> van een getal, in het bereik -pi/2 tot pi/2"}, "ASINH": {"a": "(getal)", "d": "Berekent de inverse sinus hyperbolicus van een getal"}, "ATAN": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de boogtangens van een getal, in het bereik -pi/2 tot pi/2"}, "ATAN2": {"a": "(x_getal; y_getal)", "d": "Berekent de boogtangens van de x- en y-coördinaten in radialen, tussen -pi en pi, met -pi uitgesloten"}, "ATANH": {"a": "(getal)", "d": "Berekent de inverse tangens hyperbolicus van een getal"}, "BASE": {"a": "(getal; grondtal; [min_lengte])", "d": "Converteert een getal in een tekstweergave met het opgegeven grondtal (basis)"}, "CEILING": {"a": "(getal; significantie)", "d": "<PERSON>dt een getal naar boven af op het dichtstbijzijnde significante veelvoud"}, "CEILING.MATH": {"a": "(getal; [significantie]; [modus])", "d": "<PERSON>dt een getal naar boven af tot op het dichtstbijzijnde gehele getal of op het dichtstbijzijnde veelvoud van significantie"}, "CEILING.PRECISE": {"a": "(getal; [significantie])", "d": "Geeft als resultaat een getal dat is afgerond naar boven op het dichtstbijzijnde gehele getal of het dichtstbijzijnde meervoud van <PERSON> significantie"}, "COMBIN": {"a": "(getal; aantal-gekozen)", "d": "Geeft als resultaat het aantal combinaties voor een gegeven aantal objecten"}, "COMBINA": {"a": "(getal; aantal_gekozen)", "d": "Geeft als resultaat het aantal combinaties met herhalingen voor een opgegeven aantal items"}, "COS": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de cosinus van een getal"}, "COSH": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de cosinus hyperbolicus van een getal"}, "COT": {"a": "(getal)", "d": "Geeft als resultaat de cotangens van een hoek"}, "COTH": {"a": "(getal)", "d": "Geeft als resultaat de cotangens hyperbolicus van een getal"}, "CSC": {"a": "(getal)", "d": "Geeft als resultaat de cosecans van een hoek"}, "CSCH": {"a": "(getal)", "d": "Geeft als resultaat de cosecans hyperbolicus van een hoek"}, "DECIMAL": {"a": "(getal; grondtal)", "d": "Converteert een tekstweergave van een getal in een bepaalde basis naar een decimaal getal"}, "DEGREES": {"a": "(hoek)", "d": "Converteert radialen naar graden"}, "ECMA.CEILING": {"a": "(getal; significantie)", "d": "<PERSON>dt een getal naar boven af op het dichtstbijzijnde significante veelvoud"}, "EVEN": {"a": "(getal)", "d": "Rondt een positief getal naar boven af, en een negatief getal naar beneden, op het dichtstbijzijnde gehele even getal"}, "EXP": {"a": "(getal)", "d": "Verheft e tot de macht van het gegeven getal"}, "FACT": {"a": "(getal)", "d": "Berekent de faculteit van een getal. Dit is gelijk aan 1*2*3*...*Getal"}, "FACTDOUBLE": {"a": "(getal)", "d": "Berekent de dubbele faculteit van een getal"}, "FLOOR": {"a": "(getal; significantie)", "d": "<PERSON>dt een getal naar beneden af op het dichtstbijzijnde significante veelvoud"}, "FLOOR.PRECISE": {"a": "(getal; [significantie])", "d": "Geeft als resultaat een getal dat naar beneden is afgerond op het dichtstbijzijnde gehele getal of het dichtstbijzijnde significante veelvoud"}, "FLOOR.MATH": {"a": "(getal; [significantie]; [modus])", "d": "<PERSON>dt het getal naar beneden af op het dichtstbijzijnde gehele getal of op het dichtstbijzijnde veelvoud van significantie"}, "GCD": {"a": "(complex_getal1; [complex_getal2]; ...)", "d": "Geeft als resultaat de grootste algemene deler"}, "INT": {"a": "(getal)", "d": "<PERSON>dt een getal naar beneden af op het dichtstbijzijnde gehele getal"}, "ISO.CEILING": {"a": "(getal; significantie)", "d": "Geeft als resultaat een getal dat is afgerond naar boven op het dichtstbijzijnde gehele getal of het dichtstbijzijnde meervoud van de significantie. Het getal wordt afgerond ongeacht het teken van het getal. Als het getal of de significantie echter nul is, is nul het resultaat."}, "LCM": {"a": "(complex_getal1; [complex_getal2]; ...)", "d": "Berekent het kleinste gemene veelvoud"}, "LN": {"a": "(getal)", "d": "Berekent de natuurlijke logaritme van een getal"}, "LOG": {"a": "(getal; [grondtal])", "d": "<PERSON><PERSON><PERSON> de logaritme van een getal met het door u opgegeven grondtal"}, "LOG10": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de logaritme met grondtal 10 van een getal"}, "MDETERM": {"a": "(matrix)", "d": "Berekent de determinant van een matrix"}, "MINVERSE": {"a": "(matrix)", "d": "Berekent de inverse van een matrix die is opgeslagen in een matrix"}, "MMULT": {"a": "(matrix1; matrix2)", "d": "Berekent het product van twee matrices, een matrix met hetzelfde aantal rijen als matrix1 en hetzelfde aantal kolommen als matrix2"}, "MOD": {"a": "(getal; deler)", "d": "Geeft als resultaat het restgetal bij de deling van een getal door een deler"}, "MROUND": {"a": "(getal; veelvoud)", "d": "Geeft een getal dat is afgerond op het gewenste veelvoud"}, "MULTINOMIAL": {"a": "(complex_getal1; [complex_getal2]; ...)", "d": "Berekent de multinomiaal van een set getallen"}, "MUNIT": {"a": "(afmeting)", "d": "Geeft als resultaat een eenheidmatrix van de opgegeven afmeting"}, "ODD": {"a": "(getal)", "d": "Rondt de absolute waarde van een positief getal naar boven af, en een negatief getal naar beneden, op het dichtstbijzijnde oneven gehele getal"}, "PI": {"a": "()", "d": "Geeft als resultaat de waarde van de rekenkundige constante pi (3,14159265358979), nauw<PERSON><PERSON><PERSON> tot op 15 cijfers"}, "POWER": {"a": "(getal; macht)", "d": "Geeft als resultaat een getal verheven tot een macht"}, "PRODUCT": {"a": "(getal1; [getal2]; ...)", "d": "Vermenigvuldigt de getallen die zijn opgegeven als argumenten met <PERSON><PERSON><PERSON>"}, "QUOTIENT": {"a": "(teller; noemer)", "d": "Geeft de uitkomst van een deling in gehele getallen"}, "RADIANS": {"a": "(hoek)", "d": "Converteert graden naar radialen"}, "RAND": {"a": "()", "d": "Geeft als resultaat een will<PERSON> getal, geli<PERSON><PERSON><PERSON> verdeeld, dat groter is dan of gelijk is aan 0 en kleiner is dan 1 (wijzigt bij herberekening)"}, "RANDARRAY": {"a": "([rijen]; [kolommen]; [min]; [max]; [geheel])", "d": "Retour<PERSON>t een matrix met will<PERSON><PERSON><PERSON> getallen"}, "RANDBETWEEN": {"a": "(laagst; hoogst)", "d": "Geeft een willekeurig getal tussen de getallen die u hebt opgegeven"}, "ROMAN": {"a": "(getal; [type_getal])", "d": "Converteert Arabische cijfers naar Romeinse cijfers, als tekst"}, "ROUND": {"a": "(getal; aantal-decimalen)", "d": "Rondt een getal af op het opgegeven aantal decimalen"}, "ROUNDDOWN": {"a": "(getal; aantal-decimalen)", "d": "<PERSON>dt de absolute waarde van een getal naar beneden af"}, "ROUNDUP": {"a": "(getal; aantal-decimalen)", "d": "<PERSON>dt de absolute waarde van een getal naar boven af"}, "SEC": {"a": "(getal)", "d": "Geeft als resultaat de secans van een hoek"}, "SECH": {"a": "(getal)", "d": "Geeft als resultaat de secans hyperbolicus van een hoek"}, "SERIESSUM": {"a": "(x; n; m; coëff<PERSON>en)", "d": "Be<PERSON><PERSON> de som van een machtreeks die is gebaseerd op de formule"}, "SIGN": {"a": "(getal)", "d": "Geeft als resultaat het teken van een getal: 1 als het getal positief is, nul als het getal nul is en -1 als het getal negatief is"}, "SIN": {"a": "(getal)", "d": "Berekent de sinus van de opgegeven hoek"}, "SINH": {"a": "(getal)", "d": "<PERSON><PERSON>ent de sinus hyperbolicus van een getal"}, "SQRT": {"a": "(getal)", "d": "Be<PERSON>ent de vierkantswortel van een getal"}, "SQRTPI": {"a": "(getal)", "d": "Berekent de vierkantswortel van (getal * pi)"}, "SUBTOTAL": {"a": "(functie_getal; verw1; ...)", "d": "Be<PERSON>ent een subtotaal in een lijst of database"}, "SUM": {"a": "(getal1; [getal2]; ...)", "d": "Telt de getallen in een celbereik op"}, "SUMIF": {"a": "(bereik; criterium; [optelbereik])", "d": "Telt de cellen bij elkaar op die voldoen aan het criterium dat of de voorwaarde die u hebt ingesteld"}, "SUMIFS": {"a": "(optelbereik; criteriumbereik; criteria; ...)", "d": "Telt het aantal cellen op dat wordt gespecificeerd door een gegeven set voorwaarden of criteria"}, "SUMPRODUCT": {"a": "(matrix1; [matrix2]; [matrix3]; ...)", "d": "Geeft als resultaat de som van de producten van corresponderende bereiken of matrices"}, "SUMSQ": {"a": "(getal1; [getal2]; ...)", "d": "<PERSON><PERSON><PERSON> de som van de kwadraten van de argumenten. Dit kunnen getallen zijn of namen, matrices of verwi<PERSON>zingen naar cellen die getallen bevatten"}, "SUMX2MY2": {"a": "(x-matrix; y-matrix)", "d": "Telt de verschillen tussen de kwadraten van twee corresponderende bereiken of matrices op"}, "SUMX2PY2": {"a": "(x-matrix; y-matrix)", "d": "Geeft het somtotaal van de som van de kwadraten van waarden in twee corresponderende bereiken of matrices als resultaat"}, "SUMXMY2": {"a": "(x-matrix; y-matrix)", "d": "Telt de kwadraten van de verschillen tussen twee corresponderende bereiken of matrices op"}, "TAN": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de tangens van een getal"}, "TANH": {"a": "(getal)", "d": "<PERSON><PERSON>ent de tangens hyperbolicus van een getal"}, "TRUNC": {"a": "(getal; [aantal-decimalen])", "d": "Kapt een getal af tot een geheel getal door het decimale (of gebroken) gede<PERSON>te van het getal te verwijderen"}, "ADDRESS": {"a": "(rij_getal; kolom_getal; [abs_getal]; [A1]; [blad_tekst])", "d": "Geeft als resultaat een celverwi<PERSON>zing, in de vorm van tekst, gegeven bepaalde rij- en kolomnummers"}, "CHOOSE": {"a": "(index_getal; waarde1; [waarde2]; ...)", "d": "Kiest een waarde uit de lijst met waarden op basis van een indexnummer"}, "COLUMN": {"a": "([verw])", "d": "Geeft als resultaat het kolomnummer van een verwijzing"}, "COLUMNS": {"a": "(matrix)", "d": "Geeft als resultaat het aantal kolommen in een matrix of een verwijzing"}, "FORMULATEXT": {"a": "(ver<PERSON><PERSON><PERSON>)", "d": "Geeft als resultaat een formule als tekenreeks"}, "HLOOKUP": {"a": "(zoekwaarde; tabelmatrix; rij-index_getal; [bereik])", "d": "<PERSON><PERSON> in de bovenste rij van een tabel of matrix met waarden naar waarden en geeft als resultaat de waarde in dezelfde kolom uit een opgegeven kolom"}, "HYPERLINK": {"a": "(locatie_link; [mak<PERSON><PERSON><PERSON><PERSON>_naam])", "d": "Maakt een snelkoppeling of sprong die een document opent dat is opgeslagen op de harde schijf, op een netwerkserver of op internet"}, "INDEX": {"a": "(matrix; rij_getal; [kolom_getal]!verw; rij_getal; [kolom_getal]; [bereik_getal])", "d": "Geeft als resultaat een waarde of verwi<PERSON><PERSON> van de cel op het snijpunt van een bepaalde rij en kolom in een opgegeven bereik"}, "INDIRECT": {"a": "(verw_tekst; [A1])", "d": "Geeft als resultaat een verwijzing aangegeven door een tekstwaarde"}, "LOOKUP": {"a": "(zoekwa<PERSON>e; zoekvector; [resultaatvector]!zoekwaarde; matrix)", "d": "Zoekt een waarde uit een bereik met <PERSON><PERSON> rij of <PERSON><PERSON> kolom of uit een matrix. De functie is achterwaarts compatibel"}, "MATCH": {"a": "(zoekwaarde; zoeken-matrix; [criteriumtype_getal])", "d": "Geeft als resultaat de relatieve positie in een matrix van een item dat overeenkomt met een opgegeven waarde in een opgegeven volgorde"}, "OFFSET": {"a": "(verw; rijen; kolommen; [hoogte]; [breedte])", "d": "Geeft als resultaat een verwijzing naar een bereik dat een opgegeven aantal rijen en kolommen van een opgegeven verwijzing is"}, "ROW": {"a": "([verw])", "d": "Geeft als resultaat het rijnummer van een verwijzing"}, "ROWS": {"a": "(matrix)", "d": "Geeft als resultaat het aantal rijen in een verwijzing of matrix"}, "TRANSPOSE": {"a": "(matrix)", "d": "Converteert een verticaal celbereik naar een horizontaal bereik en omgekeerd"}, "UNIQUE": {"a": "(matrix; [per_kolom]; [exact_eenmaal])", "d": "Retourneert de unieke waarden uit een bereik of matrix."}, "VLOOKUP": {"a": "(zoekwaarde; tabelmatrix; kolomindex_getal; [ben<PERSON><PERSON>])", "d": "Zoe<PERSON> in de meest linkse kolom van een matrix naar een bepaalde waarde en geeft als resultaat de waarde uit dezelfde rij in een opgegeven kolom. Standaard moet de tabel in oplopende volgorde worden gesorteerd"}, "XLOOKUP": {"a": "(zoekwaarde; zoeken-matrix; matrix_retourneren; [indien_niet_gevonden]; [overeenkomstmodus]; [zoekmodus])", "d": "Zoekt in een bereik of matrix naar een overeenkomst en retourneert het bijbehorende item uit een tweede bereik of matrix. Standaard wordt een exacte overeenkomst gebruikt"}, "CELL": {"a": "(infotype; [verwij<PERSON>])", "d": "Geeft als resultaat informatie over de op<PERSON>, locatie of inhoud van een cel"}, "ERROR.TYPE": {"a": "(foutwaarde)", "d": "<PERSON>ft als resultaat een nummer dat overeen<PERSON>t met een fout<PERSON>e."}, "ISBLANK": {"a": "(waarde)", "d": "Controleert of een verwijzing naar een lege cel verwijst en geeft als resultaat WAAR of ONWAAR"}, "ISERR": {"a": "(waarde)", "d": "Controleert of een waarde een fout anders dan #N/B is en geeft als resultaat WAAR of ONWAAR"}, "ISERROR": {"a": "(waarde)", "d": "Controleert of een waarde een fout is en geeft als resultaat WAAR of ONWAAR"}, "ISEVEN": {"a": "(getal)", "d": "Resulteert in WAAR als het getal even is"}, "ISFORMULA": {"a": "(ver<PERSON><PERSON><PERSON>)", "d": "Controleert of een verwijzing verwijst naar een cel die een formule bevat en geeft WAAR of ONWAAR als resultaat"}, "ISLOGICAL": {"a": "(waarde)", "d": "Controleert of een waarde een logische waarde is (WAAR of ONWAAR), en geeft vervolgens WAAR of ONWAAR als resultaat"}, "ISNA": {"a": "(waarde)", "d": "Controleert of een waarde #N/B is en geeft als resultaat WAAR of ONWAAR"}, "ISNONTEXT": {"a": "(waarde)", "d": "Controleert of een waarde geen tekst is (lege cellen zijn geen tekst), en geeft WAAR of ONWAAR als resultaat"}, "ISNUMBER": {"a": "(waarde)", "d": "Controleert of een waarde een getal is en geeft als resultaat WAAR of ONWAAR"}, "ISODD": {"a": "(getal)", "d": "Resulteert in WAAR als het getal oneven is"}, "ISREF": {"a": "(waarde)", "d": "Controleert of een waarde een verwijzing is en geeft als resultaat WAAR of ONWAAR"}, "ISTEXT": {"a": "(waarde)", "d": "Controleert of een waarde tekst is en geeft als resultaat WAAR of ONWAAR"}, "N": {"a": "(waarde)", "d": "Converteert een waarde die geen getal is naar een getal, datums naar seri<PERSON><PERSON> getallen, WAAR naar 1 en overige waarden naar 0 (nul)"}, "NA": {"a": "()", "d": "Geeft als resultaat de foutwaarde #N/B (waarde niet be<PERSON>)"}, "SHEET": {"a": "([waarde])", "d": "Geeft als resultaat het bladnummer van het werkblad waarnaar wordt verwezen"}, "SHEETS": {"a": "([verwi<PERSON><PERSON>])", "d": "Geeft als resultaat het aantal bladen in een verwijzing"}, "TYPE": {"a": "(waarde)", "d": "Geeft als resultaat een geheel getal dat het gegevenstype van de waarde aangeeft: getal = 1; tekst = 2; logische waarde = 4, formule = 8; foutwaarde = 16; matrix = 64; samengestelde gegevens =128"}, "AND": {"a": "(logisch1; [logisch2]; ...)", "d": "Controleert of alle argumenten WAAR zijn. Als dit het geval is, wordt als resultaat WAAR gegeven"}, "FALSE": {"a": "()", "d": "Geeft als resultaat de logische waarde ONWAAR"}, "IF": {"a": "(logische-test; [waarde-als-waar]; [waarde-als-onwaar])", "d": "Controleert of er aan een voorwaarde is voldaan. Geeft een bepaalde waarde als resultaat als de opgegeven voorwaarde WAAR is en een andere waarde als deze ONWAAR is"}, "IFS": {"a": "(logische_test; waarde_indien_waar; ...)", "d": "Controleert of is voldaan aan een of meer voorwaarden en retourneert een waarde die overeenkomt met de eerste WAAR-voorwaarde"}, "IFERROR": {"a": "(waarde; waarde_indien_fout)", "d": "Geeft als resultaat de waarde_indien_fout als de expressie een fout is en anders de waarde van de expressie zelf"}, "IFNA": {"a": "(waarde; waarde_als_nvt)", "d": "Geeft als resultaat de waarde die u opgeeft als de expressie wordt omgezet in #N.v.t, anders wordt het resultaat van de expressie geretourneerd"}, "NOT": {"a": "(logisch)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> de waarde ONWAAR in WAAR, of WAAR in ONWAAR"}, "OR": {"a": "(logisch1; [logisch2]; ...)", "d": "Controleert of een van de argumenten WAAR is, en geeft als resultaat WAAR of ONWAAR. Geeft alleen ONWAAR als resultaat als alle argumenten ONWAAR zijn"}, "SWITCH": {"a": "(expressie; waarde1; resultaat1; [standaard_of_waarde2]; [resultaat2]; ...)", "d": "Evalueert een expressie met een lij<PERSON> met waarden en retourneert het resultaat dat overeenkomt met de eerste overeenkomende waarde. Als er geen overeenkomst is, wordt er een optionele standaardwaarde geretourneerd"}, "TRUE": {"a": "()", "d": "Geeft als resultaat de logische waarde WAAR"}, "XOR": {"a": "(logisch1; [logisch2]; ...)", "d": "Geeft als resultaat een logische 'Exclusieve of' van alle argumenten"}, "TEXTBEFORE": {"a": "(tekst, scheiding<PERSON>ken, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retourneert tekst voor scheidingstekens."}, "TEXTAFTER": {"a": "(tekst, scheiding<PERSON>ken, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retourneert tekst na scheidingstekens."}, "TEXTSPLIT": {"a": "(tekst, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Hi<PERSON><PERSON> wordt tekst gesplitst in rijen of kolommen met scheidingstekens."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": " Hier<PERSON> wordt een rij- of kolomvector achter een opgegeven aantal waarden verpakt."}, "VSTACK": {"a": "(matrix1, [matrix2], ...)", "d": "Stapelt matrices verticaal in één matrix."}, "HSTACK": {"a": "(matrix1, [matrix2], ...)", "d": "Stapelt matrices horizontaal in één matrix."}, "CHOOSEROWS": {"a": "(matrix, row_num1, [row_num2], ...)", "d": "Retourneert rijen uit een matrix of verwijzing."}, "CHOOSECOLS": {"a": "(matrix, col_num1, [col_num2], ...)", "d": "Retourneert kolommen uit een matrix of verwi<PERSON>zing."}, "TOCOL": {"a": "(matrix, [negeren], [scan_by_column])", "d": "Retourneert de matrix als één kolom."}, "TOROW": {"a": "(matrix, [negeren], [scan_by_column])", "d": "Retourneert de matrix als één rij."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": " Hier<PERSON> wordt een rij- of kolomvector achter een opgegeven aantal waarden verpakt."}, "TAKE": {"a": "(matrix, rijen, [kolommen])", "d": "Hier<PERSON> worden rijen of kolommen geretourneerd vanaf het begin of einde van de matrix."}, "DROP": {"a": "(matrix, rijen, [kolommen])", "d": "<PERSON><PERSON><PERSON><PERSON> of kolommen worden verwijderd uit het begin of einde van de matrix."}}
{"DATE": {"a": "(year; month; day)", "d": "<PERSON><PERSON><PERSON> về số thể hiện ngày tháng theo mã ngày-giờ"}, "DATEDIF": {"a": "(start-date; end-date; unit)", "d": "<PERSON><PERSON><PERSON> to<PERSON> s<PERSON> ng<PERSON>, tháng hoặc năm giữa hai ngày"}, "DATEVALUE": {"a": "(date_text)", "d": "Chuyển đổi ngày tháng dưới dạng văn bản sang số thể hiện ngày tháng theo mã ngày-giờ"}, "DAY": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> về ngà<PERSON> trong tháng, một số từ 1 đến 31."}, "DAYS": {"a": "(end_date; start_date)", "d": "Trả về số lượng ngày giữa hai ngày"}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "<PERSON><PERSON><PERSON> về số ngày giữa hai ngày trên cơ sở năm 360-ngày (12 tháng 30-ngày)"}, "EDATE": {"a": "(start_date; months)", "d": "Tr<PERSON> lại số tuần tự của ngày tháng là số chỉ báo các tháng trước hay sau ngày bắt đầu"}, "EOMONTH": {"a": "(start_date; months)", "d": "Tr<PERSON> lại số tuần tự của ngày cuối cùng của tháng trước hay sau số tháng chỉ định"}, "HOUR": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> về giờ dưới dạng số từ 0 (12:00 SA) đến 23 (11:00 CH)."}, "ISOWEEKNUM": {"a": "(date)", "d": "Trả về số tuần ISO trong năm đối với ngày cho trước"}, "MINUTE": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> v<PERSON>, m<PERSON><PERSON> số từ 0 đế<PERSON> 59."}, "MONTH": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> về tháng, m<PERSON>t số từ 1 (<PERSON><PERSON><PERSON>) tới 12 (<PERSON><PERSON><PERSON><PERSON>)."}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "<PERSON><PERSON><PERSON> lại số ngày làm việc đầy đủ giữa hai mốc ngày"}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "Trả về số ngày làm việc cả ngày giữa hai ngày với tham số ngày cuối tuần tùy chỉnh"}, "NOW": {"a": "()", "d": "<PERSON><PERSON><PERSON> về ngày tháng hiện tại theo dạng thức ngày tháng và thời gian."}, "SECOND": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> về <PERSON>, m<PERSON><PERSON> số từ 0 đế<PERSON> 59."}, "TIME": {"a": "(hour; minute; second)", "d": "Chuyển đổi giờ, <PERSON><PERSON><PERSON><PERSON>, gi<PERSON><PERSON> đã cho sang số tuần tự, đ<PERSON><PERSON><PERSON> định dạng theo dạng thức thời gian"}, "TIMEVALUE": {"a": "(time_text)", "d": "Chuyển đổi thời gian vă<PERSON> b<PERSON><PERSON> sang số tuần tự cho thời gian, một số từ 0 (12:00:00 SA) thành 0.999988426 (11:59:59 CH). <PERSON><PERSON><PERSON> dạng số bằng định dạng thời gian sau khi nhập công thức"}, "TODAY": {"a": "()", "d": "<PERSON><PERSON><PERSON> về ngày hiện thời theo dạng thức ngày tháng."}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "<PERSON><PERSON><PERSON> về một số từ 1 đến 7 thể hiện ngày trong tuần."}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "<PERSON><PERSON><PERSON> lại số tuần trong năm"}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "Trả lại số tuần tự của ngày trước hay sau một số ngày làm việc được chỉ ra"}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "Tr<PERSON> về số ngày tuần tự trước hoặc sau số ngày làm việc được chỉ định với tham số ngày cuối tuần tùy chỉnh"}, "YEAR": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> về năm củ<PERSON>, một số nguyên trong k<PERSON> 1900 - 9999."}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "<PERSON><PERSON><PERSON> lại phân số năm thể hiện số ngày nguyên giữa start_date và end_date"}, "BESSELI": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON> về hàm <PERSON> biến đổi In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON> v<PERSON> hàm <PERSON>(x)"}, "BESSELK": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON> về hàm <PERSON> biến đổi Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON> v<PERSON> hàm <PERSON>(x)"}, "BIN2DEC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> số nhị phân thành thập phân"}, "BIN2HEX": {"a": "(number; [places])", "d": "<PERSON><PERSON><PERSON><PERSON> số nhị phân thành thập lục phân"}, "BIN2OCT": {"a": "(number; [places])", "d": "Chuyển số nhị phân thành bát phân"}, "BITAND": {"a": "(number1; number2)", "d": "Trả về \"And\" theo bit của hai số"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "Trả về một số được dịch trái bởi các bit shift_amount"}, "BITOR": {"a": "(number1; number2)", "d": "Trả về \"Or\" theo bit của hai số"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "Trả về một số được dịch phải bởi các bit shift_amount "}, "BITXOR": {"a": "(number1; number2)", "d": "Trả về \"Exclusive Or\" theo bit của hai số"}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "<PERSON>y<PERSON><PERSON> đổi hệ số thực và ảo thành số phức"}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "Chuyển đổi một số từ hệ đo này sang hệ khác"}, "DEC2BIN": {"a": "(number; [places])", "d": "<PERSON><PERSON><PERSON><PERSON> số thập phân thành nhị phân"}, "DEC2HEX": {"a": "(number; [places])", "d": "<PERSON><PERSON><PERSON><PERSON> số thập phân thành thập lục phân"}, "DEC2OCT": {"a": "(number; [places])", "d": "<PERSON>y<PERSON><PERSON> số thập phân thành bát phân"}, "DELTA": {"a": "(number1; [number2])", "d": "<PERSON><PERSON>m tra hai số có bằng nhau không"}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "<PERSON><PERSON><PERSON> về hàm lỗi"}, "ERF.PRECISE": {"a": "(X)", "d": "Trả về hàm lỗi"}, "ERFC": {"a": "(x)", "d": "<PERSON><PERSON><PERSON> về hàm lỗi bổ sung"}, "ERFC.PRECISE": {"a": "(X)", "d": "Trả về hàm lỗi bù"}, "GESTEP": {"a": "(number; [step])", "d": "<PERSON><PERSON><PERSON> tra số có lớn hơn giá trị ngưỡng không"}, "HEX2BIN": {"a": "(number; [places])", "d": "<PERSON><PERSON><PERSON><PERSON> số thập lục phân thành nhị phân"}, "HEX2DEC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> số thập lục phân thành thập phân"}, "HEX2OCT": {"a": "(number; [places])", "d": "<PERSON><PERSON><PERSON><PERSON> số thập lục phân thành bát phân"}, "IMABS": {"a": "(inumber)", "d": "Trả về giá trị tuyệt đối của số phức"}, "IMAGINARY": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về hệ số ảo của số phức"}, "IMARGUMENT": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về tham đối q, g<PERSON><PERSON> theo r<PERSON>ian"}, "IMCONJUGATE": {"a": "(inumber)", "d": "<PERSON><PERSON>ả về số liên hợp của số phức"}, "IMCOS": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về c<PERSON>sin của số phức"}, "IMCOSH": {"a": "(inumber)", "d": "Trả về hàm cos hyperbol của số phức"}, "IMCOT": {"a": "(number)", "d": "Trả về hàm cotang của một số phức"}, "IMCSC": {"a": "(number)", "d": "Trả về hàm cosec của một số phức"}, "IMCSCH": {"a": "(number)", "d": "Trả về hàm cosec hyperbol của một số phức"}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "<PERSON><PERSON><PERSON> về thương số của hai số phức"}, "IMEXP": {"a": "(inumber)", "d": "<PERSON><PERSON>ả về số mũ của số phức"}, "IMLN": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về lô-ga-rít tự nhiên của số phức"}, "IMLOG10": {"a": "(inumber)", "d": "<PERSON><PERSON>ả về lô-ga-r<PERSON><PERSON> c<PERSON> số 10 của số phức"}, "IMLOG2": {"a": "(inumber)", "d": "<PERSON><PERSON>ả về lô-ga-r<PERSON><PERSON> c<PERSON> số 2 của số phức"}, "IMPOWER": {"a": "(inumber; number)", "d": "<PERSON><PERSON><PERSON> về số phức lũy thừa nguyên"}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "<PERSON><PERSON><PERSON> về tích của 1 đến 255 số phức"}, "IMREAL": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về hệ số thực của số phức"}, "IMSEC": {"a": "(number)", "d": "Trả về hàm sec của một số phức"}, "IMSECH": {"a": "(number)", "d": "Trả về hàm sec hyperbol của một số phức"}, "IMSIN": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về sin của số phức"}, "IMSINH": {"a": "(inumber)", "d": "Trả về hàm sin hyperbol của số phức"}, "IMSQRT": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về căn bậc hai của số phức"}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "<PERSON><PERSON><PERSON> về độ chênh lệch giữa hai số phức"}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "<PERSON><PERSON>ả về tổng của các số phức"}, "IMTAN": {"a": "(number)", "d": "Trả về hàm tang của một số phức"}, "OCT2BIN": {"a": "(number; [places])", "d": "Chuyển số bát phân thành nhị phân"}, "OCT2DEC": {"a": "(number)", "d": "Chuyển số bát phân thành thập phân"}, "OCT2HEX": {"a": "(number; [places])", "d": "Chuyển số bát phân thành thập lục phân"}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "Trung bình giá trị của cột trong danh sách hoặc trong cơ sở dữ liệu thoả mãn điều kiện chỉ định"}, "DCOUNT": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON> số ô chứa giá trị số trong trường (cột) của các bản ghi thuộc cơ sở dữ liệu thoả mãn các điều kiện chỉ định"}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON> số ô không trắng trong trường (cột) của các bản ghi trong cơ sở dữ liệu trùng khớp với điều kiện chỉ định"}, "DGET": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON><PERSON> xuất từ cơ sở dữ liệu một bản ghi trùng khớp với các điều kiện chỉ định"}, "DMAX": {"a": "(database; field; criteria)", "d": "Tr<PERSON> về giá trị lớn nhất trong trườ<PERSON> (cột) của các bản ghi thuộc cơ sở dữ liệu thoả mãn điều kiện chỉ định"}, "DMIN": {"a": "(database; field; criteria)", "d": "Tr<PERSON> về giá trị nhỏ nhất trong trường (cột) của các bản ghi thuộc cơ sở dữ liệu thoả mãn điều kiện chỉ định"}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "Nhân giá trị trong trường (cột) của các bản ghi trong cơ sở dữ liệu trùng khớp với điều kiện chỉ định"}, "DSTDEV": {"a": "(database; field; criteria)", "d": "Ước lượng độ lệch tiêu chuẩn dựa trên mẫu từ các mục trong cơ sở dữ liệu"}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON> toán độ lệch tiêu chuẩn dựa trên tập toàn bộ của các mục trong cơ sở dữ liệu đã chọn"}, "DSUM": {"a": "(database; field; criteria)", "d": "<PERSON>hê<PERSON> số vào trườ<PERSON> (cột) của các bản ghi thuộc cơ sở dữ liệu thoả mãn điều kiện chỉ định"}, "DVAR": {"a": "(database; field; criteria)", "d": "Ước lượng phương sai dựa trên mẫu từ các mục của cơ sở dữ liệu lựa chọn"}, "DVARP": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON>h toán dung sai dựa trên tập toàn bộ của các mục trong cơ sở dữ liệu đã chọn"}, "CHAR": {"a": "(number)", "d": "Trả về ký tự xác định bởi số hiệu mã từ tập ký tự trên máy tính"}, "CLEAN": {"a": "(text)", "d": "Loại bỏ mọi ký tự không in trong văn bản"}, "CODE": {"a": "(text)", "d": "Tr<PERSON> về mã số của ký tự đầu tiên trong văn bản, theo tập ký tự sử dụng trong máy"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "<PERSON><PERSON><PERSON> kết vài xâu văn bản vào một xâu văn bản"}, "CONCAT": {"a": "(text1; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> danh sách hoặc phạm vi các chuỗi văn bản"}, "DOLLAR": {"a": "(number; [decimals])", "d": "Chuyển đổi số sang văn bản, sử dụng định dạng tiền tệ"}, "EXACT": {"a": "(text1; text2)", "d": "Ki<PERSON>m tra hai xâu văn bản có chính xác giố<PERSON>u, và trả về ĐÚNG hoặc SAI. Hàm EXACT có phân biệt chữ hoa, chữ thường"}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "Trả về vị trí bắt đầu của một xâu văn bản nằm trong xâu văn bản khác. Hàm FIND có phân biệt chữ hoa, chữ thường"}, "FINDB": {"a": "(find_text; within_text; [start_num])", "d": "Định vị một chuỗi văn bản nằm trong chuỗi văn bản thứ hai và trả về số của vị trí bắt đầu của chuỗi văn bản thứ nhất tính từ ký tự thứ nhất của chuỗi văn bản thứ hai, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc"}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "<PERSON><PERSON><PERSON> tròn số theo phần thập phân chỉ định và trả về kết quả dạng văn bản có dấu hàng đơn vị hoặc không"}, "LEFT": {"a": "(text; [num_chars])", "d": "Tr<PERSON> về số ký tự xác định từ vị trí bắt đầu của xâu văn bản"}, "LEFTB": {"a": "(text; [num_chars])", "d": "Trả về một hoặc nhiều ký tự đầu tiên trong một chuỗi, dựa vào số byte mà bạn chỉ định, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc"}, "LEN": {"a": "(text)", "d": "Trả về số lượng ký tự trong xâu văn bản"}, "LENB": {"a": "(text)", "d": "Trả về số byte dùng để biểu thị các ký tự trong một chuỗi văn bản, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc"}, "LOWER": {"a": "(text)", "d": "Chuyển đổi mọi chữ cái trong xâu văn bản sang chữ thường"}, "MID": {"a": "(text; start_num; num_chars)", "d": "Tr<PERSON> về các ký tự ở giữa xâu văn bản, với vị trí bắt đầu và độ dài chỉ định"}, "MIDB": {"a": "(text; start_num; num_chars)", "d": "Trả về một số lượng ký tự cụ thể từ một chuỗi văn bản, bắt đầu từ vị trí do bạn chỉ định, dựa vào số lượng byte do bạn chỉ định, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc"}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "Chuyển đổi văn bản sang con số độc lập miền địa phương"}, "PROPER": {"a": "(text)", "d": "Chuyển đổi chuỗi văn bản sang dạng chữ thích hợp; chữ cái đầu tiên của từ là chữ hoa, mọi chữ cái khác là chữ thường"}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Thay thế một phần của xâu văn bản bằng xâu văn bản khác"}, "REPLACEB": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Thay thế một phần của chuỗi văn bản, dựa vào số byte do bạn chỉ định, bằng một chuỗi văn bản khác, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc"}, "REPT": {"a": "(text; number_times)", "d": "Lặp lại văn bản theo số lần chỉ định. Sử dụng hàm REPT để điền xâu văn bản nhiều lần vào ô"}, "RIGHT": {"a": "(text; [num_chars])", "d": "Trả về số ký tự xác định từ vị trí cuối của xâu văn bản"}, "RIGHTB": {"a": "(text; [num_chars])", "d": "Trả về một hoặc nhiều ký tự cuối cùng trong một chuỗi, dựa vào số byte mà bạn chỉ định, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc"}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "Tr<PERSON> về số ký tự tại vị trí xuất hiện đầu tiên của ký tự hoặc xâu văn bản cho trướ<PERSON>, t<PERSON>h từ trái qua phải (không phân biệt chữ hoa, chữ thường)"}, "SEARCHB": {"a": "(find_text; within_text; [start_num])", "d": "Trả về một chuỗi văn bản bên trong chuỗi văn bản thứ hai và trả về số vị trí bắt đầu của chuỗi văn bản thứ nhất từ ký tự đầu tiên của chuỗi văn bản thứ hai, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc"}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "Thay thế văn bản hiện thời bằng văn bản mới trong xâu văn bản"}, "T": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra một giá trị có phải là văn bản, và trả về văn bản nếu đúng, hoặc cặp dấu nh<PERSON><PERSON> k<PERSON> (văn bản rỗng) nếu sai"}, "TEXT": {"a": "(gi<PERSON>_trị; định_dạng_văn_bản)", "d": "Chuyển đổi một giá trị sang văn bản theo một định dạng số cụ thể"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> danh sách hoặc phạm vi các chuỗi văn bản bằng dấu tách"}, "TRIM": {"a": "(text)", "d": "Loại bỏ mọi dấu cách trong xâu văn bản ngoại trừ dấu cách đơn gi<PERSON>a các từ"}, "UNICHAR": {"a": "(number)", "d": "Trả về ký tự Unicode được tham chiếu bởi giá trị số cho trước"}, "UNICODE": {"a": "(text)", "d": "Trả về con số (điểm mã) tương ứng với ký tự đầu tiên của đoạn văn bản"}, "UPPER": {"a": "(text)", "d": "Chuyển đổi xâu văn bản sang chữ hoa"}, "VALUE": {"a": "(text)", "d": "Chuyển đổi xâu văn bản thể hiện số thành số"}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "Trả về trung bình độ lệch tuyệt đối giữa các điểm dữ liệu với giá trị trung bình của chúng. Tham đối có thể là số hoặc tên, m<PERSON><PERSON>, tham chiếu chứa số"}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về giá trị trung bình (trung bình số học) của các tham đối, chún<PERSON> có thể là các số, t<PERSON><PERSON>, mảng hoặc các tham chiếu có chứa số"}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "Tr<PERSON> về trung bình (trung bình số học) các tham đối của nó, văn bản và SAI được coi là 0; Đ<PERSON><PERSON> được coi là 1. Tham đối có thể là số, t<PERSON><PERSON>, m<PERSON><PERSON>, hoặc tham chiếu"}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "Tìm trung bình(trung bình số học) cho ô được chỉ ra bởi điều kiện hay chỉ tiêu cho trước"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": "Tìm trung bình(trung bình số học) cho các ô được xác định bởi tập cho trước các điều kiện hoặc tiêu chí"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "<PERSON><PERSON><PERSON> về hàm mật độ xác suất bê-ta lũy tích"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Tr<PERSON> về giá trị đảo của hàm phân bố tích lũy bê-ta (BETADIST)"}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "<PERSON><PERSON>ả về hàm phân phối xác su<PERSON> beta"}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o của hàm mật độ phân phối beta tích lũy (BETA.DIST)"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "<PERSON><PERSON><PERSON> về thời hạn riêng của nhị thức phân bố xác suất"}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "<PERSON><PERSON><PERSON> về thời hạn riêng của nhị thức phân bố xác suất"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "Trả về xác suất của kết quả thử sử dụng phân phối nhị thức"}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "Tr<PERSON> về giá trị nhỏ nhất có nhị thức phân bố tích lũy lớn hơn hoặc bằng giá trị tiêu chuẩn"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "<PERSON><PERSON>ả về xác suất một phía của phân bố chi-bình-phương"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "Trả về giá trị đảo xác suất một phía của phân bố chi-bình phương"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "Tr<PERSON> về kiểm tra tính độc lập: gi<PERSON> trị từ phân bố thống kê chi-bình-phương và bậc tự do tương ứng"}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "<PERSON><PERSON><PERSON> về xác suất phần dư bên trái của phân phối chi-bình phư<PERSON>ng"}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "Trả về xác suất nhánh phải của phân bố chi bình phương"}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o của x<PERSON>c suất phần dư bên trái của phân phối chi-bình phương"}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o của x<PERSON>c suất phần dư bên phải của phân phối chi-bình phương"}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "Tr<PERSON> về <PERSON>ể<PERSON> định tính độc lập: gi<PERSON> trị từ phân bố thống kê chi-bình-phương và bậc tự do tương <PERSON>ng"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "<PERSON>r<PERSON> về khoảng tin cậy của trung bình tổng thể, sử dụng phân bố bình thường"}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "<PERSON><PERSON><PERSON> về khoảng tin cậy cho trung bình của tập hợp, sử dụng phân ph<PERSON>i thông thường"}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "<PERSON><PERSON><PERSON> về khoảng tin cậy cho trung bình của tập hợp, sử dụng phân phối Student T"}, "CORREL": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON> về hệ số tương quan giữa hai tập dữ liệu"}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "<PERSON><PERSON><PERSON> c<PERSON>c ô trong phạm vi chứa giá trị số"}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "<PERSON><PERSON><PERSON> số ô không rỗng trong phạm vi và các giá trị nằm trong danh sách tham đối"}, "COUNTBLANK": {"a": "(range)", "d": "<PERSON><PERSON><PERSON> số ô rỗng trong khoảng các ô xác định"}, "COUNTIF": {"a": "(range; criteria)", "d": "<PERSON><PERSON><PERSON> số ô trong khoảng thoả mãn điều kiện cho trước"}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "<PERSON><PERSON><PERSON> số ô được xác định bởi tập cho trước các điều kiện hoặc tiêu chí"}, "COVAR": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON> về hiệp ph<PERSON><PERSON><PERSON>i, là trung bình tích độ lệch của mỗi cặp điểm dữ liệu trong hai tập dữ liệu"}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "Tr<PERSON> về hiệp phương sai mẫu, trung bình của các tích độ lệch cho mỗi cặp điểm dữ liệu trong hai tập hợp dữ liệu"}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "Tr<PERSON> về hiệp phương sai mẫu, trung bình của các tích độ lệch cho mỗi cặp điểm dữ liệu trong hai tập hợp dữ liệu"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "Tr<PERSON> về giá trị nhỏ nhất có phân bố nhị thức tích lũy lớn hơn hoặc bằng giá trị tiêu chuẩn"}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về tổng bình phương độ lệch của các điểm dữ liệu so với trung bình mẫu của chúng"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "<PERSON><PERSON><PERSON> về phân bố hàm mũ"}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "<PERSON><PERSON><PERSON> về phân bố hàm mũ"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Tr<PERSON> về phân bố xác suất F (độ đa dạng) của hai tập dữ liệu"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Tr<PERSON> về giá trị đảo của phân bố xác suất F: nếu p = FDIST(x,...), thì FINV(p,...) = x"}, "FTEST": {"a": "(array1; array2)", "d": "Tr<PERSON> về kết quả của kiểm tra-F, là xác suất hai phía có phương sai trong Mảng1 và Mảng2 không quá khác nhau"}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "Tr<PERSON> về phân phối xác suất F (phần dư bên trái) (độ đa dạng) cho hai tập dữ liệu"}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Tr<PERSON> về phân phối xác suất <PERSON> (phần dư bên phải) (độ đa dạng) cho hai tập dữ liệu"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Tr<PERSON> về nghịch đảo của phân phối xác suất F (phần dư bên trái): nếu p = F.DIST(x,...), thì F.INV(p,...) = x"}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Tr<PERSON> về nghịch đảo của phân phối xác suất F (phần dư bên trái): nếu p = F.DIST(x,...), thì F.INV(p,...) = x"}, "F.TEST": {"a": "(array1; array2)", "d": "Tr<PERSON> về kết quả của kiểm-chứng-<PERSON>, là xác suất hai phía có phương sai trong Mảng1 và Mảng2 không quá khác nhau"}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON><PERSON> về biến đổi <PERSON>ơ"}, "FISHERINV": {"a": "(y)", "d": "Tr<PERSON> về giá trị đảo của biến đổi Fi-sơ:nếu y = FISHER(x), thì FISHERINV(y) = x"}, "FORECAST": {"a": "(x; known_ys; known_xs)", "d": "<PERSON><PERSON><PERSON> toán hoặc dự đoán giá trị tương lai theo xu hướng tuyến tính bằng cách dùng các giá trị hiện có."}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "Tr<PERSON> về giá trị dự báo cho một ngày mục tiêu xác định trong tương lai bằng phư<PERSON><PERSON> thứ<PERSON> sang bằng hàm số mũ."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Tr<PERSON> về một khoảng tin cậy cho giá trị dự báo vào ngày mục tiêu được chỉ định."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Tr<PERSON> về độ dài của một mô hình lặp mà ứng dụng phát hiện cho một chuỗi thời gian được chỉ định."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "T<PERSON><PERSON> về số liệu thống kê yêu cầu cho dự báo."}, "FORECAST.LINEAR": {"a": "(x; known_ys; known_xs)", "d": "<PERSON><PERSON><PERSON> toán hoặc dự đoán một giá trị trong tương lai theo xu hướng tuyến tính bằng cách dùng các giá trị hiện có."}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "<PERSON><PERSON>h số lần xuất hiện của một giá trị trong khoảng các giá trị và trả về một mảng dọc các số có nhiều phần tử hơn Bảng_đựng"}, "GAMMA": {"a": "(x)", "d": "Trả về giá trị hàm Gamma"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "<PERSON><PERSON>ả về phân bố Gamma"}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "Trả về phân phối gamma"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "Tr<PERSON> về giá trị đảo của phân bố tích lũy gamma: nếu p = GAMMADIST(x,...) thì GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o của hàm tích lũy gamma: nếu p = GAMMA.DIST(x,...), thì GAMMA.INV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "<PERSON><PERSON>ả về lô-ga-rít tự nhiên của hàm gamma"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Trả về loga tự nhiên của hàm gamma"}, "GAUSS": {"a": "(x)", "d": "Trả về 0.5 ít hơn hàm phân phối tích lũy chuẩn chuẩn hóa"}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về trung bình hình học của một mảng hoặc khoảng dữ liệu số dương"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "<PERSON><PERSON><PERSON> về các số tăng theo hướng hàm mũ phù hợp với các điểm dữ liệu"}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "Trả về trung bình điều hoà của tập dữ liệu số dương: số nghịch đảo của trung bình nghịch đảo số học"}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "<PERSON><PERSON>ả về phân phối siêu hình học"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "<PERSON><PERSON>ả về phân bố siêu hình học"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "<PERSON><PERSON>h toán giao điểm của đường thẳng với trục y dùng đường hồi quy khớp nhất vẽ qua các giá trị x và giá trị y đã biết"}, "KURT": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> về độ nhọn của tập dữ liệu"}, "LARGE": {"a": "(array; k)", "d": "Tr<PERSON> về giá trị lớn thứ k trong tập dữ liệu. <PERSON><PERSON>, số lớn thứ năm"}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "<PERSON><PERSON><PERSON> về thống kê mô tả xu hướng tuyến tính phù hợp với các điểm dữ liệu, bằng cách khớp đường thẳng dùng phương pháp bình quân nhỏ nhất"}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Tr<PERSON> về thống kê mô tả đường hàm mũ phù hợp với các điểm dữ liệu"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "Trả về giá trị đảo của hàm phân bố tích lũy loga chuẩn của x, mà ln(x) đ<PERSON><PERSON><PERSON> phân phối chuẩn với tham số Mean và Standard_dev"}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Trả về phân phối lôga bình thường của x, trong đó ln(x) được phân phối chuẩn với tham số Trung bình và Độ lệch chuẩn"}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Tr<PERSON> về giá trị đảo của hàm phân bố lô-ga-rít chuẩn tích lũy, trong đó ln(x) <PERSON><PERSON><PERSON><PERSON> phân bố chuẩn với tham số Trung_bình và Độ_lệch_tiêu _chuẩn"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "Tr<PERSON> về phân bố lô-ga-rít chuẩn tích lũ<PERSON>, trong đó ln(x) đ<PERSON><PERSON><PERSON> phân bố chuẩn với tham số Mean và Standard_dev"}, "MAX": {"a": "(number1; [number2]; ...)", "d": "Trả về giá trị lớn nhất trong tập hợp giá trị. Không tính giá trị lô gíc và văn bản"}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "Trả về giá trị lớn nhất trong tập các giá trị. Không loại trừ giá trị lô gíc và văn bản"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Tr<PERSON> về giá trị tối đa trong số các ô được xác định bởi bộ các điều kiện hoặc tiêu chí đã cho"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "Trả lại số trung bình hoặc số ở khoảng giữa tập hợp các số đã cho"}, "MIN": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về số nhỏ nhất trong tập hợp giá trị. Không tính giá trị lô gíc và văn bản"}, "MINA": {"a": "(value1; [value2]; ...)", "d": "Trả về giá trị nhỏ nhất trong tập các giá trị. Không loại trừ giá trị lô gíc và văn bản"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Tr<PERSON> về giá trị tối thiểu trong số các ô được xác định bởi bộ các điều kiện hoặc tiêu chí đã cho"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về mức xuất hiện thường xuyên hoặc mức lặp của một giá trị trong mảng hoặc khoảng dữ liệu"}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về mảng dọc các giá trị xuất hiện thường xuyên nhất, hoặc lặp lại trong mảng hoặc vùng dữ liệu.  Đối với mảng ngang, sử dụng =TRANSPOSE(MODE.MULT(number1,number2,...))"}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> về mức xuất hiện thư<PERSON><PERSON> xuyê<PERSON>, hoặc mức lặp của một giá trị trong mảng hoặc khoảng dữ liệu"}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "Tr<PERSON> về xác suất nh<PERSON> thức âm, x<PERSON><PERSON> suất sẽ là Number_f lần thất bại trước lần thành công thứ Number_s, với xác suất thành công Probability_s"}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "Trả về phân bố nhị thức âm, là xác suất sẽ có Number_f lần thất bại trước thành công thứ Number_s-th, với xác suất của thành công là Probability_s"}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Tr<PERSON> về phân phối bình thường cho độ lệch chuẩn và trung bình đã chỉ định"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Trả về phân bố tích lũy chuẩn của trung bình và độ lệch tiêu chuẩn cho trước"}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Trả về giá trị đảo của phân bố tích lũy chuẩn của trung bình và độ lệch tiêu chuẩn cho trước"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "Trả về giá trị đảo của phân bố tích lũy chuẩn cho độ lệch trung bình và tiêu chuẩn cho trước"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "Tr<PERSON> về phân phối bình thường chuẩn (có trung bình là không và độ lệch chuẩn là một)"}, "NORMSDIST": {"a": "(z)", "d": "<PERSON><PERSON><PERSON> về phân bố tích lũy chuẩn (có trung bình là không và độ lệch tiêu chuẩn là 1)"}, "NORM.S.INV": {"a": "(probability)", "d": "Tr<PERSON> về giá trị đảo của phân bố tích lũy chuẩn (có trung bình là không và độ lêch tiêu chuẩn là 1)"}, "NORMSINV": {"a": "(probability)", "d": "Tr<PERSON> về giá trị đảo của phân bố tích lũy chuẩn (có trung bình là không và độ lêch tiêu chuẩn là 1)"}, "PEARSON": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON> về hệ số tương quan mômen tích <PERSON><PERSON>, r"}, "PERCENTILE": {"a": "(array; k)", "d": "Trả về phân vị thứ k của các giá trị trong khoảng"}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "Tr<PERSON> về phân vị thứ k của giá trị trong một kho<PERSON>ng, trong đó k nằm trong khoảng 0..1, bao g<PERSON>m"}, "PERCENTILE.INC": {"a": "(array; k)", "d": "Tr<PERSON> về phân vị thứ k của giá trị trong một kho<PERSON>ng, trong đó k nằm trong khoảng 0..1, bao g<PERSON>m"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "Tr<PERSON> về cấp của giá trị trong tập dữ liệu đ<PERSON><PERSON><PERSON> gán như số phần trăm của tập dữ liệu"}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "<PERSON><PERSON><PERSON> về thứ hạng của một giá trị trong tập hợp dữ liệu dưới dạng phần trăm của tập hợp dữ liệu dưới dạng phần trăm (0..1, bao gồ<PERSON> cả 0 và 1) của tập hợp dữ liệu"}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "<PERSON><PERSON><PERSON> về thứ hạng của một giá trị trong tập hợp dữ liệu dưới dạng phần trăm của tập hợp dữ liệu dưới dạng phần trăm (0..1, bao gồ<PERSON> cả 0 và 1) của tập hợp dữ liệu"}, "PERMUT": {"a": "(number; number_chosen)", "d": "Trả về số hoán vị của số đối tượng đư<PERSON><PERSON> chọn từ tổng số đối tượng"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "Trả về số lượng hoán vị của một số lượng đối tượng cho trước (có trùng lặp) mà có thể được chọn từ tổng số đối tượng"}, "PHI": {"a": "(x)", "d": "Trả về hàm mật độ của phân phối chuẩn chuẩn hóa"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "<PERSON><PERSON><PERSON> về phân b<PERSON>"}, "POISSON.DIST": {"a": "(x; mean; cumulative)", "d": "<PERSON><PERSON><PERSON> về phân b<PERSON>ơn"}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "Trả về xác suất của các giá trị trong khoảng hai giới hạn hoặc bằng giới hạn dưới"}, "QUARTILE": {"a": "(array; quart)", "d": "<PERSON><PERSON><PERSON> về tứ phân vị của tập dữ liệu"}, "QUARTILE.INC": {"a": "(array; quart)", "d": "<PERSON>r<PERSON> về tứ phân vị của tập hợp dữ liệ<PERSON>, d<PERSON><PERSON> trên giá trị phần trăm từ 0..1, bao g<PERSON><PERSON> cả 0 và 1"}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "<PERSON>r<PERSON> về tứ phân vị của tập hợp dữ liệ<PERSON>, d<PERSON><PERSON> trên giá trị phần trăm từ 0..1, bao g<PERSON><PERSON> cả 0 và 1"}, "RANK": {"a": "(number; ref; [order])", "d": "Tr<PERSON> về cấp của số trong danh sách: là độ lớn tương đối của nó so với các giá trị khác trong danh sách"}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "Trả về xếp hạng của số trong danh sách các số: kích cỡ của nó là tương đối với các giá trị khác trong danh sách; nếu có nhiều hơn một giá trị với cùng xếp hạng, sẽ trả về xếp hạng trung bình"}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "Trả về xếp hạng của số trong danh sách các số: kích cỡ của nó là tương đối với các giá trị khác trong danh sách; nếu có nhiều hơn một giá trị với cùng xếp hạng, sẽ trả về xếp hạng trung bình"}, "RSQ": {"a": "(known_ys; known_xs)", "d": "<PERSON><PERSON><PERSON> về bình phương hệ số tương quan mômen tích <PERSON>-sơn từ các điểm dữ liệu đã cho"}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về độ xiên của phân bố: đặc trưng mức độ mất đối xứng của phân bố xung quanh trung bình của nó"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "Trả về độ xiên của phân phối dựa trên một tập mẫu: đặc tính của mức độ bất đối xứng của một phân phối xoay quanh giá trị trung bình"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "T<PERSON><PERSON> về độ dốc của đường hồi quy tuyến tính từ các điểm dữ liệu đã cho"}, "SMALL": {"a": "(array; k)", "d": "Tr<PERSON> về giá trị lớn thứ k trong tập dữ liệu. <PERSON><PERSON>, số lớn thứ năm"}, "STANDARDIZE": {"a": "(x; mean; standard_dev)", "d": "Tr<PERSON> về giá trị chuẩn hóa từ phân bố đặc trưng bởi trung bình và độ lệch tiêu chuẩn"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "Ước lượng độ lệch tiêu chuẩn dựa trên mẫu (không tính giá trị logic và văn bản trong mẫu)"}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "T<PERSON>h toán độ lệch tiêu chuẩn dựa trên tập toàn bộ là các tham đối (không tính giá trị lô gíc và văn bản)"}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> nhận độ lệch tiêu chuẩn dựa trên mẫu (không tính giá trị lô gíc và văn bản trong mẫu)"}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "Ước đoán độ lệch tiêu chuẩn dựa trên mẫu, t<PERSON>h cả giá trị lô gíc và văn bản. Văn bản và giá trị lô gíc SAI có giá trị 0; giá trị lô gíc ĐÚNG có giá trị 1"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "T<PERSON>h toán độ lệch tiêu chuẩn dựa trên tập toàn bộ là các đối số (không tính giá trị logic và văn bản)"}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "T<PERSON>h toán độ lệch tiêu chuẩn dựa trên tập toàn bộ, tính cả giá trị lô gíc và văn bản. Văn bản và giá trị lô gíc SAI có giá trị 0; giá trị lô gíc ĐÚNG có giá trị 1"}, "STEYX": {"a": "(known_ys; known_xs)", "d": "Tr<PERSON> về độ sai chuẩn của giá trị y ước đoán cho mỗi giá trị x trong hồi quy"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "<PERSON><PERSON><PERSON> về phân bố t Student"}, "TINV": {"a": "(probability; deg_freedom)", "d": "Trả về giá trị đảo của phân bố t Student"}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "Tr<PERSON> về phân phối Student-t có phần dư bên trái"}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "Trả về phân bố t Student hai nhánh"}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "Trả về phân bố t Student nhánh phải"}, "T.INV": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o phần dư bên trái của phân phối Student t"}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o phần dư bên trái của phân phối Student t"}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "<PERSON><PERSON><PERSON> về xác suất gắn với kiểm định t Student"}, "TREND": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "<PERSON><PERSON><PERSON> về các số trong xu hướng tuyến tính phù hợp với các điểm dữ liệu, dùng phương pháp bình phương nhỏ nhất"}, "TRIMMEAN": {"a": "(array; percent)", "d": "Tr<PERSON> về trung bình phần bên trong của tập giá trị dữ liệu"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "<PERSON><PERSON><PERSON> về xác suất gắn với kiểm tra t Student"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "Ước lượng phương sai dựa trên mẫu (không tính giá trị logic và văn bản trong mẫu)"}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "Tính toán dung sai trên cơ sở tập toàn bộ (không tính các giá trị lô gíc và văn bản trong tập toàn bộ)"}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "Ước lượng phương sai dựa trên mẫu (không tính giá trị lô gíc và văn bản trong mẫu)"}, "VARA": {"a": "(value1; [value2]; ...)", "d": "Ước đoán độ lệch tiêu chuẩn dựa trên mẫu, t<PERSON>h cả giá trị lô gíc và văn bản. Văn bản và giá trị lô gíc SAI có giá trị 0; giá trị lô gíc ĐÚNG có giá trị 1"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "T<PERSON>h toán dung sai trên cơ sở tập toàn bộ (không tính các giá trị logic và văn bản trong tập toàn bộ)"}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "<PERSON><PERSON>h toán phương sai dựa trên tập toàn bộ, t<PERSON>h cả giá trị lô gíc và văn bản. V<PERSON>n bản và giá trị lô gíc SAI có giá trị 0; giá trị lô gíc ĐÚNG có giá trị 1"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "<PERSON><PERSON><PERSON> về ph<PERSON> b<PERSON>"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "<PERSON><PERSON><PERSON> về ph<PERSON> b<PERSON>"}, "Z.TEST": {"a": "(array; x; [sigma])", "d": "<PERSON>r<PERSON> về giá trị P một phần dư của kiểm định z"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "Trả về giá trị P một phần dư của kiểm tra z"}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "Trả về lãi suất cộng dồn cho chứng khoán trả lãi định kỳ."}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "Trả về lãi suất cộng dồn cho chứng khoán trả lãi khi tới hạn"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Trả về khấu hao theo tỷ lệ của tài nguyên theo mỗi chu kỳ kế toán."}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Trả về khấu hao theo tỷ lệ của tài nguyên theo mỗi chu kỳ kế toán."}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Trả về số ngày từ đầu chu kỳ cu-pôn đến ngày thanh kho<PERSON>n"}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về số ngày trong chu kỳ cu-pôn chứa ngày thanh k<PERSON>n"}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về số ngày từ ngày thanh khoản đến ngày cu-pôn tiếp theo"}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về ngày cu-pôn tiếp theo sau ngày thanh kho<PERSON>n chứng khoán"}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "Trả về số lượng cu-pôn phải trả giữa ngày thanh khoản chứng khoán và ngày tới hạn"}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Trả về ngày cu-pôn đã qua trước ngày thanh k<PERSON>n"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Tr<PERSON> lại tiền trả lợi nhuận cộng dồn giữa hai chu kỳ"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Tr<PERSON> lại tiền gốc cộng dồn phải trả cho khoản vay giữa hai chu kỳ"}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "<PERSON><PERSON><PERSON> về khấu hao của tài sản cho một chu kỳ xác định sử dụng phương pháp giảm dần cố định"}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "<PERSON><PERSON><PERSON> về khấu hao của tài sản cho một chu kỳ xác định dùng phương pháp giảm dần kép hoặc một số phương pháp chỉ định khác"}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "<PERSON><PERSON><PERSON> về hệ số quy đổi của chứ<PERSON> k<PERSON>"}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "Chuyển đổi giá đô-la, thể hiện như phân số, sang giá đô-la, thể hiện như số thập phân"}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "<PERSON>y<PERSON>n đổi giá đô-la, thể hiện như số thập phân, sang giá đô-la, thể hiện như phân số"}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Tr<PERSON> về thời đoạn hàng năm của chứng khoán trả lãi định kỳ"}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "<PERSON>r<PERSON> lại tỷ suất lợi nhuận có hiệu lực hàng năm"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "Tr<PERSON> về giá trị tương lai của khoản đầu tư trên cơ sở các khoản thanh toán, lãi suất không đổi theo chu kỳ"}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "Tr<PERSON> lại giá trị tương lai của vốn ban đầu sau khi áp dụng chuỗi tỷ suất lợi nhuận tổng gộp"}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "Tr<PERSON> về lãi suất cho chứng khoán đầu tư đầy đủ"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Trả về lãi suất thanh toán cho một chu kỳ định trước của một khoản đầu tư trên cơ sở thanh toán không đổi và lãi suất không đổi theo chu kỳ"}, "IRR": {"a": "(values; [guess])", "d": "Tr<PERSON> về tỷ lệ thu hồi nội tại của một chuỗi các lưu chuyển tiền tệ"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Trả về lãi phải trả trong chu kỳ xác định của khoản đầu tư"}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về thời đoạn sửa đổi theo <PERSON> cho chứng khoán với mệnh giá giả định là $100"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "Tr<PERSON> về tỷ lệ thu hồi nội tại của một chuỗi các lưu chuyển tiền tệ định kỳ, có tính đến cả chi phí đầu tư và lợi tức của việc tái đầu tư tiền mặt"}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "<PERSON><PERSON><PERSON> lại tỷ suất lợi nhuận danh nghĩa hàng năm"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "Tr<PERSON> về số chu kỳ của khoản đầu tư trên cơ sở các khoản thanh toán, lãi suất không đổi theo chu kỳ"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "Tr<PERSON> lại giá trị thực hiện tại của khoản đầu tư dựa trên tỷ lệ khấu trừ và chuỗi các khoản thanh toán (gi<PERSON> trị âm) và thu nhập (gi<PERSON> trị dươ<PERSON>) tương lai"}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "Trả về giá trên $100 mệnh giá của chứng khoán với một chu kỳ đầu lẻ"}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về lợi nhuận của chứng khoán với một chu kỳ đầu lẻ"}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "Trả về giá trên $100 mệnh giá của chứng khoán với một chu kỳ cuối lẻ"}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về lợi nhuận của chứng khoán với một chu kỳ cuối lẻ"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "Trả về số lượng chu kỳ để khoản đầu tư đạt đến giá trị được chỉ định"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "<PERSON><PERSON>h toán phần phải thanh toán cho khoản vay trên cơ sở các khoản thanh toán, lãi suất không đổi"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Tr<PERSON> về khoản thanh toán vốn của một khoản đầu tư trên cơ sở thanh toán không đổi và lãi suất không đổi theo chu kỳ"}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "Trả về giá trên $100 mệnh giá cho chứng khoán trả lãi định kỳ"}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "Trả về giá trên $100 mệnh giá của chứng khoán giảm giá"}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "Trả về giá cho mỗi $100 mệnh giá chứng khoán trả lãi lúc tới hạn"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "Tr<PERSON> về giá trị hiện thời của khoản đầu tư: có tính luôn tổng của một chuỗi các khoản thanh toán tương lai"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "là lãi suất theo chu kỳ của khoản vay hoặc khoản đầu tư. <PERSON><PERSON> dụ, khoản thanh toán hàng quý là 6% APR sẽ dùng 6%/4"}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "T<PERSON><PERSON> về số tiền sẽ nhận được lúc tới hạn cho chứng khoán đầu tư đầy đủ"}, "RRI": {"a": "(nper; pv; fv)", "d": "Trả về lãi suất tương ứng cho sự tăng trưởng của khoản đầu tư"}, "SLN": {"a": "(cost; salvage; life)", "d": "Trả về khấu hao đều của tài sản cho một chu kỳ"}, "SYD": {"a": "(cost; salvage; life; per)", "d": "Trả về số khấu hao tổng cả năm của tài sản cho một chu kỳ xác đ<PERSON>nh"}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "<PERSON><PERSON><PERSON> lại thu hồi trái phiếu đổi ngang cho trái phiếu kho bạc"}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "Trả lại giá trên $100 mệnh giá cho trái phiếu kho bạc"}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "<PERSON><PERSON><PERSON> lại thu hồi cho trái phiếu kho bạc"}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "T<PERSON><PERSON> về khấu hao của tài sản cho bất kỳ chu kỳ được chỉ định, t<PERSON><PERSON> cả một phần chu kỳ, sử dụng phương pháp giảm dần kép hoặc một số phương pháp xác định khác"}, "XIRR": {"a": "(values; dates; [guess])", "d": "Tr<PERSON> về tỷ lệ thu hồi nội bộ của lịch trình lưu chuyển tiền tệ"}, "XNPV": {"a": "(rate; values; dates)", "d": "<PERSON>r<PERSON> về giá trị thực hiện tại cho lịch trình lưu chuyển tiền tệ"}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "Tr<PERSON> về lợi nhuận cho chứng khoán trả lãi định kỳ"}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Tr<PERSON> về lợi nhuận hàng năm cho chứng khoán giảm giá. <PERSON><PERSON> dụ, tr<PERSON>i phiếu kho bạc"}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "Tr<PERSON> về lợi nhuận hàng năm của chứng khoán trả lãi lúc tới hạn"}, "ABS": {"a": "(number)", "d": "Tr<PERSON> về giá trị tuyệt đối một số, giá trị số không dấu"}, "ACOS": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về ArcCosin của một số, theo radian trong khoảng từ 0 đến Pi. ArcCosin là góc có <PERSON> bằng Số"}, "ACOSH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Cosin hi-péc-bôn đ<PERSON><PERSON> của một số"}, "ACOT": {"a": "(number)", "d": "Trả về hàm arccotang của con số, theo đơn vị đo góc từ 0 đến Pi."}, "ACOTH": {"a": "(number)", "d": "Trả về hàm cotang hyperbol ngược của con số"}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "<PERSON><PERSON><PERSON> về tập hợp trong danh sách hoặc cơ sở dữ liệu"}, "ARABIC": {"a": "(text)", "d": "Chuyển đổi chữ số La Ma<PERSON> sang chữ số Ả Rập"}, "ASC": {"a": "(text)", "d": "<PERSON><PERSON><PERSON> với những ngôn ngữ dùng bộ ký tự byte kép (DBCS), hàm này gi<PERSON><PERSON> thay đổi các ký tự có độ rộng toàn phần (byte kép) thành các ký tự có độ rộng b<PERSON> phần (byte đơn)"}, "ASIN": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Arc<PERSON> của một số theo radian, trong kho<PERSON>ng từ -Pi/2 đến Pi/2"}, "ASINH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Sin hi-péc-bôn đ<PERSON><PERSON> của một số"}, "ATAN": {"a": "(number)", "d": "Trở về Arc<PERSON>ang của một số theo <PERSON>, trong kho<PERSON>ng từ -Pi/2 đến Pi/2"}, "ATAN2": {"a": "(x_num; y_num)", "d": "Tr<PERSON> về ArcTang của cặp toạ độ x và y, theo radian trong khoảng từ -<PERSON> đến Pi, kh<PERSON>ng tính -Pi"}, "ATANH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Tang hi-péc-bôn đ<PERSON><PERSON> của một số"}, "BASE": {"a": "(number; radix; [min_length])", "d": "Chuyển một con số thành biểu diễn bằng văn bản với cơ số (gốc) cho trước"}, "CEILING": {"a": "(number; significance)", "d": "Làm tròn số lên, tới số nguyên hoặc bội gần nhất của số có nghĩa"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "Làm tròn số lên tới số nguyên gần nhất hoặc lên bội số có nghĩa gần nhất"}, "CEILING.PRECISE": {"a": "(number; [significance])", "d": "Tr<PERSON> về một số được làm tròn lên tới số nguyên gần nhất hoặc tới bội số có nghĩa gần nhất"}, "COMBIN": {"a": "(number; number_chosen)", "d": "Trả về số tổ hợp của một số phần tử cho trước"}, "COMBINA": {"a": "(number; number_chosen)", "d": "Trả về số lượng tổ hợp có lặp lại của một số lượng khoản mục cho trước"}, "COS": {"a": "(number)", "d": "Trả về Cosin của góc"}, "COSH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Cosin hi-péc-bôn của một số"}, "COT": {"a": "(number)", "d": "Trả về hàm cotang của một góc"}, "COTH": {"a": "(number)", "d": "Trả về hàm cotang hyperbol của một con số"}, "CSC": {"a": "(number)", "d": "Trả về hàm cosec của một góc"}, "CSCH": {"a": "(number)", "d": "Trả về hàm cosec hyperbol của một góc"}, "DECIMAL": {"a": "(number; radix)", "d": "Chuyển dạng biểu diễn bằng văn bản của một con số với cơ số cho trước sang con số ở hệ thập phân"}, "DEGREES": {"a": "(angle)", "d": "Chuyển đổi từ radian sang độ"}, "ECMA.CEILING": {"a": "(number; significance)", "d": "Làm tròn số lên, tới số nguyên hoặc bội gần nhất của số có nghĩa"}, "EVEN": {"a": "(number)", "d": "Làm tròn số dương lên và số âm xuống số nguyên chẵn gần nhất"}, "EXP": {"a": "(number)", "d": "Trả về giá trị e mũ số chỉ định"}, "FACT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về giai thừa của một số, bằng 1*2*3*...* Số"}, "FACTDOUBLE": {"a": "(number)", "d": "<PERSON><PERSON>ả về giai thừa kép của một số"}, "FLOOR": {"a": "(number; significance)", "d": " làm tròn number xuống bội số gần nhất của significance"}, "FLOOR.PRECISE": {"a": "(number; [significance])", "d": "Tr<PERSON> về một số được làm tròn xuống tới số nguyên gần nhất hoặc tới bội số có nghĩa gần nhất"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "Làm tròn số xuống về số nguyên gần nhất hoặc về bội số có nghĩa gần nhất"}, "GCD": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON>ả lại ước số chung lớn nhất"}, "INT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> tròn số xuống giá trị nguyên gần nhất"}, "ISO.CEILING": {"a": "(number; [significance])", "d": "Tr<PERSON> về một số được làm tròn lên tới số nguyên gần nhất hoặc tới bội số có nghĩa gần nhất. Bất chấp dấu của số, số sẽ được làm tròn lên. <PERSON><PERSON>, nếu đối số số hoặc đối số số có nghĩa là không, thì kết quả là không."}, "LCM": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> lại mẫu số chung nhỏ nhất"}, "LN": {"a": "(number)", "d": "Tr<PERSON> về lô-ga-rít tự nhiên của một số"}, "LOG": {"a": "(number; [base])", "d": "trả về lô-ga-rít của một số theo cơ số chỉ định"}, "LOG10": {"a": "(number)", "d": "Trả về lô-ga-r<PERSON>t cơ số 10 của một số"}, "MDETERM": {"a": "(array)", "d": "<PERSON>r<PERSON> về ma trận xác đ<PERSON>nh mảng"}, "MINVERSE": {"a": "(array)", "d": "Trả về ma trận đảo của ma trận trong mảng"}, "MMULT": {"a": "(array1; array2)", "d": "Tr<PERSON> về ma trận tích của hai mảng, là một mảng có cùng số hàng như Mảng1 và số cột như Mảng2"}, "MOD": {"a": "(number; divisor)", "d": "<PERSON><PERSON><PERSON> về phần dư của một số sau khi bị chia"}, "MROUND": {"a": "(number; multiple)", "d": "Trả về giá trị đã làm tròn theo bội số"}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> lại đa thức từ một tập số"}, "MUNIT": {"a": "(dimension)", "d": "Trả về ma trận đơn vị của chiều được chỉ định"}, "ODD": {"a": "(number)", "d": "Là tròn số dương lên và số âm xuống số lẻ gần nhất"}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON> về giá trị số <PERSON>, 3.14159265358979, <PERSON><PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON> tới 15 chữ số"}, "POWER": {"a": "(number; power)", "d": "Trả về giá trị hàm mũ của một số"}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> tất cả các số là tham đối"}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "<PERSON><PERSON><PERSON> về phần nguyên của phép chia"}, "RADIANS": {"a": "(angle)", "d": "Chuyển đổi độ sang radian"}, "RAND": {"a": "()", "d": "Trả về số ngẫu nhiên phân bổ đều trong khoảng lớn hơn hoặc bằng 0 và nhỏ hơn 1 (thay đổi khi tính lại)"}, "RANDARRAY": {"a": "([hàng]; [cột]; [nhỏ_nhất]; [lớn_nhất]; [số_nguyên])", "d": "<PERSON><PERSON><PERSON> về một mảng số ngẫu nhiên"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "Trả lại số ngẫu nhiên giữa các số được chỉ định"}, "ROMAN": {"a": "(number; [form])", "d": "<PERSON><PERSON><PERSON>n đổi chữ số <PERSON><PERSON><PERSON><PERSON><PERSON> sang La mã, n<PERSON><PERSON> v<PERSON><PERSON> bản"}, "ROUND": {"a": "(number; num_digits)", "d": "<PERSON><PERSON><PERSON> tròn số theo số chữ số được chỉ định"}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "<PERSON><PERSON><PERSON> tròn số xuố<PERSON>, tiến tới không"}, "ROUNDUP": {"a": "(number; num_digits)", "d": "<PERSON><PERSON><PERSON> tròn số lên, xa khỏi không"}, "SEC": {"a": "(number)", "d": "Trả về hàm sec của một góc"}, "SECH": {"a": "(number)", "d": "Trả về hàm sec hyperbol của một góc"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "<PERSON><PERSON><PERSON> về tổng chuỗi lũy thừa theo công thức"}, "SIGN": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về dấu của số: 1 nếu số dươ<PERSON>, 0 nếu là số 0, hoặc -1 nếu số âm"}, "SIN": {"a": "(number)", "d": "Trả về Sin của góc"}, "SINH": {"a": "(number)", "d": "<PERSON><PERSON>ả về Sin hi-péc-bôn của một số"}, "SQRT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về căn bậc hai của một số"}, "SQRTPI": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về căn bậ<PERSON>i (số * Pi)"}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "<PERSON><PERSON><PERSON> về tổng con trong danh sách hoặc cơ sở dữ liệu"}, "SUM": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> tất cả các số trong phạm vi ô"}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "<PERSON><PERSON><PERSON> các ô chỉ định theo điều kiện hoặc tiêu chí cho trước"}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "<PERSON>h<PERSON><PERSON> ô được chỉ ra bởi tập cho trước các điều kiện hay chỉ tiêu"}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "Trả về tổng tích của các khoảng hoặc mảng tương ứng"}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> về tổng bình phương các tham đối. Tham đối có thể là số, m<PERSON><PERSON>, tên hoặc tham chiếu tới ô chứa số"}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "Tổng độ lệch gi<PERSON>a bình phương trong hai khoảng hoặc mảng tương ứng"}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "T<PERSON>ả về tổng cộng của tổng bình phương các số trong hai khoảng hoặc mảng tương ứng"}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "Tổng bình phương độ lệch trong hai khoảng hoặc mảng tương ứng"}, "TAN": {"a": "(number)", "d": "<PERSON><PERSON>ả về <PERSON> của góc"}, "TANH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Tang hi-péc-bôn của một số"}, "TRUNC": {"a": "(number; [num_digits])", "d": "<PERSON><PERSON><PERSON> ngắn số thành số nguyên bằng cách loại bỏ phần thập phân, hoặc phần phân số của nó"}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "T<PERSON>o tham chiếu ô dạng văn bản, c<PERSON> số hàng và cột xác định"}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "Chọn giá trị hoặc thao tác thực hiện từ danh sách các giá trị, dựa trên số hiệu chỉ mục"}, "COLUMN": {"a": "([reference])", "d": "<PERSON><PERSON><PERSON> về số hiệu cột của tham chiếu"}, "COLUMNS": {"a": "(array)", "d": "<PERSON><PERSON><PERSON> về số cột trong một mảng hoặc tham chiếu"}, "FORMULATEXT": {"a": "(reference)", "d": "Trả về công thức như là một xâu"}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "Tìm giá trị trong hàng trên cùng của bảng hoặc mảng các giá trị và trả về giá trị cùng cột từ hàng chỉ định"}, "HYPERLINK": {"a": "(link_location; [friendly_name])", "d": "T<PERSON>o T<PERSON><PERSON> lối tắt hoặc bước nhảy để mở tài liệu lưu trên ổ cứng, máy chủ mạng hoặc trên internet"}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "Tr<PERSON> về giá trị hoặc tham chiếu tới ô giao của hàng và cột trong vùng chỉ định"}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "<PERSON><PERSON><PERSON> về tham chiếu chỉ định bởi xâu văn bản"}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "Tìm trong phạm vi một hàng, mộ<PERSON> cột, hoặc trong mảng.  <PERSON><PERSON><PERSON><PERSON> đưa ra để đảm bảo tương thích ngược"}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "Tr<PERSON> về vị trí tương đối của một phần tử trong mảng khớp với giá trị cho trước theo trật tự nhất định"}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "Tr<PERSON> về tham chiếu tới khoảng từ số hàng và số cột của một khoảng cho trước"}, "ROW": {"a": "([reference])", "d": "<PERSON><PERSON><PERSON> về số hiệu hàng của tham chiếu"}, "ROWS": {"a": "(array)", "d": "<PERSON><PERSON><PERSON> về số hàng trong một tham chiếu hoặc mảng"}, "TRANSPOSE": {"a": "(array)", "d": "Chuyển một dãy dọc các ô sang dãy ngang, hoặc ngược lại,"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": " Tr<PERSON> lại giá trị duy nhất từ một phạm vi hoặc mảng."}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "Tìm giá trị trong cột bên trái nhất củ<PERSON> bả<PERSON>, và trả về giá trị cùng hàng từ cột chỉ định. Mặc định, bảng đư<PERSON><PERSON> sắp xếp theo trật tự tăng dần"}, "XLOOKUP": {"a": "(giá_trị_tra_cứu; mảng_tra_cứu; mảng_trả_về; [nếu_không_tìm_thấy]; [chế_độ_khớp]; [chế_độ_tìm_kiếm])", "d": "T<PERSON><PERSON> kiếm giá trị trùng khớp trong một dải ô hoặc một mảng và trả về mục tương ứng từ dải ô hoặc mảng thứ hai. <PERSON> mặc <PERSON>, sẽ sử dụng giá trị khớp hoàn toàn"}, "CELL": {"a": "(info_type; [reference])", "d": "Tr<PERSON> về thông tin về định dạng, vị trí hay nội dung của một ô"}, "ERROR.TYPE": {"a": "(error_val)", "d": "Tr<PERSON> về một số khớp với giá trị lỗi."}, "ISBLANK": {"a": "(value)", "d": "<PERSON><PERSON>m tra tham chiếu có phải tới một ô rỗng, và trả về ĐÚNG hoặc SAI"}, "ISERR": {"a": "(giá_trị)", "d": "<PERSON><PERSON>m tra xem giá trị có phải là lỗi khác ngoài #N/A hay không và trả về TRUE hoặc FALSE"}, "ISERROR": {"a": "(giá_trị)", "d": "<PERSON><PERSON><PERSON> tra xem giá trị có phải là lỗi hay không và trả về TRUE hoặc FALSE"}, "ISEVEN": {"a": "(number)", "d": "Trả về ĐÚNG nếu là số chẵn"}, "ISFORMULA": {"a": "(reference)", "d": "Kiểm tra xem tham chiếu tới một ô có bao gồm công thức không, và trả về TRUE (ĐÚNG) hoặc FALSE (SAI)"}, "ISLOGICAL": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra giá trị có phải là giá trị lô-gic (ĐÚNG hoặc SAI), và tr<PERSON> về ĐÚNG hoặc SAI"}, "ISNA": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra giá trị có phải là #N/A, trả về ĐÚNG hoặc SAI"}, "ISNONTEXT": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra giá trị có phải là văn bản (ô trắng không phải là văn bản), và trả về ĐÚNG hoặc SAI"}, "ISNUMBER": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra một giá trị có phải là số, và trả về ĐÚNG hoặc SAI"}, "ISODD": {"a": "(number)", "d": "Trả về ĐÚNG nếu là số lẻ"}, "ISREF": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra giá trị có phải là tham chiếu, trả về ĐÚNG hoặc SAI"}, "ISTEXT": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra một giá trị có phải là văn bản, và trả về ĐÚNG hoặc SAI"}, "N": {"a": "(value)", "d": "<PERSON>yển đổi giá trị khác số thành số, ng<PERSON><PERSON> tháng thành số tuần tự, ĐÚNG thành 1, c<PERSON><PERSON> gi<PERSON> trị khác thành 0 (kh<PERSON><PERSON>)"}, "NA": {"a": "()", "d": "Tr<PERSON> về giá trị lỗi #N/A (giá trị không áp dụng)"}, "SHEET": {"a": "([value])", "d": "Trả về số của trang tính của trang tính được tham chiếu"}, "SHEETS": {"a": "([reference])", "d": "Trả về số lượng trang tính trong một tham chiếu"}, "TYPE": {"a": "(giá_trị)", "d": "Tr<PERSON> về một số nguyên đại diện cho kiểu dữ liệu của một giá trị: số = 1; văn bản = 2; giá trị lô-gic = 4; gi<PERSON> trị lỗi = 16; mảng = 64; dữ liệu phức hợp = 128"}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "<PERSON><PERSON><PERSON> tra nếu tất cả các tham đối là TRUE, trả về giá trị TRUE nếu tất cả tham đối là TRUE"}, "FALSE": {"a": "()", "d": "Trả về giá trị lô-gic SAI"}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "<PERSON><PERSON><PERSON> tra điều kiện có đáp ứng không và trả về một giá trị nếu ĐÚNG, trả về một giá trị khác nếu SAI"}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "<PERSON><PERSON>m tra xem một hay nhiều điều kiện được đáp ứng và trả về giá trị tương ứng với điều kiện TRUE đầu tiên"}, "IFERROR": {"a": "(value; value_if_error)", "d": "Trả lại value_if_error nếu biểu thức có lỗi và giá trị của biểu thức nếu không"}, "IFNA": {"a": "(value; value_if_na)", "d": "Trả về giá trị bạn chỉ định nếu biểu thức cho ra kết quả #N/A, nếu không thì sẽ trả về giá trị của biểu thức"}, "NOT": {"a": "(logical)", "d": "Đổi SAI thành ĐÚNG, ĐÚNG thành SAI"}, "OR": {"a": "(logical1; [logical2]; ...)", "d": "<PERSON><PERSON><PERSON> tra nếu tất cả các tham đối là SAI, trả về giá trị SAI"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "T<PERSON>h giá trị biểu thức dựa vào danh sách các giá trị và trả về kết quả tương ứng với giá trị khớp đầu tiên. <PERSON>ếu không có giá trị khớp, giá trị mặc định tùy chọn đượ<PERSON> trả về"}, "TRUE": {"a": "()", "d": "Trả về giá trị lô-gic ĐÚNG"}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "Trả về hàm \"Exclusive Or\" lô-gic của tất cả tham đối"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Trả về văn bản trư<PERSON><PERSON> khi phân tách ký tự."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Trả về văn bản sau khi phân tách các ký tự."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "<PERSON><PERSON><PERSON> văn bản thành các hàng hoặc cột bằng dấu phân tách."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": " <PERSON><PERSON> b<PERSON><PERSON> một véc-tơ hàng hoặc cột sau một số giá trị được chỉ định."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "<PERSON><PERSON><PERSON> chồng theo chiều dọc các mảng thành một mảng."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "<PERSON><PERSON><PERSON> chồng theo chiều ngang các mảng thành một mảng."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "<PERSON><PERSON><PERSON> về các hàng từ mảng hoặc tham chiếu."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "<PERSON><PERSON><PERSON> về các cột từ mảng hoặc tham chiếu."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON> về mảng dưới dạng một cột."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON> về mảng dưới dạng một hàng."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "<PERSON><PERSON> b<PERSON><PERSON> một véc-tơ hàng hoặc cột sau một số Giá trị được chỉ định."}, "TAKE": {"a": "(array, rows, [columns])", "d": "<PERSON><PERSON><PERSON> về hàng hoặc cột từ đầu hoặc cuối mảng."}, "DROP": {"a": "(array, rows, [columns])", "d": "<PERSON><PERSON><PERSON> hàng hoặc cột từ đầu hoặc cuối mảng."}}
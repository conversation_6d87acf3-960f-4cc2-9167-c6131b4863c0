{"DATE": {"a": "(año; mes; día)", "d": "Devuelve el número que representa la fecha en código de fecha y hora"}, "DATEDIF": {"a": "( fecha-inicio; fecha-final; unidad )", "d": "Devuelve la diferencia entre dos valores de fecha (fecha de inicio y fecha de fin), según el intervalo (unidad) especificado"}, "DATEVALUE": {"a": "(texto_de_fecha)", "d": "Convierte una fecha en forma de texto en un número que representa la fecha en código de fecha y hora"}, "DAY": {"a": "(núm_de_serie)", "d": "Devuelve el día del mes (un número de 1 a 31)."}, "DAYS": {"a": "(fecha_inicial; fecha_final)", "d": "Devuelve la cantidad de días entre las dos fechas."}, "DAYS360": {"a": "(fecha_inicial; fecha_final; [método])", "d": "Calcula el número de días entre dos fechas basándose en un año de 360 días (doce meses de 30 días)"}, "EDATE": {"a": "(fecha_inicial; meses)", "d": "Devuelve el número de serie de la fecha que es el número indicado de meses antes o después de la fecha inicial"}, "EOMONTH": {"a": "(fecha_inicial; meses)", "d": "Devuelve el número de serie del último día del mes antes o después del número especificado de meses"}, "HOUR": {"a": "(núm_de_serie)", "d": "Devuelve la hora como un número de 0 (12:00 a.m.) a 23 (11:00 p.m.)."}, "ISOWEEKNUM": {"a": "(fecha)", "d": "Devuelve el número de semana ISO del año para una fecha determinada"}, "MINUTE": {"a": "(núm_de_serie)", "d": "Devuelve el minuto, un número de 0 a 59."}, "MONTH": {"a": "(núm_de_serie)", "d": "Devuelve el mes, un número entero de 1 (enero) a 12 (diciembre)."}, "NETWORKDAYS": {"a": "(fecha_inicial; fecha_final; [vacaciones])", "d": "Devuelve el número total de días laborables entre dos fechas"}, "NETWORKDAYS.INTL": {"a": "(fecha_inicial; fecha_final; [fin_de_semana]; [días_no_laborables])", "d": "Devuelve el número de días laborables completos entre dos fechas con parámetros de fin de semana personalizados"}, "NOW": {"a": "()", "d": "Devuelve la fecha y hora actuales con formato de fecha y hora."}, "SECOND": {"a": "(núm_de_serie)", "d": "Devuelve el segundo, un número de 0 a 59."}, "TIME": {"a": "(hora; minuto; segundo)", "d": "Convierte horas, minutos y segundos dados como números en un número de serie, con formato de hora"}, "TIMEVALUE": {"a": "(texto_de_hora)", "d": "Convierte una hora de texto en un número de serie para una hora, un número de 0 (12:00:00 a.m.) a 0.999988426 (11:59:59 p.m.). Da formato al número con un formato de hora después de introducir la fórmula"}, "TODAY": {"a": "()", "d": "Devuelve la fecha actual con formato de fecha."}, "WEEKDAY": {"a": "(núm_de_serie; [tipo])", "d": "Devuelve un número de 1 a 7 que identifica el día de la semana."}, "WEEKNUM": {"a": "(número_serie; [tipo_devuelto])", "d": "Devuelve el número de semanas en el año"}, "WORKDAY": {"a": "(fecha_inicial; días; [vacaciones])", "d": "Devuelve el número de serie de la fecha antes o después de un número especificado de días laborables"}, "WORKDAY.INTL": {"a": "(fecha_inicial; días; [fin_de_semana]; [días_no_laborables])", "d": "Devuelve el número de serie de la fecha anterior o posterior a un número especificado de días laborables con parámetros de fin de semana personalizados"}, "YEAR": {"a": "(núm_de_serie)", "d": "Devuelve el año, un número entero en el rango 1900-9999."}, "YEARFRAC": {"a": "(fecha_inicial; fecha_final; [base])", "d": "Devuelve la fracción del año que representa el número de días completos entre la fecha_inicial y la fecha_final"}, "BESSELI": {"a": "(x; n)", "d": "Devuelve la función Bessel In(x) modificada"}, "BESSELJ": {"a": "(x; n)", "d": "Devuelve la función Bessel Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Devuelve la función Bessel Kn(x) modificada"}, "BESSELY": {"a": "(x; n)", "d": "Devuelve la función Bessel Yn(x)"}, "BIN2DEC": {"a": "(número)", "d": "Convierte un número binario en decimal"}, "BIN2HEX": {"a": "(número; [posiciones])", "d": "Convierte un número binario en hexadecimal"}, "BIN2OCT": {"a": "(número; [posiciones])", "d": "Convierte un número binario en octal"}, "BITAND": {"a": "(número1; número2)", "d": "Devuelve un bit a bit 'And' de dos números"}, "BITLSHIFT": {"a": "(número; cambio_cantidad)", "d": "Devuelve un número desplazado a la izquierda por bits de desplazamiento"}, "BITOR": {"a": "(número1; número2)", "d": "Devuelve un bit a bit 'Or' de dos números"}, "BITRSHIFT": {"a": "(número; cambio_cantidad)", "d": "Devuelve un número desplazado a la derecha por bits de desplazamiento"}, "BITXOR": {"a": "(número1; número2)", "d": "Devuelve un bit a bit 'Exclusive Or' de dos números"}, "COMPLEX": {"a": "(núm_real; i_núm; [sufijo])", "d": "Convierte el coeficiente real e imaginario en un número complejo"}, "CONVERT": {"a": "(número; desde_unidad; a_unidad)", "d": "Convierte un número de un sistema decimal a otro"}, "DEC2BIN": {"a": "(número; [posiciones])", "d": "Convierte un número decimal en binario"}, "DEC2HEX": {"a": "(número; [posiciones])", "d": "Convierte un número decimal en hexadecimal"}, "DEC2OCT": {"a": "(número; [posiciones])", "d": "Convierte un número decimal en octal"}, "DELTA": {"a": "(número1; [número2])", "d": "<PERSON><PERSON><PERSON> si los dos números son iguales"}, "ERF": {"a": "(límite_inferior; [límite_superior])", "d": "Devuelve la función de error"}, "ERF.PRECISE": {"a": "(X)", "d": "Devuelve la función de error"}, "ERFC": {"a": "(x)", "d": "Devuelve la función de error complementaria"}, "ERFC.PRECISE": {"a": "(X)", "d": "Devuelve la función de error complementaria"}, "GESTEP": {"a": "(número; [paso])", "d": "Prue<PERSON> si un número es mayor que el valor de referencia"}, "HEX2BIN": {"a": "(número; [posiciones])", "d": "Convierte un número hexadecimal en binario"}, "HEX2DEC": {"a": "(número)", "d": "Convierte un número hexadecimal en decimal"}, "HEX2OCT": {"a": "(número; [posiciones])", "d": "Convierte un número hexadecimal en octal"}, "IMABS": {"a": "(inúmero)", "d": "Devuelve el valor absoluto (módulo) de un número complejo"}, "IMAGINARY": {"a": "(inúmero)", "d": "Devuelve el coeficiente imaginario de un número complejo"}, "IMARGUMENT": {"a": "(inúmero)", "d": "Devuelve el argumento q, un ángulo expresado en radianes"}, "IMCONJUGATE": {"a": "(inúmero)", "d": "Devuelve el conjugado complejo de un número complejo"}, "IMCOS": {"a": "(inúmero)", "d": "Devuelve el coseno de un número complejo"}, "IMCOSH": {"a": "(númeroi)", "d": "Devuelve el coseno hiperbólico de un número complejo"}, "IMCOT": {"a": "(númeroi)", "d": "Devuelve la cotangente de un número complejo"}, "IMCSC": {"a": "(númeroi)", "d": "Devuelve la cosecante de un número complejo"}, "IMCSCH": {"a": "(númeroi)", "d": "Devuelve la cosecante hiperbólica de un número complejo"}, "IMDIV": {"a": "(inúmero1; inúmero2)", "d": "Devuelve el cociente de dos números complejos"}, "IMEXP": {"a": "(inúmero)", "d": "Devuelve el valor exponencial de un número complejo"}, "IMLN": {"a": "(inúmero)", "d": "Devuelve el logaritmo natural de un número complejo"}, "IMLOG10": {"a": "(inúmero)", "d": "Devuelve el logaritmo de base 10 de un número complejo"}, "IMLOG2": {"a": "(inúmero)", "d": "Devuelve el logaritmo de base 2 de un número complejo"}, "IMPOWER": {"a": "(inúmero; número)", "d": "Devuelve un número complejo elevado a la potencia del entero"}, "IMPRODUCT": {"a": "(inúmero1; [inúmero2]; ...)", "d": "Devuelve el producto de 1 a 255 números complejos"}, "IMREAL": {"a": "(inúmero)", "d": "Devuelve el coeficiente real de un número complejo"}, "IMSEC": {"a": "(númeroi)", "d": "Devuelve la secante de un número complejo"}, "IMSECH": {"a": "(númeroi)", "d": "Devuelve la secante hiperbólica de un número complejo "}, "IMSIN": {"a": "(inúmero)", "d": "Devuelve el seno de un número complejo"}, "IMSINH": {"a": "(númeroi)", "d": "Devuelve el seno hiperbólico de un número complejo"}, "IMSQRT": {"a": "(inúmero)", "d": "Devuelve la raíz cuadrada de un número complejo"}, "IMSUB": {"a": "(inúmero1; inúmero2)", "d": "Devuelve la diferencia de dos números complejos"}, "IMSUM": {"a": "(inúmero1; [inúmero2]; ...)", "d": "Devuelve la suma de números complejos"}, "IMTAN": {"a": "(númeroi)", "d": "Devuelve la tangente de un número complejo"}, "OCT2BIN": {"a": "(número; [posiciones])", "d": "Convierte un número octal en binario"}, "OCT2DEC": {"a": "(número)", "d": "Convierte un número octal en decimal"}, "OCT2HEX": {"a": "(número; [posiciones])", "d": "Convierte un número octal en hexadecimal"}, "DAVERAGE": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Obtiene el promedio de los valores de una columna, lista o base de datos que cumplen las condiciones especificadas"}, "DCOUNT": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Cuenta las celdas que contienen números en el campo (columna) de registros de la base de datos que cumplen las condiciones especificadas"}, "DCOUNTA": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Cuenta el número de celdas que no están en blanco en el campo (columna) de los registros de la base de datos que cumplen las condiciones especificadas"}, "DGET": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Extrae de una base de datos un único registro que coincide con las condiciones especificadas"}, "DMAX": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Devuelve el número máximo en el campo (columna) de registros de la base de datos que coinciden con las condiciones especificadas"}, "DMIN": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Devuelve el número menor del campo (columna) de registros de la base de datos que coincide con las condiciones especificadas"}, "DPRODUCT": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Multiplica los valores del campo (columna) de registros en la base de datos que coinciden con las condiciones especificadas"}, "DSTDEV": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Calcula la desviación estándar basándose en una muestra de las entradas seleccionadas de una base de datos"}, "DSTDEVP": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Calcula la desviación estándar basándose en la población total de las entradas seleccionadas de una base de datos"}, "DSUM": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Suma los números en el campo (columna) de los registros que coinciden con las condiciones especificadas"}, "DVAR": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Calcula la varianza basándose en una muestra de las entradas seleccionadas de una base de datos"}, "DVARP": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Calcula la varianza basándose en la población total de las entradas seleccionadas de una base de datos"}, "CHAR": {"a": "(número)", "d": "Devuelve el carácter especificado por el número de código a partir del juego de caracteres establecido en su PC"}, "CLEAN": {"a": "(texto)", "d": "Quita todos los caracteres no imprimibles del texto"}, "CODE": {"a": "(texto)", "d": "Devuelve el número de código del primer carácter del texto del juego de caracteres usados por su PC"}, "CONCATENATE": {"a": "(texto1; [texto2]; ...)", "d": "Une varios elementos de texto en uno solo"}, "CONCAT": {"a": "(texto1; ...)", "d": "Concatena una lista o rango de cadenas de texto"}, "DOLLAR": {"a": "(número; [núm_de_decimales])", "d": "Convierte un número en texto usando formato de moneda"}, "EXACT": {"a": "(texto1; texto2)", "d": "Comprueba si dos cadenas de texto son exactamente iguales y devuelve VERDADERO o FALSO. IGUAL distingue mayúsculas de minúsculas"}, "FIND": {"a": "(texto_buscado; dentro_del_texto; [núm_inicial])", "d": "Devuelve la posición inicial de una cadena de texto dentro de otra cadena de texto. ENCONTRAR distingue mayúsculas de minúsculas"}, "FINDB": {"a": "(cadena-1; cadena-2; [posición-inicio])", "d": "Encuentra la subcadena especificada (cadena-1) dentro de una cadena (cadena-2) y está destinada a los idiomas del conjunto de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc."}, "FIXED": {"a": "(número; [decimales]; [no_separar_millares])", "d": "Redondea un número al número especificado de decimales y devuelve el resultado como texto con o sin comas"}, "LEFT": {"a": "(texto; [núm_de_caracteres])", "d": "Devuelve el número especificado de caracteres del principio de una cadena de texto"}, "LEFTB": {"a": "(cadena; [número-caracteres])", "d": "Extrae la subcadena de la cadena especificada a partir del carácter izquierdo y está destinada a idiomas que utilizan el juego de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc."}, "LEN": {"a": "(texto)", "d": "Devuelve el número de caracteres de una cadena de texto"}, "LENB": {"a": "( cadena )", "d": "Analiza la cadena especificada y devolver el número de caracteres que contiene y está destinada a idiomas que utilizan el juego de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc."}, "LOWER": {"a": "(texto)", "d": "Convierte todas las letras de una cadena de texto en minúsculas"}, "MID": {"a": "(texto; posición_inicial; núm_de_caracteres)", "d": "Devuelve los caracteres del centro de una cadena de texto, dada una posición y longitud iniciales"}, "MIDB": {"a": "(cadena; posición-empiece; número-caracteres)", "d": "Extrae los caracteres de la cadena especificada a partir de cualquier posición y está destinada a idiomas que utilizan el juego de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc."}, "NUMBERVALUE": {"a": "(texto; [separador_decimal]; [separador_grupo])", "d": "Convierte texto a número de manera independiente a la configuración regional"}, "PROPER": {"a": "(texto)", "d": "Convierte una cadena de texto en mayúsculas o minúsculas, según corresponda; la primera letra de cada palabra en mayúscula y las demás letras en minúscula"}, "REPLACE": {"a": "(texto_original; núm_inicial; núm_de_caracteres; texto_nuevo)", "d": "Reemplaza parte de una cadena de texto por otra"}, "REPLACEB": {"a": "(cadena-1; pos-inicio; número-caracteres; cadena-2)", "d": "Reemplaza un conjunto de caracteres, basado en el número de caracteres y la posición inicial que especifique, por un nuevo conjunto de caracteres y está destinada a idiomas que utilizan el conjunto de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc."}, "REPT": {"a": "(texto; núm_de_veces)", "d": "Repite el texto un número determinado de veces. Use REPETIR para rellenar una celda con el número de repeticiones de una cadena de texto"}, "RIGHT": {"a": "(texto; [núm_de_caracteres])", "d": "Devuelve el número especificado de caracteres del final de una cadena de texto"}, "RIGHTB": {"a": "(cadena; [número-caracteres])", "d": "Extrae una subcadena de una cadena a partir del carácter más a la derecha, basada en el número especificado de caracteres y está destinada a idiomas que utilizan el juego de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc."}, "SEARCH": {"a": "(texto_buscado; dentro_del_texto; [núm_inicial])", "d": "Devuelve el número de carácter en el que se encuentra un carácter o una cadena de texto en particular, leyendo de izquierda a derecha (no distingue mayúsculas de minúsculas)."}, "SEARCHB": {"a": "(cadena-1; cadena-2; [posición-inicio])", "d": "Devuelve la ubicación de la subcadena especificada en una cadena y está destinada a idiomas que utilizan el juego de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc."}, "SUBSTITUTE": {"a": "(texto; texto_original; texto_nuevo; [núm_de_repeticiones])", "d": "Reemplaza el texto existente con texto nuevo en una cadena"}, "T": {"a": "(valor)", "d": "Comprueba si un valor es texto y devuelve texto si lo es o comillas dobles (sin texto) si no lo es"}, "TEXT": {"a": "(valor; formato)", "d": "Convierte un valor en texto, con un formato de número específico"}, "TEXTJOIN": {"a": "(delimitador; ignorar_vacías; texto1; ...)", "d": "Concatena una lista o rango de cadenas de texto mediante una cadena o carácter delimitador"}, "TRIM": {"a": "(texto)", "d": "Quita todos los espacios del texto excepto los espacios individuales entre palabras"}, "UNICHAR": {"a": "(número)", "d": "Devuelve el carácter Unicode al que hace referencia el valor numérico dado"}, "UNICODE": {"a": "(texto)", "d": "Devuelve el número (punto de código) que corresponde al primer carácter del texto"}, "UPPER": {"a": "(texto)", "d": "Convierte una cadena de texto en letras mayúsculas"}, "VALUE": {"a": "(texto)", "d": "Convierte un argumento de texto que representa un número en un número"}, "AVEDEV": {"a": "(número1; [número2]; ...)", "d": "Devuelve el promedio de las desviaciones absolutas de la media de los puntos de datos. Los argumentos pueden ser números, nombres, matrices o referencias que contienen números"}, "AVERAGE": {"a": "(número1; [número2]; ...)", "d": "Devuelve el promedio (media aritmética) de los argumentos, los cuales pueden ser números, nombres, matrices o referencias que contengan números"}, "AVERAGEA": {"a": "(valor1; [valor2]; ...)", "d": "Devuelve el promedio (media aritmética) de los argumentos; 0 evalúa el texto como FALSO; 1 como VERDADERO. Los argumentos pueden ser números, nombres, matrices o referencias"}, "AVERAGEIF": {"a": "(rango; criterio; [rango_promedio])", "d": "Busca el promedio (media aritmética) de las celdas que cumplen un determinado criterio o condición"}, "AVERAGEIFS": {"a": "(rango_promedio; rango_criterios; criterio; ...)", "d": "Busca el promedio (media aritmética) de las celdas que cumplen un determinado conjunto de condiciones o criterios"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Devuelve la función de densidad de probabilidad beta acumulativa"}, "BETAINV": {"a": "(probabilidad; alfa; beta; [A]; [B])", "d": "Devuelve el inverso de la función de densidad de probabilidad beta acumulativa (DISTR.BETA)"}, "BETA.DIST": {"a": "(x; alfa; beta; acumulado; [A]; [B])", "d": "Devuelve la función de distribución de probabilidad beta"}, "BETA.INV": {"a": "(probabilidad; alfa; beta; [A]; [B])", "d": "Devuelve el inverso de la función de densidad de probabilidad beta acumulativa (DISTR.BETA.N)"}, "BINOMDIST": {"a": "(núm_éxito; ensayos; prob_éxito; acumulado)", "d": "Devuelve la probabilidad de la distribución binomial del término individual"}, "BINOM.DIST": {"a": "(núm_éxito; ensayos; prob_éxito; acumulado)", "d": "Devuelve la probabilidad de una variable aleatoria discreta siguiendo una distribución binomial"}, "BINOM.DIST.RANGE": {"a": "(ensayos; probabilidad_s; número_s; [número_s2])", "d": "Devuelve la probabilidad de un resultado de prueba que usa una distribución binomial"}, "BINOM.INV": {"a": "(ensayos; prob_éxito; alfa)", "d": "Devuelve el menor valor cuya distribución binomial acumulativa es mayor o igual que un valor de criterio"}, "CHIDIST": {"a": "(x; grado<PERSON>_<PERSON>_libertad)", "d": "Devuelve la probabilidad de cola derecha de la distribución chi cuadrado"}, "CHIINV": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de una probabilidad dada, de una cola derecha, en una distribución chi cuadrado"}, "CHITEST": {"a": "(rango_real; rango_esperado)", "d": "Devuelve la prueba de independencia: el valor de distribución chi cuadrado para la estadística y los grados de libertad apropiados"}, "CHISQ.DIST": {"a": "(x; grados_de_libertad; acumulado)", "d": "Devuelve la probabilidad de cola izquierda de la distribución chi cuadrado"}, "CHISQ.DIST.RT": {"a": "(x; grados_de_probabilidad)", "d": "Devuelve la probabilidad de cola derecha de la distribución chi cuadrado"}, "CHISQ.INV": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de la probabilidad de cola izquierda de la distribución chi cuadrado"}, "CHISQ.INV.RT": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de la probabilidad de cola derecha de la distribución chi cuadrado"}, "CHISQ.TEST": {"a": "(rango_real; rango_esperado)", "d": "Devuelve la prueba de independencia: el valor de la distribución chi cuadrado para la estadística y los grados adecuados de libertad"}, "CONFIDENCE": {"a": "(alfa; desv_estándar; tama<PERSON>)", "d": "Devuelve el rango de confianza para la media de una población, con una distribución normal"}, "CONFIDENCE.NORM": {"a": "(alfa; desv_estándar; tama<PERSON>)", "d": "Devuelve el rango de confianza para una media de población con una distribución normal"}, "CONFIDENCE.T": {"a": "(alfa; desv_estándar; tama<PERSON>)", "d": "Devuelve el rango de confianza para una media de población con distribución de T de Student"}, "CORREL": {"a": "(matriz1; matriz2)", "d": "Devuelve el coeficiente de correlación de dos conjuntos de datos"}, "COUNT": {"a": "(valor1; [valor2]; ...)", "d": "Cuenta el número de celdas de un rango que contienen números"}, "COUNTA": {"a": "(valor1; [valor2]; ...)", "d": "Cuenta el número de celdas no vacías de un rango"}, "COUNTBLANK": {"a": "(rango)", "d": "Cuenta el número de celdas en blanco dentro de un rango especificado"}, "COUNTIF": {"a": "(rango; criterio)", "d": "Cuenta las celdas en el rango que coinciden con la condición dada"}, "COUNTIFS": {"a": "(rango_criterios; criterio; ...)", "d": "Cuenta el número de celdas que cumplen un determinado conjunto de condiciones o criterios"}, "COVAR": {"a": "(matriz1; matriz2)", "d": "Devuelve la covarianza, que es el promedio de los productos de las desviaciones de los pares de puntos de datos en dos conjuntos de datos"}, "COVARIANCE.P": {"a": "(matriz1; matriz2)", "d": "Devuelve la covarianza de población, el promedio de los productos de las desviaciones para cada pareja de puntos de datos en dos conjuntos de datos"}, "COVARIANCE.S": {"a": "(matriz1; matriz2)", "d": "Devuelve la covarianza, el promedio de los productos de las desviaciones para cada pareja de puntos de datos en dos conjuntos de datos"}, "CRITBINOM": {"a": "(ensayos; prob_éxito; alfa)", "d": "Devuelve el menor valor cuya distribución binomial acumulativa es mayor o igual que un valor de criterio"}, "DEVSQ": {"a": "(número1; [número2]; ...)", "d": "Devuelve la suma de los cuadrados de las desviaciones de los puntos de datos con respecto al promedio de la muestra"}, "EXPONDIST": {"a": "(x; lambda; acum)", "d": "Devuelve la distribución exponencial"}, "EXPON.DIST": {"a": "(x; lambda; acum)", "d": "Devuelve la distribución exponencial"}, "FDIST": {"a": "(x; grados_de_libertad1; grados_de_libertad2)", "d": "Devuelve la distribución de probabilidad F (grado de diversidad) (de cola derecha) de dos conjuntos de datos"}, "FINV": {"a": "(probabilidad; grados_de_libertad1; grados_de_libertad2)", "d": "Devuelve el inverso de la distribución de probabilidad F (cola derecha): si p = DISTR.F (x,...), entonces INV.F(p,...) = x"}, "FTEST": {"a": "(matriz1; matriz2)", "d": "Devuelve el resultado de una prueba F, la probabilidad de dos colas de que las varianzas en Matriz1 y Matriz2 no sean significativamente diferentes"}, "F.DIST": {"a": "(x; grados_de_libertad1; grados_de_libertad2; acumulado)", "d": "Devuelve la distribución (de cola izquierda) de probabilidad F (grado de diversidad) para dos conjuntos de datos"}, "F.DIST.RT": {"a": "(x; grados_de_libertad1; grados_de_libertad2)", "d": "Devuelve la distribución (de cola derecha) de probabilidad F (grado de diversidad) para dos conjuntos de datos"}, "F.INV": {"a": "(probabilidad; grados_de_libertad1; grados_de_libertad2)", "d": "Devuelve el inverso de la distribución de probabilidad F (de cola izquierda): si p = DISTR.F(x,...), entonces INV.F(p,...) = x"}, "F.INV.RT": {"a": "(probabilidad; grados_de_libertad1; grados_de_libertad2)", "d": "Devuelve el inverso de la distribución de probabilidad F (de cola derecha): si p = DISTR.F.CD(x,...), entonces INV.F.CD(p,...) = x"}, "F.TEST": {"a": "(matriz1; matriz2)", "d": "Devuelve el resultado de una prueba F, la probabilidad de dos colas de que las varianzas en Matriz 1 y Matriz 2 no sean significativamente diferentes"}, "FISHER": {"a": "(x)", "d": "Devuelve la transformación Fisher o coeficiente Z"}, "FISHERINV": {"a": "(y)", "d": "Devuelve la función inversa de la transformación Fisher o coeficiente Z: si y = FISHER (x), entonces PRUEBA.FISHER.INV (y) = x"}, "FORECAST": {"a": "(x; conocido_y; conocido_x)", "d": "Calcula o predice un valor futuro en una tendencia lineal usando valores existentes"}, "FORECAST.ETS": {"a": "(fecha_objetivo; valores; escaladetiempo; [estacionalidad]; [finalización_datos]; [agregación])", "d": "Devuelve el valor previsto para una fecha objetivo usando el método de suavizado exponencial."}, "FORECAST.ETS.CONFINT": {"a": "(fecha_objetivo; valores; escaladetiempo; [nivel_confianza]; [estacionalidad]; [finalización_datos]; [agregación])", "d": "Devuelve un rango de confianza para el valor previsto en la fecha objetivo especificada."}, "FORECAST.ETS.SEASONALITY": {"a": "(valores; escaladetiempo; [finalización_datos]; [agregación])", "d": "Devuelve la longitud del patrón repetitivo que la aplicación detecta para la serie de tiempo especificado."}, "FORECAST.ETS.STAT": {"a": "(valores; escaladetiempo; tipo_estadística; [estacionalidad]; [completación_datos]; [agregación])", "d": "Devuelve la estadística requerida de la previsión."}, "FORECAST.LINEAR": {"a": "(x; conocido_y; conocido_x)", "d": "Calcula o predice un valor futuro en una tendencia lineal usando valores existentes"}, "FREQUENCY": {"a": "(datos; grupos)", "d": "Calcula la frecuencia con la que ocurre un valor dentro de un rango de valores y devuelve una matriz vertical de números con más de un elemento que grupos"}, "GAMMA": {"a": "(x)", "d": "Devuelve los valores de la función gamma"}, "GAMMADIST": {"a": "(x; alfa; beta; acumulado)", "d": "Devuelve la distribución gamma"}, "GAMMA.DIST": {"a": "(x; alfa; beta; acumulado)", "d": "Devuelve la distribución gamma"}, "GAMMAINV": {"a": "(prob; alfa; beta)", "d": "Devuelve el inverso de la distribución gamma acumulativa: si p = DISTR.GAMMA(x,...), entonces INV.GAMMA(p,...) = x"}, "GAMMA.INV": {"a": "(probabilidad; alfa; beta)", "d": "Devuelve el inverso de la distribución gamma acumulativa: si p = DISTR.GAMMA.N(x,...), entonces INV.GAMMA(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Devuelve el logaritmo natural de la función gamma"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Devuelve el logaritmo natural de la función gamma"}, "GAUSS": {"a": "(x)", "d": "Devuelve un 0,5 menos que la distribución acumulativa normal estándar"}, "GEOMEAN": {"a": "(número1; [número2]; ...)", "d": "Devuelve la media geométrica de una matriz o rango de datos numéricos positivos"}, "GROWTH": {"a": "(conocido_y; [conocido_x]; [nueva_matriz_x]; [constante])", "d": "Devuelve números en una tendencia de crecimiento exponencial coincidente con puntos de datos conocidos"}, "HARMEAN": {"a": "(número1; [número2]; ...)", "d": "Devuelve la media armónica de un conjunto de números positivos: el recíproco de la media aritmética de los recíprocos"}, "HYPGEOM.DIST": {"a": "(muestra_éxito; núm_de_muestra; población_éxito; núm_de_población; acumulado)", "d": "Devuelve la distribución hipergeométrica"}, "HYPGEOMDIST": {"a": "(muestra_éxito; núm_de_muestra; población_éxito; núm_de_población)", "d": "Devuelve la distribución hipergeométrica"}, "INTERCEPT": {"a": "(conocido_y; conocido_x)", "d": "Calcula el punto en el cual una línea formará intersección con el eje Y usando una línea de regresión optimizada trazada a través de los valores conocidos de X e Y"}, "KURT": {"a": "(número1; [número2]; ...)", "d": "Devuelve la curtosis de un conjunto de datos."}, "LARGE": {"a": "(matriz; k)", "d": "Devuelve el valor k-ésimo mayor de un conjunto de datos. <PERSON><PERSON> e<PERSON><PERSON><PERSON>, el trigésimo número más grande"}, "LINEST": {"a": "(conocido_y; [conocido_x]; [const]; [stats])", "d": "Devuelve estadísticas que describen una tendencia lineal que coincide con puntos de datos conocidos, mediante una línea recta usando el método de mínimos cuadrados"}, "LOGEST": {"a": "(conocido_y; [conocido_x]; [constante]; [estadística])", "d": "Devuelve estadísticas que describen una curva exponencial, coincidente con puntos de datos conocidos"}, "LOGINV": {"a": "(probabilidad; media; desv_estándar)", "d": "Devuelve el inverso de la función de distribución logarítmico-normal acumulativa de x, donde ln(x) se distribuye de forma normal con los parámetros Media y desv_estándar"}, "LOGNORM.DIST": {"a": "(x; media; desv_estándar; acumulado)", "d": "Devuelve la distribución logarítmico-normal de x, donde ln(x) se distribuye normalmente con los parámetros media y desv_estándar"}, "LOGNORM.INV": {"a": "(probabilidad; media; desv_estándar)", "d": "Devuelve el inverso de la distribución logarítmico-normal de x, donde ln(x) se distribuye de forma normal con los parámetros Media y desv_estándar"}, "LOGNORMDIST": {"a": "(x; media; desv_estándar)", "d": "Devuelve la distribución logarítmico-normal acumulativa de x, donde In(x) se distribuye de forma normal con los parámetros Media y desv_estándar"}, "MAX": {"a": "(número1; [número2]; ...)", "d": "Devuelve el valor máximo de una lista de valores. Omite los valores lógicos y el texto"}, "MAXA": {"a": "(valor1; [valor2]; ...)", "d": "Devuelve el valor máximo de un conjunto de valores. Incluye valores lógicos y texto"}, "MAXIFS": {"a": "(rango_max; rango_criterios; criterios; ...)", "d": "Devuelve el valor máximo entre las celdas de un determinado conjunto de condiciones o criterios"}, "MEDIAN": {"a": "(número1; [número2]; ...)", "d": "Devuelve la mediana o el número central de un conjunto de números"}, "MIN": {"a": "(número1; [número2]; ...)", "d": "Devuelve el valor mínimo de una lista de valores. Omite los valores lógicos y el texto"}, "MINA": {"a": "(valor1; [valor2]; ...)", "d": "Devuelve el valor mínimo de un conjunto de valores. Incluye valores lógicos y texto"}, "MINIFS": {"a": "(rango_min; rango_criterios; criterios; ...)", "d": "Devuelve el valor mínimo entre las celdas de un determinado conjunto de condiciones o criterios"}, "MODE": {"a": "(número1; [número2]; ...)", "d": "Devuelve el valor más frecuente o que más se repite en una matriz o rango de datos"}, "MODE.MULT": {"a": "(número1; [número2]; ...)", "d": "Devuelve una matriz vertical de los valores más frecuente o repetitivos de una matriz o rango de datos. Para una matriz horizontal, use =TRANSPONER(MODA.VARIOS(número1,número2,...))"}, "MODE.SNGL": {"a": "(número1; [número2]; ...)", "d": "Devuelve el valor más frecuente o repetitivo de una matriz o rango de datos"}, "NEGBINOM.DIST": {"a": "(núm_fracasos; núm_éxitos; prob_éxito; acumulado)", "d": "Devuelve la distribución binomial negativa, la probabilidad de encontrar núm_fracasos antes que núm_éxito, con probabilidad probabilidad_s de éxito"}, "NEGBINOMDIST": {"a": "(núm_fracasos; núm_éxitos; prob_éxito)", "d": "Devuelve la distribución binomial negativa, la probabilidad de encontrar núm_fracasos antes que núm_éxito, con la probabilidad probabilidad_éxito de éxito"}, "NORM.DIST": {"a": "(x; media; desv_estándar; acumulado)", "d": "Devuelve la distribución normal para la media  y la desviación estándar especificadas"}, "NORMDIST": {"a": "(x; media; desv_estándar; acum)", "d": "Devuelve la distribución acumulativa normal para la media y desviación estándar especificadas"}, "NORM.INV": {"a": "(probabilidad; media; desv_estándar)", "d": "Devuelve el inverso de la distribución acumulativa normal para la media y desviación estándar especificadas"}, "NORMINV": {"a": "(probabilidad; media; desv_estándar)", "d": "Devuelve el inverso de la distribución acumulativa normal para la media y desviación estándar especificadas"}, "NORM.S.DIST": {"a": "(z; acumulado)", "d": "Devuelve la distribución normal estándar (tiene una media de cero y una desviación estándar de uno)"}, "NORMSDIST": {"a": "(z)", "d": "Devuelve la distribución normal estándar acumulativa. Tiene una media de cero y una desviación estándar de uno"}, "NORM.S.INV": {"a": "(probabilidad)", "d": "Devuelve el inverso de la distribución normal estándar acumulativa. Tiene una media de cero y una desviación estándar de uno"}, "NORMSINV": {"a": "(probabilidad)", "d": "Devuelve el inverso de la distribución normal estándar acumulativa. Tiene una media de cero y una desviación estándar de uno"}, "PEARSON": {"a": "(matriz1; matriz2)", "d": "Devuelve el coeficiente de correlación producto o momento r de <PERSON>, r"}, "PERCENTILE": {"a": "(matriz; k)", "d": "Devuelve el percentil k-ésimo de los valores de un rango"}, "PERCENTILE.EXC": {"a": "(matriz; k)", "d": "Devuelve el percentil k-ésimo de los valores de un rango, donde k está en el rango 0..1, exclusivo"}, "PERCENTILE.INC": {"a": "(matriz; k)", "d": "Devuelve el percentil k-ésimo de los valores de un rango, donde k está en el rango 0..1, inclusive"}, "PERCENTRANK": {"a": "(matriz; x; [cifra_significativa])", "d": "Devuelve el rango de un valor en un conjunto de datos como porcentaje del conjunto"}, "PERCENTRANK.EXC": {"a": "(matriz; x; [cifra_significativa])", "d": "Devuelve la jerarquía de un valor en un conjunto de datos como un porcentaje del conjunto de datos como un porcentaje (0..1, exclusivo) del conjunto de datos"}, "PERCENTRANK.INC": {"a": "(matriz; x; [cifra_significativa])", "d": "Devuelve la jerarquía de un valor en un conjunto de datos como un porcentaje del conjunto de datos como un porcentaje (0..1, inclusive) del conjunto de datos"}, "PERMUT": {"a": "(número; tamaño)", "d": "Devuelve el número de permutaciones para un número determinado de objetos que pueden ser seleccionados de los objetos totales"}, "PERMUTATIONA": {"a": "(número; número_elegido)", "d": "Devuelve la cantidad de permutaciones de una cantidad determinada de objetos (con repeticiones) que pueden seleccionarse del total de objetos"}, "PHI": {"a": "(x)", "d": "Devuelve el valor de la función de densidad para una distribución normal estándar"}, "POISSON": {"a": "(x; media; acumulado)", "d": "Devuelve la distribución de Poisson"}, "POISSON.DIST": {"a": "(x; media; acumulado)", "d": "Devuelve la distribución de Poisson"}, "PROB": {"a": "(rango_x; rango_probabilidad; límite_inf; [límite_sup])", "d": "Devuelve la probabilidad de que los valores de un rango se encuentren entre dos límites o sean iguales a un límite inferior"}, "QUARTILE": {"a": "(matriz; cuartil)", "d": "Devuelve el cuartil de un conjunto de datos"}, "QUARTILE.INC": {"a": "(matriz; cuartil)", "d": "Devuelve el cuartil de un conjunto de datos en función de los valores del percentil de 0..1, inclusive"}, "QUARTILE.EXC": {"a": "(matriz; cuartil)", "d": "Devuelve el cuartil de un conjunto de datos en función de los valores del percentil de 0..1, exclusivo"}, "RANK": {"a": "(número; referencia; [orden])", "d": "Devuelve la jerarquía de un número dentro de una lista: su tamaño depende de los otros valores de la lista"}, "RANK.AVG": {"a": "(número; referencia; [orden])", "d": "Devuelve la jerarquía de un número dentro de una lista de números: su tamaño en relación con otros valores de la lista; si más de un valor tiene la misma jerarquía, se devuelve el promedio de jerarquía"}, "RANK.EQ": {"a": "(número; referencia; [orden])", "d": "Devuelve la jerarquía de un número dentro de una lista de números: su tamaño en relación con otros valores de la lista; si más de un valor tiene la misma jerarquía, se devuelve la jerarquía superior de ese conjunto de valores"}, "RSQ": {"a": "(conocido_y; conocido_x)", "d": "Devuelve el cuadrado del coeficiente del momento de correlación del producto Pearson de los puntos dados"}, "SKEW": {"a": "(número1; [número2]; ...)", "d": "Devuelve el sesgo de una distribución: una caracterización del grado de asimetría de una distribución alrededor de su media"}, "SKEW.P": {"a": "(número1; [número2]; ...)", "d": "Devuelve el sesgo de una distribución basado en una población: una caracterización del grado de asimetría de una distribución alrededor de su media"}, "SLOPE": {"a": "(conocido_y; conocido_x)", "d": "Devuelve la pendiente de una línea de regresión lineal de los puntos dados"}, "SMALL": {"a": "(matriz; k)", "d": "Devuelve el valor k-ésimo menor de un conjunto de datos. <PERSON>r eje<PERSON>lo, el trigésimo número menor"}, "STANDARDIZE": {"a": "(x; media; desv_estándar)", "d": "Devuelve un valor normalizado de una distribución caracterizada por una media y desviación estándar"}, "STDEV": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviación estándar de una muestra (se omiten los valores lógicos y el texto de la muestra)"}, "STDEV.P": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviación estándar en función de la población total proporcionada como argumentos (omite los valores lógicos y el texto)"}, "STDEV.S": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviación estándar en función de una muestra (omite los valores lógicos y el texto)"}, "STDEVA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la desviación estándar de una muestra, incluidos valores lógicos y texto. El texto y el valor lógico FALSO tienen el valor 0. El valor lógico VERDADERO tiene el valor 1."}, "STDEVP": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviación estándar de la población total proporcionada como argumentos (se omiten los valores lógicos y el texto)"}, "STDEVPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la desviación estándar a partir de toda una población, incluidos valores lógicos y texto. El texto y el valor lógico FALSO tienen el valor 0. El valor lógico VERDADERO tiene el valor 1."}, "STEYX": {"a": "(conocido_y; conocido_x)", "d": "Devuelve el error típico del valor de Y previsto para cada X de la regresión"}, "TDIST": {"a": "(x; grados_de_libertad; colas)", "d": "Devuelve la distribución t de Student "}, "TINV": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de dos colas de la distribución t de Student"}, "T.DIST": {"a": "(x; grados_de_libertad; acumulado)", "d": "Devuelve la distribución t de Student de cola izquierda"}, "T.DIST.2T": {"a": "(x; grado<PERSON>_<PERSON>_libertad)", "d": "Devuelve la distribución t de Student de dos colas"}, "T.DIST.RT": {"a": "(x; grado<PERSON>_<PERSON>_libertad)", "d": "Devuelve la distribución t de Student de cola derecha"}, "T.INV": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de cola izquierda de la distribución t de Student"}, "T.INV.2T": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de dos colas de la distribución t de Student"}, "T.TEST": {"a": "(matriz1; matriz2; colas; tipo)", "d": "Devuelve la probabilidad asociada con la prueba t de Student"}, "TREND": {"a": "(conocido_y; [conocido_x]; [nueva_matriz_x]; [constante])", "d": "Devuelve números en una tendencia lineal que coincide con puntos de datos conocidos, usando el método de mínimos cuadrados"}, "TRIMMEAN": {"a": "(matriz; porcentaje)", "d": "Devuelve la media de la porción interior de un conjunto de valores de datos"}, "TTEST": {"a": "(matriz1; matriz2; colas; tipo)", "d": "Devuelve la probabilidad asociada con la prueba t de Student"}, "VAR": {"a": "(número1; [número2]; ...)", "d": "Calcula la varianza de una muestra (se omiten los valores lógicos y el texto de la muestra)"}, "VAR.P": {"a": "(número1; [número2]; ...)", "d": "Calcula la varianza en función de la población total (omite los valores lógicos y el texto)"}, "VAR.S": {"a": "(número1; [número2]; ...)", "d": "Calcula la varianza en función de una muestra (omite los valores lógicos y el texto)"}, "VARA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la varianza de una muestra, incluyendo valores lógicos y texto. Los valores lógicos y el texto con valor FALSO tiene valor asignado 0, los de valor lógico VERDADERO tienen valor 1"}, "VARP": {"a": "(número1; [número2]; ...)", "d": "Calcula la varianza de la población total (se omiten los valores lógicos y el texto de la población)"}, "VARPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la varianza de la población total, incluyendo valores lógicos y texto. Los valores lógicos y el texto con valor FALSO tienen el valor asignado 0, los de valor lógico VERDADERO tienen valor 1"}, "WEIBULL": {"a": "(x; alfa; beta; acumulado)", "d": "Devuelve la probabilidad de <PERSON>"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; acumulado)", "d": "Devuelve la probabilidad de una variable aleatoria siguiendo una distribución de Weibull"}, "Z.TEST": {"a": "(matriz; x; [sigma])", "d": "Devuelve el valor P de una cola de una prueba z"}, "ZTEST": {"a": "(matriz; x; [sigma])", "d": "Devuelve el valor P de una cola de una prueba z"}, "ACCRINT": {"a": "(emisión; primer_interés; liquidación; tasa; par; frecuencia; [base]; [método_calc])", "d": "Devuelve el interés devengado de un valor bursátil que paga intereses periódicos."}, "ACCRINTM": {"a": "(emisión; liquidación; tasa; par; [base])", "d": "Devuelve el interés devengado para un valor bursátil que paga intereses al vencimiento"}, "AMORDEGRC": {"a": "(costo; fecha_compra; primer_período; valor_residual; período; tasa; [base])", "d": "Devuelve la depreciación lineal prorrateada de un activo para cada período contable especificado."}, "AMORLINC": {"a": "(costo; fecha_compra; primer_período; valor_residual; período; tasa; [base])", "d": "Devuelve la depreciación lineal prorrateada de un activo para cada período contable especificado."}, "COUPDAYBS": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve el número de días del inicio del período nominal hasta la fecha de liquidación"}, "COUPDAYS": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve el número de días en el período nominal que contiene la fecha de liquidación"}, "COUPDAYSNC": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve el número de días de la fecha de liquidación hasta la siguiente fecha nominal"}, "COUPNCD": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve la próxima fecha nominal después de la fecha de liquidación"}, "COUPNUM": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve el número de cupones pagables entre la fecha de liquidación y la fecha de vencimiento"}, "COUPPCD": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve la fecha de cupón anterior antes de la fecha de liquidación"}, "CUMIPMT": {"a": "(tasa; nper; va; período_inicial; período_final; tipo)", "d": "Devuelve el pago de intereses acumulativo entre dos períodos"}, "CUMPRINC": {"a": "(tasa; nper; va; período_inicial; período_final; tipo)", "d": "Devuelve el principal acumulado pagado de un préstamo entre dos períodos"}, "DB": {"a": "(costo; valor_residual; vida; período; [mes])", "d": "Devuelve la depreciación de un activo durante un período específico usando el método de depreciación de saldo fijo"}, "DDB": {"a": "(costo; valor_residual; vida; período; [factor])", "d": "Devuelve la depreciación de un activo en un período específico mediante el método de depreciación por doble disminución de saldo u otro método que se especifique"}, "DISC": {"a": "(liquidación; vencimiento; pr; amortización; [base])", "d": "Devuelve la tasa de descuento del valor bursátil"}, "DOLLARDE": {"a": "(dólar_fraccional; fracción)", "d": "Convierte un precio en dólares expresado como fracción en un precio en dólares expresado como número decimal"}, "DOLLARFR": {"a": "(dólar_decimal; fracción)", "d": "Convierte un precio en dólares expresado como número decimal en un precio en dólares expresado como fracción"}, "DURATION": {"a": "(liquidación; vencimiento; cupón; rdto; frecuencia; [base])", "d": "Devuelve la duración anual de un valor bursátil con pagos de interés periódicos"}, "EFFECT": {"a": "(tasa_nominal; núm_per_año)", "d": "Devuelve la tasa de interés anual efectiva"}, "FV": {"a": "(tasa; nper; pago; [va]; [tipo])", "d": "Devuelve el valor futuro de una inversión basado en pagos periódicos y constantes, y una tasa de interés también constante."}, "FVSCHEDULE": {"a": "(principal; programación)", "d": "Devuelve el valor futuro de un principal inicial después de aplicar una serie de tasas de interés compuestas"}, "INTRATE": {"a": "(liquidación; vencimiento; inversión; amortización; [base])", "d": "Devuelve la tasa de interés para la inversión total en un valor bursátil"}, "IPMT": {"a": "(tasa; período; nper; va; [vf]; [tipo])", "d": "Devuelve el interés pagado por una inversión durante un período determinado, basado en pagos periódicos y constantes y una tasa de interés constante"}, "IRR": {"a": "(valores; [estimar])", "d": "Devuelve la tasa interna de retorno de una inversión para una serie de valores en efectivo"}, "ISPMT": {"a": "(tasa; período; nper; va)", "d": "Devuelve el interés de un préstamo de pagos directos"}, "MDURATION": {"a": "(liquidación; vencimiento; cupón; rdto; frecuencia; [base])", "d": "Devuelve la duración modificada de Macauley para un valor bursátil con un valor nominal asumido de 100 $"}, "MIRR": {"a": "(valores; tasa_financiamiento; tasa_reinversión)", "d": "Devuelve la tasa interna de retorno para una serie de flujos de efectivo periódicos, considerando costo de la inversión e interés al volver a invertir el efectivo"}, "NOMINAL": {"a": "(tasa_efect; núm_per_año)", "d": "Devuelve la tasa de interés nominal anual"}, "NPER": {"a": "(tasa; pago; va; [vf]; [tipo])", "d": "Devuelve el número de pagos de una inversión, basado en pagos constantes y periódicos y una tasa de interés constante"}, "NPV": {"a": "(tasa; valor1; [valor2]; ...)", "d": "Devuelve el valor neto presente de una inversión a partir de una tasa de descuento y una serie de pagos futuros (valores negativos) y entradas (valores positivos)"}, "ODDFPRICE": {"a": "(liquidación; vencimiento; emisión; primer_cupón; tasa; rdto; amortización; frecuencia; [base])", "d": "Devuelve el precio de un valor nominal de 100 $ de un valor bursátil con un período inicial impar"}, "ODDFYIELD": {"a": "(liquidación; vencimiento; emisión; primer_cupón; tasa; pr; amortización; frecuencia; [base])", "d": "Devuelve el rendimiento de un valor bursátil con un primer período impar"}, "ODDLPRICE": {"a": "(liquidación; vencimiento; último_interés; tasa; rdto; amortización; frecuencia; [base])", "d": "Devuelve el precio por un valor nominal de 100 $ de un valor bursátil con un período final impar"}, "ODDLYIELD": {"a": "(liquidación; vencimiento; último_interés; tasa; pr; amortización; frecuencia; [base])", "d": "Devuelve la amortización de un valor bursátil con un período final impar"}, "PDURATION": {"a": "(tasa; va; vf)", "d": "Devuelve la cantidad de períodos necesarios para que una inversión alcance un valor especificado"}, "PMT": {"a": "(tasa; nper; va; [vf]; [tipo])", "d": "Calcula el pago de un préstamo basado en pagos y tasa de interés constantes"}, "PPMT": {"a": "(tasa; período; nper; va; [vf]; [tipo])", "d": "Devuelve el pago del capital de una inversión determinada, basado en pagos constantes y periódicos, y una tasa de interés constante"}, "PRICE": {"a": "(liquidación; vencimiento; tasa; rdto; amortización; frecuencia; [base])", "d": "Devuelve el precio por 100 $ de valor nominal de un valor bursátil que paga una tasa de interés periódica"}, "PRICEDISC": {"a": "(liquidación; vencimiento; descuento; amortización; [base])", "d": "Devuelve el precio por 100 $ de un valor nominal de un valor bursátil con descuento"}, "PRICEMAT": {"a": "(liquidación; vencimiento; emisión; tasa; rdto; [base])", "d": "Devuelve el precio por 100 $ de un valor nominal que genera intereses al vencimiento"}, "PV": {"a": "(tasa; nper; pago; [vf]; [tipo])", "d": "Devuelve el valor presente de una inversión: la suma total del valor actual de una serie de pagos futuros"}, "RATE": {"a": "(nper; pago; va; [vf]; [tipo]; [estimar])", "d": "Devuelve la tasa de interés por período de un préstamo o una inversión. Por ejemplo, use 6%/4 para pagos trimestrales al 6% TPA"}, "RECEIVED": {"a": "(liquidación; vencimiento; inversión; descuento; [base])", "d": "Devuelve la cantidad recibida al vencimiento para un valor bursátil completamente invertido"}, "RRI": {"a": "(nper; va; vf)", "d": "Devuelve una tasa de interés equivalente para el crecimiento de una inversión"}, "SLN": {"a": "(costo; valor_residual; vida)", "d": "Devuelve la depreciación por método directo de un activo en un período dado"}, "SYD": {"a": "(costo; valor_residual; vida; período)", "d": "Devuelve la depreciación por método de anualidades de un activo durante un período específico"}, "TBILLEQ": {"a": "(liquidación; vencimiento; descuento)", "d": "Devuelve el rendimiento para un bono equivalente a una letra de tesorería"}, "TBILLPRICE": {"a": "(liquidación; vencimiento; descuento)", "d": "Devuelve el precio de un valor nominal de 100 $ para una letra de tesorería"}, "TBILLYIELD": {"a": "(liquidación; vencimiento; pr)", "d": "Devuelve el rendimiento de una letra de tesorería"}, "VDB": {"a": "(costo; valor_residual; vida; período_inicial; período_final; [factor]; [sin_cambios])", "d": "Devuelve la depreciación de un activo para cualquier período especificado, incluyendo períodos parciales, usando el método de depreciación por doble disminución del saldo u otro método que especifique"}, "XIRR": {"a": "(valores; fechas; [estimar])", "d": "Devuelve la tasa interna de retorno para un flujo de caja que no es necesariamente periódico"}, "XNPV": {"a": "(tasa; valores; fechas)", "d": "Devuelve el valor neto actual para un flujo de caja que no es necesariamente periódico"}, "YIELD": {"a": "(liquidación; vencimiento; tasa; pr; amortización; frecuencia; [base])", "d": "Devuelve el rendimiento de un valor bursátil que obtiene intereses periódicos"}, "YIELDDISC": {"a": "(liquidación; vencimiento; pr; amortización; [base])", "d": "Devuelve el rendimiento anual para el valor bursátil con descuento. Por ejemplo, una letra de tesorería"}, "YIELDMAT": {"a": "(liquidación; vencimiento; emisión; tasa; pr; [base])", "d": "Devuelve el interés anual de un valor que genera intereses al vencimiento"}, "ABS": {"a": "(número)", "d": "Devuelve el valor absoluto de un número, es decir, un número sin signo"}, "ACOS": {"a": "(número)", "d": "Devuelve el arcoseno de un número, en radianes, dentro del rango de 0 a Pi. El arcoseno es el ángulo cuyo coseno es Número"}, "ACOSH": {"a": "(número)", "d": "Devuelve el coseno hiperbólico inverso de un número"}, "ACOT": {"a": "(número)", "d": "Devuelve el arco tangente de un número en radianes dentro del rango de 0 a Pi."}, "ACOTH": {"a": "(número)", "d": "Devuelve la cotangente hiperbólica inversa de un número"}, "AGGREGATE": {"a": "(núm_función; opciones; ref1; ...)", "d": "Devuelve un agregado de una lista o base de datos"}, "ARABIC": {"a": "(texto)", "d": "Convierte un número romano en arábigo"}, "ASC": {"a": "(texto)", "d": "Para los idiomas que empleen juegos de caracteres de dos bytes (DBCS), convierte los caracteres de ancho completo (de dos bytes) en caracteres de ancho medio (de un byte)"}, "ASIN": {"a": "(número)", "d": "Devuelve el arcoseno de un número en radianes, dentro del rango -Pi/2 a Pi/2"}, "ASINH": {"a": "(número)", "d": "Devuelve el seno hiperbólico inverso de un número"}, "ATAN": {"a": "(número)", "d": "Devuelve el arco tangente de un número en radianes, dentro del rango -Pi/2 a Pi/2"}, "ATAN2": {"a": "(coord_x; coord_y)", "d": "Devuelve el arco tangente de las coordenadas X e Y especificadas, en un valor en radianes comprendido entre -Pi y Pi, excluyendo -Pi"}, "ATANH": {"a": "(número)", "d": "Devuelve la tangente hiperbólica inversa de un número"}, "BASE": {"a": "(númer<PERSON>; raíz; [longitud_mín])", "d": "Convierte un número en una representación de texto con la base dada"}, "CEILING": {"a": "(número; cifra_significativa)", "d": "Redondea un número hacia arriba, hasta el múltiplo significativo más cercano"}, "CEILING.MATH": {"a": "(número; [cifra_significativa]; [moda])", "d": "Redondea un número hacia arriba, al entero más cercano o al múltiplo significativo más cercano"}, "CEILING.PRECISE": {"a": "( x; [significado])", "d": "Devuelve un número que se redondea hacia arriba al entero más cercano o al múltiplo de significación más cercano."}, "COMBIN": {"a": "(número; tamaño)", "d": "Devuelve el número de combinaciones para un número determinado de elementos"}, "COMBINA": {"a": "(número; número_elegido)", "d": "Devuelve la cantidad de combinaciones con repeticiones de una cantidad determinada de elementos"}, "COS": {"a": "(número)", "d": "Devuelve el coseno de un ángulo"}, "COSH": {"a": "(número)", "d": "Devuelve el coseno hiperbólico de un número"}, "COT": {"a": "(número)", "d": "Devuelve la cotangente de un ángulo"}, "COTH": {"a": "(número)", "d": "Devuelve la cotangente hiperbólica de un número"}, "CSC": {"a": "(número)", "d": "Devuelve la cosecante de un ángulo"}, "CSCH": {"a": "(número)", "d": "Devuelve la cosecante hiperbólica de un ángulo"}, "DECIMAL": {"a": "(número; raíz)", "d": "Convierte una representación de texto de un número en una base dada en un número decimal"}, "DEGREES": {"a": "(á<PERSON><PERSON>)", "d": "Convierte radianes en grados"}, "ECMA.CEILING": {"a": "( x; significado)", "d": "Redondea el número hasta el múltiplo de significación más cercano"}, "EVEN": {"a": "(número)", "d": "Redondea un número positivo hacia arriba y un número negativo hacia abajo hasta el próximo entero par. Los números negativos se ajustan alejándolos de cero"}, "EXP": {"a": "(número)", "d": "Devuelve e elevado a la potencia de un número determinado"}, "FACT": {"a": "(número)", "d": "Devuelve el factorial de un número, igual a 1*2*3*...*Número"}, "FACTDOUBLE": {"a": "(número)", "d": "Devuelve el factorial doble de un número"}, "FLOOR": {"a": "(número; cifra_significativa)", "d": "Redondea un número hacia abajo, hasta el múltiplo significativo más cercano"}, "FLOOR.PRECISE": {"a": "( x; [significado])", "d": "Devuelve un número que se redondea hacia abajo al entero más cercano o al múltiplo de significación más cercano."}, "FLOOR.MATH": {"a": "(número; [cifra_significativa]; [moda])", "d": "Redondea un número hacia abajo, al entero más cercano o al múltiplo significativo más cercano"}, "GCD": {"a": "(número1; [número2]; ...)", "d": "Devuelve el máximo común divisor"}, "INT": {"a": "(número)", "d": "Redondea un número hasta el entero inferior más próximo"}, "ISO.CEILING": {"a": "(número; [significado])", "d": "Devuelve un número que se redondea hacia arriba al entero más cercano o al múltiplo de significación más cercano, independientemente del signo del número. Sin embargo, si el número o el significado es cero, se devuelve cero."}, "LCM": {"a": "(número1; [número2]; ...)", "d": "Devuelve el mínimo común múltiplo"}, "LN": {"a": "(número)", "d": "Devuelve el logaritmo natural de un número"}, "LOG": {"a": "(número; [base])", "d": "Devuelve el logaritmo de un número en la base especificada"}, "LOG10": {"a": "(número)", "d": "Devuelve el logaritmo en base 10 de un número"}, "MDETERM": {"a": "(matriz)", "d": "Devuelve el determinante matricial de una matriz"}, "MINVERSE": {"a": "(matriz)", "d": "Devuelve la matriz inversa de una matriz dentro de una matriz"}, "MMULT": {"a": "(matriz1; matriz2)", "d": "Devuelve el producto matricial de dos matrices, una matriz con el mismo número de filas que Matriz1 y columnas que Matriz2"}, "MOD": {"a": "(número; núm_divisor)", "d": "Proporciona el residuo después de dividir un número por un divisor"}, "MROUND": {"a": "(n<PERSON><PERSON><PERSON>; múltiplo)", "d": "Devuelve un número redondeado al múltiplo deseado"}, "MULTINOMIAL": {"a": "(número1; [número2]; ...)", "d": "Devuelve el polinomio de un conjunto de números"}, "MUNIT": {"a": "(dimension)", "d": "Devuelve la matriz de la unidad para la dimensión especificada"}, "ODD": {"a": "(número)", "d": "Redondea un número positivo hacia arriba y un número negativo hacia abajo hasta el próximo entero impar"}, "PI": {"a": "()", "d": "Devuelve el valor Pi, 3,14159265358979, con precisión de 15 dígitos"}, "POWER": {"a": "(número; potencia)", "d": "Devuelve el resultado de elevar el número a una potencia"}, "PRODUCT": {"a": "(número1; [número2]; ...)", "d": "Multiplica todos los números especificados como argumentos"}, "QUOTIENT": {"a": "(numerador; denominador)", "d": "Devuelve la parte entera de una división"}, "RADIANS": {"a": "(á<PERSON><PERSON>)", "d": "Convierte grados en radianes"}, "RAND": {"a": "()", "d": "Devuelve un número aleatorio mayor o igual que 0 y menor que 1, distribuido (cambia al actualizarse)"}, "RANDARRAY": {"a": "([filas]; [columnas]; [min]; [max]; [entero])", "d": "Devuelve una matriz de números aleatorios"}, "RANDBETWEEN": {"a": "(inferior; superior)", "d": "Devuelve el número aleatorio entre los números que especifique"}, "ROMAN": {"a": "(número; [forma])", "d": "Convierte un número arábigo en romano, en formato de texto"}, "ROUND": {"a": "(número; núm_decimales)", "d": "Redondea un número al número de decimales especificado"}, "ROUNDDOWN": {"a": "(número; núm_decimales)", "d": "Redondea un número hacia abajo, hacia cero"}, "ROUNDUP": {"a": "(número; núm_decimales)", "d": "Redondea un número hacia arriba, en dirección contraria a cero"}, "SEC": {"a": "(número)", "d": "Devuelve la secante de un ángulo"}, "SECH": {"a": "(número)", "d": "Devuelve la secante hiperbólica de un ángulo"}, "SERIESSUM": {"a": "(x; n; m; coeficientes)", "d": "Devuelve la suma de una serie de potencias basándose en la fórmula"}, "SIGN": {"a": "(número)", "d": "Devuelve el signo de un número: 1, si el número es positivo; cero, si el número es cero y -1, si el número es negativo"}, "SIN": {"a": "(número)", "d": "Devuelve el seno de un ángulo determinado"}, "SINH": {"a": "(número)", "d": "Devuelve el seno hiperbólico de un número"}, "SQRT": {"a": "(número)", "d": "Devuelve la raíz cuadrada de un número"}, "SQRTPI": {"a": "(número)", "d": "Devuelve la raíz cuadrada de (número * Pi)"}, "SUBTOTAL": {"a": "(núm_función; ref1; ...)", "d": "Devuelve un subtotal dentro de una lista o una base de datos"}, "SUM": {"a": "(número1; [número2]; ...)", "d": "Suma todos los números en un rango de celdas"}, "SUMIF": {"a": "(rango; criterio; [rango_suma])", "d": "Suma las celdas que cumplen determinado criterio o condición"}, "SUMIFS": {"a": "(rango_suma; rango_criterios; criterio; ...)", "d": "Suma las celdas que cumplen un determinado conjunto de condiciones o criterios"}, "SUMPRODUCT": {"a": "(matriz1; [matriz2]; [matriz3]; ...)", "d": "Devuelve la suma de los productos de rangos o matrices correspondientes"}, "SUMSQ": {"a": "(número1; [número2]; ...)", "d": "Devuelve la suma de los cuadrados de los argumentos. Los argumentos pueden ser números, matrices, nombres o referencias a celdas que contengan números"}, "SUMX2MY2": {"a": "(matriz_x; matriz_y)", "d": "Suma las diferencias entre cuadrados de dos rangos o matrices correspondientes"}, "SUMX2PY2": {"a": "(matriz_x; matriz_y)", "d": "Devuelve la suma de total de las sumas de cuadrados de números en dos rangos o matrices correspondientes"}, "SUMXMY2": {"a": "(matriz_x; matriz_y)", "d": "Suma los cuadrados de las diferencias en dos rangos correspondientes de matrices"}, "TAN": {"a": "(número)", "d": "Devuelve la tangente de un ángulo"}, "TANH": {"a": "(número)", "d": "Devuelve la tangente hiperbólica de un número"}, "TRUNC": {"a": "(número; [núm_decimales])", "d": "Convierte un número decimal a uno entero al quitar la parte decimal o de fracción"}, "ADDRESS": {"a": "(fila; columna; [abs]; [a1]; [hoja])", "d": "Crea una referencia de celda en forma de texto una vez especificados los números de fila y columna"}, "CHOOSE": {"a": "(núm_índice; valor1; [valor2]; ...)", "d": "Elige un valor o una acción de una lista de valores a partir de un número de índice"}, "COLUMN": {"a": "([ref])", "d": "Devuelve el número de columna de una referencia"}, "COLUMNS": {"a": "(matriz)", "d": "Devuelve el número de columnas en una matriz o referencia"}, "FORMULATEXT": {"a": "(referencia)", "d": "Devuelve una fórmula como una cadena"}, "HLOOKUP": {"a": "(valor_buscado; matriz_buscar_en; indicador_filas; [ordenado])", "d": "Busca en la primera fila de una tabla o matriz de valores y devuelve el valor en la misma columna desde una fila especificada"}, "HYPERLINK": {"a": "(ubicación_del_vínculo; [nombre_descriptivo])", "d": "Crea un acceso directo o salto que abre un documento guardado en el disco duro, en un servidor de red o en Internet"}, "INDEX": {"a": "(matriz; núm_fila; [núm_columna]!ref; núm_fila; [núm_columna]; [núm_área])", "d": "Devuelve un valor o referencia de la celda en la intersección de una fila y columna en particular, en un rango especificado"}, "INDIRECT": {"a": "(ref; [a1])", "d": "Devuelve una referencia especificada por un valor de texto"}, "LOOKUP": {"a": "(valor_buscado; vector_de_comparación; [vector_resultado]!valor_buscado; matriz)", "d": "Busca valores de un rango de una columna o una fila o desde una matriz. Proporcionado para compatibilidad con versiones anteriores"}, "MATCH": {"a": "(valor_buscado; matriz_buscada; [tipo_de_coincidencia])", "d": "Devuelve la posición relativa de un elemento en una matriz, que coincide con un valor dado en un orden especificado"}, "OFFSET": {"a": "(ref; filas; columnas; [alto]; [ancho])", "d": "Devuelve una referencia a un rango que es un número especificado de filas y columnas de una referencia dada"}, "ROW": {"a": "([ref])", "d": "Devuelve el número de fila de una referencia"}, "ROWS": {"a": "(matriz)", "d": "Devuelve el número de filas de una referencia o matriz"}, "TRANSPOSE": {"a": "(matriz)", "d": "Devuelve un rango vertical de celdas como un rango horizontal, o viceversa"}, "UNIQUE": {"a": "(matriz; [by_col]; [exactly_once])", "d": " Devuelve los valores únicos de un rango o matriz."}, "VLOOKUP": {"a": "(valor_buscado; matriz_buscar_en; indicador_columnas; [ordenado])", "d": "Busca un valor en la primera columna de la izquierda de una tabla y luego devuelve un valor en la misma fila desde una columna especificada. De forma predeterminada, la tabla se ordena de forma ascendente"}, "XLOOKUP": {"a": "(valor_buscado; matriz_buscada; matriz_devuelta; [si_no_se_encuentra]; [modo_de_coincidencia]; [modo_de_búsqueda])", "d": "Busca una coincidencia en un rango o una matriz y devuelve el elemento correspondiente de un segundo rango o matriz. De forma predeterminada, se usa una coincidencia exacta"}, "CELL": {"a": "(info_type; [reference])", "d": "Devuelve información sobre el formato, la ubicación o el contenido de una celda"}, "ERROR.TYPE": {"a": "(valor_de_error)", "d": "Devuelve un número que coincide con un valor de error."}, "ISBLANK": {"a": "(valor)", "d": "Comprueba si se refiere a una celda vacía y devuelve VERDADERO o FALSO"}, "ISERR": {"a": "(valor)", "d": "Comprueba si un valor es un error diferente de #N/D y devuelve VERDADERO o FALSO"}, "ISERROR": {"a": "(valor)", "d": "Comprueba si un valor es un error y devuelve VERDADERO o FALSO"}, "ISEVEN": {"a": "(número)", "d": "Devuelve verdadero si el número es par"}, "ISFORMULA": {"a": "(referencia)", "d": "Comprueba si la referencia es a una celda que contiene una fórmula y devuelve VERDADERO o FALSO"}, "ISLOGICAL": {"a": "(valor)", "d": "Comprueba si un valor es un valor lógico (VERDADERO o FALSO), y devuelve VERDADERO o FALSO"}, "ISNA": {"a": "(valor)", "d": "Comprueba si un valor de error es #N/A (valor no aplicable) y devuelve VERDADERO o FALSO"}, "ISNONTEXT": {"a": "(valor)", "d": "Comprueba si un valor no es texto (las celdas en blanco no son texto), y devuelve VERDADERO o FALSO"}, "ISNUMBER": {"a": "(valor)", "d": "Comprueba si un valor es un número y devuelve VERDADERO o FALSO"}, "ISODD": {"a": "(número)", "d": "Devuelve VERDADERO si el número es impar"}, "ISREF": {"a": "(valor)", "d": "Comprueba si valor es una referencia y devuelve VERDADERO o FALSO"}, "ISTEXT": {"a": "(valor)", "d": "Comprueba si un valor es texto y devuelve VERDADERO o FALSO"}, "N": {"a": "(valor)", "d": "Convierte valores no numéricos en números, fechas en números de serie, VERDADERO en 1 y cualquier otro en 0 (cero)"}, "NA": {"a": "()", "d": "Devuelve el valor de error #N/A (valor no disponible)"}, "SHEET": {"a": "([valor])", "d": "Devuelve el número de la hoja a la que se hace referencia"}, "SHEETS": {"a": "([referencia])", "d": "Devuelve la cantidad de hojas de una referencia"}, "TYPE": {"a": "(valor)", "d": "Devuelve un entero que representa el tipo de datos de un valor: número = 1; texto = 2; valor lógico = 4; valor de error = 16; matriz = 64; datos compuestos = 128"}, "AND": {"a": "(valor_lógico1; [valor_lógico2]; ...)", "d": "Comprueba si todos los argumentos son VERDADEROS, y devuelve VERDADERO si todos los argumentos son VERDADEROS"}, "FALSE": {"a": "()", "d": "Devuelve el valor lógico FALSO"}, "IF": {"a": "(prue<PERSON>_lógica; [valor_si_verdadero]; [valor_si_falso])", "d": "Comprueba si se cumple una condición y devuelve una valor si se evalúa como VERDADERO y otro valor si se evalúa como FALSO"}, "IFS": {"a": "(prue<PERSON>_lógica; valor_si_verdadero; ...)", "d": "Comprueba si se cumplen una o más condiciones y devuelve un valor correspondiente a la primera condición verdadera"}, "IFERROR": {"a": "(valor; valor_si_error)", "d": "Devuelve valor_si_error si la expresión es un error y el valor de la expresión no lo es"}, "IFNA": {"a": "(valor; valor_si_nd)", "d": "Devuelve el valor que especificas, si la expresión se convierte en #N/A. De lo contrario, devuelve el resultado de la expresión"}, "NOT": {"a": "(valor_lógico)", "d": "Cambia FALSO por VERDADERO y VERDADERO por FALSO"}, "OR": {"a": "(valor_lógico1; [valor_lógico2]; ...)", "d": "Comprueba si alguno de los argumentos es VERDADERO, y devuelve VERDADERO o FALSO. Devuelve FALSO si todos los argumentos son FALSOS"}, "SWITCH": {"a": "(expresión; valor1; resultado1; [predeterminado_o_valor2]; [resultado2]; ...)", "d": "Evalúa una expresión con una lista de valores y devuelve el resultado correspondiente al primer valor coincidente. Si no hay ninguna coincidencia, se devuelve un valor predeterminado opcional"}, "TRUE": {"a": "()", "d": "Devuelve el valor lógico VERDADERO"}, "XOR": {"a": "(lógico1; [lógico2]; ...)", "d": "Devuelve una 'Exclusive Or' lógica de todos los argumentos"}, "TEXTBEFORE": {"a": "(texto, delimitador, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Devuelve el texto que está antes de delimitar caracteres."}, "TEXTAFTER": {"a": "(texto, delimitador, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Devuelve el texto que está después de delimitar caracteres."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Divide el texto en filas o columnas con delimitadores."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ajusta un vector de fila o columna después de un número especificado de valores."}, "VSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Apilar verticalmente matrices en una matriz."}, "HSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Apilar horizontalmente matrices en una matriz."}, "CHOOSEROWS": {"a": "(matriz, row_num1, [row_num2], ...)", "d": "Devuelve filas de una matriz o referencia."}, "CHOOSECOLS": {"a": "(matriz, col_num1, [col_num2], ...)", "d": "Devuelve columnas de una matriz o referencia."}, "TOCOL": {"a": "(matriz, [ignorar], [scan_by_column])", "d": "Devuelve la matriz como una columna."}, "TOROW": {"a": "(matriz, [ignorar], [scan_by_column])", "d": "Devuelve la matriz como una fila."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Envuelve un vector de fila o columna después de un número especificado de valores."}, "TAKE": {"a": "(matriz, filas, [columnas])", "d": "Devuelve filas o columnas desde el inicio o el final de la matriz."}, "DROP": {"a": "(matriz, filas, [columnas])", "d": "Quita filas o columnas desde el inicio o el final de la matriz."}}
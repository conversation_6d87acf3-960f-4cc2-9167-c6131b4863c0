{"DATE": {"a": "(year; month; day)", "d": "Retorna la xifra que representa la data en codi de data i hora"}, "DATEDIF": {"a": "(data_inicial; data_final; unitat)", "d": "Retorna la diferència entre dos valors de data (data inicial i data final), basada en l'interval (unitat) especificat"}, "DATEVALUE": {"a": "(date_text)", "d": "Converteix una data en forma de text en una xifra que representa la data en el codi de data i hora"}, "DAY": {"a": "(serial_number)", "d": "Retorna el dia del mes, un valor de l'1 al 31."}, "DAYS": {"a": "(data_fi; data_inici)", "d": "Retorna el nombre de dies entre les dues dates."}, "DAYS360": {"a": "(data_inicial; data_final; [mètode])", "d": "Retorna el nombre de dies entre dues dates basant-se en un any de 360 dies (dotze mesos de 30 dies)"}, "EDATE": {"a": "(data_inicial; mesos)", "d": "Retorna el número de sèrie de la data, que és el nombre indicat de mesos abans o després de la data inicial"}, "EOMONTH": {"a": "(data_inicial; mesos)", "d": "Retorna el número de sèrie del darrer dia del mes abans o després d'un nombre especificat de mesos"}, "HOUR": {"a": "(serial_number)", "d": "Retorna l'hora com una xifra del 0 (12:00 a. m.) al 23 (11:00 p. m.)."}, "ISOWEEKNUM": {"a": "(date)", "d": "Torna el número de setmana ISO de l'any d'una data determinada"}, "MINUTE": {"a": "(serial_number)", "d": "Retorna els minuts, una xifra del 0 al 59."}, "MONTH": {"a": "(serial_number)", "d": "<PERSON><PERSON>na el me<PERSON>, una xifra de l'1 (gener) al 12 (desembre)."}, "NETWORKDAYS": {"a": "(data_inicial; data_final; [festius])", "d": "Retorna el nombre de dies laborables sencers entre dues dates"}, "NETWORKDAYS.INTL": {"a": "(data_inicial; data_final; [cap_de_setmana]; [dies_festius])", "d": "Retorna el nombre de dies feiners entre dues dates amb paràmetres de cap de setmana personalitzats"}, "NOW": {"a": "()", "d": "Retorna la data i l'hora actuals amb format de data i hora."}, "SECOND": {"a": "(serial_number)", "d": "Retorna els segons, una xifra del 0 al 59."}, "TIME": {"a": "(hora; minut; segon)", "d": "Converteix les hores, els minuts i els segons especificats com a números en un número de sèrie, amb format d'hora"}, "TIMEVALUE": {"a": "(time_text)", "d": "Converteix una hora de text en un número de sèrie per a una hora, una xifra de 0 (12:00:00) a 0,999988426 (11:59:59). Aplica un format d'hora a la xifra després d'introduir la fórmula"}, "TODAY": {"a": "()", "d": "Retorna la data actual amb format de data."}, "WEEKDAY": {"a": "(núm_de_sèrie; [tipus])", "d": "Retorna un número de l'1 al 7 que identifica el dia de la setmana d'una data."}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "Retorna el número de setmana de l'any"}, "WORKDAY": {"a": "(data_inicial; dies; [festius])", "d": "Retorna el número de sèrie de la data abans o després d'un nombre de dies laborables especificat"}, "WORKDAY.INTL": {"a": "(data_inicial; dies; [cap_de_setmana]; [dies_festius])", "d": "Retorna el número de sèrie de la data abans o després d'un nombre especificat de dies feiners amb paràmetres personalitzats de cap de setmana"}, "YEAR": {"a": "(serial_number)", "d": "Retorna l'any d'una data, un enter de l'interval de 1.900 a 9.999."}, "YEARFRAC": {"a": "(data_inicial; data_final; [base])", "d": "Retorna la fracció de l'any que representa el nombre de dies sencers entre la data_inicial i la data_final"}, "BESSELI": {"a": "(x; n)", "d": "Retorna la funció Bessel In(x) modificada"}, "BESSELJ": {"a": "(x; n)", "d": "Retorna la funció Bessel Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Retorna la funció Bessel Kn(x) modificada"}, "BESSELY": {"a": "(x; n)", "d": "Retorna la funció Bessel Yn(x)"}, "BIN2DEC": {"a": "(nombre)", "d": "Converteix un nombre binari en decimal"}, "BIN2HEX": {"a": "(nombre; [posicions])", "d": "Converteix un nombre binari en hexadecimal"}, "BIN2OCT": {"a": "(nombre; [posicions])", "d": "Converteix un nombre binari en octal"}, "BITAND": {"a": "(número1; número2)", "d": "<PERSON>na un bit a bit 'i' de dos números"}, "BITLSHIFT": {"a": "(nombre; canvi_quantitat)", "d": "Retorna un número desplaçat a l'esquerra per shift_amount bits"}, "BITOR": {"a": "(número1; número2)", "d": "<PERSON>na un bit a bit '<PERSON>' de dos números"}, "BITRSHIFT": {"a": "(nombre; canvi_quantitat)", "d": "Retorna un número desplaçat a la dreta per shift_amount bits"}, "BITXOR": {"a": "(número1; número2)", "d": "Torna un bit a bit \"Exclusiu o\" de dos números"}, "COMPLEX": {"a": "(nombre_real; i_nombre; [sufix])", "d": "Converteix coeficients reals i imaginaris en un nombre complex"}, "CONVERT": {"a": "(nombre; des_d'unitat; a_unitat)", "d": "Converteix un nombre d'un sistema de mesura a un altre"}, "DEC2BIN": {"a": "(nombre; [posicions])", "d": "Converteix un nombre decimal en binari"}, "DEC2HEX": {"a": "(nombre; [posicions])", "d": "Converteix un nombre decimal en hexadecimal"}, "DEC2OCT": {"a": "(nombre; [posicions])", "d": "Converteix un nombre decimal en octal"}, "DELTA": {"a": "(nombre1; [nombre2])", "d": "Prova si dos nombres són iguals"}, "ERF": {"a": "(límit_inferior; [límit_superior])", "d": "Retorna la funció d'error"}, "ERF.PRECISE": {"a": "(X)", "d": "Retorna la funció d'error"}, "ERFC": {"a": "(x)", "d": "Retorna la funció d'error complementària"}, "ERFC.PRECISE": {"a": "(X)", "d": "Retorna la funció d'error complementària"}, "GESTEP": {"a": "(nombre; [pas])", "d": "Prova si un nombre és major que un valor de llindar"}, "HEX2BIN": {"a": "(nombre; [posicions])", "d": "Converteix un nombre hexadecimal en binari"}, "HEX2DEC": {"a": "(nombre)", "d": "Converteix un nombre hexadecimal en decimal"}, "HEX2OCT": {"a": "(nombre; [posicions])", "d": "Converteix un nombre hexadecimal en octal"}, "IMABS": {"a": "(inombre)", "d": "Retorna el valor absolut (mòdul) d'un nombre complex"}, "IMAGINARY": {"a": "(inombre)", "d": "Retorna el coeficient imaginari d'un nombre complex"}, "IMARGUMENT": {"a": "(inombre)", "d": "Retorna l'argument q, un angle expressat en radians"}, "IMCONJUGATE": {"a": "(inombre)", "d": "Retorna el conjugat complex d'un nombre complex"}, "IMCOS": {"a": "(inombre)", "d": "Retorna el cosinus d'un nombre complex"}, "IMCOSH": {"a": "(número)", "d": "Retorna el cosinus hiperbòlic d'un número complex"}, "IMCOT": {"a": "(número)", "d": "Torna la cotangent d'un número complex"}, "IMCSC": {"a": "(número)", "d": "Torna la cosecant d'un número complex"}, "IMCSCH": {"a": "(número)", "d": "Torna la cosecant hiperbòlica d'un número complex"}, "IMDIV": {"a": "(inombre1; inombre2)", "d": "Retorna el quocient de dos nombres complexos"}, "IMEXP": {"a": "(inombre)", "d": "Retorna el valor exponencial d'un nombre complex"}, "IMLN": {"a": "(inombre)", "d": "Retorna el logaritme natural d'un nombre complex"}, "IMLOG10": {"a": "(inombre)", "d": "Retorna el logaritme de base 10 d'un nombre complex"}, "IMLOG2": {"a": "(inombre)", "d": "Retorna el logaritme de base 2 d'un nombre complex"}, "IMPOWER": {"a": "(inombre; nombre)", "d": "Retorna un nombre complex elevat a una potència d'enter"}, "IMPRODUCT": {"a": "(inombre1; [inombre2]; ...)", "d": "Retorna el producte d'1 a 255 nombres complexos"}, "IMREAL": {"a": "(inombre)", "d": "Retorna el coeficient real d'un nombre complex"}, "IMSEC": {"a": "(número)", "d": "Torna la secant d'un número complex"}, "IMSECH": {"a": "(número)", "d": "Torna la secant hiperbòlica d'un número complex"}, "IMSIN": {"a": "(inombre)", "d": "Retorna el sinus d'un nombre complex"}, "IMSINH": {"a": "(número)", "d": "Retorna el sinus hiperbòlic d'un número complex"}, "IMSQRT": {"a": "(inombre)", "d": "Retorna l'arrel quadrada d'un nombre complex"}, "IMSUB": {"a": "(inombre1; inombre2)", "d": "Retorna la diferència de dos nombres complexos"}, "IMSUM": {"a": "(inombre1; [inombre2]; ...)", "d": "Retorna la suma de nombres complexos"}, "IMTAN": {"a": "(número)", "d": "Torna la tangent d'un número complex"}, "OCT2BIN": {"a": "(nombre; [posicions])", "d": "Converteix un nombre octal en binari"}, "OCT2DEC": {"a": "(nombre)", "d": "Converteix un nombre octal en decimal"}, "OCT2HEX": {"a": "(nombre; [posicions])", "d": "Converteix un nombre octal en hexadecimal"}, "DAVERAGE": {"a": "(base_de_dades; camp; criteris)", "d": "Obté la mitjana dels valors d'una columna en una llista o base de dades que coincideixen amb les condicions especificades"}, "DCOUNT": {"a": "(base_de_dades; camp; criteris)", "d": "Compta les cel·les que contenen números al camp (columna) de registres a la base de dades que coincideixen amb les condicions especificades"}, "DCOUNTA": {"a": "(base_de_dades; camp; criteris)", "d": "Compta les cel·les que no estan en blanc del camp (columna) de registres de la base de dades que coincideixen amb les condicions especificades"}, "DGET": {"a": "(base_de_dades; camp; criteris)", "d": "Extreu un únic registre d'una base de dades que coincideix amb les condicions especificades"}, "DMAX": {"a": "(base_de_dades; camp; criteris)", "d": "Retorna el número més gran del camp (columna) de registres de la base de dades que coincideixen amb les condicions especificades"}, "DMIN": {"a": "(base_de_dades; camp; criteris)", "d": "Retorna el número més petit del camp (columna) de registres de la base de dades que coincideixen amb les condicions especificades"}, "DPRODUCT": {"a": "(base_de_dades; camp; criteris)", "d": "Multiplica els valors del camp (columna) de registres de la base de dades que coincideixen amb les condicions especificades"}, "DSTDEV": {"a": "(base_de_dades; camp; criteris)", "d": "Calcula la desviació estàndard a partir d'una mostra de les entrades seleccionades d'una base de dades"}, "DSTDEVP": {"a": "(base_de_dades; camp; criteris)", "d": "Calcula la desviació estàndard a partir de tota la població d'entrades seleccionades a la base de dades"}, "DSUM": {"a": "(base_de_dades; camp; criteris)", "d": "Suma els números al camp (columna) de registres de la base de dades que coincideixen amb les condicions especificades"}, "DVAR": {"a": "(base_de_dades; camp; criteris)", "d": "Calcula la variança a partir d'una mostra de les entrades seleccionades a una base de dades"}, "DVARP": {"a": "(base_de_dades; camp; criteris)", "d": "Calcula la variança a partir de tota la població d'entrades seleccionades a la base de dades"}, "CHAR": {"a": "(número)", "d": "Retorna el caràcter especificat pel número de codi a partir del conjunt de caràcters definit a l'ordinador"}, "CLEAN": {"a": "(text)", "d": "Suprimeix tots els caràcters no imprimibles del text"}, "CODE": {"a": "(text)", "d": "Retorna un codi numèric per al primer caràcter d'una cadena de text, en el conjunt de caràcters utilitzat per l'ordinador"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Uneix diverses cadenes de text en una de sola"}, "CONCAT": {"a": "(text1; ...)", "d": "Concatena una llista o un interval de cadenes de text"}, "DOLLAR": {"a": "(número; [decimals])", "d": "Converteix un número en text, utilitzant format de moneda"}, "EXACT": {"a": "(text1; text2)", "d": "Comprova si dues cadenes de text són exactament iguals i retorna CERT o FALS. IGUAL fa distinció entre majúscules i minúscules"}, "FIND": {"a": "(text_cercat; dins_text; [núm_inicial])", "d": "Retorna la posició inicial d'una cadena de text a dins d'una altra cadena de text. BUSCA fa distinció entre majúscules i minúscules"}, "FINDB": {"a": "(text_cercat; dins_text; [núm_inicial])", "d": "Cerca la subcadena especificada (cadena-1) dins d'una cadena (cadena-2) i està destinada a llenguatges amb el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc."}, "FIXED": {"a": "(número; [decimals]; [sense_comes])", "d": "Arrodoneix un número al nombre de decimals especificat i retorna el resultat com a text amb o sense comes"}, "LEFT": {"a": "(text; [nombre_caràcters])", "d": "Retorna el número especificat de caràcters des del començament d'una cadena de text"}, "LEFTB": {"a": "(text; [nombre_caràcters])", "d": "Extreu la subcadena de la cadena especificada començant pel caràcter de l'esquerra i està destinada a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc."}, "LEN": {"a": "(text)", "d": "Retorna el número de caràcters d'una cadena de text"}, "LENB": {"a": "(text)", "d": "Analitza la cadena especificada i retorna el nombre de caràcters que conté i està pensat per a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc."}, "LOWER": {"a": "(text)", "d": "Converteix totes les lletres d'una cadena de text en minúscules"}, "MID": {"a": "(text; posició_inicial; nombre_caràcters)", "d": "Retorna els caràcters del centre d'una cadena de text, donada una posició inicial i la longitud"}, "MIDB": {"a": "(text; posició_inicial; nombre_caràcters)", "d": "Extreu els caràcters de la cadena especificada començant des de qualsevol posició i està pensat per a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc."}, "NUMBERVALUE": {"a": "(text; [separador_decimal]; [separador_grup])", "d": "Converteix text en número de manera local i independent"}, "PROPER": {"a": "(text)", "d": "Converteix una cadena de text en majúscules; la primera lletra de cada paraula en majúscules i la resta en minúscules"}, "REPLACE": {"a": "(text_original; núm_inicial; nombre_caràcters; text_nou)", "d": "Substitueix una part d'una cadena de text per una cadena de text diferent"}, "REPLACEB": {"a": "(text_original; núm_inicial; nombre_caràcters; text_nou)", "d": "Situa un conjunt de caràcters, basat en el nombre de caràcters i la posició inicial que especifiqueu, amb un nou conjunt de caràcters i està pensat per a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc."}, "REPT": {"a": "(text; nú<PERSON><PERSON>_de_vegades)", "d": "Repeteix el text un número determinat de vegades. <PERSON><PERSON><PERSON>eu REPETEIX per omplir una cel·la amb el número d'ocurrències d'una cadena de text"}, "RIGHT": {"a": "(text; [nombre_caràcters])", "d": "Retorna el número especificat de caràcters des de la fi d'una cadena de text"}, "RIGHTB": {"a": "(text; [nombre_caràcters])", "d": "Extreu una subcadena d'una cadena que comença des del caràcter de més a la dreta, basant-se en el nombre especificat de caràcters i està destinat a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc."}, "SEARCH": {"a": "(text_cercat; dins_text; [núm_inicial])", "d": "Retorna el número del caràcter on es troba un caràcter concret o una cadena de text, llegint d'esquerra a dreta (no fa distinció entre majúscules i minúscules)"}, "SEARCHB": {"a": "(text_cercat; dins_text; [núm_inicial])", "d": "Retorna la ubicació de la subcadena especificada en una cadena i està destinada a llenguatges que utilitzen el joc de caràcters de doble byte (DBCS) com el japonès, el xinès, el coreà, etc."}, "SUBSTITUTE": {"a": "(text; text_original; text_nou; [núm_ocurrència])", "d": "Substitueix el text existent amb text nou en una cadena de text"}, "T": {"a": "(valor)", "d": "Comprova si un valor és text i retorna el text si ho és o bé cometes dobles (sense text) si no ho és"}, "TEXT": {"a": "(valor; format)", "d": "Converteix un valor en text, amb un format de número específic"}, "TEXTJOIN": {"a": "(delimitador; ignora_buit; text1; ...)", "d": "Concatena una llista o un interval de cadenes de text mitjançant un delimitador"}, "TRIM": {"a": "(text)", "d": "Sup<PERSON>ei<PERSON> tots els espais d'una cadena de text excepte els espais individuals entre les paraules"}, "UNICHAR": {"a": "(número)", "d": "Retorna el caràcter Unicode referenciat pel valor numèric determinat"}, "UNICODE": {"a": "(text)", "d": "Retorna el nombre (punt de codi) corresponent al primer caràcter del text"}, "UPPER": {"a": "(text)", "d": "Converteix totes les lletres d'una cadena de text en majúscules"}, "VALUE": {"a": "(text)", "d": "Converteix una cadena de text que representa un número en un número"}, "AVEDEV": {"a": "(número1; [número2]; ...)", "d": "Retorna la mitjana de les desviacions absolutes de la mitjana dels punts de dades. Els arguments poden ser números o noms, matrius o referències que contenen números"}, "AVERAGE": {"a": "(número1; [número2]; ...)", "d": "Retorna la mitjana (aritmètica) dels seus arguments, que poden ser números o noms, matrius o referències que continguin números"}, "AVERAGEA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna la mitjana (mitjana aritmè<PERSON>) dels seus arguments; el text i el valor FALS es calculen com a 0; CERT es calcula com a 1. Els arguments poden ser números, noms, matrius o referències"}, "AVERAGEIF": {"a": "(interval; criteris; [interval_mitjana])", "d": "Troba la mitjana (aritmètica) per a les cel·les especificades per una condició o un criteri determinats"}, "AVERAGEIFS": {"a": "(interval_mitjana; interval_criteris; criteris; ...)", "d": "Troba la mitjana (aritmètica) per a les cel·les especificades per un conjunt de condicions o criteris determinat"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "Retorna la funció de densitat de probabilitat beta acumulativa"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Retorna l'invers de la funció de densitat de probabilitat beta acumulativa (DIST.BETA)"}, "BETA.DIST": {"a": "(x; alfa; beta; acumulat; [A]; [B])", "d": "Retorna la funció de distribució de densitat de probabilitat beta"}, "BETA.INV": {"a": "(probabilitat; alfa; beta; [A]; [B])", "d": "Retorna l'invers de la funció de densitat de probabilitat beta acumulativa (DISTRIBUCIO.BETA)"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Retorna la probabilitat d'una variable aleatòria discreta seguint una distribució binomial"}, "BINOM.DIST": {"a": "(nombre_èxits; proves; probabilitat_èxit; acumulat)", "d": "Retorna la probabilitat d'una variable aleatòria discreta seguint una distribució binomial"}, "BINOM.DIST.RANGE": {"a": "(intents; prob_èxit; nombre_èxit; [nombre_èxit2])", "d": "Torna la probabilitat d'un resultat de prova utilitzant una distribució binominal"}, "BINOM.INV": {"a": "(proves; probabilitat_èxit; alfa)", "d": "Retorna el valor més petit la distribució binomial acumulativa del qual és major o igual que un valor de criteri"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "Retorna la probabilitat d'una variable aleatòria contínua seguint una distribució chi quadrat d'una única cua"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "Retorna l'invers d'una probabilitat determinada, d'una única cua, amb una distribució chi quadrat"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "Retorna la prova d'independència: el valor de la distribució chi quadrat de l'estadística i els graus de llibertat corresponents"}, "CHISQ.DIST": {"a": "(x; graus_llibertat; acumulat)", "d": "Retorna la probabilitat de cua esquerra de la distribució khi quadrat"}, "CHISQ.DIST.RT": {"a": "(x; graus_llibertat)", "d": "Retorna la probabilitat de cua dreta de la distribució khi quadrat"}, "CHISQ.INV": {"a": "(probabilitat; graus_llibertat)", "d": "Retorna l'invers de la probabilitat de cua esquerra amb una distribució khi quadrat"}, "CHISQ.INV.RT": {"a": "(probabilitat; graus_llibertat)", "d": "Retorna l'invers de la probabilitat de cua dreta amb una distribució khi quadrat"}, "CHISQ.TEST": {"a": "(interval_real; interval_esperat)", "d": "Retorna la prova d'independència: el valor de la distribució chi quadrat de l'estadística i els graus de llibertat corresponents"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "Retorna l'interval de confiança de la mitjana d'una població, utilitzant una distribució normal"}, "CONFIDENCE.NORM": {"a": "(alfa; desv_estàndard; mida)", "d": "Retorna l'interval de confiança de la mitjana d'una població utilitzant una distribució normal"}, "CONFIDENCE.T": {"a": "(alfa; desv_estàndard; mida)", "d": "Retorna l'interval de confiança de la mitjana d'una població utilitzant una distribució en T de Student"}, "CORREL": {"a": "(matriu1; matriu2)", "d": "Retorna el coeficient de correlació de dos conjunts de dades"}, "COUNT": {"a": "(valor1; [valor2]; ...)", "d": "Compta el nombre de cel·les d'un interval que contenen nombres"}, "COUNTA": {"a": "(valor1; [valor2]; ...)", "d": "Compta el nombre de cel·les d'un interval que no són buides"}, "COUNTBLANK": {"a": "(interval)", "d": "Compta el nombre de cel·les buides que hi ha en un interval de cel·les especificat"}, "COUNTIF": {"a": "(interval; criteris)", "d": "Compta el nombre de cel·les d'un interval que compleixen la condició especificada"}, "COUNTIFS": {"a": "(interval_criteris; criteris; ...)", "d": "Recompta el número de cel·les especificades per un conjunt de condicions o criteris determinat"}, "COVAR": {"a": "(array1; array2)", "d": "Retorna la covariància, que és la mitjana dels productes de les desviacions, dels parells de punts de dades de dos conjunts de dades"}, "COVARIANCE.P": {"a": "(matriu1; matriu2)", "d": "Retorna la covariança de la població, que és la mitjana dels productes de les desviacions, dels parells de punts de dades a dos conjunts de dades"}, "COVARIANCE.S": {"a": "(matriu1; matriu2)", "d": "Retorna la covariança de la mostra, que és la mitjana dels productes de les desviacions, dels parells de punts de dades de dos conjunts de dades"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "Retorna el valor més petit la distribució binomial acumulativa del qual és major o igual que un valor de criteri"}, "DEVSQ": {"a": "(número1; [número2]; ...)", "d": "Retorna la suma dels quadrats de les desviacions de punts de dades en relació amb la mitjana de la mostra"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "Retorna la distribució exponencial"}, "EXPON.DIST": {"a": "(x; lambda; acumulat)", "d": "Retorna la distribució exponencial"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Retorna la distribució de probabilitat F (grau de diversitat) de cua dreta de dos conjunts de dades"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Retorna l'invers de la distribució de probabilitat F: (de cua dreta) si p = DISTR.F(x...), aleshores DISTR.F.INV(p,...) = x"}, "FTEST": {"a": "(array1; array2)", "d": "Retorna el resultat d'una prova F, la probabilitat de dues cues que les variàncies de matriu1 i matriu2 no siguin significativament diferents"}, "F.DIST": {"a": "(x; graus_llibertat1; graus_llibertat2; acumulat)", "d": "Retorna la distribució (de cua esquerra) de probabilitat F (grau de diversitat) de dos conjunts de dades"}, "F.DIST.RT": {"a": "(x; graus_llibertat1; graus_llibertat2)", "d": "Retorna la distribució (de cua dreta) de probabilitat F (grau de diversitat) de dos conjunts de dades"}, "F.INV": {"a": "(probabilitat; graus_llibertat1; graus_llibertat2)", "d": "Retorna l'invers de la distribució de probabilitat F: si p = DISTR.F.N(x...), aleshores INV.F(p,...) = x"}, "F.INV.RT": {"a": "(probabilitat; graus_llibertat1; graus_llibertat2)", "d": "Retorna l'invers (de cua dreta) de la distribució de probabilitat F: si p = DISTR.F.CD(x...), aleshores INV.F.CD(p,...) = x"}, "F.TEST": {"a": "(matriu1; matriu2)", "d": "Retorna el resultat d'una prova F, la probabilitat de dues cues que les variàncies de matriu1 i matriu2 no siguin significativament diferents"}, "FISHER": {"a": "(x)", "d": "Retorna la transformació Fisher"}, "FISHERINV": {"a": "(y)", "d": "Retorna la funció inversa de la transformació Fisher: si y = FISHER(x), aleshores PROVA.FISHER.INV(y) = x"}, "FORECAST": {"a": "(x; conegut_ys; conegut_xs)", "d": "Calcula o prediu un valor futur en una tendència lineal utilitzant valors existents"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [agreggation])", "d": "Retorna el valor previst per a una determinada data de destinació futura mitjançant el mètode de suavització exponencial ."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Retorna un interval de confiança per al valor de pronòstic en la data de destinació especificada."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Retorna la durada del patró repetitiu que una aplicació detecta per a la sèrie de temps especificada."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Retorna l’estadística sol·licitada per a la predicció."}, "FORECAST.LINEAR": {"a": "(xs; conegut_ys; conegut_xs)", "d": "Calcula o prediu un valor futur en una tendència lineal utilitzant valors existents"}, "FREQUENCY": {"a": "(dades; grups)", "d": "Calcula la freqüència amb què es produeix un valor en un interval de valors i retorna una matriu vertical de números amb més d'un element que Grups"}, "GAMMA": {"a": "(x)", "d": "Retorna el valor de la funció Gamma"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "Retorna la distribució gamma"}, "GAMMA.DIST": {"a": "(x; alfa; beta; acumulat)", "d": "Retorna la distribució gamma"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "Retorna el valor invers de la distribució gamma acumulativa: si p = DIST.GAMMA(x,...), aleshores DIST.GAMMA.INV(p,...) = x"}, "GAMMA.INV": {"a": "(probabilitat; alfa; beta)", "d": "Retorna el valor invers de la distribució gamma acumulativa: si p = DISTRIBUCIO.GAMMA(x,...), aleshores INV.GAMMA(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Retorna el logaritme natural de la funció gamma"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Retorna el logaritme natural de la funció gamma"}, "GAUSS": {"a": "(x)", "d": "Torna un 0,5 menys que la distribució acumulativa estàndard normal"}, "GEOMEAN": {"a": "(número1; [número2]; ...)", "d": "Retorna la mitjana geomètrica d'una matriu o un interval de dades numèriques positives"}, "GROWTH": {"a": "(conegut_ys; [conegut_xs]; [nova_xs]; [constant])", "d": "Retorna números en una tendència de creixement exponencial que coincideix amb punts de dades coneguts"}, "HARMEAN": {"a": "(número1; [número2]; ...)", "d": "Retorna la mitjana harmònica d'un conjunt de dades de números positius: el recíproc de la mitjana aritmètica dels recíprocs"}, "HYPGEOM.DIST": {"a": "(mostra_èxits; número_mostra; població_èxit; número_població; acumulat)", "d": "Retorna la distribució hipergeomètrica"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "Retorna la distribució hipergeomètrica"}, "INTERCEPT": {"a": "(conegut_ys; conegut_xs)", "d": "Calcula el punt en què la línia intersecarà amb l'eix Y utilitzant una línia de regressió optimitzada traçada a través dels valors coneguts d'X i Y"}, "KURT": {"a": "(número1; [número2]; ...)", "d": "Retorna la curtosi d'un conjunt de dades"}, "LARGE": {"a": "(matriu; k)", "d": "Retorna el valor k-èsim més gran d'un conjunt de dades. Per exemple, el cinquè número més gran"}, "LINEST": {"a": "(conegut_ys; [conegut_xs]; [constant]; [estadística])", "d": "Retorna estadístiques que descriuen una tendència lineal que coincideix amb punts de dades coneguts, per mitjà d'una línia recta fent servir el mètode dels mínims quadres"}, "LOGEST": {"a": "(conegut_ys; [conegut_xs]; [constant]; [estadística])", "d": "Retorna estadístiques que descriuen una corba exponencial, que coincideix amb punts de dades coneguts"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "Retorna l'invers de la distribució logarítmica-normal acumulativa de x, on ln(x) es distribueix de forma normal amb els paràmetres Mitjana i Desv_estàndard"}, "LOGNORM.DIST": {"a": "(x; mitjana; desv_estàndard; acumulat)", "d": "Retorna la distribució logarítmica normal d'x, on ln(x) es distribueix de manera normal amb els paràmetres Mitjana i Desv_estàndard"}, "LOGNORM.INV": {"a": "(probabilitat; mitjana; desv_estàndard)", "d": "Retorna l'invers de la distribució logarítmica-normal acumulativa d'x, on ln(x) es distribueix de forma normal amb els paràmetres Mitjana i Desv_estàndard"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "Retorna la distribució logarítmica normal de x, on ln(x) es distribueix de forma normal amb els paràmetres Mitjana i Desv_estàndard"}, "MAX": {"a": "(número1; [número2]; ...)", "d": "Retorna el valor més gran d'un conjunt de valors. Ignora els valors lògics i el text"}, "MAXA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna el valor més gran d'un conjunt de valors. No ignora els valors lògics ni el text"}, "MAXIFS": {"a": "(interval_max; interval_de_criteris; criteris; ...)", "d": "Retorna el valor màxim de totes les cel·les especificat per un conjunt determinat de condicions o de criteris"}, "MEDIAN": {"a": "(número1; [número2]; ...)", "d": "Retorna la mitjana o el número central d'un conjunt de números"}, "MIN": {"a": "(número1; [número2]; ...)", "d": "Retorna el número més petit d'un conjunt de valors. Ignora els valors lògics i el text"}, "MINA": {"a": "(valor1; [valor2]; ...)", "d": "Retorna el valor més petit d'un conjunt de valors. No ignora els valors lògics ni el text"}, "MINIFS": {"a": "(interval_min; interval_de_criteris; criteris; ...)", "d": "Retorna el valor mínim de totes les cel·les especificat per un conjunt determinat de condicions o de criteris"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "Retorna el valor més freqüent o més repetitiu d'una matriu o un interval de dades"}, "MODE.MULT": {"a": "(número1; [número2]; ...)", "d": "Retorna una matriu vertical dels valors més freqüents o més repetitius d'una matriu o un interval de dades. Per a una matriu horitzontal, utilitz<PERSON> =TRANSPOSA(MODA.DIVERSOS(número1,número2,...))"}, "MODE.SNGL": {"a": "(número1; [número2]; ...)", "d": "Retorna el valor més freqüent o més repetitiu d'una matriu o un interval de dades"}, "NEGBINOM.DIST": {"a": "(nombre_fracassos; nombre_èxits; probabilitat_èxit; acumulat)", "d": "Retorna la distribució binomial negativa, la probabilitat que es produeixin els fracassos de nombre_fracassos abans de l'èxit nombre_èxits, amb la probabilitat d'èxit probabilitat_èxit"}, "NEGBINOMDIST": {"a": "(nombre_fracassos; nombre_èxits; probabilitat_èxit)", "d": "Retorna la distribució binomial negativa, la probabilitat que es produeixin els fracassos de nombre_fracassos abans de l'èxit nombre_èxits, amb probabilitat d’èxit probabilitat_èxit"}, "NORM.DIST": {"a": "(x; mitjana; desv_estàndard; acumulat)", "d": "Retorna la distribució normal de la mitjana i la desviació estàndard especificades"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Retorna la distribució normal acumulativa de la mitjana i la desviació estàndard especificades"}, "NORM.INV": {"a": "(probabilitat; mitjana; desv_estàndard)", "d": "Retorna l'invers de la distribució acumulativa normal de la mitjana i la desviació estàndard especificades"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "Retorna l'invers de la distribució acumulativa normal de la mitjana i la desviació estàndard especificades"}, "NORM.S.DIST": {"a": "(z; acumulat)", "d": "Retorna la distribució normal estàndard (té una mitjana de zero i una desviació estàndard d'u)"}, "NORMSDIST": {"a": "(z)", "d": "Retorna la distribució normal estàndard acumulativa (té una mitjana de zero i una distribució estàndard d'u)"}, "NORM.S.INV": {"a": "(probabilitat)", "d": "Retorna l'invers de la distribució normal estàndard acumulativa (té una mitjana de zero i una distribució estàndard d'u)"}, "NORMSINV": {"a": "(probability)", "d": "Retorna l'invers de la distribució normal estàndard acumulativa (té una mitjana de zero i una distribució estàndard d'u)"}, "PEARSON": {"a": "(matriu1; matriu2)", "d": "Retorna el coeficient del moment de correlació del producte Pearson, r"}, "PERCENTILE": {"a": "(array; k)", "d": "Retorna el percentil k-èsim de valors d'un interval"}, "PERCENTILE.EXC": {"a": "(matriu; k)", "d": "Retorna el percentil k-èsim de valors d'un interval en què k es troba dins l'interval 0..1, exclosos"}, "PERCENTILE.INC": {"a": "(matriu; k)", "d": "Retorna el percentil k-èsim de valors d'un interval en què k es troba dins l'interval 0..1, inclosos"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "Retorna la classificació d'un valor en un conjunt de dades com un percentatge del conjunt de dades"}, "PERCENTRANK.EXC": {"a": "(matriu; x; [xifra_significativa])", "d": "Retorna la classificació d'un valor en un conjunt de dades com un percentatge del conjunt de dades com un percentatge (0..1, exclosos) del conjunt de dades"}, "PERCENTRANK.INC": {"a": "(matriu; x; [xifra_significativa])", "d": "Retorna la classificació d'un valor en un conjunt de dades com un percentatge del conjunt de dades com un percentatge (0..1, inclosos) del conjunt de dades"}, "PERMUT": {"a": "(número; número_triat)", "d": "Retorna el nombre de permutacions d'un nombre determinat d'objectes que es poden seleccionar del total d'objectes"}, "PERMUTATIONA": {"a": "(número; número_triat)", "d": "Retorna el nombre de permutacions d'un determinat nombre d'objectes (amb repeticions) que es poden seleccionar dels objectes totals"}, "PHI": {"a": "(x)", "d": "Retorna el valor de la funció de densitat d'una distribució estàndard normal"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "Retorna la distribució de Poisson"}, "POISSON.DIST": {"a": "(x; mitjana; acumulat)", "d": "Retorna la distribució de Poisson"}, "PROB": {"a": "(interval_x; interval_probabilitat; límit_inf; [límit_sup])", "d": "Retorna la probabilitat que els valors d'un interval es trobin entre dos límits o siguin iguals que un límit inferior"}, "QUARTILE": {"a": "(array; quart)", "d": "Retorna el quartí d'un conjunt de dades"}, "QUARTILE.INC": {"a": "(mitjana; quartí)", "d": "Retorna el quartí d'un conjunt de dades basat en el percentil dels valors entre 0..1, inclosos"}, "QUARTILE.EXC": {"a": "(mitjana; quartí)", "d": "Retorna el quartí d'un conjunt de dades basat en el percentil dels valors entre 0..1, exclosos"}, "RANK": {"a": "(number; ref; [order])", "d": "Retorna la jerarquia d'una xifra en una llista: la seva mida és relativa als altres valors de la llista"}, "RANK.AVG": {"a": "(número; ref; [ordre])", "d": "Retorna la classificació d'un número en una llista: la seva mida és relativa als altres valors de la llista; si més d'un valor té la mateixa classificació, es retorna la classificació mitjana"}, "RANK.EQ": {"a": "(número; ref; [ordre])", "d": "Retorna la classificació d'un número en una llista: la seva mida és relativa als altres valors de la llista; si més d'un valor té la mateixa classificació, es retorna la classificació superior d'aquest conjunt de valors"}, "RSQ": {"a": "(conegut_ys; conegut_xs)", "d": "Retorna el quadrat del coeficient del moment de correlació del producte Pearson dels punts de dades donats"}, "SKEW": {"a": "(número1; [número2]; ...)", "d": "Retorna el biaix d'una distribució: una caracterització del grau d'asimetria d'una distribució entorn de la seva mitjana"}, "SKEW.P": {"a": "(número1; [número2]; ...)", "d": "Retorna el biaix d'una distribució basada en una població: una caracterització del grau d'asimetria d'una distribució entorn de la seva mitjana"}, "SLOPE": {"a": "(conegut_ys; conegut_xs)", "d": "Retorna la pendent d'una línia de regressió lineal dels punts de dades donats"}, "SMALL": {"a": "(matriu; k)", "d": "Retorna el valor k-èsim més petit d'un conjunt de dades. Per exemple, el cinquè número més petit"}, "STANDARDIZE": {"a": "(x; mitjana; desv_estàndard)", "d": "Retorna un valor normalitzat d'una distribució caracteritzada per una mitjana i una desviació estàndard"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "Calcula la desviació estàndard d'una mostra (ignora els valors lògics i el text de la mostra)"}, "STDEV.P": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviació estàndard de la població total especificada com a arguments (ignora els valors lògics i el text)"}, "STDEV.S": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviació estàndard d'una mostra (ignora els valors lògics i el text de la mostra)"}, "STDEVA": {"a": "(valor1; [valor2]; ...)", "d": "Fa una estimació de la desviació estàndard a partir d'una mostra, inclosos els valors lògics i el text. El text i el valor lògic FALS tenen el valor 0; el valor lògic CERT té el valor 1"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "Calcula la desviació estàndard de la població total especificada com a arguments (ignora els valors lògics i el text)"}, "STDEVPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la desviació estàndard a partir de tota una població, inclosos els valors lògics i el text. El text i el valor lògic FALS tenen el valor 0; el valor lògic CERT té el valor 1"}, "STEYX": {"a": "(conegut_ys; conegut_xs)", "d": "Retorna l'error típic del valor d'Y previst per a cada X de la regressió"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "Retorna la distribució t de Student"}, "TINV": {"a": "(probability; deg_freedom)", "d": "Retorna l'invers de la distribució t de Student"}, "T.DIST": {"a": "(x; graus_llibertat; acumulat)", "d": "Retorna la distribució T de Student de cua esquerra"}, "T.DIST.2T": {"a": "(x; graus_llibertat)", "d": "Retorna la distribució T de Student de dues cues"}, "T.DIST.RT": {"a": "(x; graus_llibertat)", "d": "Retorna la distribució T de Student de cua dreta"}, "T.INV": {"a": "(probabilitat; graus_llibertat)", "d": "Retorna l'invers de cua esquerra de la distribució T de Student"}, "T.INV.2T": {"a": "(probabilitat; graus_llibertat)", "d": "Retorna l'invers de dues cues de la distribució T de Student"}, "T.TEST": {"a": "(matriu1; matriu2; cues; tipus)", "d": "Retorna la probabilitat associada amb una prova t de Student"}, "TREND": {"a": "(conegut_ys; [conegut_xs]; [nova_xs]; [constant])", "d": "Retorna números en una tendència lineal que coincideix amb punts de dades coneguts, utilitzant el mètode dels mínims quadrats"}, "TRIMMEAN": {"a": "(matriu; percentatge)", "d": "Retorna la mitjana de la part interior d'un conjunt de valors de dades"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "Retorna la probabilitat associada amb una prova t de Student"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "Calcula la variància d'una mostra (ignora els valors lògics i el text)"}, "VAR.P": {"a": "(número1; [número2]; ...)", "d": "Calcula la variància de la població total (ignora els valors lògics i el text de la població)"}, "VAR.S": {"a": "(número1; [número2]; ...)", "d": "Calcula la variància d'una mostra (ignora els valors lògics i el text)"}, "VARA": {"a": "(valor1; [valor2]; ...)", "d": "Fa una estimació de la variància a partir d'una mostra, inclosos els valors lògics i el text. El text i el valor lògic FALS tenen el valor 0; el valor lògic CERT té el valor 1"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "Calcula la variància de la població total (ignora els valors lògics i el text de la població)"}, "VARPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la variància a partir de tota la població, inclosos els valors lògics i el text. El text i el valor lògic FALS tenen el valor 0; el valor lògic CERT té el valor 1"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "Retorna la distribució <PERSON>"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; acumulat)", "d": "Retorna la distribució <PERSON>"}, "Z.TEST": {"a": "(matriu; x; [sigma])", "d": "Retorna el valor P d'una cua d'una prova, z"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "Retorna el valor P d'una cua d'una prova, z"}, "ACCRINT": {"a": "(emissió; primer_interès; liquidació; taxa; valor_nominal; freqüència; [base]; [mètode_calc])", "d": "Retorna el cupó corregut d'un títol valor que paga interessos periòdics."}, "ACCRINTM": {"a": "(emissió; liquidació; taxa; valor_nominal; [base])", "d": "Retorna el cupó corregut per a un títol valor que paga interessos al venciment"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Retorna l'amortització lineal prorratejada d'un actiu per a cada període comptable."}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Retorna l'amortització lineal prorratejada d'un actiu per a cada període comptable."}, "COUPDAYBS": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna el nombre de dies des de l'inici del període de cupó fins a la data de liquidació"}, "COUPDAYS": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna el nombre de dies del període de cupó que conté la data de liquidació"}, "COUPDAYSNC": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna el nombre de dies des de la data de liquidació fins a la propera data de cupó"}, "COUPNCD": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna la següent data de cupó després de la data de liquidació"}, "COUPNUM": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna el nombre de cupons a pagar entre la data de liquidació i la data de venciment"}, "COUPPCD": {"a": "(liquidació; venciment; freqüència; [base])", "d": "Retorna la data de cupó anterior abans de la data de liquidació"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Retorna l'interès acumulat pagat entre dos períodes"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Retorna el capital acumulat pagat en un préstec entre dos períodes"}, "DB": {"a": "(cost; valor_residual; vida; període; [mes])", "d": "Retorna la depreciació d'un bé durant un període específic per mitjà del mètode de depreciació fixa de saldo"}, "DDB": {"a": "(cost; valor_residual; vida; període; [factor])", "d": "Retorna la depreciació d'un bé durant un període específic per mitjà del mètode de depreciació per doble disminució de saldo o un altre mètode que s'especifiqui"}, "DISC": {"a": "(liquidació; venciment; pr; amortització; [base])", "d": "Retorna la taxa de descompte d'un títol valor"}, "DOLLARDE": {"a": "(dòlar_fraccional; fracció)", "d": "Converteix un preu en dòlars expressat com a fracció en un preu en dòlars expressat com a nombre decimal"}, "DOLLARFR": {"a": "(dòlar_decimal; fracció)", "d": "Converteix un preu en dòlars expressat com a nombre decimal en un preu en dòlars expressat com a fracció"}, "DURATION": {"a": "(liquidació; venciment; cupó; rdt; freqüència; [base])", "d": "Retorna la durada anual d'un títol valor amb pagaments d'interès periòdics"}, "EFFECT": {"a": "(taxa_nominal; nombre_per_any)", "d": "Retorna la taxa d'interès efectiva anual"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "Retorna el valor futur d'una inversió en funció de pagaments periòdics i constants i un tipus d'interès constant"}, "FVSCHEDULE": {"a": "(capital; planificació)", "d": "Retorna el valor futur d'un capital inicial després d'aplicar-hi una sèrie de taxes d'interès compost"}, "INTRATE": {"a": "(liquidació; venciment; inversió; amortització; [base])", "d": "Retorna la taxa d'interès per a un títol valor completament invertit"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Retorna l'interès pagat per una inversió en un període determinat, en funció de pagaments periòdics i constants i un tipus d'interès constant"}, "IRR": {"a": "(valors; [estimació])", "d": "Retorna la taxa interna de retorn per a una sèrie de fluxos en efectiu"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Retorna l'interès pagat durant un període específic d'una inversió"}, "MDURATION": {"a": "(liquidació; venciment; cupó; rdt; freqüència; [base])", "d": "Retorna la durada modificada de Macauley per a un títol valor amb un valor nominal assumit de 100 €"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "Retorna la taxa interna de retorn per a una sèrie de fluxos d'efectiu periòdics tenint en compte el cost de la inversió i l'interès en tornar a invertir l'efectiu"}, "NOMINAL": {"a": "(taxa_efectiva; nombre_per_any)", "d": "Retorna la taxa d'interès nominal anual"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "Retorna el número de pagaments d'una inversió en funció de pagaments periòdics i constants i d’un tipus d'interès constant"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "Retorna el valor net actual d'una inversió en funció d'una taxa de descompte i d’una sèrie de pagaments futurs (valors negatius) i d'ingressos (valors positius)"}, "ODDFPRICE": {"a": "(liquidació; venciment; emissió; primer_cupó; taxa; rdt; amortització; freqüència; [base])", "d": "Retorna el preu per cada 100 € de valor nominal d'un títol valor amb un període inicial senar"}, "ODDFYIELD": {"a": "(liquidació; venciment; emissió; primer_cupó; taxa; pr; amortització; freqüència; [base])", "d": "Retorna el rendiment d'un títol valor amb un primer període senar"}, "ODDLPRICE": {"a": "(liquidació; venciment; darrer_interès; taxa; rdt; amortització; freqüència; [base])", "d": "Retorna el preu per cada 100 € de valor nominal d'un títol valor amb un període final senar"}, "ODDLYIELD": {"a": "(liquidació; venciment; darrer_interès; taxa; pr; amortització; freqüència; [base])", "d": "Retorna el rendiment d'un títol valor amb un període final senar"}, "PDURATION": {"a": "(taxa; va; vf)", "d": "Retorna el nombre de períodes necessaris per a que una inversió arribi a un valor especificat"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "Calcula el pagament d'un préstec en funció de pagaments constants i d’un tipus d'interès constant"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Retorna el pagament del capital d'una inversió determinada en funció de pagaments constants i periòdics i un tipus d'interès constant"}, "PRICE": {"a": "(liquidació; venciment; taxa; rdt; amortització; freqüència; [base])", "d": "Retorna el preu per cada 100 € de valor nominal d'un títol valor que paga interessos periòdics"}, "PRICEDISC": {"a": "(liquidació; venciment; descompte; amortització; [base])", "d": "Retorna el preu per cada 100 € de valor nominal d'un títol valor amb descompte"}, "PRICEMAT": {"a": "(liquidació; venciment; emissió; taxa; rdt; [base])", "d": "Retorna el preu per cada 100 € de valor nominal d'un títol valor que paga interessos al venciment"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "Retorna el valor actual d'una inversió: l’import total al qual ascendeix una sèrie de pagaments futurs"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "Retorna el tipus d'interès per període d'un préstec o d’una inversió. Per exemple, podeu utilitzar 6%/4 per als pagaments trimestrals al 6 % TPA"}, "RECEIVED": {"a": "(liquidació; venciment; inversió; descompte; [base])", "d": "Retorna la quantitat rebuda al venciment d'un títol valor completament invertit"}, "RRI": {"a": "(nper; va; vf)", "d": "Retorna un tipus d'interès equivalent al creixement d'una inversió"}, "SLN": {"a": "(cost; valor_residual; vida)", "d": "Retorna la depreciació per mètode directe d'un bé en un període determinat"}, "SYD": {"a": "(cost; valor_residual; vida; període)", "d": "Retorna la depreciació per mètode d'anualitats d'un bé durant un període específic"}, "TBILLEQ": {"a": "(liquidació; venciment; descompte)", "d": "Retorna el rendiment equivalent a bo d'una lletra del tresor"}, "TBILLPRICE": {"a": "(liquidació; venciment; descompte)", "d": "Retorna el preu per cada 100 € de valor nominal d'una lletra del tresor"}, "TBILLYIELD": {"a": "(liquidació; venciment; pr)", "d": "Retorna el rendiment d'una lletra del tresor"}, "VDB": {"a": "(cost; valor_residual; vida; període_inicial; període_final; [factor]; [sense_canvis])", "d": "Retorna la depreciació d'un bé per a qualsevol període especificat, inclosos els períodes parcials, utilitzant el mètode de depreciació per doble disminució del saldo o un altre mètode que especifiqueu"}, "XIRR": {"a": "(values; dates; [guess])", "d": "Retorna la taxa interna de retorn per a una planificació de fluxos d'efectiu"}, "XNPV": {"a": "(rate; values; dates)", "d": "Retorna el valor actual net per a una planificació de fluxos d'efectiu"}, "YIELD": {"a": "(liquidació; venciment; taxa; pr; amortització; freqüència; [base])", "d": "Retorna el rendiment d'un títol valor que paga interessos periòdics"}, "YIELDDISC": {"a": "(liquidació; venciment; pr; amortització; [base])", "d": "Retorna el rendiment anual per a un títol valor amb descompte. Per exemple, una lletra del tresor"}, "YIELDMAT": {"a": "(liquidació; venciment; emissió; taxa; pr; [base])", "d": "Retorna el rendiment anual d'un títol valor que genera interessos al venciment"}, "ABS": {"a": "(número)", "d": "Retorna el valor absolut d'un número, és a dir, un número sense signe"}, "ACOS": {"a": "(número)", "d": "Retorna l'arc cosinus d'un número en radians, en l'interval de 0 a Pi. L'arc cosinus és l'angle el cosinus del qual és Número"}, "ACOSH": {"a": "(número)", "d": "Retorna el cosinus hiperbòlic invers d'un número"}, "ACOT": {"a": "(número)", "d": "Torna l'arctangent d'un número, en radiants en l'interval 0 a Pi."}, "ACOTH": {"a": "(número)", "d": "Torna la cotangent hiperbòlica inversa d'un número"}, "AGGREGATE": {"a": "(funció_número; opcions; ref1; ...)", "d": "Retorna un valor afegit en una llista o base de dades"}, "ARABIC": {"a": "(text)", "d": "Converteix un numeral llatí en àrab"}, "ASC": {"a": "(text)", "d": "Per a llenguatges de doble byte (DBCS), la funció canvia caràcters d'amplada completa (doble byte) a caràcters de mitja amplada (un byte)"}, "ASIN": {"a": "(número)", "d": "Retorna l'arc sinus d'un número en radians, en l'interval de -Pi/2 a Pi/2"}, "ASINH": {"a": "(número)", "d": "Retorna el sinus hiperbòlic invers d'un número"}, "ATAN": {"a": "(número)", "d": "Retorna l'arc tangent d'un número en radians, dins de l'interval de -Pi/2 a Pi/2"}, "ATAN2": {"a": "(coord_x; coord_y)", "d": "Retorna l'arc tangent de les coordenades x i y especificades, en radians entre -Pi i Pi, excloent -Pi"}, "ATANH": {"a": "(número)", "d": "Retorna la tangent hiperbòlica inversa d'un número"}, "BASE": {"a": "(número; base; [longitud_mín])", "d": "Converteix un número en una representació de text amb la base proporcionada"}, "CEILING": {"a": "(número; xifra_significativa)", "d": "Arrodoneix un número cap amunt en el múltiple significatiu més proper"}, "CEILING.MATH": {"a": "(nombre; [xifra_significativa]; [moda])", "d": "Arrodoneix un número cap amunt, cap a l'enter més proper o el múltiple significatiu més proper"}, "CEILING.PRECISE": {"a": "(nombre; [significat])", "d": "Retorna un nombre que s'arrodoneix fins a l'enter més proper o al múltiple més proper de significat"}, "COMBIN": {"a": "(nombre; nombre_triat)", "d": "Retorna el nombre de combinacions d'un nombre determinat d'elements"}, "COMBINA": {"a": "(número; número_triat)", "d": "Retorna el nombre de combinacions amb repeticions per a un determinat nombre d'elements"}, "COS": {"a": "(número)", "d": "Retorna el cosinus d'un angle"}, "COSH": {"a": "(número)", "d": "Retorna el cosinus hiperbòlic d'un número"}, "COT": {"a": "(número)", "d": "Torna la cotangent d'un angle"}, "COTH": {"a": "(número)", "d": "Torna la cotangent hiperbòlica d'un número"}, "CSC": {"a": "(número)", "d": "Torna la cosecant d'un angle"}, "CSCH": {"a": "(número)", "d": "Torna la cosecant hiperbòlica d'un angle"}, "DECIMAL": {"a": "(número; base)", "d": "Converteix una representació de text d'un número d'una base determinada en un número decimal"}, "DEGREES": {"a": "(angle)", "d": "Converteix els radians en graus"}, "ECMA.CEILING": {"a": "(número; significat)", "d": "Arrodoneix el nombre fins al múltiple més proper d'importància"}, "EVEN": {"a": "(número)", "d": "Arrodoneix un número positiu cap amunt i un número negatiu cap avall fins a l'enter parell més proper"}, "EXP": {"a": "(número)", "d": "Retorna e elevat a la potència d'un número determinat"}, "FACT": {"a": "(número)", "d": "Retorna el factorial d'un número, igual a 1*2*3*...* Número"}, "FACTDOUBLE": {"a": "(nombre)", "d": "Retorna el factorial doble d'un nombre"}, "FLOOR": {"a": "(número; xifra_significativa)", "d": "Arrodoneix un número cap avall en el múltiple significatiu més proper"}, "FLOOR.PRECISE": {"a": "(nombre; [significat] )", "d": "Retorna un nombre que s'arrodoneix cap avall a l'enter més proper o al múltiple més proper de significat"}, "FLOOR.MATH": {"a": "(nombre; [xifra_significativa]; [moda])", "d": "Arrodoneix un número cap a baix, cap a l'enter més proper o el múltiple significatiu més proper"}, "GCD": {"a": "(nombre1; [nombre2]; ...)", "d": "Retorna el màxim comú divisor"}, "INT": {"a": "(número)", "d": "Arrodoneix un número fins a l'enter inferior més proper"}, "ISO.CEILING": {"a": "(nombre; [significat])", "d": "Retorna un nombre que s'arrodoneix fins a l'enter més proper o al múltiple més proper d'importància independentment del signe del nombre. No obstant això, si el nombre o el significat és zero, es retorna zero."}, "LCM": {"a": "(nombre1; [nombre2]; ...)", "d": "Retorna el mínim comú múltiple"}, "LN": {"a": "(número)", "d": "Retorna l'algoritme natural d'un número"}, "LOG": {"a": "(número; [base])", "d": "Retorna el logaritme d'un número en la base que especifiqueu"}, "LOG10": {"a": "(número)", "d": "Retorna l'algoritme en base 10 d'un número"}, "MDETERM": {"a": "(matriu)", "d": "Retorna el determinant matricial d'una matriu"}, "MINVERSE": {"a": "(matriu)", "d": "Retorna la matriu inversa de la matriu a dins d'una matriu"}, "MMULT": {"a": "(matriu1; matriu2)", "d": "Retorna el producte matricial de dues matrius, una matriu amb el mateix nombre de files que matriu1 i columnes que matriu2"}, "MOD": {"a": "(número; núm_divisor)", "d": "Retorna el residu després de dividir un número per un divisor"}, "MROUND": {"a": "(nombre; múltiple)", "d": "Retorna un nombre arrodonit al múltiple desitjat"}, "MULTINOMIAL": {"a": "(nombre1; [nombre2]; ...)", "d": "Retorna la distribució multinomial d'un conjunt de nombres"}, "MUNIT": {"a": "(dimensió)", "d": "Torna la matriu unitària de la dimensió especificada"}, "ODD": {"a": "(número)", "d": "Arrodoneix un número positiu cap amunt i un número negatiu cap avall, cap a l'enter imparell més proper"}, "PI": {"a": "()", "d": "Retorna el valor de Pi, 3,14159265358979, amb una precisió de 15 decimals"}, "POWER": {"a": "(número; potencia)", "d": "Retorna el resultat d'un número elevat a una potència"}, "PRODUCT": {"a": "(número1; [número2]; ...)", "d": "Multiplica tots els números especificats com a arguments"}, "QUOTIENT": {"a": "(numerador; denominador)", "d": "Retorna la part entera d'una divisió"}, "RADIANS": {"a": "(angle)", "d": "Converteix els graus en radians"}, "RAND": {"a": "()", "d": "Retorna un número aleatori major o igual que 0 i menor que 1, distribu<PERSON>t (canvia en tornar a calcular)"}, "RANDARRAY": {"a": "([files]; [columnes]; [mínim]; [màxim]; [enter])", "d": "Retorna una matriu de nombres aleatoris"}, "RANDBETWEEN": {"a": "(inferior; superior)", "d": "Retorna un nombre aleatori entre els nombres que especifiqueu"}, "ROMAN": {"a": "(número; [forma])", "d": "Converteix un número aràbic en romà, en format de text"}, "ROUND": {"a": "(número; núm_decimals)", "d": "Arrodoneix un número al número de decimals especificat"}, "ROUNDDOWN": {"a": "(número; núm_decimals)", "d": "Arrodoneix un número cap a baix, cap a zero"}, "ROUNDUP": {"a": "(número; núm_decimals)", "d": "Arrodoneix un número cap a dalt, en direcció contrària a zero"}, "SEC": {"a": "(número)", "d": "Torna la secant d'un angle"}, "SECH": {"a": "(número)", "d": "Torna la secant hiperbòlica d'un angle"}, "SERIESSUM": {"a": "(x; n; m; coeficients)", "d": "Retorna la suma d'una sèrie de potències basant-se en la fórmula"}, "SIGN": {"a": "(número)", "d": "Retorna el signe d'un número: 1 si és número és positiu, zero si el número és zero i -1 si el número és negatiu"}, "SIN": {"a": "(número)", "d": "Retorna el sinus d'un angle determinat"}, "SINH": {"a": "(número)", "d": "Retorna el sinus hiperbòlic d'un número"}, "SQRT": {"a": "(número)", "d": "Retorna l'arrel quadrada d'un número"}, "SQRTPI": {"a": "(nombre)", "d": "Retorna l'arrel quadrada de (nombre * Pi)"}, "SUBTOTAL": {"a": "(funció_número; ref1; ...)", "d": "Retorna un subtotal en una llista o base de dades"}, "SUM": {"a": "(número1; [número2]; ...)", "d": "Suma tots els números d'un interval de cel·les"}, "SUMIF": {"a": "(interval; criteris; [interval_suma])", "d": "Suma les cel·les especificades amb una condició o uns criteris especificats"}, "SUMIFS": {"a": "(interval_suma; interval_criteris; criteris; ...)", "d": "Suma les cel·les especificades per un conjunt de condicions o criteris determinat"}, "SUMPRODUCT": {"a": "(matriu1; [matriu2]; [matriu3]; ...)", "d": "Retorna la suma dels productes dels intervals o les matrius corresponents"}, "SUMSQ": {"a": "(número1; [número2]; ...)", "d": "Retorna la suma dels quadrats dels arguments. Els arguments poden ser números, matrius, noms o referències a cel·les que contenen números"}, "SUMX2MY2": {"a": "(matriu_x; matriu_y)", "d": "Suma les diferències entre els quadrats de dos intervals o matrius corresponents"}, "SUMX2PY2": {"a": "(matriu_x; matriu_y)", "d": "Retorna la suma total de les sumes dels quadrats de números de dos intervals o matrius corresponents"}, "SUMXMY2": {"a": "(matriu_x; matriu_y)", "d": "<PERSON><PERSON> els quadrats de les diferències en dos intervals o matrius corresponents"}, "TAN": {"a": "(número)", "d": "Retorna la tangent d'un angle"}, "TANH": {"a": "(número)", "d": "Retorna la tangent hiperbòlica d'un número"}, "TRUNC": {"a": "(número; [núm_decimals])", "d": "Converteix un número decimal en un d'enter suprimint-ne la part decimal o de fracció"}, "ADDRESS": {"a": "(fila; columna; [abs]; [a1]; [full])", "d": "Crea una referència de cel·la en forma de text una vegada s'han especificat els números de fila i columna"}, "CHOOSE": {"a": "(núm_índex; valor1; [valor2]; ...)", "d": "Escull un valor o una acció d'una llista de valors a partir d'un número d'índex"}, "COLUMN": {"a": "([ref])", "d": "Retorna el número de columna d'una referència"}, "COLUMNS": {"a": "(matriu)", "d": "Retorna el número de columnes d'una matriu o una referència"}, "FORMULATEXT": {"a": "(referència)", "d": "Torna una fórmula com una cadena"}, "HLOOKUP": {"a": "(valor_cercat; matriu_taula; indicador_files; [interval_cercat])", "d": "Cerca un valor a la fila superior d'una taula o una matriu de valors i retorna el valor a la mateixa columna des d'una fila especificada"}, "HYPERLINK": {"a": "(ubic<PERSON><PERSON><PERSON>_enllaç; [nom_descriptiu])", "d": "Crea una drecera o salt que obre un document emmagatzemat al disc dur, a un servidor de xarxa o a Internet"}, "INDEX": {"a": "(matriu; núm_fila; [núm_columna]!ref; núm_fila; [núm_columna]; [núm_à<PERSON>])", "d": "Retorna un valor o una referència de la cel·la a la intersecció d'una fila i una columna concretes, en un interval determinat"}, "INDIRECT": {"a": "(ref; [a1])", "d": "Retorna la referència especificada per una cadena de text"}, "LOOKUP": {"a": "(valor_cercat; vector_cercat; [vector_resultat]!valor_cercat; matriu)", "d": "Cerca un valor a partir d'un interval d'una fila o una columna o a partir d'una matriu. Proporcionat per a compatibilitat universal"}, "MATCH": {"a": "(valor_cercat; matriu_cercada; [tipus_coincidència])", "d": "Retorna la posició relativa d'un element en una matriu, que coincideix amb un valor determinat en un ordre especificat"}, "OFFSET": {"a": "(ref; files; columnes; [alçada]; [amplada])", "d": "Retorna una referència a un interval que és un número determinat de files i columnes d'una referència especificada"}, "ROW": {"a": "([ref])", "d": "Retorna el número de fila d'una referència"}, "ROWS": {"a": "(matriu)", "d": "Retorna el número de files d'una referència o una matriu"}, "TRANSPOSE": {"a": "(matriu)", "d": "Converteix un interval vertical de cel·les en un interval horitzontal o viceversa"}, "UNIQUE": {"a": "(matriu; [per_columna]; [apareix_una_vegada])", "d": "Retorna els valors únics d'un interval o una matriu."}, "VLOOKUP": {"a": "(valor_cercat; matriu_taula; indicador_columnes; [interval_cercat])", "d": "Cerca un valor a la columna de més a l'esquerra d'una taula i retorna un valor a la mateixa fila des d'una columna especificada. Per defecte, la taula s'ordena de manera ascendent"}, "XLOOKUP": {"a": "(valor_consulta; matriu_consulta; matriu_retorn; [si_no_es_troba]; [mode_coincidència]; [mode_cerca])", "d": "Cerca una coincidència en un interval o una matriu i retorna l'element corresponent d'un segon interval o d'una segona matriu. Per defecte, s'utilitzarà una coincidència exacta"}, "CELL": {"a": "(info_tipus; [referència])", "d": "Retorna la informació sobre el format, la ubicació o el contingut d'una cel·la"}, "ERROR.TYPE": {"a": "(val_error)", "d": "Retorna un número que coincideix amb un valor d'error."}, "ISBLANK": {"a": "(valor)", "d": "Comprova si una referència és una cel·la buida i retorna CERT o FALS"}, "ISERR": {"a": "(value)", "d": "Comprova si un valor és un error diferent de #N/D i retorna TRUE o FALSE"}, "ISERROR": {"a": "(value)", "d": "Comprova si un valor és un error i retorna TRUE o FALSE"}, "ISEVEN": {"a": "(nombre)", "d": "Retorna CERT si el nombre és parell"}, "ISFORMULA": {"a": "(referència)", "d": "Comprova si una referència correspon a una cel·la amb una fórmula i torna CERT o FALS"}, "ISLOGICAL": {"a": "(valor)", "d": "Comprova si un valor és un valor lògic (CERT o FALS) i retorna CERT o FALS"}, "ISNA": {"a": "(valor)", "d": "Comprova si un valor és #N/D i retorna CERT o FALS"}, "ISNONTEXT": {"a": "(valor)", "d": "Comprova si un valor no és text (les cel·les en blanc no són text) i retorna CERT o FALS"}, "ISNUMBER": {"a": "(valor)", "d": "Comprova si un valor és un número i retorna CERT o FALS"}, "ISODD": {"a": "(nombre)", "d": "Retorna CERT si el nombre és senar"}, "ISREF": {"a": "(valor)", "d": "Comprova si un valor és una referència i retorna CERT o FALS"}, "ISTEXT": {"a": "(valor)", "d": "Comprova si un valor és text i retorna CERT o FALS"}, "N": {"a": "(valor)", "d": "Converteix els valors no numèrics en números, les dates en números de sèrie, CERT en 1, qualsevol altra cosa en 0 (zero)"}, "NA": {"a": "()", "d": "Retorna el valor d'error #N/D (valor no disponible)"}, "SHEET": {"a": "([valor])", "d": "Retorna el número de full del full de referència"}, "SHEETS": {"a": "([referència])", "d": "Retorna el nombre de fulls d'una referència"}, "TYPE": {"a": "(valor)", "d": "Retorna un valor enter que representa el tipus de dades d'un valor: nombre = 1; text = 2; valor lògic = 4; valor d'error = 16; matriu = 64; dades compostes = 128"}, "AND": {"a": "(valor_lògic1; [valor_lògic2]; ...)", "d": "Comprova si tots els arguments són CERT i retorna CERT si tots els arguments són CERT"}, "FALSE": {"a": "()", "d": "Retorna el valor lògic FALS"}, "IF": {"a": "(prova_lògica; [valor_si_cert]; [valor_si_fals])", "d": "Comprova si es compleix una condició i retorna un valor si és CERT i un altre valor si és FALS"}, "IFS": {"a": "(prova_lògica1; valor_si_cert; ...)", "d": "Comprova si es compleix una condició com a mínim i retorna un valor que correspon a la primera condició CERT"}, "IFERROR": {"a": "(valor; valor_si_error)", "d": "Retorna valor_si_error si l'expressió és un error i el valor de l'expressió si no ho és"}, "IFNA": {"a": "(valor; valor_si_nd)", "d": "Retorna el valor que heu especificat si el resultat de l'expressió és #ND, o bé torna el resultat de l'expressió"}, "NOT": {"a": "(valor_lògic)", "d": "Canvia FALS per CERT, o CERT per FALS"}, "OR": {"a": "(valor_lògic1; [valor_lògic2]; ...)", "d": "Comprova si algun dels arguments és CERT i retorna CERT o FALS. Retorna FALS si tots els arguments són FALS"}, "SWITCH": {"a": "(expressió; valor1; resultat1; [per_defecte_o_valor2]; [resultat2]; ...)", "d": "Calcula una expressió amb una llista de valors i retorna el resultat corresponent al primer valor que coincideixi. Si no hi ha coincidències, retorna un valor per defecte opcional"}, "TRUE": {"a": "()", "d": "Retorna el valor lògic CERT"}, "XOR": {"a": "(lògica1; [lògica2]; ...)", "d": "Torna una lògica \"Exclusiu o\" de tots els arguments"}, "TEXTBEFORE": {"a": "(text, delimitador, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retorna text que és abans de delimitar caràcters."}, "TEXTAFTER": {"a": "(text, delimitador, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retorna text que és després de delimitar caràcters."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": " Divideix el text en files o columnes utilitzant delimitadors."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ajusta un vector de fila o columna després d’un nombre de valors especificat."}, "VSTACK": {"a": "(array1, [matriu2], ...)", "d": "<PERSON><PERSON><PERSON> les matrius verticalment en una sola matriu."}, "HSTACK": {"a": "(array1, [matriu2], ...)", "d": "<PERSON><PERSON><PERSON> les matrius horitzontalment en una sola matriu."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Retorna files d'una matriu o una referència."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Retorna columnes d'una matriu o una referència."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Retorna la matriu com una columna."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Retorna la matriu com una fila."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ajusta un vector de fila o columna després d’un nombre de valors especificat."}, "TAKE": {"a": "(array, files, [columnes])", "d": "Retorna files o columnes des de l'inici o el final de la matriu."}, "DROP": {"a": "(array, rows, [columns])", "d": "Deixa anar files o columnes de l'inici o el final de la matriu."}}
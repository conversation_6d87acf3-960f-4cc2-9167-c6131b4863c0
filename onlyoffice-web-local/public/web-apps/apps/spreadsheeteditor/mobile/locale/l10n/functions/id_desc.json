{"DATE": {"a": "(year; month; day)", "d": "Mengembalikan angka yang mewakili tanggal dalam kode tanggal-waktu"}, "DATEDIF": {"a": "(start-date; end-date; unit)", "d": "Menghitung jumlah hari, bula<PERSON>, atau tahun di antara dua tanggal"}, "DATEVALUE": {"a": "(date_text)", "d": "Mengonversi tanggal dalam bentuk teks ke angka yang mewakili tanggal dalam kode tanggal-waktu"}, "DAY": {"a": "(serial_number)", "d": "Mengembalik<PERSON> hari dari bulan, angka dari 1 sampai 31."}, "DAYS": {"a": "(end_date; start_date)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah hari antara dua tanggal."}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "Menampilkan jumlah hari di antara dua tanggal berdasarkan pada 360-hari setahun (dua belas bulan per bulan 30-hari)"}, "EDATE": {"a": "(start_date; months)", "d": "Menampilkan angka seri tanggal yang menunjukkan jumlah bulan sebelum atau sesudah tanggal mulai"}, "EOMONTH": {"a": "(start_date; months)", "d": "Menampilkan angka seri hari terakhir dari bulan sebelum atau setelah jumlah bulan yang ditentukan"}, "HOUR": {"a": "(serial_number)", "d": "Mengembalikan jam sebagai angka dari 0 (12:00 A.M.) sampai 23 (11:00 P.M.)."}, "ISOWEEKNUM": {"a": "(date)", "d": "Mengembalikan jumlah minggu ISO dalam setahun untuk tanggal tertentu"}, "MINUTE": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> menit, an<PERSON><PERSON> dari 0 sampai 59."}, "MONTH": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulan, ang<PERSON> dari 1 (<PERSON><PERSON><PERSON>) sampai 12 (<PERSON><PERSON><PERSON>)."}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "Menampilkan jumlah dari seluruh hari kerja antara dua tanggal"}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah seluruh hari kerja antara dua tanggal dengan parameter akhir pekan kustom"}, "NOW": {"a": "()", "d": "Menampilkan tanggal dan waktu sekarang terformat sebagai tanggal dan waktu."}, "SECOND": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, an<PERSON><PERSON> dari 0 sampai 59."}, "TIME": {"a": "(hour; minute; second)", "d": "Konversi jam, menit, dan detik yang ditentukan sebagai angka ke nomor seri, diformat dengan format waktu"}, "TIMEVALUE": {"a": "(time_text)", "d": "Mengonversi waktu teks ke nomor seri untuk waktuu, angka dari 0 (12:00:00 AM) sampai 0.999988426 (11:59:59 PM). Format nomor dengan format waktu setelah memasukkan rumus"}, "TODAY": {"a": "()", "d": "Menampilkan format tanggal sekarang sebagai tanggal."}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "Menampilkan angka dari 1 sampai 7 untuk menunjukkan hari dari minggu dari tanggal."}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "Mengembalikan jumlah minggu dalam setahun"}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "Menampilkan angka seri tanggal sebelum atau sesudah dari bilangan tertentu dari hari kerja"}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah seluruh hari kerja antara dua tanggal dengan parameter akhir pekan kustom"}, "YEAR": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> tahun dari tanggal, bilangan bulat dalam rentang 1900 - 9999."}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "Menampilkan pecahan tahun yang mewakili angka seluruh hari antara tanggal_mulai dan tanggal_akhir"}, "BESSELI": {"a": "(x; n)", "d": "Menampilkan fungsi Bessel termodifikasi In(x)"}, "BESSELJ": {"a": "(x; n)", "d": "Menampilkan fungsi Bessel Jn(x)"}, "BESSELK": {"a": "(x; n)", "d": "Menampilkan fungsi Bessel termodifikasi Kn(x)"}, "BESSELY": {"a": "(x; n)", "d": "Menampilkan fungsi Bessel Yn(x)"}, "BIN2DEC": {"a": "(number)", "d": "Mengonversi sebuah bilangan biner ke desimal"}, "BIN2HEX": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan biner ke hexadesimal"}, "BIN2OCT": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan biner ke oktal"}, "BITAND": {"a": "(number1; number2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bitwise '<PERSON>' dari dua angka"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "Menampilkan angka yang dialihkan ke kiri dengan shift_amount bits"}, "BITOR": {"a": "(number1; number2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bitwise '<PERSON><PERSON>' dari dua angka"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "Menampilkan angka yang dialihkan ke kanan dengan shift_amount bits"}, "BITXOR": {"a": "(number1; number2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bit<PERSON> '<PERSON><PERSON><PERSON><PERSON><PERSON>' dari dua angka"}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "Mengonversi koefisien nyata dan bayangan ke dalam bilangan kompleks"}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "Mengubah bilangan dari satu sistem pengukuran ke lainnya"}, "DEC2BIN": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan desimal ke biner"}, "DEC2HEX": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan desimal ke hexadesimal"}, "DEC2OCT": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan desimal ke oktal"}, "DELTA": {"a": "(number1; [number2])", "d": "<PERSON><PERSON><PERSON> a<PERSON>h dua bilangan sama"}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "Mengembalikan fungsi kesalahan"}, "ERF.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> fungsi kes<PERSON>han"}, "ERFC": {"a": "(x)", "d": "Mengembalik<PERSON> kes<PERSON>han tambahan fungsi"}, "ERFC.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> fungsi kes<PERSON>han peleng<PERSON>p"}, "GESTEP": {"a": "(number; [step])", "d": "<PERSON><PERSON><PERSON> a<PERSON>h bilangan lebih besar dari nilai ambang"}, "HEX2BIN": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan Hexadesimal ke biner"}, "HEX2DEC": {"a": "(number)", "d": "Mengonversi sebuah bilangan hexadesimal ke desimal"}, "HEX2OCT": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan hexadesimal ke oktal"}, "IMABS": {"a": "(inumber)", "d": "Menampilkan bilangan absolut (modul) dari bilangan kompleks"}, "IMAGINARY": {"a": "(inumber)", "d": "Menampilkan koefisien bayangan dari bilangan kompleks"}, "IMARGUMENT": {"a": "(inumber)", "d": "Menampilkan argumen q, sebuah sudut terekspresi dalam radian"}, "IMCONJUGATE": {"a": "(inumber)", "d": "Menampilkan konjugasi kompleks dari bilangan kompleks"}, "IMCOS": {"a": "(inumber)", "d": "Menampilkan kosinus dari bilangan kompleks"}, "IMCOSH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kosinus hiperbolik dari bilangan kompleks"}, "IMCOT": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kotangen dari bilangan kompleks"}, "IMCSC": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kosekan dari bilangan kompleks"}, "IMCSCH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON>an kosekan hiperbolik dari bilangan kompleks"}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "Menampilkan hasil bagi dari dua bilangan kompleks"}, "IMEXP": {"a": "(inumber)", "d": "Menampilkan eksponensial dari bilangan kompleks"}, "IMLN": {"a": "(inumber)", "d": "Menampilkan logaritma alami dari bilangan kompleks"}, "IMLOG10": {"a": "(inumber)", "d": "Menampilkan logaritma dasar 10 dari bilangan kompleks"}, "IMLOG2": {"a": "(inumber)", "d": "Menampilkan logaritma dasar 2 dari bilangan kompleks"}, "IMPOWER": {"a": "(inumber; number)", "d": "Menampilkan bilangan kompleks yang dinaikkan ke kekuatan bilangan bulat"}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "Menampilkan produk dari 1 sampai 255 bilangan kompleks"}, "IMREAL": {"a": "(inumber)", "d": "Menampilkan koefisien nyata dari bilangan kompleks"}, "IMSEC": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> sekan dari bilangan kompleks"}, "IMSECH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> sekan hiperbolik dari bilangan kompleks"}, "IMSIN": {"a": "(inumber)", "d": "Menampilkan sinus dari bilangan kompleks"}, "IMSINH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON>lkan sinus hiperbolik dari bilangan kompleks"}, "IMSQRT": {"a": "(inumber)", "d": "Menampilkan akar pangkat dua dari bilangan kompleks"}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "Menampilkan perbedaan dari dua bilangan kompleks"}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "Menampilkan penjumlahan bilangan kompleks"}, "IMTAN": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> tangen dari bilangan kompleks"}, "OCT2BIN": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan oktal ke biner"}, "OCT2DEC": {"a": "(number)", "d": "Mengonversi sebuah bilangan oktal ke desimal"}, "OCT2HEX": {"a": "(number; [places])", "d": "Mengonversi bilangan oktal ke hexadesimal"}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "Merata-rata nilai dalam kolom pada daftar atau database yang cocok dengan kondisi yang Anda tentukan"}, "DCOUNT": {"a": "(database; field; criteria)", "d": "Menghitung sel yang mengandung angka dalam bidang (kolom) dari catatan dalam database yang sesuai dengan kondisi yang Anda tentukan"}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "Menghitung sel yang tidak kosong dalam bidang (kolom) dari catatan dalam database yang cocok dengan kondisi yang Anda tentukan"}, "DGET": {"a": "(database; field; criteria)", "d": "Mengekstrak dari database rekaman tunggal yang cocok dengan kondisi yang Anda tentukan"}, "DMAX": {"a": "(database; field; criteria)", "d": "Menampilkan angka terbesar dalam bidang (kolom) dari catatan dalam database yang sesuai dengan kondisi yang Anda tentukan"}, "DMIN": {"a": "(database; field; criteria)", "d": "Menampilkan angka terkecil dalam bidang (kolom) dari catatan dalam database yang sesuai dengan kondisi yang Anda tentukan"}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "Mengalikan nilai dalam bidang (kolom) dari rekaman dalam database yang cocok dengan kondisi yang Anda tentukan"}, "DSTDEV": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON>rak<PERSON> standar deviasi berdasarkan pada contoh dari entri database terpilih"}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "Menghitung simpangan baku berdasarkan seluruh populasi entri database terpilih"}, "DSUM": {"a": "(database; field; criteria)", "d": "Menambah angka dalam bidang (kolom) dari rekaman dalam database yang sesuai dengan kondisi yang Anda tentukan"}, "DVAR": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> variansi berdasarkan contoh dari entri database terpilih"}, "DVARP": {"a": "(database; field; criteria)", "d": "Menghitung variansi berdasarkan pada seluruh populasi entri database terpilih"}, "CHAR": {"a": "(number)", "d": "Menampilkan karakter yang ditentukan oleh nomor kode dari perangkat karakter untuk komputer Anda"}, "CLEAN": {"a": "(text)", "d": "Menghapus semua karakter yang tidak dapat dicetak dari teks"}, "CODE": {"a": "(text)", "d": "Menampilkan kode numerik untuk karakter pertama pada string teks, pada perangkat karakter yang digunakan oleh komputer Anda"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Gabungkan beberapa string teks ke dalam satu string teks"}, "CONCAT": {"a": "(text1; ...)", "d": "Menggabungkan daftar atau rentang string teks"}, "DOLLAR": {"a": "(number; [decimals])", "d": "Mengonversi angka ke teks, menggunakan format mata uang"}, "EXACT": {"a": "(text1; text2)", "d": "Mengecek apakah kedua string teks tersebut tepat sama, dan men<PERSON><PERSON><PERSON>an BENAR atau SALAH. EXACT membedakan huruf besar dan kecil"}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "Menampilkan posisi awal satu string teks dalam string teks lain. FIND membedakan huruf besar dan kecil"}, "FINDB": {"a": "(find_text; within_text; [start_num])", "d": "Menemukan satu string teks ketika string teks kedua, dan men<PERSON>an nomor posisi mulai string teks pertama dari karakter pertama string teks kedua, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - Je<PERSON>g, Tionghoa, dan Korea"}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "Membulatkan angka ke angka desimal tertentu dan mengembalikan hasilnya sebagai teks dengan atau tanpa koma"}, "LEFT": {"a": "(text; [num_chars])", "d": "Menampilkan jumlah karakter tertentu dari awal string teks"}, "LEFTB": {"a": "(text; [num_chars])", "d": "Mengembalikan karakter pertama atau beberapa karakter dalam string teks, berdasarkan jumlah byte yang Anda tentukan, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - <PERSON><PERSON><PERSON>, T<PERSON>ghoa, dan Korea"}, "LEN": {"a": "(text)", "d": "Menampilkan jumlah karakter dalam string teks"}, "LENB": {"a": "(text)", "d": "Mengembalikan jumlah byte yang digunakan untuk menunjukkan karakter dalam string teks, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - <PERSON><PERSON>g, Tionghoa, dan Korea"}, "LOWER": {"a": "(text)", "d": "Mengonversi semua huruf dalam string teks ke huruf kecil"}, "MID": {"a": "(text; start_num; num_chars)", "d": "Menampilkan karakter dari tengah string teks, memberikan posisi awal dan panjang"}, "MIDB": {"a": "(text; start_num; num_chars)", "d": "Mengembalikan jumlah karakter tertentu dari sebuah string teks, dimulai dari posisi yang Anda tentukan, berdasarkan jumlah byte yang Anda tentukan, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - Je<PERSON>g, Tionghoa, dan Korea"}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "Mengonversi teks ke angka tanpa tergantung pada lokalnya"}, "PROPER": {"a": "(text)", "d": "Mengonversi string teks ke huruf judul; huruf pertama pada tiap kata berhuruf besar, dan semua huruf lain berhuruf kecil"}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Mengganti bagian dari string teks dengan string teks yang lain"}, "REPLACEB": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Menggantikan bagian dari string teks, berdasarkan jumlah byte yang Anda tentukan, dengan string teks berbeda, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - <PERSON><PERSON><PERSON>, T<PERSON>ghoa, dan Korea"}, "REPT": {"a": "(text; number_times)", "d": "Mengulangi teks sesuai yang ditentukan. Gunakan REPT untuk mengisi sel dengan sejumlah contoh string teks"}, "RIGHT": {"a": "(text; [num_chars])", "d": "Menampilkan jumlah karakter tertentu dari bagian akhir string teks"}, "RIGHTB": {"a": "(text; [num_chars])", "d": "Menampilkan karakter terakhir atau karakter dalam string teks, berdasarkan jumlah byte yang Anda tentukan, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - <PERSON><PERSON><PERSON>, T<PERSON>ghoa, dan Korea"}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "Menampilkan jumlah dari karakter saat karakter atau string teks tertentu ditemukan pertama kali, membaca dari kiri ke kanan (huruf besar dan kecil tidak dibedakan)"}, "SEARCHB": {"a": "(find_text; within_text; [start_num])", "d": "Menemukan satu string teks dalam string teks kedua, dan men<PERSON>an nomor posisi awal string teks pertama dari karakter pertama string teks kedua, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - <PERSON><PERSON><PERSON>, <PERSON><PERSON>ghoa, dan Korea"}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "Mengganti teks yang ada dengan teks baru dalam string teks"}, "T": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai adalah teks, dan menge<PERSON><PERSON>an teksnya jika benar, atau mengembalikan tanda petik ganda (teks kosong) jika tidak benar"}, "TEXT": {"a": "(value; format_text)", "d": "Mengonversi nilai ke teks dalam format angka tertentu"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "Menggabungkan daftar atau rentang string teks menggunakan pembatas"}, "TRIM": {"a": "(text)", "d": "Menghapus semua spasi dari string teks kecuali untuk spasi tunggal di antara kata"}, "UNICHAR": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON>an karakter Unicode yang diacu oleh nilai numerik te<PERSON>"}, "UNICODE": {"a": "(text)", "d": "Mengembalikan angka (poin kode) yang sesuai dengan karakter pertama teks"}, "UPPER": {"a": "(text)", "d": "Mengonversi string teks menjadi huruf besar semua"}, "VALUE": {"a": "(text)", "d": "Mengonversi string teks yang menun<PERSON>n angka ke angka"}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "Menampilkan rata-rata simpangan absolut poin data dari nilai rata-ratanya. Argumen dapat berupa angka atau nama, aray atau referensi yang mengandung angka"}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "Menampilkan rata-rata (nilai rata-rata aritmatika) dari argume<PERSON>a, yang dapat berupa angka atau nama, array, atau referensi yang mengandung angka"}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "Menampilkan rata-rata (nilai rata-rata aritmatika) dari argume<PERSON><PERSON>, mengevaluasi teks dan SALAH dalam argumen sebagai 0; BENAR sebagai 1. Argumen dapat berupa angka atau nama, array, atau referensi"}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "<PERSON><PERSON><PERSON> rata-rata(rata-rata aritmatik)untuk sel yang ditentukan oleh kondisi atau kriteria yang diberikan"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": "<PERSON><PERSON><PERSON> rata-rata(rata-rata arit<PERSON><PERSON>) untuk sel yang ditentukan dengan diberikan sebuah rangkaian kondisi atau kriteria"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "Mengembalikan fungsi kepadatan probabilitas beta kumulatif"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Mengembalikan inversi dari fungsi kepadatan probabilitas kumulatif beta (BETADIST)"}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> fungsi distribusi probabilitas beta"}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> balikan dari fungsi kerapatan probabilitas kumulatif beta (BETA.DIST)"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Mengembalikan probabilitas distribusi binomial istilah individu"}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Menampilkan probabilitas distribusi binomial term individu"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "<PERSON><PERSON><PERSON><PERSON>an probabilitas hasil percobaan dengan distribusi binomial"}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "Menampilkan nilai terkecil di mana distribusi kumulatif binomial lebih besar dari atau sama dengan nilai standar"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "Mengembalikan probabilitas sisi-kanan dari distribusi khi-kuadrat"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "Mengembalikan inversi dari probabilitas sisi-kanan dari distribusi khi-kuadrat"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "Mengembalikan uji independensi: nilai dari distribusi khi-kuadrat untuk statistik dan derajat kebebasan yang tepat"}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON>an probabilitas lemparan-kiri dari distribusi khi-kuadrat"}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> probabilitas lemparan-kanan dari distribusi khi-kuadrat"}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> balikan dari probabilitas lemparan-kiri dari distribusi khi-kuadrat"}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> balikan dari probabilitas lemparan-kanan dari distribusi khi-kuadrat"}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "Menampilkan tes untuk independen: nilai dari distribusi khi-kuadrat untuk statistik dan pangkat yang tepat"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "Mengembalikan interval kepercayaan untuk mean populasi, menggunakan distribusi normal"}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> interval k<PERSON><PERSON>yaan untuk rata-rata populasi, dengan distribusi normal"}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> interval k<PERSON><PERSON>yaan untuk rata-rata populasi, dengan distribusi T <PERSON>elajar"}, "CORREL": {"a": "(array1; array2)", "d": "Menampilkan koefisien korelasi di antara dua perangkat data"}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "Menghitung jumlah sel dalam rentang yang memuat angka"}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "Menghitung jumlah sel dalam rentang yang tidak kosong"}, "COUNTBLANK": {"a": "(range)", "d": "Menghitung jumlah sel kosong dalam rentang sel tertentu"}, "COUNTIF": {"a": "(range; criteria)", "d": "Menghitung jumlah sel dalam rentang yang sesuai dengan kondisi yang diberikan"}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "Hitung jumlah sel yang ditentukan oleh pemberian set dari kondisi atau kriteria"}, "COVAR": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, rata-rata produk dari simpangan untuk tiap pasangan titik data dalam dua perangkat data"}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, rata-rata produk dari simpangan untuk tiap pasangan poin data dalam dua set data"}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> samp<PERSON>, rata-rata produk dari simpangan untuk tiap pasangan poin data dalam dua set data"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "Menampilkan nilai terkecil di mana distribusi binomial kumulatif lebih besar dari atau sama dengan nilai kriteria"}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "Menampilkan penju<PERSON>lahan kuadrat simpangan poin data dari nilai rata-rata contoh poin"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "Mengembalikan distribusi eksponensial"}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "Menampilkan distribusi eksponensial"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Mengembalikan distribusi probabilitas F (sisi-kanan) untuk dua set data"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Mengembalikan inversi distribusi probabilitas F (sisi-kanan): jika p = FDIST(x,...), maka FINV(p,...) = x"}, "FTEST": {"a": "(array1; array2)", "d": "Men<PERSON><PERSON><PERSON><PERSON> hasil uji-F, probabilitas dua-sisi di mana varian dalam Array1 dan Array2 tidak memiliki perbedaan yang signifikan"}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "Menghasilkan distribusi probabilitas F (lemparan-kiri) untuk dua set data"}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Men<PERSON><PERSON>lkan distribusi probabilitas F (lemparan-kanan) untuk dua set data"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Men<PERSON><PERSON>lkan balikan distribusi probabilitas F (lemparan-kiri): jika p = F.DIST(x,...), maka F.INV(p,...) = x"}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "<PERSON><PERSON><PERSON><PERSON>an balikan distribusi probabilitas F (lemparan-kanan): jika p = F.DIST(x,...), maka F.INV.RT(p,...) = x"}, "F.TEST": {"a": "(array1; array2)", "d": "Menampilkan hasil tes-F, probabilitas dua lemparan di mana variansi dalam Array1 dan Array2 tidak memiliki perbedaan yang berarti"}, "FISHER": {"a": "(x)", "d": "Menampilkan transformasi Fisher"}, "FISHERINV": {"a": "(y)", "d": "Menampilkan invers dari transformasi Fisher: Jika y = FISHER(x), maka FISHERINV(y) = x"}, "FORECAST": {"a": "(x; known_y's; known_x's)", "d": "<PERSON><PERSON><PERSON><PERSON>, at<PERSON> me<PERSON><PERSON>, nilai mendatang beserta tren linear menggunakan nilai yang ada"}, "FORECAST.ETS": {"a": "(tanggal_target; nilai; garis_waktu; [musiman]; [penyelesaian_data]; [agregat])", "d": "Mengembalikan nilai prakiraan untuk tanggal target tertentu di masa depan menggunakan metode pemulusan eksponensial."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Mengembalikan interval kepercayaan untuk nilai prakiraan pada tanggal target yang ditentukan."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Mengembalikan panjang pola berulang yang dideteksi oleh aplikasi untuk rangkaian waktu tertentu."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Mengembalikan statistik diminta untuk prakiraan."}, "FORECAST.LINEAR": {"a": "(x; known_y's; known_x's)", "d": "<PERSON><PERSON><PERSON><PERSON>, at<PERSON> me<PERSON><PERSON>, nilai mendatang beserta tren linear menggunakan nilai yang ada"}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "Menghitung seberapa sering nilai terdapat dalam rentang nilai kemudian kembalikan array vertikal dari angka yang memiliki lebih dari satu elemen dari array_Bins"}, "GAMMA": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai fun<PERSON>i <PERSON>"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "Mengembalikan distribusi gamma"}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribusi gamma"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "Mengembalikan inversi distribusi kumulatif gamma: jika p = GAMMADIST(x,...), maka GAMMAINV(p,...) = x"}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "<PERSON><PERSON><PERSON><PERSON>an balikan distribusi kumulatif gamma: jika p = GAMMADIST(x,...), maka GAMMAINV(p,...) = x"}, "GAMMALN": {"a": "(x)", "d": "Menampilkan logaritma alami fungsi gamma"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> logaritma alami fungsi gamma"}, "GAUSS": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> 0,5 kurang dari distribusi kumulatif normal standar"}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "Menampilkan nilai rata-rata geometrik array atau rentang data numerik yang positif"}, "GROWTH": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "Mengembalikan angka dalam tren pertumbuhan eksponensial yang cocok dengan titik data yang diketahui"}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "Menampilkan nilai rata-rata harmonik perangkat data dari angka positif: resiprokal dari nilai rata-rata aritmatika resiprokal"}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON>an distribusi hipergeometrik"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "Mengembalikan distribusi hipergeometrik"}, "INTERCEPT": {"a": "(known_y's; known_x's)", "d": "Menghitung titik tempat garis akan memotong sumbu-y menggunakan garis regresi yang paling cocok yang diplot melalui nilai-x dan nilai-y yang diketahui"}, "KURT": {"a": "(number1; [number2]; ...)", "d": "Menampilkan kurtosis dari perangkat data"}, "LARGE": {"a": "(array; k)", "d": "Menampilkan nilai k-th terbesar dalam perangkat data. <PERSON><PERSON><PERSON>, angka terbesar kelima"}, "LINEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "Mengembalikan statistik yang menggambarkan tren linear yang cocok dengan titik data yang diketahui, dengan mencocokkan garis lurus menggunakan metode kuadrat terkecil"}, "LOGEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "Mengembalikan statistik yang menggambarkan kurva eksponensial yang cocok dengan titik data yang diketahui"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "Mengembalikan inversi fungsi distribusi kumulatif lognormal dari x, di mana ln(x) secara normal terdistribusi dengan parameter Mean dan Standar_dev"}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON>an distribusi lognormal dari x, di mana ln(x) terdistribusi secara normal dengan parameter <PERSON><PERSON> rata-rata dan Standard_dev"}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Menampilkan invers fungsi distribusi kumulatif lognormal dari x, di mana ln(x) secara normal terdistribusi dengan parameter Nilai rata-rata dan <PERSON>ar_dev"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "Mengembalikan distribusi lognormal kumulatif dari x, di mana ln(x) terdistribusi secara normal dengan parameter Mean dan Standar_dev"}, "MAX": {"a": "(number1; [number2]; ...)", "d": "Menampilkan nilai terbesar dalam seperangkat nilai. Abaikan nilai dan teks logis"}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "Menampilkan nilai terbesar dalam serang<PERSON>an nilai. Tidak mengabaikan nilai logis dan teks"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Mengembalikan nilai maksimum antara sel-sel yang ditentukan oleh seperangkat kondisi atau kriteria tertentu"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "Menampilkan nilai tengah, atau angka di tengah-tengah perangkat angka yang ditentukan"}, "MIN": {"a": "(number1; [number2]; ...)", "d": "Menampilkan nilai terkecil dalam seperangkat nilai. Abaikan nilai dan teks logis"}, "MINA": {"a": "(value1; [value2]; ...)", "d": "Menampilkan nilai terkecil dalam perangkat nilai. Tidak mengabaikan nilai logika dan teks"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Mengembalikan nilai minimum antara sel-sel yang ditentukan oleh seperangkat kondisi atau kriteria tertentu"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "Mengembalikan nilai yang paling sering muncul, atau berulang, dalam arry atau rentang data"}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "Mengh<PERSON>lkan array vertikal dari nilai yang paling sering muncul, atau berulang dalam array atau rentang data.  Untuk array horizontal, gunakan =TRANSPOSE(MODE.MULT(angka1,angka2,...))"}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "Menampilkan yang paling sering terjadi, atau berulang, nilai dalam array atau rentang data"}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribusi negatif binomial, probabilitas bahwa mungkin ada kegagalan Number_f agar Number_ke-s berhasil, dengan probabilitas keberhasilan Probability_s"}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "Menampilkan distribusi negatif binomial, probabilitas yang mungkin terdapat Number_f kegagalan sebelum Number_s-th berhasil, dengan Probability_s probabilitas dari keb<PERSON>n"}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON>an distribusi normal untuk nilai rata-rata dan simpangan standar tertentu"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Mengembalikan distribusi kumulatif normal untuk mean dan simpangan baku yang ditentukan"}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Menampilkan invers distribusi kumulatif normal untuk nilai rata-rata dan simpangan baku tertentu"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "Mengembalikan inversi distribusi kumulatif normal untuk mean dan simpangan baku yang ditentukan"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribusi kumulatif standar normal (memiliki nilai rata-rata nol dan simpangan standar satu)"}, "NORMSDIST": {"a": "(z)", "d": "Mengembalikan distribusi kumulatif normal standar (memiliki mean nol dan simpangan baku satu)"}, "NORM.S.INV": {"a": "(probability)", "d": "Menampilkan invers distribusi kumulatif standar normal (memiliki nilai rata-rata nol dan simpangan baku satu)"}, "NORMSINV": {"a": "(probability)", "d": "Mengembalikan inversi distribusi kumulatif normal standar (memiliki mean nol dan simpangan baku satu)"}, "PEARSON": {"a": "(array1; array2)", "d": "Menampilkan koefisien korelasi momen produk <PERSON>, r"}, "PERCENTILE": {"a": "(array; k)", "d": "Mengembalikan persentil k-th dari nilai dalam rentang"}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> persentil ke-k dari nilai di suatu rentang, di mana k dalam rentang 0..1, tidak termasuk"}, "PERCENTILE.INC": {"a": "(array; k)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> persentil ke-k dari nilai di suatu rentang, di mana k dalam rentang 0..1, term<PERSON><PERSON>"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "Mengembalikan peringkat nilai dalam perangkat data sebagai persentase dari perangkat data"}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> peringkat nilai dalam set data sebagai persentase dari set data sebagai persentase (0..1, tidak termasuk) dari set data"}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> peringkat nilai dalam set data sebagai persentase dari set data sebagai persentase (0..1, termasuk) dari set data"}, "PERMUT": {"a": "(number; number_chosen)", "d": "Menampilkan jumlah permutasi untuk jumlah objek yang ditentukan yang dapat dipilih dari objek total"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah permutasi untuk sejumlah objek te<PERSON> (dengan pengulangan) yang dapat dipilih dari objek total"}, "PHI": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai fungsi kerapatan untuk distribusi normal standar"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "Mengembalikan distribusi Poisson"}, "POISSON.DIST": {"a": "(x; mean; cumulative)", "d": "Menampilkan distribusi Poisson"}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "Menampilkan probabilitas bahwa nilai dalam rentang adalah di antara dua limit atau sama dengan limit terendah"}, "QUARTILE": {"a": "(array; quart)", "d": "Mengembalikan kuartil perangkat data"}, "QUARTILE.INC": {"a": "(array; quart)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuartil set data, berdasarkan nilai persentil dari 0..1, term<PERSON><PERSON>"}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuartil set data, berdasarkan nilai persentil dari 0..1, tidak termasuk"}, "RANK": {"a": "(number; ref; [order])", "d": "Mengembalikan peringkat angka dalam daftar angka: ukurannya relatif terhadap nilai lain dalam daftar"}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> peringkat angka dalam daftar angka: ukurannya relatif dibandingkan nilai lain dalam daftar; jika lebih dari satu nilai memiliki peringkat yang sama, yang dihas<PERSON>kan adalah peringkat rata-ratanya"}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> peringkat angka dalam daftar angka: ukurannya relatif dibandingkan nilai lain dalam daftar; jika lebih dari satu nilai memiliki peringkat yang sama, yang dihasilkan adalah peringkat atas dari set nilai tersebut"}, "RSQ": {"a": "(known_y's; known_x's)", "d": "Mengembalikan kuadrat koefisien korelasi momen produk Pearson melalui titik data yang ditentukan"}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "Menampilkan kecondongan distribusi: ka<PERSON><PERSON><PERSON><PERSON> derajat asimetri dari distribusi disekitar nilai rata-ratanya"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kecondongan distribusi berdasarkan populasi: karak<PERSON><PERSON><PERSON> derajat asimetri dari distribusi di sekitar nilai rata-ratanya"}, "SLOPE": {"a": "(known_y's; known_x's)", "d": "Mengembalikan kemiringan garis regresi linear melalui titik data yang ditentukan"}, "SMALL": {"a": "(array; k)", "d": "Menampilkan nilai k-th terkecil dalam perangkat data. <PERSON><PERSON><PERSON>, angka terkecil kelima"}, "STANDARDIZE": {"a": "(x; mean; standard_dev)", "d": "Menampilkan nilai yang dinormalisasi dari distribusi terkarakterisasi oleh nilai rata-rata dan simpangan baku"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "Memperkirakan simpangan baku berdasarkan contoh (abaikan nilai dan teks logis dalam contoh)"}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "Menghitung simpangan baku berdasarkan pada seluruh populasi yang ditentukan sebagai argumen (abaikan nilai dan teks logis)"}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "Memperkirakan simpangan baku berdasarkan pada contoh (mengabaikan nilai dan teks logis dalam contoh)"}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "Memperkirakan simpangan baku be<PERSON><PERSON><PERSON> contoh, term<PERSON><PERSON> nilai dan teks logis. Teks dan nilai logis SALAH memiliki nilai 0; nilai logis BENAR memiliki nilai 1"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "Menghitung simpangan baku berdasarkan seluruh populasi yang ditentukan sebagai argumen (abaikan nilai dan teks logis)"}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "Menghitung simpangan baku berdasar<PERSON> selu<PERSON>h populasi, termasuk nilai dan teks logis. Teks dan nilai logis SALAH bernilai 0; nilai logis BENAR bernilai 1"}, "STEYX": {"a": "(known_y's; known_x's)", "d": "Mengembalikan kesalahan standar dari nilai-y yang diperkirakan untuk tiap x dalam regresi"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "Mengembalikan distribusi-t Pelajar"}, "TINV": {"a": "(probability; deg_freedom)", "d": "Mengembalikan inversi dua-sisi distribusi-t <PERSON>elajar"}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribus<PERSON>-t <PERSON><PERSON>jar le<PERSON>-kiri"}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribus<PERSON>-t <PERSON><PERSON><PERSON> dua-le<PERSON><PERSON>"}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribus<PERSON>-t <PERSON><PERSON><PERSON>-kanan"}, "T.INV": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> balikan lemparan-kiri dari distribusi-t <PERSON><PERSON>jar"}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> balikan dua-lemparan dari distribusi-t <PERSON><PERSON><PERSON>"}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "Menampilkan probabilitas yang berhubungan dengan <PERSON>-t <PERSON>"}, "TREND": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "Mengembalikan angka dalam tren linear yang cocok dengan titik data yang diketahui, menggunakan metode kuadrat terkecil"}, "TRIMMEAN": {"a": "(array; percent)", "d": "Menampilkan nilai rata-rata bagian interior dari perangkat nilai data"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "Mengembalikan probabilitas yang terkait dengan U<PERSON>-t <PERSON>jar"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON>rak<PERSON> varian berdasarkan contoh (abaikan nilai dan teks logis dalam contoh)"}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "Menghitung varians berdasarkan seluruh populasi (mengabaikan nilai dan teks logis dalam populasi)"}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON>rakan variansi berdasarkan contoh (mengabaikan nilai dan teks logis dalam contoh)"}, "VARA": {"a": "(value1; [value2]; ...)", "d": "Memperkirakan varians be<PERSON><PERSON><PERSON> con<PERSON>, term<PERSON><PERSON> nilai dan teks logis. Teks dan nilai logis SALAH memiliki nilai 0; nilai logis BENAR memiliki nilai 1"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "Menghitung variansi berdasarkan seluruh populasi (abaikan nilai dan teks logis dalam populasi)"}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "Menghitung varians berdasarkan pada selu<PERSON>h populasi, term<PERSON><PERSON> nilai dan teks logis. Teks dan nilai logis SALAH bernilai 0; nilai logis BENAR bernilai 1"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "Mengembalikan distribus<PERSON>"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "Menampilkan distribus<PERSON>"}, "Z.TEST": {"a": "(array; x; [sigma])", "d": "Menampilkan nilai-<PERSON> satu-lemparan dari uji-z"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "Mengembalikan nilai-<PERSON> satu-sisi dari uji-z"}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "Menampilkan bunga akrual untuk surat berharga yang membayar suku bunga periodik."}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "Menampilkan bunga akrual untuk surat berharga yang membayar bunga pada jatuh tempo"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Menampilkan depresiasi linear prorata dari aset untuk tiap periode akuntansi."}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Menampilkan depresiasi linear prorata dari aset untuk tiap periode akuntansi."}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan jumlah hari dari awal periode kupon sampai tanggal pelunasan"}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan jumlah angka dalam periode kupon yang berisi tanggal pelunasan"}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan jumlah hari dari tanggal pelunasan sampai tanggal kupon selanjutnya"}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan tanggal kupon selanjutnya setelah tanggal pelunasan"}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan jumlah kupon yang dapat dibayarkan antara tanggal pelunasan dan tanggal jatuh tempo"}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan tanggal kupon sebelumnya sebelum tanggal pelunasan"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Menampilkan bunga kumulatif yang dibayarkan antara dua periode"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Menampilkan pokok kumulatif yang di<PERSON>an pada pinjaman antara dua periode"}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "Menampilkan depresiasi aset untuk periode tertentu menggunakan metode pengurangan-tetap saldo"}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "Menampilkan depresiasi aset untuk periode tertentu menggunakan metode saldo pengu<PERSON>an-ganda atau beberapa metode lain yang Anda tentukan"}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Menampilkan tingkat diskon untuk surat berharga"}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "<PERSON><PERSON><PERSON> harga dolar, yang dinyatakan sebagai pecahan, ke dalam harga dolar, yang dinyatakan sebagai angka desimal"}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "<PERSON><PERSON><PERSON> harga dolar, yang dinyatakan sebagai angka desimal, ke dalam harga dolar, yang dinyatakan sebagai pecahan"}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Menampilkan durasi tahunan surat berharga dengan pembayaran bunga periodik"}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "Menampilkan suku bunga tahunan yang berlaku"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "Menampilkan nilai masa depan investasi berdasarkan pembayaran periodik yang konstan dan suku bunga konstan"}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "Menampilkan nilai akan datang dari nilai pokok awal sesudah menerapkan seri suku bunga majemuk"}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "Menampilkan tingkat bunga untuk surat berharga yang diinvestasikan penuh"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Menampilkan pembayaran bunga untuk periode tertentu untuk investasi, berdasarkan pada pembayaran periodik yang konstan dan suku bunga konstan"}, "IRR": {"a": "(values; [guess])", "d": "Menampilkan angka internal pengembalian untuk seri aliran kas"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Menampilkan bunga yang dibayarkan selama periode investasi tertentu"}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Menampilkan durasi yang disesuaikan Macauley untuk surat berharga dengan nilai par asumsi $100"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "Menampilkan angka internal dari pengembalian serangkaikan aliran kas secara periodik, mempertimbangkan biaya investasi dan bunga pada investasi kembali uang"}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "Menampilkan suku bunga tahunan"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "Menampilkan jumlah periode untuk investasi berdasarkan pembayaran periodik yang konstan dan suku bunga konstan"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "Menampilkan nilai bersih sekarang dari investasi berdasar pada tingkat diskon dan serangkaikan pembayaran di masa depan (nilai negatif) dan pendapatan (nilai positif)"}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "Menampilkan harga per $100 nilai nominal surat berharga dengan periode ganjil pertama"}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "Menampilkan hasil surat berharga dengan periode ganjil pertama"}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "Menampilkan harga per $100 nilai nominal surat berharga dengan periode terakhir ganjil"}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "Menampilkan hasil surat berharga dengan periode terakhir ganjil"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> seju<PERSON>lah periode yang diperlukan oleh investasi untuk mencapai nilai yang ditentukan"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "Menghitung pembayaran untuk pinjaman berdasarkan pembayaran yang konstan pembayaran dan suku bunga konstan"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Menampilkan pembayaran pada uang pokok untuk investasi tertentu berdasarkan pada pembayaran periodik yang konstan dan suku bunga konstan"}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "Menampilkan harga per $100 nilai nominal surat berharga yang membayarkan bunga periodik"}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "Menampilkan harga per $100 nilai nominal dari surat berharga yang didiskon"}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "Menampilkan harga per nilai nominal $100 dari surat berharga yang membayar tingkat bunga pada saat jatuh tempo"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "Menampilkan nilai investasi sekarang: jumlah total nilai sekarang dari serangkaian pembayaran di masa depan"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "Menampilkan suku bunga per periode pinjaman atau investasi. <PERSON><PERSON><PERSON>, gunakan 6%/4 untuk pembayaran tiap kuartal pada APR 6%"}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "Menampilkan jumlah yang diterima saat tanggal jatuh tempo untuk surat berharga yang diinvestasikan penuh"}, "RRI": {"a": "(nper; pv; fv)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> suku bunga setara untuk pertumbuhan investasi"}, "SLN": {"a": "(cost; salvage; life)", "d": "Menampilkan depresiasi garis-lurus modal untuk satu periode"}, "SYD": {"a": "(cost; salvage; life; per)", "d": "Menampilkan jumlah depresiasi digit tahun dari aset untuk periode tertentu"}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "Menampilkan hasil setara obligasi untuk surat utang"}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "Menampilkan harga per nilai awal $100 untuk surat utang"}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "Menampilkan hasil untuk Surat utang"}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "Menampilkan depresiasi aset untuk periode yang Anda tentukan, termasuk periode parsial, menggunakan metode saldo pengu<PERSON>an-ganda atau beberapa metode lain yang Anda tentukan"}, "XIRR": {"a": "(values; dates; [guess])", "d": "Menampilkan tingkat internal pengembalian untuk jadwal arus kas"}, "XNPV": {"a": "(rate; values; dates)", "d": "Menampilkan nilai bersih saat ini untuk jadwal arus kas"}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "Menampilkan hasil surat berharga yang membayar bunga periodik"}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Menampilkan hasil tahunan untuk surat berharga yang didiskon. Sebagai contoh, surat utang negara"}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "Menampilkan hasil tahunan surat berharga yang membayar bunga pada tanggal jatuh tempo"}, "ABS": {"a": "(number)", "d": "Menampilkan nilai absolut dari angka, angka tanpa tanda tersebut"}, "ACOS": {"a": "(number)", "d": "Menampilkan kurva kosinus dari ang<PERSON>, dalam radian pada rentang 0 sampai Pi. <PERSON><PERSON> kosinus adalah sudut yang kosinus<PERSON> berupa <PERSON>"}, "ACOSH": {"a": "(number)", "d": "Menampilkan kosinus hiperbolik invers dari angka"}, "ACOT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON>an kurva kotangen dari angka dalam radian pada rentang 0 hingga Pi."}, "ACOTH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON>an kotangen balikan hiperbolik dari angka"}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "Menampilkan agregasi dalam sebuah daftar atau database"}, "ARABIC": {"a": "(text)", "d": "Mengonversi angka Romawi ke angka Arab"}, "ASC": {"a": "(text)", "d": "<PERSON>tuk bahasa Perangkat karakter bit ganda (DBCS, Double-byte character set), fungsi tersebut mengubah karakter lebar penuh (bit ganda) menjadi lebar setengah (bit tunggal)"}, "ASIN": {"a": "(number)", "d": "Menampilkan kurva sinus angka dalam radian, dalam rentang -Pi/2 sampai Pi/2"}, "ASINH": {"a": "(number)", "d": "Menampilkan sinus hiperbolik invers dari angka"}, "ATAN": {"a": "(number)", "d": "Menampilkan kurva tangen dari angka dalam radian, pada rentang -Pi/2 ke Pi/2"}, "ATAN2": {"a": "(x_num; y_num)", "d": "Menampilkan kurva tangen dari koordinat -x dan -y yang ditentu<PERSON>, dalam radian antara -<PERSON> dan <PERSON>, tidak term<PERSON>uk -<PERSON>"}, "ATANH": {"a": "(number)", "d": "Menampilkan invers tangen hiperbolik dari angka"}, "BASE": {"a": "(number; radix; [min_length])", "d": "Mengonversi angka menjadi representasi teks dengan radiks tertentu (dasar)"}, "CEILING": {"a": "(number; significance)", "d": "Membulatkan angka ke atas, ke bilangan bulat terdekat atau ke multipel terdekat dari signifikansi"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "Membulatkan angka ke atas, ke bilangan bulat terdekat atau ke kelipatan signifikansi terdekat"}, "CEILING.PRECISE": {"a": "(number; [significance])", "d": "Mengembalikan angka yang dibulatkan ke atas ke bilangan bulat terdekat atau ke kelipatan signifikansi terdekat"}, "COMBIN": {"a": "(number; number_chosen)", "d": "Menampilkan jumlah kombinasi untuk jumlah item yang ditentukan"}, "COMBINA": {"a": "(number; number_chosen)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah kombinasi dengan pengulangan untuk sejumlah objek tertentu"}, "COS": {"a": "(number)", "d": "Menampilkan kosinus dari sudut"}, "COSH": {"a": "(number)", "d": "Menampilkan kosinus hiperbolik dari angka"}, "COT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kotangen sudut"}, "COTH": {"a": "(number)", "d": "Mengh<PERSON>lkan kotangen hiperbolik angka"}, "CSC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kosekan sudut"}, "CSCH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kosekan hiperbolik angka"}, "DECIMAL": {"a": "(number; radix)", "d": "Mengonversi representasi teks suatu angka dalam dasar tertentu ke angka desimal"}, "DEGREES": {"a": "(angle)", "d": "Mengonversi radian ke derajat"}, "ECMA.CEILING": {"a": "(number; significance)", "d": "Membulatkan angka ke atas, ke bilangan bulat terdekat atau ke multipel terdekat dari signifikansi"}, "EVEN": {"a": "(number)", "d": "Membulatkan angka positif ke atas dan angka negatif ke bawah ke bilangan bulat genap terdekat"}, "EXP": {"a": "(number)", "d": "Menampilkan ke e pangkat dari angka yang ditentukan"}, "FACT": {"a": "(number)", "d": "Menampilkan faktorial dari angka, sama dengan 1*2*3*...* Angka"}, "FACTDOUBLE": {"a": "(number)", "d": "Menampilkan faktorial ganda dari bilangan"}, "FLOOR": {"a": "(number; significance)", "d": "Membulatkan angka ke bawah, mendekati multipel terdekat dari signifikansi"}, "FLOOR.PRECISE": {"a": "(number; [significance])", "d": "Mengembalikan angka yang dibulatkan ke bawah ke bilangan bulat terdekat atau ke kelipatan signifikansi terdekat"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "Membulatkan angka ke atas, ke bilangan bulat terdekat atau ke kelipatan signifikansi terdekat"}, "GCD": {"a": "(number1; [number2]; ...)", "d": "Menampilkan pembagi terbesar yang umum"}, "INT": {"a": "(number)", "d": "Membulatkan angka ke bawah ke bilangan bulat terdekat"}, "ISO.CEILING": {"a": "(number; [significance])", "d": "Mengembalikan angka yang dibulatkan ke atas ke bilangan bulat terdekat atau ke kelipatan signifikansi terdekat. Tanpa memperhatikan lambang angkanya, bilangan itu dibulatkan ke atas. <PERSON><PERSON> tetapi, jika angka signifikansinya nol, maka hasilnya nol."}, "LCM": {"a": "(number1; [number2]; ...)", "d": "Menampilkan multipel umum paling sedikit"}, "LN": {"a": "(number)", "d": "Menampilkan logaritma alami dari angka"}, "LOG": {"a": "(number; [base])", "d": "Menampilkan logarima sebuah angka ke basis yang Anda tentukan"}, "LOG10": {"a": "(number)", "d": "Menampilkan logaritma basis-10 dari angka"}, "MDETERM": {"a": "(array)", "d": "Menampilkan determinan matrik dari array"}, "MINVERSE": {"a": "(array)", "d": "Menampilkan matrik invers untuk matrik yang disimpan dalam array"}, "MMULT": {"a": "(array1; array2)", "d": "Menampilkan produk matrik dari dua array, array dengan jumlah baris yang sama dengan Array1 dan jumlah kolom yang sama dengan Array2"}, "MOD": {"a": "(number; divisor)", "d": "Menampilkan sisanya setelah angka dibagi dengan pembagi"}, "MROUND": {"a": "(number; multiple)", "d": "Menampilkan bilangan yang dibulatkan ke multipel yang diinginkan"}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "Menampilkan multinomial dari set bilangan"}, "MUNIT": {"a": "(dimension)", "d": "<PERSON><PERSON><PERSON><PERSON>an matriks satuan untuk dimensi yang ditentukan"}, "ODD": {"a": "(number)", "d": "Membulatkan angka positif ke atas dan angka negatif ke bawah ke bilangan bulat ganjil terdekat"}, "PI": {"a": "()", "d": "Menampilkan nilai dari <PERSON>, 3,14159265358979, a<PERSON><PERSON> untuk 15 digit"}, "POWER": {"a": "(number; power)", "d": "Menampilkan hasil dari nilai yang dipang<PERSON>kan"}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "Mengalikan semua angka yang ditentukan sebagai argumen"}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "Menampilkan porsi bilangan bulat dari pembagian"}, "RADIANS": {"a": "(angle)", "d": "Mengonversi derajat ke radian"}, "RAND": {"a": "()", "d": "Menampilkan angka acak lebih besar dari 0 dan kurang dari 1, terdistribusi secara merata (perubahan pada perhitungan ulang)"}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> larik berisi nomor acak"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "Menampilkan bilangan acak antara bilangan yang anda tentukan"}, "ROMAN": {"a": "(number; [form])", "d": "Mengonversi angka Arab ke Romawi, sebagai teks"}, "ROUND": {"a": "(number; num_digits)", "d": "Membulatkan angka ke jumlah digit yang ditentukan"}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "Membulatkan angka ke bawah, terhadap nol"}, "ROUNDUP": {"a": "(number; num_digits)", "d": "Membulatkan angka ke atas, menjauhi nol"}, "SEC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> sekan sudut"}, "SECH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> sekan hiperbolik angka"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "Menampilkan jumlah dari seri kekuatan berdasarkan pada rumus"}, "SIGN": {"a": "(number)", "d": "Menampilkan tanda dari angka: 1 jika angka adalah positif, nol jika angka adalah nol, atau -1 jika angka adalah negatif"}, "SIN": {"a": "(number)", "d": "Menampilkan sinus dari sudut"}, "SINH": {"a": "(number)", "d": "Menampilkan sinus hiperbolik dari angka"}, "SQRT": {"a": "(number)", "d": "Menampilkan akar pangkat dua dari angka"}, "SQRTPI": {"a": "(number)", "d": "Menampilkan akar pangkat dua dari (bilangan * Pi)"}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "Menampilkan subtotal dalam daftar atau database"}, "SUM": {"a": "(number1; [number2]; ...)", "d": "Menambah semua angka dalam rentang sel"}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "Menambah sel yang ditentukan oleh kondisi atau kriteria tertentu"}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "Tambahkan sel yang ditentukan oleh pemberian set aturan atau kriteria"}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "Menampilkan penjumlahan produk dari rentang atau array terkait"}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "Menampilkan jumlah kuadrat argumen. Argumen dapat berupa angka, array, nama, atau referensi ke sel yang mengandung angka"}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "Menjumlahkan perbedaan di antara kuadrat dua rentang array yang berhubungan"}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "Menampilkan total jumlah penjumlahan kuadrat angka dalam dua rentang array berhubungan"}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "Menjumlahkan kuadrat perbedaan dalam dua rentang array yang berhubungan"}, "TAN": {"a": "(number)", "d": "Menampilkan tangen dari sudut"}, "TANH": {"a": "(number)", "d": "Menampilkan tangen hiperbolik dari angka"}, "TRUNC": {"a": "(number; [num_digits])", "d": "Memotong angka ke bilangan bulat dengan menghapus desimalnya, atau pecahan, bagian dari angka tersebut"}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "Membuat referensi sel sebagai teks, memberikan nomor baris dan kolom tertentu"}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> nilai atau tindakan yang dilakukan dari daftar nilai, berda<PERSON><PERSON> nomor indeks"}, "COLUMN": {"a": "([reference])", "d": "Menampilkan nomor kolom dari referensi"}, "COLUMNS": {"a": "(array)", "d": "Menampilkan jumlah kolom dalam array atau referensi"}, "FORMULATEXT": {"a": "(reference)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> rumus sebagai string"}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "Mencari nilai dalam baris teratas dari tabel atau array nilai dan mengembalikan nilai dalam kolom yang sama dari baris yang Anda tentukan"}, "HYPERLINK": {"a": "(link_location; [friendly_name])", "d": "Membuat pintasan atau loncatan yang akan membuka dokumen yang disimpan dalam hard drive Anda, server jaringan, atau pada Internet"}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "Menampilkan nilai atau referensi sel pada persimpangan baris atau kolom tertentu, dalam rentang yang ditentukan"}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "Menampilkan referensi yang ditentukan dengan string teks"}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "Mencari nilai dari rentang satu-baris atau satu-kolom atau dari array. Tersedia untuk kompatibilitas terbalik."}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "Menampilkan posisi relatif item dalam array yang cocok dengan nilai tertentu dalam urutan tertentu"}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "Menampilkan referensi ke rentang yang adalah jumlah baris dan kolom yang ditentukan dari referensi yang ditentukan"}, "ROW": {"a": "([reference])", "d": "Menampilkan nomor baris dari referensi"}, "ROWS": {"a": "(array)", "d": "Menampilkan jumlah baris dalam referensi atau array"}, "TRANSPOSE": {"a": "(array)", "d": "Mengonversi rentang sel vertikal ke rentang horizontal, atau sebaliknya"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": "Mengembalikan nilai unik dari rentang atau larik."}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "Mencari nilai dalam kolom terkiri dari tabel, k<PERSON><PERSON>an mengembalikan nilai dalam baris yang sama dengan kolom yang Anda tentukan. Secara default, tabel harus diurutkan dalam urutan naik"}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "Mencari rentang atau larik untuk dicocokkan dan mengh<PERSON>lkan item yang sesuai dari rentang atau larik kedua. Secara default, kecocokan yang sama persis akan digunakan"}, "CELL": {"a": "(info_type; [reference])", "d": "Mengembalikan informasi tentang pemformatan, lokasi, atau konten sel"}, "ERROR.TYPE": {"a": "(error_val)", "d": "Mengembalikan nilai yang sesuai dengan nilai kes<PERSON>han."}, "ISBLANK": {"a": "(value)", "d": "Mengecek apakah referensi ke sel kosong, dan men<PERSON><PERSON><PERSON>an BENAR atau SALAH"}, "ISERR": {"a": "(value)", "d": "Memerik<PERSON> apakah nilai adalah kesalahan selain #N/A, dan men<PERSON><PERSON>an TRUE atau FALSE"}, "ISERROR": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apa<PERSON>h nilai adalah k<PERSON>, dan <PERSON><PERSON><PERSON><PERSON> TRUE atau FALSE"}, "ISEVEN": {"a": "(number)", "d": "Menampilkan BENAR jika bilangan genap"}, "ISFORMULA": {"a": "(reference)", "d": "Memeriksa apakah referensi menuju ke sel yang berisi rumus, dan <PERSON><PERSON><PERSON><PERSON>an TRUE atau FALSE"}, "ISLOGICAL": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai adalah nilai logika (BENAR atau SALAH), dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH"}, "ISNA": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai adalah #N/A, dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH"}, "ISNONTEXT": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai bukan teks (sel kosong bukan teks), dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH"}, "ISNUMBER": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai adalah se<PERSON> an<PERSON>, dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH"}, "ISODD": {"a": "(number)", "d": "Menampilkan BENAR jika bilangan ganjil"}, "ISREF": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai ad<PERSON>h <PERSON>, dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH"}, "ISTEXT": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai adalah te<PERSON>, dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH"}, "N": {"a": "(value)", "d": "Mengonversi nilai non-angka ke angka, tangg<PERSON> ke nomor seri, BENAR ke 1, yang lain ke 0 (nol)"}, "NA": {"a": "()", "d": "Mengembalikan nilai kes<PERSON>han #N/A (nilai tidak tersedia)"}, "SHEET": {"a": "([value])", "d": "<PERSON><PERSON><PERSON><PERSON>an nomor lembar dari lembar yang diacu"}, "SHEETS": {"a": "([reference])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah lembar di referensi"}, "TYPE": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bilangan bulat yang mewakili jenis data dari nilai: angka = 1; teks = 2; nilai logis = 4; nilai k<PERSON> = 16; larik = 64; data gabungan = 128"}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "Memerik<PERSON> apakah semua argumen adalah BENAR, dan men<PERSON><PERSON><PERSON>an BENAR jika semua argumen adalah BENAR"}, "FALSE": {"a": "()", "d": "Menampilkan nilai logika SALAH"}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "Memerik<PERSON> apakah kond<PERSON>, dan men<PERSON><PERSON><PERSON>an satu nilai jika BENAR, dan nilai lain jika SALAH"}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "Memeriksa apakah satu atau beberapa syarat terpenuhi dan mengembalikan nilai sesuai dengan syarat TRUE pertama"}, "IFERROR": {"a": "(value; value_if_error)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai_jika_kesalahan jika ekspresi merupakan kesalahan dan nilai dari ekspresi itu sendiri jika tidak"}, "IFNA": {"a": "(value; value_if_na)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai yang Anda tentukan jika ekspresi terpisah ke #N/A, jika tidak, hasil ekspresi akan diberikan"}, "NOT": {"a": "(logical)", "d": "Mengubah SALAH ke BENAR, atau BENAR ke SALAH"}, "OR": {"a": "(logical1; [logical2]; ...)", "d": "Memerik<PERSON> apakah tiap argumen adalah BENAR, dan men<PERSON><PERSON><PERSON> BENAR atau SALAH. Menampilkan SALAH hanya jika semua argumen adalah SALAH"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "Mengevaluasi ekspresi terhadap daftar nilai-nilai dan mengembalikan hasil yang sesuai dengan nilai pertama yang cocok. Jika tidak ada yang cocok, nilai default opsional dikembalikan"}, "TRUE": {"a": "()", "d": "Menampilkan nilai logika BENAR"}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> logis dari semua argumen"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Mengembalikan teks sebelum karakter pemisah."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Mengembalikan teks setelah karakter pemisah."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Membagi teks menjadi baris atau kolom menggunakan pemisah."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Membungkus vektor baris atau kolom setelah jumlah nilai yang ditentukan."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "<PERSON><PERSON><PERSON> array secara vertikal menjadi satu array."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Menumpuk array secara horizontal menjadi satu array."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Mengembalikan baris dari array atau referensi."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Mengembalikan kolom dari array atau referensi."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Mengembalikan array sebagai satu kolom."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> array sebagai satu baris."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Membungkus vektor baris atau kolom setelah jumlah nilai yang ditentukan."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Mengembalikan baris atau kolom dari awal atau akhir array."}, "DROP": {"a": "(array, rows, [columns])", "d": "Menghapus baris atau kolom dari awal atau akhir array."}}
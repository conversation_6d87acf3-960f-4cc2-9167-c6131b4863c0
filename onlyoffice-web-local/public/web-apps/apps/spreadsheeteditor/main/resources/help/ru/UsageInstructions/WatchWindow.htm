<!DOCTYPE html>
<html>
	<head>
		<title>Окно контрольных значений</title>
		<meta charset="utf-8" />
        <meta name="description" content="Используйте Окно контрольных значений для управления большими таблицами" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>О<PERSON>но контрольных значений</h1>
            <p>При работе с большими таблицами в <a href="https://www.onlyoffice.com/ru/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>редакторе электронных таблиц</b></a> <b>Окно контрольных значений</b> может пригодиться, чтобы контролировать ячейки и формулы, которые в них используются.</p>
            <p><b>Окно контрольных значений</b> позволяет отслеживать следующие параметры контрольных значений: <em>Книга</em>, <em>Лист</em>, <em>Имя</em>, <em>Ячейка</em>, <em>Значение</em> и <em>Формула</em>.</p>
			<p><img alt="Окно контрольных значений" src="../images/watchwindow.png" /></p>
            <p>Чтобы <b>добавить</b> контрольное значение,</p>
            <ol>
                <li>Перейдите на вкладку <b>Формула</b>.</li>
                <li>Нажмите на кнопку <b>Окно контрольных значений</b>.</li>
                <li>Нажмите на кнопку <b>Добавить контрольное значение</b> в <b>Окне контрольных значений</b>.</li>
                <li>Выберите нужный диапазон данных для контроля значений.</li>
                <li>Нажмите на кнопку <b>Закрыть</b>, чтобы вернуться к рабочему листу.</li>
            </ol>
            <p>Чтобы <b>удалить</b> контрольное значение,</p>
            <ol>
                <li>Перейдите на вкладку <b>Формула</b>.</li>
                <li>Нажмите на кнопку <b>Окно контрольных значений</b>.</li>
                <li>Нажмите на кнопку <b>Удалить контрольное значение</b> и выберите, надо ли <b>Удалить контрольное значение</b> или <b>Удалить все</b>.</li>
            </ol>
		</div>
	</body>
</html>
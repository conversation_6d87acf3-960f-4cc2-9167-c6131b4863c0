<!DOCTYPE html>
<html>
	<head>
		<title>Функция ВРЕМЯ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ВРЕМЯ</h1>
			<p>Функция <b>ВРЕМЯ</b> - это одна из функций даты и времени. Используется для добавления определенного времени в выбранном формате (по умолчанию <em>чч:мм tt (указатель половины дня a.m./p.m.)</em>).</p>
			<p>Синтаксис функции <b>ВРЕМЯ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ВРЕМЯ(часы;минуты;секунды)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>часы</em></b> - число от 0 до 23.</p> 
			<p style="text-indent: 50px;"><b><em>минуты</em></b> - число от 0 до 59.</p> 
			<p style="text-indent: 50px;"><b><em>секунды</em></b> - число от 0 до 59.</p> 
			<p>Эти числовые значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>ВРЕМЯ</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>ВРЕМЯ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ВРЕМЯ" src="../images/time.png" /></p>
		</div>
	</body>
</html>
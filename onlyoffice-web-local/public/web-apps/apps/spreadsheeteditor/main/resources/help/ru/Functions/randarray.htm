<!DOCTYPE html>
<html>
	<head>
		<title>Функция СЛУЧМАССИВ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Функция СЛУЧМАССИВ</h1>
			<p>Функция <b>СЛУЧМАССИВ</b> -  это одна из математических и тригонометрических функций. Она используется для возврата массива случайных чисел и может быть настроена для заполнения определенного количества строк и столбцов, установки диапазона значений или возврата целых / десятичных чисел.</p>
			<p>Синтаксис функции <b>СЛУЧМАССИВ</b>:</p>
			<p style="text-indent: 150px;"><b><em>СЛУЧМАССИВ([строки],[столбцы],[минимум],[максимум],[целое_число])</em></b></p>
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>строки</em></b> - это необязательный аргумент, указывающий количество возвращаемых строк. Если аргумент не указан, функция вернет значение от 0 до 1.</p>
			<p style="text-indent: 50px;"><b><em>columns</em></b> - это необязательный аргумент, указывающий количество возвращаемых столбцов. Если аргумент не указан, функция вернет значение от 0 до 1.</p>
			<p style="text-indent: 50px;"><b><em>миниумм</em></b> - это необязательный аргумент, указывающий минимальное возвращаемое число. Если аргумент не указан, функция по умолчанию принимает значения 0 и 1.</p>
			<p style="text-indent: 50px;"><b><em>максимум</em></b> - необязательный аргумент, указывающий максимальное возвращаемое число. Если аргумент не указан, функция по умолчанию будет 0 и 1.</p>
			<p style="text-indent: 50px;"><b><em>целое_число</em></b> - это необязательный аргумент ИСТИНА или ЛОЖЬ. Если ИСТИНА, функция вернет целое число; ЛОЖЬ вернет десятичное число.</p>
			<p>Чтобы применить функцию <b>СЛУЧМАССИВ</b>,</p>
			<ol>
				<li>выделите ячейку, в которой требуется отобразить результат,</li>
				<li>
					щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
					<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
					<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
				</li>
				<li>выберите из списка группу функций <b>Математические и тригонометрические</b>,</li>
				<li>щелкните по функции <b>СЛУЧМАССИВ</b>,</li>
				<li>введите аргументы, разделяя их запятыми,</li>
				<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СЛУЧМАССИВ" src="../images/randarray.png" /></p>
		</div>
	</body>
</html>
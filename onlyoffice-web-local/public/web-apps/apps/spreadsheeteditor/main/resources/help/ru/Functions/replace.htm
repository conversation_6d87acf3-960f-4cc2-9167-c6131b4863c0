<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЗАМЕНИТЬ/ЗАМЕНИТЬБ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЗАМЕНИТЬ/ЗАМЕНИТЬБ</h1>
			<p>Функция <b>ЗАМЕНИТЬ/ЗАМЕНИТЬБ</b> - это одна из функций для работы с текстом и данными. Заменяет ряд символов на новый, с учетом заданного количества символов и начальной позиции. Функция <b>ЗАМЕНИТЬ</b> предназначена для языков, использующих однобайтовую кодировку (SBCS), в то время как <b>ЗАМЕНИТЬБ</b> - для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.</p>
			<p>Синтаксис функции <b>ЗАМЕНИТЬ/ЗАМЕНИТЬБ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЗАМЕНИТЬ(стар_текст;начальная_позиция;число_знаков;нов_текст)</em></b></p> 
			<p style="text-indent: 150px;"><b><em>ЗАМЕНИТЬБ(стар_текст;начальная_позиция;число_знаков;нов_текст)</em></b></p>
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>стар_текст</em></b> - исходный текст, который требуется заменить.</p> 
			<p style="text-indent: 50px;"><b><em>начальная_позиция</em></b> - начальная позиция ряда символов, которые требуется заменить.</p> 
			<p style="text-indent: 50px;"><b><em>число_знаков</em></b> - количество символов, которые требуется заменить.</p>
			<p style="text-indent: 50px;"><b><em>нов_текст</em></b> - новый текст.</p> 
			<p>Эти значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>ЗАМЕНИТЬ/ЗАМЕНИТЬБ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>ЗАМЕНИТЬ/ЗАМЕНИТЬБ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,
			<p class="note"><b>Примечание</b>: функция ЗАМЕНИТЬ <b>учитывает регистр</b>.</p>
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ЗАМЕНИТЬ" src="../images/replace.png" /></p>
		</div>
	</body>
</html>
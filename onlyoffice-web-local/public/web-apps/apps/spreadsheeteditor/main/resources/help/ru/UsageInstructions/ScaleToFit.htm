<!DOCTYPE html>
<html>
<head>
    <title>Масштабирование листа</title>
    <meta charset="utf-8" />
    <meta name="description" content="Scale to fit" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Масштабирование листа</h1>
        <p>Если вы хотите уместить целый лист электронной таблицы на листе для печати, то вам может понадобиться функция <b>Вписать</b>. Эта функция помогает сжать электронную таблицу, чтобы уместить данные на указанном количестве страниц.</p>
        <p>Для этого выполните следующие действия:</p>
        <ul>
            <li>
                на <b>верхней панели инструментов</b> войдите во вкладку <b>Макет</b> и выберите функцию <div class = "icon icon-scaletofit"></div> <b>Вписать</b>,
                <ul>
                    <li>чтобы распечатать весь лист на одной странице, в параметре <b>Высота</b> выберите <b>1 страница</b>, а <b>Ширину</b> назначьте <b>Авто</b>. Значение масштабирования будет изменено автоматически. Данное значение отображается напротив параметра <b>Масштаб</b>. Чем оно больше, тем сильнее масштабируется лист таблицы;</li>
                    <li>вы можете вручную изменять значение масштабирования. Для этого поставьте параметры <b>Высоты</b> и <b>Ширины</b> на <b>Авто</b> и при помощи кнопок «<b>+</b>» и «<b>-</b>» меняйте масштаб листа. Границы печатной страницы будут отображаться <b>пунктирными линиями</b> на листе электронной таблицы,</li>
                </ul>
                <p><img alt="Меню Вписать" src="../images/scaletofitlayout.png" /></p>
            </li>
            <li>в разделе <b>Файл</b> нажмите <b>Печать</b> или используйте горячие клавиши Ctrl+P и в появившемся окне настройте параметры печати. Например, если на листе находится много колонок, может оказаться полезным поменять <b>Ориентацию страницы</b> на <b>Книжную</b>. Или распечатать заранее <b>выделенный диапазон</b> ячеек. Подробные сведения о возможностях печати вы можете найти в <a href="SavePrintDownload.htm" onclick="onhyperlinkclick(this)">данной статье</a>.
                <p><img alt="Параметры печати" src="../images/printsettingswindow.png" /></p>
            </li>
        </ul>
        <p class="note"><b>Примечание</b>: просмотр масштабированных листов может оказаться затруднительным, поскольку все данные таблицы сжимаются.</p>
    </div>
</body>
</html>
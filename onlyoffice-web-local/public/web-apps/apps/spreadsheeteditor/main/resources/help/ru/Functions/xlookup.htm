<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПРОСМОТРX</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="search" onkeypress="doSearch(event)">
			</div>
			<h1>Функция ПРОСМОТРX</h1>
			<p>Функция <b>ПРОСМОТРX</b>  - это одна из поисковых функций. Она используется для поиска определенного элемента по строке как по горизонтали, так и по вертикали. Возвращает элемент, соответствующий первому совпадению.</p>
			<p>Синтаксис функции <b>ПРОСМОТРX</b>:</p>
			<p style="text-indent: 150px;"><b><em>ПРОСМОТРX (искомое_значение, просматриваемый_массив, возращаемый_массив, [если_ничего_не_найдено], [режим_сопоставления], [режим_поиска])</em></b></p>
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>искомое_значение-</em></b> - это искомое значение.</p>
			<p style="text-indent: 50px;"><b><em>просматриваемый_массив</em></b> - это массив или диапазон для поиска.</p>
			<p style="text-indent: 50px;"><b><em>возращаемый_массив</em></b> - это массив или диапазон, в который возвращаются результаты.</p>
			<p style="text-indent: 50px;"><b><em>если_ничего_не_найдено</em></b>  - это необязательный аргумент. Если результата поиска нет, аргумент возвращает текст, указанный в [если_ничего_не_найдено]. Если текст не указан, возвращается «Н/Д».</p>
			<p style="text-indent: 50px;">
				<b><em>режим_сопоставления</em></b> - это необязательный аргумент. Доступны следующие значения:
				<ul>
					<li><b><em>0</em></b> (установлен по умолчанию) возвращает точное совпадение; если совпадений нет, вместо них возвращается «Н/Д».</li>
					<li><b><em>-1</em></b> возвращает точное совпадение; если его нет, возвращается следующий меньший элемент.</li>
					<li><b><em>1</em></b> возвращает точное совпадение; если его нет, возвращается следующий больший элемент.</li>
					<li><b><em>2</em></b> где постановочные знаки имеют специальное значение.</li>
				</ul>
			</p>
			<p style="text-indent: 50px;">
				<b><em>режим_поиска</em></b> - необязательный аргумент. Доступны следующие значения:
				<ul>
					<li><b><em>1</em></b> запускает поиск по первому элементу (установлен по умолчанию).</li>
					<li><b><em>-1</em></b> запускает обратный поиск, т.е. по последнему элементу.</li>
					<li><b><em>2</em></b> запускает двоичный поиск с <b><em>просматриваемый_массив</em> </b>, отсортированным в порядке возрастания. Если не отсортировано, будут возвращены недопустимые результаты.</li>
					<li><b><em>-2</em></b> запускает двоичный поиск с <b><em>просматриваемый_массив</em> </b>, отсортированным в порядке убывания. Если не отсортировано, будут возвращены недопустимые результаты.</li>
				</ul>
			</p>
			<p class="note">
				Подстановочные знаки включают вопросительный знак (?), Который соответствует одному символу, и звездочку (*), которая соответствует нескольким символам. Если вы хотите найти вопросительный знак или звездочку, введите тильду (~) перед символом.
			</p>
			<p>Чтобы применить функцию <b>ПРОСМОТРX</b>,</p>
			<ol>
				<li>выделите ячейку, в которой требуется отобразить результат,</li>
				<li>
					щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
					<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
					<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
				</li>
				<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
				<li>щелкните по функции <b>ПРОСМОТРX</b>,</li>
				<li>введите требуемые аргументы в окно <b>Аргументы функции</b>,</li>
				<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ПРОСМОТРX" src="../images/xlookup.png" /></p>
		</div>
	</body>
</html>
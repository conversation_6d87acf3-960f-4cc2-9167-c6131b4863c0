<!DOCTYPE html>
<html>
	<head>
		<title>Функция ДАТАЗНАЧ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ДАТАЗНАЧ</h1>
			<p>Функция <b>ДАТАЗНАЧ</b> - это одна из функций даты и времени. Возвращает порядковый номер заданной даты.</p>
			<p>Синтаксис функции <b>ДАТАЗНАЧ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ДАТАЗНАЧ(дата_как_текст)</em></b></p> 
			<p>где <b><em>дата_как_текст</em></b> (текстовая строка, представляющая дату и время) - дата с 1 января 1900 года по 31 декабря 9999 года, введенная вручную или находящаяся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>ДАТАЗНАЧ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>ДАТАЗНАЧ</b>,</li>
			<li>введите требуемый аргумент,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ДАТАЗНАЧ" src="../images/datevalue.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПОДСТАВИТЬ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПОДСТАВИТЬ</h1>
			<p>Функция <b>ПОДСТАВИТЬ</b> - это одна из функций для работы с текстом и данными. Заменяет ряд символов на новый.</p>
			<p>Синтаксис функции <b>ПОДСТАВИТЬ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ПОДСТАВИТЬ(текст;стар_текст;нов_текст;[номер_вхождения])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>текст</em></b> - строка, в которой требуется выполнить подстановку.</p> 
			<p style="text-indent: 50px;"><b><em>стар_текст</em></b> - строка, которую требуется заменить на новую.</p> 
			<p style="text-indent: 50px;"><b><em>нов_текст</em></b> - строка на которую требуется заменить старую.</p> 
			<p style="text-indent: 50px;"><b><em>номер_вхождения</em></b> - количество вхождений, которые требуется заменить. Необязательный аргумент. Если он опущен, заменяются все вхождения в строке <b><em>текст</em></b>.</p>
			<p>Эти данные можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>ПОДСТАВИТЬ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>ПОДСТАВИТЬ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ПОДСТАВИТЬ" src="../images/substitute.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПДЛИТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ПДЛИТ</h1>
            <p>Функция <b>ПДЛИТ</b> - это одна из финансовых функций. Возвращает количество периодов, которые необходимы инвестиции для достижения заданного значения.</p>
            <p>Синтаксис функции <b>ПДЛИТ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ПДЛИТ(ставка;пс;бс)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>ставка</em></b> - процентная ставка за период.</p>
            <p style="text-indent: 50px;"><b><em>пс</em></b> - стоимость инвестиции на текущий момент.</p>
            <p style="text-indent: 50px;"><b><em>бс</em></b> - желательная стоимость инвестиции в будущем.</p>
            
            <p class="note"><b>Примечание:</b> все аргументы должны быть представлены положительными числами.</p>
            <p>Эти аргументы можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
            <p>Чтобы применить функцию <b>ПДЛИТ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Финансовые</b>,</li>
                <li>щелкните по функции <b>ПДЛИТ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ПДЛИТ" src="../images/pduration.png" /></p>
        </div>
	</body>
</html>
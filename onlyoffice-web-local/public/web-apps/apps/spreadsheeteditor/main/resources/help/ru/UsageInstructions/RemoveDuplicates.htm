<!DOCTYPE html>
<html>
	<head>
		<title>Удаление дубликатов</title>
		<meta charset="utf-8" />
        <meta name="description" content="Удалите повторяющиеся значения из выбранного диапазона данных." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Удаление дубликатов</h1>
            <p>Вы можете удалить повторяющиеся значения из выбранного диапазона данных или <a href="FormattedTables.htm" onclick="onhyperlinkclick(this)">форматированной таблицы</a>.</p>
            <p>Для удаления дубликатов:</p>
            <ol>
                <li>Выделите нужный диапазон ячеек, который содержит повторяющиеся значения.</li>
                <li>Перейдите на вкладку <b>Данные</b> и нажмите кнопку <div class = "icon icon-removeduplicates_icon"></div> <b>Удалить дубликаты</b> на верхней панели инструментов.
                <p>Если вы хотите удалить дубликаты из форматированной таблицы, также можно использовать опцию <span class="icon icon-removeduplicates"></span> <b>Удалить дубликаты</b> на правой боковой панели.</p>
                <p>Если вы выделите определенную часть диапазона данных, появится окно с предупреждением, в котором будет предложено расширить область выделения, чтобы включить в нее весь диапазон данных, или продолжить операцию с данными, выделенными в данный момент. Нажмите кнопку <b>Развернуть</b> или <b>Удалить в выделенном диапазоне</b>. Если вы выберете опцию <b>Удалить в выделенном диапазоне</b>, повторяющиеся значения в ячейках, смежных с выделенным диапазоном, не будут удалены.</p>
                <p><img alt="Удаление дубликатов - предупреждение" src="../images/removeduplicates_warning.png" /></p>
                <p>Откроется окно <b>Удалить дубликаты</b>:</p>
                    <p><img alt="Удаление дубликатов" src="../images/removeduplicates_window.png" /></p>
                </li>
                <li>Отметьте нужные опции в окне <b>Удалить дубликаты</b>:
                <ul>
                    <li><b>Мои данные содержат заголовки</b> - установите эту галочку, чтобы исключить заголовки столбцов из выделенной области.</li>
                    <li><b>Столбцы</b> - оставьте опцию <b>Выделить всё</b>, выбранную по умолчанию, или снимите с нее галочку и выделите только нужные столбцы.</li>
                </ul>
                </li>
                <li>Нажмите на кнопку <b>OK</b>.</li>
            </ol>            
            <p>Повторяющиеся значения из выбранного диапазона данных будут удалены. Появится окно с информацией о том, сколько повторяющихся значений было удалено и сколько уникальных значений осталось:</p>
            <p><img alt="Удаленные дубликаты" src="../images/removeduplicates_result.png" /></p>
            <p>Если вы хотите восстановить удаленные данные сразу после удаления, используйте кнопку <b>Отменить</b> <span class="icon icon-undo"></span> на верхней панели инструментов или сочетание клавиш <em>Ctrl+Z</em>.</p>
            
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция РАЗНДАТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция РАЗНДАТ</h1>
			<p>Функция <b>РАЗНДАТ</b>  - это одна из функций даты и времени. Возвращает разницу между двумя датами (начальной и конечной) согласно заданному интервалу (единице).</p>
			<p>Синтаксис функции <b>РАЗНДАТ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>РАЗНДАТ(нач_дата;кон_дата;единица)</em></b></p> 
			<p><em>где</em></p>
			<p><b><em>нач_дата</em></b> и <b><em>кон_дата</em></b> - две даты, разницу между которыми требуется вычислить,</p>
			<p><b><em>единица</em></b> - заданный интервал; может иметь одно из следующих значений:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Единица</b></td>
					<td><b>Пояснение</b></td>
				</tr>
				<tr>
					<td>Y</td>
					<td>Число полных лет.</td>
				</tr>
				<tr>
					<td>M</td>
					<td>Число полных месяцев.</td>
				</tr>
				<tr>
					<td>D</td>
					<td>Число дней.</td>
				</tr>
				<tr>
					<td>MD</td>
					<td>Разница между днями (значения месяца и года не учитываются).</td>
				</tr>
				<tr>
					<td>YM</td>
					<td>Разница между месяцами (значения дня и года не учитываются).</td>
				</tr>
				<tr>
					<td>YD</td>
					<td>Разница между днями (значения года не учитываются).</td>
				</tr>
			</table>
			<p>Чтобы применить функцию <b>РАЗНДАТ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>РАЗНДАТ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция DATEDIF" src="../images/datedif.png" /></p>
		</div>
	</body>
</html>
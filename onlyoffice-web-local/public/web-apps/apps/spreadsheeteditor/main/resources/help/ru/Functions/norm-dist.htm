<!DOCTYPE html>
<html>
	<head>
		<title>Функция НОРМ.РАСП</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция НОРМ.РАСП</h1>
            <p>Функция <b>НОРМ.РАСП</b> - это одна из статистических функций. Возвращает нормальную функцию распределения для указанного среднего и стандартного отклонения.</p>
            <p>Синтаксис функции <b>НОРМ.РАСП</b>:</p>
            <p style="text-indent: 150px;"><b><em>НОРМ.РАСП(x;среднее;стандартное_откл;интегральная)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>x</em></b> - значение, для которого требуется вычислить распределение; любое числовое значение.</p>
            <p style="text-indent: 50px;"><b><em>среднее</em></b> - среднее арифметическое распределения; любое числовое значение.</p>
            <p style="text-indent: 50px;"><b><em>стандартное_откл</em></b> - стандартное отклонение распределения; числовое значение больше 0.</p>
            <p style="text-indent: 50px;"><b><em>интегральная</em></b> - форма функции; логическое значение: ИСТИНА или ЛОЖЬ. Если этот аргумент имеет значение ИСТИНА, возвращается интегральная функция распределения. Если этот аргумент имеет значение ЛОЖЬ, возвращается весовая функция распределения.</p>
            <p><p>Эти значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
            <p>Чтобы применить функцию <b>НОРМ.РАСП</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>НОРМ.РАСП</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция НОРМ.РАСП" src="../images/norm-dist.png" /></p>
        </div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция НАЙТИ/НАЙТИБ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция НАЙТИ/НАЙТИБ</h1>
			<p>Функция <b>НАЙТИ</b>/<b>НАЙТИБ</b> - это одна из функций для работы с текстом и данными. Используется для поиска заданной подстроки (искомый_текст) внутри строки (просматриваемый_текст). Функция <b>НАЙТИ</b> предназначена для языков, использующих однобайтовую кодировку (SBCS), в то время как <b>НАЙТИБ</b> - для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.</p>
			<p>Синтаксис функции <b>НАЙТИ/НАЙТИБ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>НАЙТИ(искомый_текст;просматриваемый_текст;[нач_позиция])</em></b></p> 
			<p style="text-indent: 150px;"><b><em>НАЙТИБ(искомый_текст;просматриваемый_текст;[нач_позиция])</em></b></p> 
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>искомый_текст</em></b> - строка, которую требуется найти,</p>
			<p style="text-indent: 50px;"><b><em>просматриваемый_текст</em></b> - строка, в которой производится поиск,</p>
			<p style="text-indent: 50px;"><b><em>нач_позиция</em></b> - позиция в строке, откуда начнется поиск. Необязательный аргумент. Если он опущен, функция НАЙТИ/НАЙТИБ начинает поиск с начала строки.</p>
			<p>Эти значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p class="note"><b>Примечание</b>: если соответствий нет, функция НАЙТИ/НАЙТИБ возвращает ошибку <b>#ЗНАЧ!</b>.</p>
			<p>Чтобы применить функцию <b>НАЙТИ/НАЙТИБ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>НАЙТИ/НАЙТИБ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,
			<p class="note"><b>Примечание</b>: функция НАЙТИ/НАЙТИБ <b>учитывает регистр</b>.</p>
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция НАЙТИ/НАЙТИБ" src="../images/find.png" /></p>
		</div>
	</body>
</html>
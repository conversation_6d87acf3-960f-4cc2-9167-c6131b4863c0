<!DOCTYPE html>
<html>
	<head>
		<title>Функция ФИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ФИ</h1>
            <p>Функция <b>ФИ</b> - это одна из статистических функций. Возвращает значение функции плотности для стандартного нормального распределения.</p>
            <p>Синтаксис функции <b>ФИ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ФИ(x)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>x</em></b> число, для которого требуется вычислить плотность стандартного нормального распределения; любое числовое значение.</p>
            <p>Это числовое значение можно ввести вручную или использовать в качестве аргумента ссылку на ячейку.</p>
            <p>Чтобы применить функцию <b>ФИ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>ФИ</b>,</li>
                <li>введите требуемый аргумент,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><div class = "smb smb-phi"></div></p>
        </div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ОБЩДОХОД</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ОБЩДОХОД</h1>
			<p>Функция <b>ОБЩДОХОД</b> - это одна из финансовых функций. Используется для вычисления общей суммы, выплачиваемой в погашение основного долга по инвестиции между двумя периодами времени исходя из указанной процентной ставки и постоянной периодичности платежей.</p>
			<p>Синтаксис функции <b>ОБЩДОХОД</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ОБЩДОХОД(ставка;кол_пер;нз;нач_период;кон_период;тип)</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>ставка</em></b> - это процентная ставка по инвестиции.</p> 
				<p style="text-indent: 50px;"><b><em>кол_пер</em></b> - это количество платежных периодов.</p> 
				<p style="text-indent: 50px;"><b><em>нз</em></b> - это текущий размер выплат.</p>
                <p style="text-indent: 50px;"><b><em>нач_период</em></b> - это первый период, включенный в вычисления. Может принимать значение от <b><em>1</em></b> до <b><em>кол_пер</em></b>.</p>
                <p style="text-indent: 50px;"><b><em>кон_период</em></b> - это последний период, включенный в вычисления. Может принимать значение от <b><em>1</em></b> до <b><em>кол_пер</em></b>.</p>
				<p style="text-indent: 50px;"><b><em>тип</em></b> - это время, когда производится платеж. Если этот аргумент равен 0 или опущен, то 
        предполагается, что платеж производится в конце периода. Если аргумент <b><em>тип</em></b> равен 1, то платежи производятся в начале периода.</p>
            <p class="note">
                <b>Примечание:</b> выплачиваемые денежные средства (например, сберегательные вклады) представляются отрицательными числами; получаемые денежные средства (например, дивиденды) представляются положительными числами.
                Единицы измерения аргументов "ставка" и "кол_пер" должны быть согласованы между собой: используйте N%/12 для аргумента "ставка"
                и N*12 для аргумента "кол_пер", если речь идет о ежемесячных платежах, N%/4 для аргумента "ставка" и N*4 для аргумента "кол_пер",
                если речь идет о ежеквартальных платежах, N% для аргумента "ставка" и N для аргумента "кол_пер", если речь идет о ежегодных платежах.
            </p>
      <p>Значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
      <p>
        Чтобы применить функцию <b>ОБЩДОХОД</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Финансовые</b>,
        </li>
        <li>
          щелкните по функции <b>ОБЩДОХОД</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выделенной ячейке.</p>      
			<p style="text-indent: 150px;"><img alt="Функция ОБЩДОХОД" src="../images/cumprinc.png" /></p>
		</div>
	</body>
</html>
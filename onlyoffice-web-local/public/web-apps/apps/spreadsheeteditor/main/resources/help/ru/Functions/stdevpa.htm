<!DOCTYPE html>
<html>
	<head>
		<title>Функция СТАНДОТКЛОНПА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СТАНДОТКЛОНПА</h1>
			<p>Функция <b>СТАНДОТКЛОНПА</b> - это одна из статистических функций. Используется для анализа диапазона данных и возвращает стандартное отклонение по всей совокупности значений.</p>
			<p>Синтаксис функции <b>СТАНДОТКЛОНПА</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СТАНДОТКЛОНПА(список_аргументов)</em></b></p> 
			<p>где <b><em>список_аргументов</em></b> - это до 255 числовых значений, введенных вручную или находящихся в ячейках, на которые даются ссылки.</p>
      <p class="note">
        <b>Примечание:</b> текстовые значения и значения ЛОЖЬ принимаются равными 0, значения ИСТИНА принимаются равными 1, пустые ячейки игнорируются.        
      </p>
      <p>Чтобы применить функцию <b>СТАНДОТКЛОНПА</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,      
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
        <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
      <li>щелкните по функции <b>СТАНДОТКЛОНПА</b>,</li>
			<li>введите требуемые аргументы через точку с запятой или выделите диапазон ячеек мышью,</li>        
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выделенной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СТАНДОТКЛОНПА" src="../images/stdevpa.png" /></p>
		</div>
	</body>
</html>
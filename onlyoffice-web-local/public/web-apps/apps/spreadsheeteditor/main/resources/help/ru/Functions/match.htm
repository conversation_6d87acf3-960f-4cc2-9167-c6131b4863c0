<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПОИСКПОЗ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПОИСКПОЗ</h1>
			<p>Функция <b>ПОИСКПОЗ</b> - это одна из поисковых функций. Возвращает относительное положение заданного элемента в диапазоне ячеек.</p>
			<p>Синтаксис функции <b>ПОИСКПОЗ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ПОИСКПОЗ(искомое_значение;просматриваемый_массив;[тип_сопоставления])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><em><b>искомое_значение</b></em> - это значение для поиска в <em><b>просматриваемом массиве</b></em>. Оно может быть числовым, логическим, текстовым или представлять собой ссылку на ячейку.</p>
			<p style="text-indent: 50px;"><em><b>просматриваемый_массив</b></em> - это одна строка или один стобец для анализа.</p>
      <p style="text-indent: 50px;">
        <em>
          <b>тип_сопоставления</b>
        </em> - это вид совпадения. Это необязательный аргумент. Он может принимать одно из следующих числовых значений:</p>
				<table style="width: 40%">
				<tr>
					<td><b>Числовое значение</b></td>
					<td><b>Комментарий</b></td>
				</tr>
				<tr>
					<td>1 или опущено</td>
					<td>
            Значения должны быть отсортированы в порядке возрастания. Если точное совпадение не найдено, функция возвращает наибольшее значение, не превышающее <em><b>искомое_значение</b></em>.</td>
				</tr>
				<tr>
					<td>0</td>
					<td>
            Значения могут быть отсортированы в любом порядке. Если точное совпадение не найдено, функция возвращает ошибку #Н/Д.</td>
				</tr>
				<tr>
					<td>-1</td>
					<td>
            Значения должны быть отсортированы в порядке убывания. Если точное значение не найдено, функция возвращает наименьшее значение, превышающее <em><b>искомое_значение</b></em>.</td>
				</tr>
			</table>
      <p>
        Чтобы применить функцию <b>ПОИСКПОЗ</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Поиск и ссылки</b>,
        </li>
        <li>
          щелкните по функции <b>ПОИСКПОЗ</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой или выделите мышью диапазон ячеек,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ПОИСКПОЗ" src="../images/match.png" /></p>
		</div>
	</body>
</html>
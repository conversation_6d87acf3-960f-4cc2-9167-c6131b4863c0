<!DOCTYPE html>
<html>
	<head>
		<title>Функция КОДСИМВ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция КОДСИМВ</h1>
			<p>Функция <b>КОДСИМВ</b> - это одна из функций для работы с текстом и данными. Она возвращает числовой код ASCII, соответствующий заданному символу или первому символу в ячейке.</p>
			<p>Синтаксис функции <b>КОДСИМВ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>КОДСИМВ(текст)</em></b></p> 
			<p>где <b><em>текст</em></b> - данные, введенные вручную или находящиеся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>КОДСИМВ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>КОДСИМВ</b>,</li>
			<li>введите требуемый аргумент,
			<p class="note"><b>Примечание</b>: функция КОДСИМВ <b>учитывает регистр</b>.</p>
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция КОДСИМВ" src="../images/code.png" /></p>
		</div>
	</body>
</html>
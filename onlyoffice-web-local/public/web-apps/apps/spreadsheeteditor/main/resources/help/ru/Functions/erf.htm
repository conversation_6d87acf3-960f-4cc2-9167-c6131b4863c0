<!DOCTYPE html>
<html>
	<head>
		<title>Функция ФОШ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ФОШ</h1>
			<p>Функция <b>ФОШ</b> - это одна из инженерных функций. Используется для расчета значения функции ошибки, проинтегрированного в интервале от заданного нижнего до заданного верхнего предела.</p>
			<p>Синтаксис функции <b>ФОШ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ФОШ(нижний_предел;[верхний_предел])</em></b></p> 
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>нижний_предел</em></b> - это нижний предел интегрирования.</p>            
      <p style="text-indent: 50px;"><b><em>верхний_предел</em></b> - это верхний предел интегрирования. Это необязательный аргумент. Если он опущен, значение функции ошибки будет проинтегрировано в пределах от 0 до значения аргумента <b><em>нижний_предел</em></b>.</p>
			<p>Числовые значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>      
      <p>Чтобы применить функцию <b>ФОШ</b>,</p>			
        <ol>
          <li>выделите ячейку, в которой требуется отобразить результат,</li>
          <li>
            щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
            <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
            <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
          </li>
          <li>
            выберите из списка группу функций <b>Инженерные</b>,
          </li>
          <li>
            щелкните по функции <b>ФОШ</b>,
            <li>введите требуемые аргументы через точку с запятой,</li>
            <li>
              нажмите клавишу <b>Enter</b>.
            </li>
          </ol>
            <p>Результат будет отображен в выделенной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ФОШ" src="../images/erf.png" /></p>
		</div>
	</body>
</html>
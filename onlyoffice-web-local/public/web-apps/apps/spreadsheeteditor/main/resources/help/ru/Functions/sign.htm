<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЗНАК</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЗНАК</h1>
			<p>Функция <b>ЗНАК</b> - это одна из математических и тригонометрических функций. Она определяет знак числа. Если число положительное, функция возвращает значение <b>1</b>. Если число отрицательное, функция возвращает значение <b>-1</b>. Если число равно <b>0</b>, функция возвращает значение <b>0</b>.</p>
			<p>Синтаксис функции <b>ЗНАК</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЗНАК(x)</em></b></p> 
			<p>где <b><em>x</em></b> - это числовое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>ЗНАК</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>ЗНАК</b>,</li>
			<li>введите требуемый аргумент,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><em>Например:</em></p>
			<p>Заданный аргумент - это <b>A1</b>, где значение <b>A1</b> равно <b>12</b>. Это положительное число, поэтому функция возвращает значение <b>1</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция SIGN: Положительное число" src="../images/signpositive.png" /></p>
			<p>Если изменить значение <b>A1</b> с <b>12</b> на <b>-12</b>, функция возвращает <b>-1</b>:</p>
			<p style="text-indent: 150px;"><img alt="Функция SIGN: Отрицательное число" src="../images/signnegative.png" /></p>
		</div>
	</body>
</html>
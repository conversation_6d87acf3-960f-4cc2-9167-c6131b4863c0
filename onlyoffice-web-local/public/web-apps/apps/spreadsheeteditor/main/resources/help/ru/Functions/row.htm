<!DOCTYPE html>
<html>
	<head>
		<title>Функция СТРОКА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СТРОКА</h1>
			<p>Функция <b>СТРОКА</b> - это одна из поисковых функций. Она возвращает номер строки для ссылки на ячейку.</p>
			<p>Синтаксис функции <b>СТРОКА</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СТРОКА([ссылка])</em></b></p> 
			<p>где <b><em>ссылка</em></b> - ссылка на ячейку.</p>
			<p class="note"><b>Примечание</b>: <b><em>ссылка</em></b> - необязательный аргумент. Если он опущен, функция возвращает номер строки той ячейки, в которую вставлена функция <b>СТРОКА</b>. </p>
			<p>Чтобы применить функцию <b>СТРОКА</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
			<li>щелкните по функции <b>СТРОКА</b>,</li>
			<li>введите требуемый аргумент,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СТРОКА" src="../images/row.png" /></p>
		</div>
	</body>
</html>
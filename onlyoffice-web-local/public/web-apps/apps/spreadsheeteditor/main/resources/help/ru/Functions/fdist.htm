<!DOCTYPE html>
<html>
	<head>
		<title>Функция FРАСП</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция FРАСП</h1>
			<p>Функция <b>FРАСП</b> - это одна из статистических функций. Возвращает правый хвост F-распределения вероятности для двух наборов данных. Эта функция позволяет определить, имеют ли два множества данных различные степени разброса результатов.</p>
			<p>Синтаксис функции <b>FРАСП</b>:</p> 
			<p style="text-indent: 150px;"><b><em>FРАСП(x;степени_свободы1;степени_свободы2)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>x</em></b> - значение, для которого вычисляется функция. Числовое значение больше 0.</p>
			<p style="text-indent: 50px;"><b><em>степени_свободы1</em></b> - числитель степеней свободы; числовое значение больше 1 и меньше 10^10.</p>
			<p style="text-indent: 50px;"><b><em>степени_свободы2</em></b> - знаменатель степеней свободы; числовое значение больше 1 и меньше 10^10.</p>
			<p>Эти аргументы можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
 			<p>Чтобы применить функцию <b>FРАСП</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>FРАСП</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция FРАСП" src="../images/fdist.png" /></p>
		</div>
	</body>
</html>
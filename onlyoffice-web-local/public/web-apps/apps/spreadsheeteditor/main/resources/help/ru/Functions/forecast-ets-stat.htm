<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПРЕДСКАЗ.ETS.СТАТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПРЕДСКАЗ.ETS.СТАТ</h1>
			<p>Функция <b>ПРЕДСКАЗ.ETS.СТАТ</b> - это одна из статистических функций. Возвращает статистическое значение, являющееся результатом прогнозирования временного ряда. Тип статистики определяет, какая именно статистика используется этой функцией.</p>
			<p>Синтаксис функции <b>ПРЕДСКАЗ.ETS.СТАТ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ПРЕДСКАЗ.ETS.СТАТ(значения;временная_шкала;тип_статистики;[сезонность];[заполнение_данных];[агрегирование])</em></b></p> 
			<p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>значения</em></b> - диапазон ретроспективных данных, на основе которых прогнозируется новое значение.</p>
            <p style="text-indent: 50px;"><b><em>временная_шкала</em></b> - диапазон значений даты/времени, которые соответствуют ретроспективным данным. Диапазон <b><em>временная_шкала</em></b> должен быть такого же размера, что и <b><em>значения</em></b>. Значения даты/времени должны отстоять друг от друга на одинаковый интервал (хотя функция может обработать до 30% отсутствующих значений в соответствии с указанным значением аргумента <b><em>заполнение_данных</em></b> и агрегировать повторяющиеся значения в соответствии с указанным значением аргумента <b><em>агрегирование</em></b>).</p>
                <p style="text-indent: 50px;"><b><em>тип_статистики</em></b> - числовое значение от 1 до 8, указывающее, какой статистический показатель возвращается. Допустимые значения приведены в таблице ниже.</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Числовое значение</b></td>
                    <td><b>Статистический показатель</b></td>
                </tr>
                <tr>
                    <td>1</td>
                    <td>Параметр "альфа" алгоритма ETS - значение параметра базы.</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Параметр "бета" алгоритма ETS - значение параметра тренда.</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Параметр "гамма" алгоритма ETS - значение параметра сезонности.</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>Показатель MASE (средняя абсолютная масштабированная погрешность) - мера точности прогноза.</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>Показатель SMAPE (симметричная средняя абсолютная процентная погрешность) - мера точности прогноза на основе процентных погрешностей.</td>
                </tr>
                <tr>
                    <td>6</td>
                    <td>Показатель MAE (средняя абсолютная погрешность) - мера точности прогноза.</td>
                </tr>
                <tr>
                    <td>7</td>
                    <td>Показатель RMSE (среднеквадратическая погрешность) - мера расхождения между спрогнозированными и наблюдаемыми значениями.</td>
                </tr>
                <tr>
                    <td>8</td>
                    <td>Величина шага, определенная во временной шкале .</td>
                </tr>
            </table>
            <p style="text-indent: 50px;"><b><em>сезонность</em></b> - числовое значение, указывающее, какой метод должен использоваться для определения сезонности. Это необязательный аргумент. Допустимые значения приведены в таблице ниже.</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Числовое значение</b></td>
                    <td><b>Поведение</b></td>
                </tr>
                <tr>
                    <td>1 или опущено</td>
                    <td>Сезонность определяется автоматически. В качестве длины сезонного шаблона используются положительные целые числа.</td>
                </tr>
                <tr>
                    <td>0</td>
                    <td>Фактор сезонности не используется, прогноз будет линейным.</td>
                </tr>
                <tr>
                    <td>целое число, большее или равное 2</td>
                    <td>В качестве длины сезонного шаблона используется указанное число.</td>
                </tr>
            </table>
            <p style="text-indent: 50px;"><b><em>заполнение_данных</em></b> - числовое значение, указывающее, как обрабатывать отсутствующие данные в диапазоне <b><em>временная_шкала</em></b>. Это необязательный аргумент. Допустимые значения приведены в таблице ниже.</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Числовое значение</b></td>
                    <td><b>Поведение</b></td>
                </tr>
                <tr>
                    <td>1 или опущено</td>
                    <td>Отсутствующие значения вычисляются как среднее между соседними точками.</td>
                </tr>
                <tr>
                    <td>0</td>
                    <td>Отсутствующие значения рассматриваются как нулевые.</td>
                </tr>
            </table>
            <p style="text-indent: 50px;"><b><em>агрегирование</em></b> - числовое значение, указывающее, с помощью какой функции надо агрегировать одинаковые значения времени в диапазоне <b><em>временная_шкала</em></b>. Это необязательный аргумент. Допустимые значения приведены в таблице ниже.</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Числовое значение</b></td>
                    <td><b>Функция</b></td>
                </tr>
                <tr>
                    <td>1 или опущено</td>
                    <td><a href="../Functions/average.htm" onclick="onhyperlinkclick(this)">СРЗНАЧ</a></td>
                </tr>
                <tr>
                    <td>2</td>
                    <td><a href="../Functions/count.htm" onclick="onhyperlinkclick(this)">СЧЁТ</a></td>
                </tr>
                <tr>
                    <td>3</td>
                    <td><a href="../Functions/counta.htm" onclick="onhyperlinkclick(this)">СЧЁТЗ</a></td>
                </tr>
                <tr>
                    <td>4</td>
                    <td><a href="../Functions/max.htm" onclick="onhyperlinkclick(this)">МАКС</a></td>
                </tr>
                <tr>
                    <td>5</td>
                    <td><a href="../Functions/median.htm" onclick="onhyperlinkclick(this)">МЕДИАНА</a></td>
                </tr>
                <tr>
                    <td>6</td>
                    <td><a href="../Functions/min.htm" onclick="onhyperlinkclick(this)">МИН</a></td>
                </tr>
                <tr>
                    <td>7</td>
                    <td><a href="../Functions/sum.htm" onclick="onhyperlinkclick(this)">СУММ</a></td>
                </tr>
            </table> 
			<p>Чтобы применить функцию <b>ПРЕДСКАЗ.ETS.СТАТ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>ПРЕДСКАЗ.ETS.СТАТ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ПРЕДСКАЗ.ETS.СТАТ" src="../images/forecast-ets-stat.png" /></p>
		</div>
	</body>
</html>
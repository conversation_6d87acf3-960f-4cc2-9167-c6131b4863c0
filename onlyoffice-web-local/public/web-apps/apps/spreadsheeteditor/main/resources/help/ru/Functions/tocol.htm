<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПОСТОЛБЦ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Функция ПОСТОЛБЦ</h1>
			<p>Функция <b>ПОСТОЛБЦ</b> - это одна из поисковых функций. Возвращает массив в виде одного столбца.</p>
			<p class="note">Обратите внимание, что это формула массива. Чтобы узнать больше, обратитесь к статье <a href="../UsageInstructions/InsertArrayFormulas.htm" onclick="onhyperlinkclick(this)">Вставка формул массива</a>.</p>
			<p>Синтаксис функции <b>ПОСТОЛБЦ</b>:</p>
			<p style="text-indent: 150px;"><b><em>ПОСТОЛБЦ (массив, [игнорировать], [сканировать_по_столбцам])</em></b></p>
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>массив</em></b> - массив или ссылка, возвращаемая в виде столбца.</p>
			<p style="text-indent: 50px;"><b><em>игнорировать</em></b> - определяет, следует ли игнорировать определенные типы значений. По умолчанию значения не игнорируются. Используются следующие значения: <b>0</b> чтобы сохранить все значения (по умолчанию); <b>1</b> чтобы игнорировать пустые ячейки; <b>2</b> чтобы игнорировать ошибки; <b>3</b> чтобы игнорировать пустые ячейки и ошибки.</p>
			<p style="text-indent: 50px;"><b><em>сканировать_по_столбцам</em></b> - используется, чтобы сканировать массив по столбцам. По умолчанию массив сканируется по строкам. Сканирование определяет, упорядочены ли значения по строке или по столбцу.</p>
			<p>Чтобы применить функцию <b>ПОСТОЛБЦ</b>,</p>
			<ol>
				<li>выделите ячейку, в которой требуется отобразить результат,</li>
				<li>
					щелкните по значку <b>Вставить функцию</b> <div class="icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
					<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
					<br />или щелкните по значку <div class="icon icon-function"></div> перед строкой формул,
				</li>
				<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
				<li>щелкните по функции <b>ПОСТОЛБЦ</b>,</li>
				<li>введите требуемые аргументы через точку с запятой,</li>
				<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<!--<p style="text-indent: 150px;"><img alt="Функция ПОСТОЛБЦ" src="../images/tocol.png" /></p>-->
		</div>
	</body>
</html>
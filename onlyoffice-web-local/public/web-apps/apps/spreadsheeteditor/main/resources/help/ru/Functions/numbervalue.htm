<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЧЗНАЧ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЧЗНАЧ</h1>
			<p>Функция <b>ЧЗНАЧ</b> - это одна из функций для работы с текстом и данными. Преобразует текст в числовое значение независимым от локали способом. Если преобразуемый текст не является числом, функция возвращает ошибку <b>#ЗНАЧЕНИЕ!</b>.</p>
			<p>Синтаксис функции <b>ЧЗНАЧ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЧЗНАЧ(текст;[десятичный_разделитель];[разделитель_групп])</em></b></p> 
			<p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>текст</em></b> - это текстовые данные, представляющие число.</p>
            <p style="text-indent: 50px;"><b><em>десятичный_разделитель</em></b> - это символ, используемый для разделения дробной и целой части результата. Это необязательный аргумент. Если он опущен, используется текущая локаль.</p>
            <p style="text-indent: 50px;"><b><em>разделитель_групп</em></b> - это символ, используемый для разделения групп цифр, например тысяч от сотен и миллионов от тысяч. Это необязательный аргумент. Если он опущен, используется текущая локаль.</p>
            <p>Значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
            <p>Чтобы применить функцию <b>ЧЗНАЧ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Текст и данные</b>,</li>
                <li>щелкните по функции <b>ЧЗНАЧ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ЧЗНАЧ" src="../images/numbervalue.png" /></p>
		</div>
	</body>
</html>
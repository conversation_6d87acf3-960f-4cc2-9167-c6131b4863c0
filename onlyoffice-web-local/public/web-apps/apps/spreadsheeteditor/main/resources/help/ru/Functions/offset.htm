<!DOCTYPE html>
<html>
	<head>
		<title>Функция СМЕЩ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СМЕЩ</h1>
			<p>Функция <b>СМЕЩ</b> - это одна из поисковых функций. Возвращает ссылку на ячейку, отстоящую от заданной ячейки (или верхней левой ячейки в диапазоне ячеек) на определенное число строк и столбцов.</p>
			<p>Синтаксис функции <b>СМЕЩ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СМЕЩ(ссылка;смещ_по_строкам;смещ_по_столбцам;[высота];[ширина])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><em><b>ссылка</b></em> - это ссылка на исходную ячейку (диапазон ячеек).</p>
			<p style="text-indent: 50px;"><em><b>смещ_по_строкам</b></em> - это количество строк, которые требуется отсчитать вверх или вниз, чтобы левая верхняя ячейка результата ссылалась на нужную ячейку.
        Положительные числа означают, что результат смещается вниз от исходной ячейки. Отрицательные числа означают, что он смещается вверх от исходной ячейки.</p>
			<p style="text-indent: 50px;"><em><b>смещ_по_столбцам</b></em> - это количество столбцов, которые требуется отсчитать влево или вправо, чтобы левая верхняя ячейка результата ссылалась на нужную ячейку. 
      Положительные числа означают, что результат смещается вправо от исходной ячейки. Отрицательные числа означают, что он смещается влево от исходной ячейки.</p>      
      <p style="text-indent: 50px;"><em><b>высота</b></em> - это количество строк в возвращаемой ссылке. Это значение должно быть задано положительным числом. Если оно опущено, оно полагается равным высоте исходного диапазона.</p>
      <p style="text-indent: 50px;"><em><b>ширина</b></em> - это количество столбцов в возвращаемой ссылке. Это значение должно быть задано положительным числом. Если оно опущено, оно полагается равным ширине исходного диапазона.</p>
      <p>
        Чтобы применить функцию <b>СМЕЩ</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Поиск и ссылки</b>,
        </li>
        <li>
          щелкните по функции <b>СМЕЩ</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выбранной ячейке.</p>      
			<p style="text-indent: 150px;"><img alt="Функция СМЕЩ" src="../images/offset.png" /></p>
		</div>
	</body>
</html>
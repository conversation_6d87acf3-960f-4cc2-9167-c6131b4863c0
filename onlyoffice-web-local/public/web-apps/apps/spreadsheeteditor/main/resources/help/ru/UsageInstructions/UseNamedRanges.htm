<!DOCTYPE html>
<html>
	<head>
		<title>Использование именованных диапазонов</title>
		<meta charset="utf-8" />
        <meta name="description" content="Используйте именованные диапазоны, чтобы облегчить работу с формулами" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Использование именованных диапазонов</h1>
            <p>Имена - это осмысленные обозначения, которые можно присвоить ячейке или диапазону ячеек и использовать для упрощения работы с формулами. При создании формул в качестве аргумента можно использовать имя, а не ссылку на диапазон ячеек. Например, если присвоить диапазону ячеек имя <em>Годовой_доход</em>, то можно будет вводить формулу <em>=СУММ(Годовой_доход)</em> вместо <em>=СУММ(B1:B12)</em> и т.д. В таком виде формулы становятся более понятными. Эта возможность также может быть полезна, если большое количество формул ссылается на один и тот же диапазон ячеек. При изменении адреса диапазона можно один раз внести исправление в Диспетчере имен, а не редактировать все формулы по одной.</p>
			<p>Есть два типа имен, которые можно использовать:</p>
            <ul>
                <li><b>Определенное имя</b> – произвольное имя, которое вы можете задать для некоторого диапазона ячеек. К определенным именам также относятся имена, создаваемые автоматически при установке <a href="SavePrintDownload.htm#printarea" onclick="onhyperlinkclick(this)">областей печати</a>.</li>
                <li><b>Имя таблицы</b> – стандартное имя, которое автоматически присваивается <a href="FormattedTables.htm" onclick="onhyperlinkclick(this)">новой форматированной таблице</a> (<em>Таблица1</em>, <em>Таблица2</em> и т.д.). Это имя впоследствии можно отредактировать.</li>
            </ul>
            <p>Если вы создали <a href="Slicers.htm" onclick="onhyperlinkclick(this)">срез для форматированной таблицы</a>, в <b>Диспетчере имен</b> также будет отображаться автоматически присвоенное имя среза (<em>Slicer_Столбец1</em>, <em>Slicer_Столбец2</em> и т.д. Это имя состоит из части <em>Slicer_</em> и имени поля, соответствующего заголовку столбца из исходного набора данных). Это имя впоследствии можно отредактировать.</p>
            <p>Имена также классифицируются по <b>Области</b> действия, то есть по области, в которой это имя распознается. Областью действия имени может быть вся книга (имя будет распознаваться на любом листе в этой книге) или отдельный лист (имя будет распознаваться только на указанном листе). Каждое имя в пределах одной области должно быть уникальным, одинаковые имена можно использовать внутри разных областей.</p>
            <h3>Создание новых имен</h3>
			<p>Чтобы создать новое определенное имя для выделенной области:</p>
			<ol>
			<li>Выделите ячейку или диапазон ячеек, которым требуется присвоить имя.</li>
			<li>Откройте окно создания нового имени удобным для вас способом:
                <ul>
                    <li>Щелкните по выделенной области правой кнопкой мыши и выберите из контекстного меню пункт <b>Присвоить имя</b></li>
                    <li>или щелкните по значку <b>Именованные диапазоны</b> <div class = "icon icon-namedranges"></div> на вкладке <b>Главная</b> верхней панели инструментов и выберите из меню опцию <b>Присвоить имя</b>.</li>
                    <li>или щелкните по кнопке <div class = "icon icon-namedrangesicon"></div> <b>Именованные диапазоны</b> на вкладке <b>Формула</b> верхней панели инструментов и выберите из меню опцию <b>Диспетчер имен</b>. В открывшемся окне выберите опцию <b>Новое</b>.</li>
                </ul>
                <p>Откроется окно <b>Новое имя</b>:</p>
                <p><img alt="Окно Новое имя" src="../images/newnamewindow.png" /></p>
            </li>
            <li>Введите нужное <b>Имя</b> в поле ввода текста.
            <p class="note"><b>Примечание</b>: имя не может начинаться с цифры, содержать пробелы или знаки препинания. Разрешено использовать нижние подчеркивания (_). Регистр не имеет значения.</p>   
            </li>
            <li>Укажите <b>Область</b> действия диапазона. По умолчанию выбрана область <em>Книга</em>, но можно указать отдельный лист, выбрав его из списка.</li>
            <li>Проверьте адрес выбранного <b>Диапазона данных</b>. В случае необходимости его можно изменить. Нажмите на кнопку <b>Выбор данных</b> - откроется окно <b>Выбор диапазона данных</b>.
            <p><img alt="Окно Выбор диапазона данных" src="../images/selectdatarange.png" /></p>   
            <p>Измените ссылку на диапазон ячеек в поле ввода или мышью выделите новый диапазон на листе и нажмите кнопку <b>OK</b>.</p>
            </li>
            <li>Нажмите кнопку <b>OK</b>, чтобы сохранить новое имя.</li>
			</ol>
            <p>Чтобы быстро создать новое имя для выделенного диапазона ячеек, можно также ввести нужное имя в поле "Имя" слева от строки формул и нажать <b>Enter</b>. Областью действия имени, созданного таким способом, является <em>Книга</em>.</p>
            <p><img alt="Поле Имя" src="../images/namefield.png" /></p>
			<h3>Управление именами</h3>
            <p>Получить доступ ко всем существующим именам можно через <b>Диспетчер имен</b>. Чтобы его открыть:</p>
            <ul>
                <li>щелкните по значку <b>Именованные диапазоны</b> <div class = "icon icon-namedranges"></div> на вкладке <b>Главная</b> верхней панели инструментов и выберите из меню опцию <b>Диспетчер имен</b></li>
                <li>или щелкните по стрелке в поле "Имя" и выберите опцию <b>Диспетчер имен</b>.</li>
            </ul>
            <p>Откроется окно <b>Диспетчер имен</b>:</p>
            <p><img alt="Окно Диспетчер имен" src="../images/namemanagerwindow.png" /></p>
            <p>Для удобства можно фильтровать имена, выбирая ту категорию имен, которую надо показать: <b>Все</b>, <b>Определенные имена</b>, <b>Имена таблиц</b>, <b>Имена на листе</b> или <b>Имена в книге</b>. В списке будут отображены имена, относящиеся к выбранной категории, остальные имена будут скрыты.</p>
            <p>Чтобы изменить порядок сортировки для отображенного списка, нажмите в этом окне на заголовок <b>Именованные диапазоны</b> или <b>Область</b>.</p>
            <p><b>Чтобы отредактировать имя</b>, выделите его в списке и нажмите кнопку <b>Изменить</b>. Откроется окно <b>Изменение имени</b>:</p>
            <p><img alt="Окно Изменение имени" src="../images/editnamewindow.png" /></p>
            <p>Для определенного имени можно изменить имя и диапазон данных, на который оно ссылается. Для имени таблицы можно изменить только имя. Когда будут сделаны все нужные изменения, нажмите кнопку <b>OK</b>, чтобы применить их. Чтобы сбросить изменения, нажмите кнопку <b>Отмена</b>. Если измененное имя используется в какой-либо формуле, формула будет автоматически изменена соответствующим образом.</p>
            <p><b>Чтобы удалить имя</b>, выделите его в списке и нажмите кнопку <b>Удалить</b>.</p>
            <p class="note"><b>Примечание</b>: если удалить имя, которое используется в формуле, формула перестанет работать (она будет возвращать ошибку <em>#ИМЯ?</em>).</p>  
            <p>В окне <b>Диспетчер имен</b> можно также создать новое имя, нажав кнопку <b>Новое</b>.</p>
			<h3 id="usingnames">Использование имен при работе с электронной таблицей</h3>
            <p><b>Для быстрого перемещения между диапазонами ячеек</b> можно нажать на стрелку в поле "Имя" и выбрать нужное имя из списка имен – на листе будет выделен диапазон данных, соответствующий этому имени.</p>
            <p><img alt="Список имен" src="../images/namelist.png" /></p>
            <p class="note"><b>Примечание</b>: в списке имен отображены определенные имена и имена таблиц, областью действия которых является текущий лист и вся книга.</p>
            <p><b>Чтобы добавить имя в качестве аргумента формулы</b>:</p>
            <ol>
                <li>Установите курсор там, куда надо вставить имя.</li>
                <li>Выполните одно из следующих действий:
                    <ul>
                        <li>введите имя нужного именованного диапазона вручную с помощью клавиатуры. Как только вы введете начальные буквы, появится список <b>Автозавершения формул</b>. По мере ввода в нем отображаются элементы (формулы и имена), которые соответствуют введенным символам. Можно выбрать нужное <b>определенное имя</b> или <b>имя таблицы</b> из списка и вставить его в формулу, дважды щелкнув по нему или нажав клавишу <b>Tab</b>.</li>
                        <li>или щелкните по значку <b>Именованные диапазоны</b> <div class = "icon icon-namedranges"></div> на вкладке <b>Главная</b> верхней панели инструментов, выберите из меню опцию <b>Вставить имя</b>, выберите нужное имя в окне <b>Вставка имени</b> и нажмите кнопку <b>OK</b>:
                            <p><img alt="Окно Вставка имени" src="../images/pastenamewindow.png" /></p>
                        </li>
                    </ul>
                </li>
            </ol>  
            <p class="note"><b>Примечание</b>: в окне <b>Вставка имени</b> отображены определенные имена и имена таблиц, областью действия которых является текущий лист и вся книга.</p>
            <p id="internallink"><b>Использование имени в качестве внутренней гиперссылки</b>:</p>
            <ol>
                <li>Установите курсор там, куда надо вставить гиперссылку.</li>
                <li>Перейдите на вкладку <b>Вставка</b> и нажмите кнопку <div class = "icon icon-addhyperlink"></div> <b>Гиперссылка</b>.</li>
                <li>
                    В открывшемся окне <b>Параметры гиперссылки</b> выберите вкладку <b>Внутренний диапазон данных</b> и укажите лист или имя.
                    <p><img alt="Параметры гиперссылки" src="../images/name_hyperlink.png" /></p>
                </li>
                <li>Нажмите кнопку <b>OK</b>.</li>
            </ol>
		</div>
	</body>
</html>
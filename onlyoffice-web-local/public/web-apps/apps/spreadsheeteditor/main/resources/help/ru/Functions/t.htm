<!DOCTYPE html>
<html>
	<head>
		<title>Функция T</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция T</h1>
			<p>Функция <b>T</b> - это одна из функций для работы с текстом и данными. Используется для проверки, является ли значение в ячейке (или используемое как аргумент) текстом или нет. Если это не текст, функция возвращает пустой результат. Если значение/аргумент является текстом, функция возвращает это же текстовое значение.</p>
			<p>Синтаксис функции <b>T</b>:</p> 
			<p style="text-indent: 150px;"><b><em>T(значение)</em></b></p> 
			<p>где <b><em>значение</em></b> - это данные, введенные вручную или находящиеся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>T</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>T</b>,</li>
			<li>введите требуемый аргумент,
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><em>Например:</em></p>
			<p>Здесь один аргумент: <em>значение</em> = <b>A1</b>, где <b>A1</b> имеет значение <b>дата и время</b>. Следовательно, функция возвращает значение <b>дата и время</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция T: Текст" src="../images/ttext.png" /></p>
			<p>Если изменить данные в ячейке <b>A1</b> с текстового на числовое значение, функция вернет пустой результат.</p>
			<p style="text-indent: 150px;"><img alt="Функция T: Числовое значение" src="../images/tnumber.png" /></p>
		</div>
	</body>
</html>
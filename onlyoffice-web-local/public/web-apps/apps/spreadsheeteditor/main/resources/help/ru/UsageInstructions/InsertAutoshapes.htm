<!DOCTYPE html>
<html>
	<head>
		<title>Вставка и форматирование автофигур</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавьте в электронную таблицу автофигуру и настройте ее свойства." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Вставка и форматирование автофигур</h1>
			<h3>Вставка автофигур</h3>
			<p>Для добавления автофигуры в электронную таблицу,</p>
			<ol>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>щелкните по значку <div class = "icon icon-insertautoshape"></div> <b>Фигура</b> на верхней панели инструментов,</li>
				<li>выберите одну из доступных групп автофигур: <b>Последние использованные</b>, <b>Основные фигуры</b>, <b>Фигурные стрелки</b>, <b>Математические знаки</b>, <b>Схемы</b>, <b>Звезды и ленты</b>, <b>Выноски</b>, <b>Кнопки</b>, <b>Прямоугольники</b>, <b>Линии</b>,</li>
				<li>щелкните по нужной автофигуре внутри выбранной группы,</li>
				<li>установите курсор там, где требуется поместить автофигуру,</li>
				<li>после того как автофигура будет добавлена, можно <a href="ManipulateObjects.htm" onclick="onhyperlinkclick(this)">изменить ее размер и местоположение</a> и другие параметры.
			</ol>
			<h3>Изменение параметров автофигуры</h3>
			<p>Некоторые параметры автофигуры можно изменить с помощью вкладки <b>Параметры фигуры</b> на правой боковой панели. Чтобы ее открыть, выделите фигуру мышью и щелкните по значку <b>Параметры фигуры</b> <span class="icon icon-shape_settings_icon"></span> справа. Здесь можно изменить следующие свойства:</p>
			<ul>
			<li id="shape_fill"><b>Заливка</b> - используйте этот раздел, чтобы выбрать заливку автофигуры. Можно выбрать следующие варианты:
			<ul>
				<li><b>Заливка цветом</b> - выберите эту опцию, чтобы задать сплошной цвет, которым требуется заполнить внутреннее пространство выбранной фигуры.
				<p><img alt="Заливка цветом" src="../images/fill_color.png" /></p>
				<p id="color">Нажмите на цветной прямоугольник, расположенный ниже, и выберите нужный цвет из доступных наборов цветов или задайте любой цвет, который вам нравится:</p>
					<ul>
						<li><b>Цвета темы</b> - цвета, соответствующие выбранной цветовой схеме электронной таблицы.</li>
						<li><b>Стандартные цвета</b> - набор стандартных цветов.</li>
						<li><b>Пользовательский цвет</b> - щелкните по этой надписи, если в доступных палитрах нет нужного цвета. Выберите нужный цветовой диапазон, перемещая вертикальный ползунок цвета, и определите конкретный цвет, перетаскивая инструмент для выбора цвета внутри большого квадратного цветового поля. Как только Вы выберете какой-то цвет, в полях справа отобразятся соответствующие цветовые значения RGB и sRGB. Также можно задать цвет на базе цветовой модели RGB, введя нужные числовые значения в полях <b>R</b>, <b>G</b>, <b>B</b> (красный, зеленый, синий), или указать шестнадцатеричный код sRGB в поле, отмеченном знаком <b>#</b>. Выбранный цвет появится в окне предпросмотра <b>Новый</b>. Если к объекту был ранее применен какой-то пользовательский цвет, этот цвет отображается в окне <b>Текущий</b>, так что вы можете сравнить исходный и измененный цвета. Когда цвет будет задан, нажмите на кнопку <b>Добавить</b>. 
						Пользовательский цвет будет применен к автофигуре и добавлен в палитру <b>Пользовательский цвет</b>.
						</li>
					</ul>
				</li>
				<li><b>Градиентная заливка</b> - выберите эту опцию, чтобы залить фигуру двумя цветами, плавно переходящими друг в друга.
				<p><img alt="Градиентная заливка" src="../images/fill_gradient.png" /></p>
				<ul>
					<li>
						<b>Стиль</b> - выберите <b>Линейный</b> или <b>Радиальный</b>:
						<ul>
							<li><b>Линейный</b> используется, когда вам нужно, чтобы цвета изменялись слева направо, сверху вниз или под любым выбранным вами углом в одном направлении. Чтобы выбрать предустановленное направление, щелкните на стрелку рядом с окном предварительного просмотра <b>Направление</b> или же задайте точное значение угла градиента в поле <b>Угол</b>.</li>
							<li><b>Радиальный</b> используется, когда вам нужно, чтобы цвета изменялись по кругу от центра к краям.</li>
						</ul>
					</li>
					<li>
						<b>Точка градиента</b> - это определенная точка перехода от одного цвета к другому.
						<ul>
							<li>Чтобы добавить точку градиента, используйте кнопку <div class = "icon icon-addgradientpoint"></div> <b>Добавить точку градиента</b> или ползунок. Вы можете добавить до 10 точек градиента. Каждая следующая добавленная точка градиента никоим образом не повлияет на внешний вид текущей градиентной заливки. Чтобы удалить определенную точку градиента, используйте кнопку  <div class = "icon icon-removegradientpoint"></div> <b>Удалить точку градиента</b>.</li>
							<li>Чтобы изменить положение точки градиента, используйте ползунок или укажите <b>Положение</b> в процентах для точного местоположения.</li>
							<li>Чтобы применить цвет к точке градиента, щелкните точку на панели ползунка, а затем нажмите <b>Цвет</b>, чтобы выбрать нужный цвет.</li>
						</ul>
					</li>
				</ul>			
				</li>
				<li><b>Изображение или текстура</b> - выберите эту опцию, чтобы использовать в качестве фона фигуры какое-то изображение или готовую текстуру.
				<p><img alt="Заливка с помощью изображения или текстуры" src="../images/fill_picture.png" /></p>
					<ul>
						<li>Если Вы хотите использовать изображение в качестве фона фигуры, можно нажать кнопку <b>Выбрать изображение</b> и добавить изображение <b>Из файла</b>, выбрав его на жестком диске компьютера, <b>Из хранилища</b>, используя файловый менеджер ONLYOFFICE, или <b>По URL</b>, вставив в открывшемся окне соответствующий URL-адрес.</li>
						<li>Если Вы хотите использовать текстуру в качестве фона фигуры, разверните меню <b>Из текстуры</b> и выберите нужную предустановленную текстуру.
				    <p>В настоящее время доступны следующие текстуры: Холст, Картон, Темная ткань, Песок, Гранит, Серая бумага, Вязание, Кожа, Крафт-бумага, Папирус, Дерево.</p>
						</li>
					</ul>
					<ul>
						<li>В том случае, если выбранное <b>изображение</b> имеет большие или меньшие размеры, чем автофигура, можно выбрать из выпадающего списка параметр <b>Растяжение</b> или <b>Плитка</b>.
			        <p>Опция <b>Растяжение</b> позволяет подогнать размер изображения под размер автофигуры, чтобы оно могло полностью заполнить пространство.</p>
			        <p>Опция <b>Плитка</b> позволяет отображать только часть большего изображения, сохраняя его исходные размеры, или повторять меньшее изображение, сохраняя его исходные размеры, по всей площади автофигуры, чтобы оно могло полностью заполнить пространство.</p>
			        <p class="note"><b>Примечание</b>: любая выбранная предустановленная <b>текстура</b> полностью заполняет пространство, но в случае необходимости можно применить эффект <b>Растяжение</b>.</p>
						</li>
					</ul>
				</li>
				<li><b>Узор</b> - выберите эту опцию, чтобы залить фигуру с помощью двухцветного рисунка, который образован регулярно повторяющимися элементами.
				<p><img alt="Заливка с помощью узора" src="../images/fill_pattern.png" /></p>
				    <ul>
				        <li><b>Узор</b> - выберите один из готовых рисунков в меню.</li>
				        <li><b>Цвет переднего плана</b> - нажмите на это цветовое поле, чтобы изменить цвет элементов узора.</li>
				        <li><b>Цвет фона</b> - нажмите на это цветовое поле, чтобы изменить цвет фона узора.</li>
				    </ul>			
				</li>
				<li><b>Без заливки</b> - выберите эту опцию, если Вы вообще не хотите использовать заливку.</li>
			</ul>
			</li>
			<li><b>Непрозрачность</b> - используйте этот раздел, чтобы задать уровень <b>Непрозрачности</b>, перетаскивая ползунок или вручную вводя значение в процентах. Значение, заданное по умолчанию, составляет <b>100%</b>. Оно соответствует полной непрозрачности. Значение <b>0%</b> соответствует полной прозрачности.
			</li>
			<li id="shape_stroke"><b>Контур</b> - используйте этот раздел, чтобы изменить толщину, цвет или тип контура.
				<ul>
				<li>Для изменения <b>толщины</b> контура выберите из выпадающего списка <b>Толщина</b> одну из доступных опций. Доступны следующие опции: 0.5 пт, 1 пт, 1.5 пт, 2.25 пт, 3 пт, 4.5 пт, 6 пт. Или выберите опцию <b>Без линии</b>, если вы вообще не хотите использовать контур.</li>
				<li>Для изменения <b>цвета</b> контура щелкните по цветному прямоугольнику и выберите нужный цвет.</li>
				<li>Для изменения <b>типа</b> контура выберите нужную опцию из соответствующего выпадающего списка (по умолчанию применяется сплошная линия, ее можно изменить на одну из доступных пунктирных линий).</li>
				</ul>
			</li>
            <li>
                <b>Поворот</b> - используется, чтобы повернуть фигуру на 90 градусов по часовой стрелке или против часовой стрелки, а также чтобы отразить фигуру слева направо или сверху вниз. Нажмите на одну из кнопок:
                    <ul>
                        <li><div class = "icon icon-rotatecounterclockwise"></div> чтобы повернуть фигуру на 90 градусов против часовой стрелки</li>
                        <li><div class = "icon icon-rotateclockwise"></div> чтобы повернуть фигуру на 90 градусов по часовой стрелке</li>
                        <li><div class = "icon icon-fliplefttoright"></div> чтобы отразить фигуру по горизонтали (слева направо)</li>
                        <li><div class = "icon icon-flipupsidedown"></div> чтобы отразить фигуру по вертикали (сверху вниз)</li>
                    </ul>
            </li>
			<li><b>Изменить автофигуру</b> - используйте этот раздел, чтобы заменить текущую автофигуру на другую, выбрав ее из выпадающего списка.</li>
            <li><b>Отображать тень</b> - отметьте эту опцию, чтобы отображать фигуру с тенью.</li>
			</ul>
			<hr />
            <h3>Изменение дополнительныx параметров автофигуры</h3>
            <p id ="shape_advanced">Чтобы изменить <b>дополнительные параметры</b> автофигуры, используйте ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно 'Фигура - дополнительные параметры':</p>
            <p><img alt="Фигура - дополнительные параметры" src="../images/shape_properties.png" /></p>
            <p>Вкладка <b>Размер</b> содержит следующие параметры:</p>
			<ul>
				<li><b>Ширина</b> и <b>Высота</b> - используйте эти опции, чтобы изменить ширину и/или высоту автофигуры. Если нажата кнопка <b>Сохранять пропорции</b> <div class = "icon icon-constantproportions"></div> (в этом случае она выглядит так: <div class = "icon icon-constantproportionsactivated"></div>), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон фигуры.</li>
			</ul>
            <p><img alt="Фигура - дополнительные параметры: Поворот" src="../images/shape_properties_5.png" /></p>
            <p>Вкладка <b>Поворот</b> содержит следующие параметры:</p>
            <ul>
                <li><b>Угол</b> - используйте эту опцию, чтобы повернуть фигуру на точно заданный угол. Введите в поле нужное значение в градусах или скорректируйте его, используя стрелки справа. </li>
                <li><b>Отражено</b> - отметьте галочкой опцию <b>По горизонтали</b>, чтобы отразить фигуру по горизонтали (слева направо), или отметьте галочкой опцию <b>По вертикали</b>, чтобы отразить фигуру по вертикали (сверху вниз).</li>
            </ul>
			<p><img alt="Фигура - дополнительные параметры" src="../images/shape_properties_1.png" /></p>
			<p>Вкладка <b>Линии и стрелки</b> содержит следующие параметры:</p>
			<ul>
				<li><b>Стиль линии</b> - эта группа опций позволяет задать такие параметры:
				<ul>
				<li><b>Тип окончания</b> - эта опция позволяет задать стиль окончания линии, поэтому ее можно применить только для фигур с разомкнутым контуром, таких как линии, ломаные линии и т.д.: 
					<ul>
					<li><b>Плоский</b> - конечные точки будут плоскими.</li>
				    <li><b>Закругленный</b> - конечные точки будут закругленными.</li>
				    <li><b>Квадратный</b> - конечные точки будут квадратными.</li>
					</ul>
				</li>
				<li><b>Тип соединения</b> - эта опция позволяет задать стиль пересечения двух линий, например, она может повлиять на контур ломаной линии или углов треугольника или прямоугольника:
					<ul>
					<li><b>Закругленный</b> - угол будет закругленным.</li>
    				<li><b>Скошенный</b> - угол будет срезан наискось.</li>
    				<li><b>Прямой</b> - угол будет заостренным. Хорошо подходит для фигур с острыми углами.</li>
					</ul>
					<p class="note"><b>Примечание</b>: эффект будет лучше заметен при использовании контура большей толщины.</p>
				</li>
				</ul>
				</li>
				<li><b>Стрелки</b> - эта группа опций доступна только в том случае, если выбрана фигура из группы автофигур <b>Линии</b>. Она позволяет задать <b>Начальный</b> и <b>Конечный стиль</b> и <b>Размер</b> стрелки, выбрав соответствующие опции из выпадающих списков.</li>
			</ul>
            <p id="internalmargins"><img alt="Фигура - дополнительные параметры" src="../images/shape_properties_2.png" /></p>
            <p>На вкладке <b>Текстовое поле</b> можно <b>Подгонять размер фигуры под текст</b>, <b>Разрешить переполнение фигуры текстом</b> или изменить внутренние поля автофигуры <b>Сверху</b>, <b>Снизу</b>, <b>Слева</b> и <b>Справа</b> (то есть расстояние между текстом внутри фигуры и границами автофигуры).</p>
            <p class="note"><b>Примечание</b>: эта вкладка доступна, только если в автофигуру добавлен текст, в противном случае вкладка неактивна.</p>
            <p id="columns"><img alt="Свойства фигуры - вкладка Колонки" src="../images/shape_properties_4.png" /></p>
            <p>На вкладке <b>Колонки</b> можно добавить колонки текста внутри автофигуры, указав нужное <b>Количество колонок</b> (не более 16) и <b>Интервал между колонками</b>. После того как вы нажмете кнопку <b>ОК</b>, уже имеющийся текст или любой другой текст, который вы введете, в этой автофигуре будет представлен в виде колонок и будет перетекать из одной колонки в другую.</p>
            <p><img alt="Фигура - дополнительные параметры: Привязка к ячейке" src="../images/shape_properties_6.png" /></p>
            <p>Вкладка <b>Привязка к ячейке</b> содержит следующие параметры:</p>
            <ul>
                <li><b>Перемещать и изменять размеры вместе с ячейками</b> - эта опция позволяет привязать фигуру к ячейке позади нее. Если ячейка перемещается (например, при вставке или удалении нескольких строк/столбцов), фигура будет перемещаться вместе с ячейкой. При увеличении или уменьшении ширины или высоты ячейки размер фигуры также будет изменяться.</li>
                <li><b>Перемещать, но не изменять размеры вместе с ячейками</b> - эта опция позволяет привязать фигуру к ячейке позади нее, не допуская изменения размера фигуры. Если ячейка перемещается, фигура будет перемещаться вместе с ячейкой, но при изменении размера ячейки размеры фигуры останутся неизменными.</li>
                <li><b>Не перемещать и не изменять размеры вместе с ячейками</b> - эта опция позволяет запретить перемещение или изменение размера фигуры при изменении положения или размера ячейки.</li>
            </ul>
            <p><img alt="Фигура - дополнительные параметры: Альтернативный текст" src="../images/shape_properties_3.png" /></p>
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит автофигура.</p>
		    <hr />
			<h3>Вставка и форматирование текста внутри автофигуры</h3>
			<p>Чтобы вставить текст в автофигуру, выделите фигуру и начинайте печатать текст. Текст, добавленный таким способом, становится частью автофигуры (при перемещении или повороте автофигуры текст будет перемещаться или поворачиваться вместе с ней).</p>
            <p>Все параметры форматирования, которые можно применить к тексту в автофигуре, перечислены <a href="InsertTextObjects.htm#textbox_textformatting" onclick="onhyperlinkclick(this)">здесь</a>.</p>
            <hr />
            <h3>Соединение автофигур с помощью соединительных линий</h3>
            <p>Автофигуры можно соединять, используя линии с точками соединения, чтобы продемонстрировать зависимости между объектами (например, если вы хотите создать блок-схему). Для этого:</p>
            <ol>
                <li>щелкните по значку <div class = "icon icon-insertautoshape"></div> <b>Фигура</b> на вкладке <b>Вставка</b> верхней панели инструментов,</li>
                <li>
                    выберите в меню группу <b>Линии</b>,
                    <p><img alt="Фигуры - Линии" src="../images/connectors.png" /></p>
                </li>
                <li>щелкните по нужной фигуре в выбранной группе (кроме трех последних фигур, которые не являются соединительными линиями, а именно <em>Кривая</em>, <em>Рисованная кривая</em> и <em>Произвольная форма</em>),</li>
                <li>
                    наведите указатель мыши на первую автофигуру и щелкните по одной из точек соединения <div class = "icon icon-connectionpoint"></div>, появившихся на контуре фигуры,
                    <p><span class = "big big-connectors_firstshape"></span></p>
                </li>
                <li>
                    перетащите указатель мыши ко второй фигуре и щелкните по нужной точке соединения на ее контуре.
                    <p><span class="big big-connectors_secondshape"></span></p>
                </li>
            </ol>
            <p>При перемещении соединенных автофигур соединительная линия остается прикрепленной к фигурам и перемещается вместе с ними. </p>
            <p><span class="big big-connectors_moveshape"></span></p>
            <p>Можно также открепить соединительную линию от фигур, а затем прикрепить ее к любым другим точкам соединения.</p>

			<h3>Назначение макроса к фигуре</h3>
			<p>Вы можете обеспечить быстрый и легкий доступ к макросу в электронной таблице, назначив макрос любой фигуре. После назначения макроса, фигура отображается как кнопка, и вы можете запускать макрос всякий раз, когда нажимаете на фигуру.</p>
			<p>Чтобы назначить макрос,</p>
			<ul type="circle">
				<li>
					Щелкните правой кнопкой мыши по фигуре и в контекстном меню выберите пункт <b>Назначить макрос</b>.
					<p><img alt="Контекстное меню - Назначить макрос" src="../images/assignmacro_rightclickshape.png" /></p>
				</li>
				<li>Откроется окно <b>Назначить макрос</b></li>
				<li>
					Выберите макрос из списка или вручную введите название макроса и нажмите <b>ОК</b>.
					<p><img alt="Назначить макрос" src="../images/assignmacro.png" /></p>
				</li>
			</ul>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ИНДЕКС</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ИНДЕКС</h1>
			<p>Функция <b>ИНДЕКС</b> - это одна из поисковых функций. Возвращает значение в диапазоне ячеек на основании заданных номера строки и номера столбца. Существуют две формы функции <b>ИНДЕКС</b>. 
      <!--Форма массива возвращает значение в одном диапазоне ячеек. Форма ссылки возвращает значение в массиве, содержащем несколько диапазонов ячеек.--></p>
			<p>Синтаксис функции <b>ИНДЕКС</b> в форме массива:</p> 
			<p style="text-indent: 150px;"><b><em>ИНДЕКС(массив;номер_строки;[номер_столбца])</em></b></p> 
            <p>Синтаксис функции <b>ИНДЕКС</b> в ссылочной форме:</p>
            <p style="text-indent: 150px;"><b><em>ИНДЕКС(ссылка;номер_строки;[номер_столбца];[номер_области])</em></b></p>
            <p><em>где</em></p> 
            <p style="text-indent: 50px;"><em><b>массив</b></em> - это диапазон ячеек.</p>
            <p style="text-indent: 50px;"><em><b>ссылка</b></em> - это ссылка на диапазон ячеек. <!--или на несколько диапазонов, заключенных в скобки и разделенных запятыми--></p>
            <p style="text-indent: 50px;"><em><b>номер_строки</b></em> - это номер строки, из которой вы хотите получить значение. Если он опущен, нужно задать аргумент <em><b>номер_столбца</b></em>.</p>
			<p style="text-indent: 50px;"><em><b>номер_столбца</b></em> - это номер столбца, из которого вы хотите получить значение. Если он опущен, нужно задать аргумент <em><b>номер_строки</b></em>.</p>
            <p style="text-indent: 50px;"><em><b>номер_области</b></em> - это область, которую нужно использовать, если массив содержит несколько диапазонов ячеек. Это необязательный аргумент. Если он опущен, аргумент <b><em>номер_области</em></b> полагается равным 1.</p>
			<p>Эти аргументы могут быть введены вручную или находиться в ячейках, на которые дается ссылка.</p>
      <p>
        Чтобы применить функцию <b>ИНДЕКС</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Поиск и ссылки</b>,
        </li>
        <li>
          щелкните по функции <b>ИНДЕКС</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой или выделите мышью диапазон ячеек,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ИНДЕКС" src="../images/index_1.png" /></p>
		</div>
	</body>
</html>
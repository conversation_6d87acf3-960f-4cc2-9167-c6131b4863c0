<!DOCTYPE html>
<html>
	<head>
		<title>Функция РАНГ.СР</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция РАНГ.СР</h1>
			<p>Функция <b>РАНГ.СР</b> - это одна из статистических функций. Возвращает ранг числа в списке чисел. Ранг числа — это его величина относительно других значений в списке (если отсортировать список, то ранг числа будет его позицией). Если несколько значений имеют одинаковый ранг, возвращается средний ранг.</p>
			<p>Синтаксис функции <b>РАНГ.СР</b>:</p> 
			<p style="text-indent: 150px;"><b><em>РАНГ.СР(число;ссылка;[порядок])</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>число</em></b> - значение, для которого требуется определить ранг.</p>
				<p style="text-indent: 50px;"><b><em>ссылка</em></b> - выбранный диапазон ячеек, в котором содержится указанное <b><em>число</em></b>.</p>
				<p style="text-indent: 50px;"><b><em>порядок</em></b> -  числовое значение, определяющее, как упорядочивать массив <b><em>ссылка</em></b>. Необязательный аргумент. Если он равен 0 или опущен, функция <b>РАНГ.СР</b> определяет ранг <b><em>числа</em></b> так, как если бы <b><em>ссылка</em></b> была списком, отсортированным в порядке убывания. Любое другое числовое значение интерпретируется как 1, и функция <b>РАНГ.СР</b> определяет ранг <b><em>числа</em></b> так, как если бы <b><em>ссылка</em></b> была списком, отсортированным в порядке возрастания.</p>
			<p>Чтобы применить функцию <b>РАНГ.СР</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>РАНГ.СР</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция РАНГ.СР" src="../images/rank-avg.png" /></p>
		</div>
	</body>
</html>
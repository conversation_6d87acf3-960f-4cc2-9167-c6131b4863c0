<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЕНЕТЕКСТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЕНЕТЕКСТ</h1>
			<p>Функция <b>ЕНЕТЕКСТ</b> - это одна из информационных функций. Она используется для проверки на наличие значения, которое не является текстом. Если ячейка не содержит текстового значения, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ.</p>
			<p>Синтаксис функции <b>ЕНЕТЕКСТ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЕНЕТЕКСТ(значение)</em></b></p> 
			<p>где <b><em>значение</em></b> - это проверяемое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>ЕНЕТЕКСТ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Информационные</b>,</li>
			<li>щелкните по функции <b>ЕНЕТЕКСТ</b>,</li>
			<li>введите требуемый аргумент,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ЕНЕТЕКСТ" src="../images/isnontext.png" /></p>
		</div>
	</body>
</html>
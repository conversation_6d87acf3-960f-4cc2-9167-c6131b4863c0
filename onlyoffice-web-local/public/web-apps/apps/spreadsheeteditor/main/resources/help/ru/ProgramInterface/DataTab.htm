<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Данные</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора электронных таблиц - Вкладка Данные" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Вкладка Данные</h1>
            <p>Вкладка <b>Данные</b> в <a href="https://www.onlyoffice.com/ru/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редакторе электронных таблиц</b></a> позволяет управлять данными на рабочем листе.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора электронных таблиц:</p>
                <p><img alt="Вкладка Данные" src="../images/interface/datatab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора электронных таблиц:</p>
                <p><img alt="Вкладка Данные" src="../images/interface/desktop_datatab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>выполнять <a href="../UsageInstructions/SortData.htm" onclick="onhyperlinkclick(this)">сортировку и фильтрацию</a> данных,</li>
                <li>преобразовывать <a href="../UsageInstructions/CopyPasteData.htm#delimiteddata" onclick="onhyperlinkclick(this)">текст в столбцы</a>,</li>
                <li><a href="../UsageInstructions/RemoveDuplicates.htm" onclick="onhyperlinkclick(this)">удалять дубликаты</a> из диапазона данных,</li>
                <li><a href="../UsageInstructions/GroupData.htm" onclick="onhyperlinkclick(this)">группировать и отменять группировку</a> данных,</li>
                <li>устанавливать параметры <a href="../UsageInstructions/DataValidation.htm" onclick="onhyperlinkclick(this)">проверки данных</a>,</li>
                <li>получать данные <a href="../HelpfulHints/ImportData.htm" onclick="onhyperlinkclick(this)">из текстового/CSV-файла</a>,</li>
                <li>просматривать другие файлы, с которыми связана таблица, с помощью кнопки <b>Внешние ссылки</b>.</li>
            </ul>
		</div>
	</body>
</html>
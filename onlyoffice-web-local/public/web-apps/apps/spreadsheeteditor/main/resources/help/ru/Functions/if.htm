<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЕСЛИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЕСЛИ</h1>
			<p>Функция <b>ЕСЛИ</b> - это одна из логических функций. Используется для проверки логического выражения и возвращает одно значение, если проверяемое условие имеет значение ИСТИНА, и другое, если оно имеет значение ЛОЖЬ.</p>
			<p>Синтаксис функции <b>ЕСЛИ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЕСЛИ(лог_выражение;значение_если_истина;[значение_если_ложь])</em></b></p> 
			<p>где <b><em>лог_выражение</em></b>, <b><em>значение_если_истина</em></b>, <b><em>значение_если_ложь</em></b> - это значения, введенные вручную или находящиеся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>ЕСЛИ</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Логические</b>,</li>
			<li>щелкните по функции <b>ЕСЛИ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><em>Например:</em></p>
			<p>Здесь три аргумента: <em>лог_выражение</em> = <b>A1&lt;100</b>; <em>значение_если_истина</em> = <b>0</b>; <em>значение_если_ложь</em> = <b>1</b>, где <b>A1</b> имеет значение <b>12</b>. Данное логическое выражение имеет значение <b>ИСТИНА</b>. Следовательно, функция возвращает значение <b>0</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция ЕСЛИ: ИСТИНА" src="../images/iftrue.png" /></p>
			<p>Если изменить значение <b>A1</b> с <b>12</b> на <b>112</b>, функция возвращает значение <b>1</b>:</p>
			<p style="text-indent: 150px;"><img alt="Функция ЕСЛИ: ЛОЖЬ" src="../images/iffalse.png" /></p>
		</div>
	</body>
</html>
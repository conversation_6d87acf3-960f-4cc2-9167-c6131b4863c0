<!DOCTYPE html>
<html>
	<head>
		<title>Получение данных из текстового/CSV-файла</title>
		<meta charset="utf-8" />
		<meta name="description" content="Получить данные из текстового/CSV-файла" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Получение данных из текстового/CSV-файла</h1>
			<p>Если вам нужно быстро получить данные из .txt/.csv файла и правильно расположить их в электронной таблице, используйте функцию <b>Получить данные из текстового/CSV-файла</b>, находящуюся на вкладке <b>Данные</b>.</p>
			<h2 id="step1">Шаг 1. Импорт файла</h2>
			<ol>
				<li>Щелкните функцию <b>Получить данные из текстового/CSV-файла</b> на вкладке <b>Данные</b>.</li>
				<li>Выберите один из вариантов импорта:
					<ul>
						<li><b>Из локального TXT/CSV файла</b>: найдите нужный файл на жестком диске, выберите его и нажмите <b>Открыть</b>.</li>
						<li><b>По URL TXT/CSV файла</b>: вставьте ссылку на файл или веб-страницу в поле <b>Вставьте URL-адрес данных</b> и нажмите <b>ОК</b>.
							<p class="note">
								В этом случае нельзя использовать ссылку для просмотра или редактирования файла, хранящегося на портале ONLYOFFICE или в стороннем хранилище. Воспользуйтесь ссылкой, чтобы <b>скачать</b> файл.
							</p>
						</li>
						<li><b>Из локальной XML</b>: найдите нужный файл на жестком диске, выберите его и нажмите <b>Открыть</b>.
							<p class="note">В настоящее время поддерживается только электронная таблица XML 2003.</p>
						</li>
					</ul>
				</li>
			</ol>
			<h2 id="step2">Шаг 2. Настройка параметров</h2>
			<p>Окно <b>Мастер импорта текста</b> содержит четыре раздела: <b>Кодировка</b>, <b>Разделитель</b>, <b>Просмотр</b> и <b>Выберите, где поместить данные</b>.</p>
			<p><img alt="Мастер импорта текста" src="../images/textimport.png" /></p>
			<ol>
				<li><b>Кодировка</b>. По умолчанию для параметра установлено значение <em>UTF-8</em>. Оставьте это или выберите нужный тип из выпадающего меню.</li>
				<li>
					<b>Разделитель</b>. Параметр устанавливает тип разделителя, используемый для распределения текста по ячейкам. Доступны следующие разделители: <em>Запятая</em>, <em>Точка с запятой</em>, <em>Двоеточие</em>, <em>Табуляция</em>, <em>Пробел</em> и <em>Другое</em> (введите вручную).
						<p>Нажмите кнопку <b>Дополнительно</b>, расположенную справа, чтобы настроить параметры для числовых данных:</p>
						<p><img alt="Дополнительыне параметры Получить данные" src="../images/dataimport_advanced.png" /></p>
						<ul>
							<li>Установите <b>Десятичный разделитель</b> и <b>Разделитель разрядов тысяч</b>. Разделителями по умолчанию являются <b>«.»</b> для десятков и <b>«,»</b> для тысяч.</li>
							<li>
								Выберите <b>Классификатор текста</b>. <b>Классификатор текста</b> – это символ, который используется для распознавания начала и окончания текста при импорте данных. Доступные варианты: <b>(нет)</b>, <b>двойные кавычки</b> и <b>запятая</b>.
							</li>
						</ul>
				</li>
				<li><b>Просмотр</b>. В разделе показано, как текст будет располагаться в ячейках таблицы.</li>
				<li><b>Выберите, где поместить данные</b>. Введите требуемый диапазон в поле или выберите его с помощью кнопки <b>Выбор данных</b>.</li>
				<li>Нажмите <b>ОК</b>, чтобы получить данные из файла и выйти из <b>Мастера импорта текста</b>.</li>
			</ol>
		</div>
	</body>
</html>
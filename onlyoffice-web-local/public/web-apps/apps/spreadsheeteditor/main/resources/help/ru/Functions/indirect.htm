<!DOCTYPE html>
<html>
	<head>
		<title>Функция ДВССЫЛ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ДВССЫЛ</h1>
			<p>Функция <b>ДВССЫЛ</b> это одна из поисковых функций. Она возвращает ссылку на ячейку, указанную с помощью текстовой строки.</p>
			<p>Синтаксис функции <b>ДВССЫЛ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ДВССЫЛ(ссылка_на_текст;[a1])</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>ссылка_на_текст</em></b> - ссылка на ячейку в формате текстовой строки.</p> 
				<p style="text-indent: 50px;"><b><em>a1</em></b> - тип представления. Необязательное логическое значение: ИСТИНА или ЛОЖЬ. Если этот аргумент имеет значение ИСТИНА или опущен, аргумент <b><em>ссылка_на_текст</em></b> анализируется как ссылка типа A1. Если этот аргумент имеет значение ЛОЖЬ, аргумент <b><em>ссылка_на_текст</em></b> интерпретируется как ссылка типа R1C1.</p>
				
			<p>Чтобы применить функцию <b>ДВССЫЛ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
			<li>щелкните по функции <b>ДВССЫЛ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ДВССЫЛ" src="../images/indirect.png" /></p>
		</div>
	</body>
</html>
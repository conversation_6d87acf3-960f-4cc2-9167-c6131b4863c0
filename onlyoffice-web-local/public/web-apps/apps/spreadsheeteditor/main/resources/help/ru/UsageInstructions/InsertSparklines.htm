<!DOCTYPE html>
<html>
	<head>
		<title>Вставка спаркл<PERSON>йнов</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавьте в электронную таблицу диаграмму и скорректируйте ее положение, размер и свойства" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вставка спарклайнов</h1>
            <h3>Использование спарклайнов</h3>
            <p>Спарклайн – это небольшая диаграмма, помещенная в одну ячейку. Спарклайны имеют ограниченную функциональность по сравнению с обычными диаграммами, но являются отличным инструментом, который позволяет быстро отслеживать динамически изменяющиеся данные. Размер спарклайна зависит от размера ячейки. Чтобы настроить размер спарклайна, измените ширину и длину ячейки. После добавления спарклайна вы все равно можете вводить текст или добавлять условное форматирование в ячейку. Редактор электронных таблиц ONLYOFFICE предлагает три типа спарклайнов:</p>
            <p><div class = "big big-sparklines_types"></div> </p>
            <ul>
                <li><b>Гистограмма</b> - этот тип аналогичен обычной <b>Гистограмме</b>.</li>
                <li><b>График</b> - этот тип аналогичен обычному <b>Графику</b>.</li>
                <li><b>Выигрыш/проигрыш</b> - этот тип подходит для представления данных, которые включают как положительные, так и отрицательные значения.</li>
            </ul>
            <h3>Вставка спарклайнов</h3>
            <p>Чтобы вставить <b>Спарклайн</b>, выделите диапазон ячеек, содержащих данные, перейдите на вкладку <b>Вставка</b> верхней панели инструментов и щелкните по значку <b>Спарклайн</b> <span class="icon icon-sparklines_button"></span>. Выберите подходящий тип спарклайна. Откроется окно <b>Создание спарклайнов</b>, щелкните значок <b>Выбор данных</b>, чтобы указать диапазон данных и расположение спарклайна и нажмите <b>ОК</b> для подтверждения</p>
			<p><img alt="Окно Создание спарклайнов" src="../images/createsparkline_window.png" /></p>
            <h3>Изменение параметров спарклайнов</h3>
            <p>После того как вы вставили спарклайн, вы можете настроить и отредактировать его. Для этого перейдите в раздел <b>Параметры спарклайна</b> на правой боковой панели.</p>
            <p><img alt="Раздел Параметры спарклайна" src="../images/sparklinesettings_tab.png" /></p>
            <ul>
                <li>В разделе <b>Тип</b> из раскрывающегося списка вы можете выбрать один из доступных типов спарклайнов:
                <p><b>Гистограмма</b> похож на обычную <b>Гистограмму</b>.</p>
                <p><b>График</b> похож на обычную диаграмму <b>График</b>.</p>
                <p><b>Выигрыш/проигрыш</b> подходит для отображения данных, которые включают как положительные, так и отрицательные значения.</p>
                </li>
                <li>В разделе <b>Стиль</b> из раскрывающегося списка <b>Шаблон</b> можно выбрать наиболее подходящий стиль, необходимый <b>Цвет</b> и <b>Толщину линии</b> спарклайна (доступно только для спарклайна <b>График</b>).</li>
                <li>
                    В разделе <b>Показать</b> вы можете выделить или отметить определенные данные в спарклайне:
                    <p><i>Максимальная точка</i> – чтобы выделить точки, представляющие максимальные значения.</p>
                    <p><i>Минимальная точка</i> – чтобы выделить точки, представляющие минимальные значения.</p>
                    <p><i>Отрицательная точка</i> – чтобы выделить точки, представляющие отрицательные значения.</p>
                    <p><i>Первая точка</i> – чтобы выделить точку, представляющую первое значение.</p>
                    <p><i>Последняя точка</i> - чтобы выделить точку, представляющую последнее значение.</p>
                    <p><i>Маркеры</i> (доступно только для типа <b>График</b>) – чтобы выделить все значения.</p>
                </li>
            </ul>    
            <p>Щелкните стрелку вниз в цветовом поле, чтобы выбрать цвет для каждой точки.</p>
            <p>Если вам нужно сделать спарклайн более точным и удобным для просмотра, нажмите кнопку <b>Дополнительные параметры</b>, чтобы открыть окно <b>Спарклайн - дополнительные параметры</b>.</p>
            <p><img alt="Окно Спарклайн - дополнительные параметры" src="../images/sparkline_advancedsettings1.png" /> </p>   
            <p> </p>
            <p>На вкладке <b>Тип и данные</b> можно изменить <b>Тип</b> и <b>Стиль</b> спарклайна, а также изменить параметры отображения <b>Скрытых и пустых ячеек</b>:</p>
            <ul>
                <li>
                    <b>Показывать пустые ячейки как</b> - эта опция позволяет настраивать то, как отображаются спарклайны, если некоторые ячейки в диапазоне данных пусты. Выберите подходящий вариант из списка:
                    <ul>
                        <li><b>Пустые значения</b> - чтобы отображать спарклайн с разрывами вместо отсутствующих данных,</li>
                        <li><b>Нулевые значения</b> - чтобы отображать спарклайн так, как если бы значение в пустой ячейке было нулем,</li>
                        <li><b>Соединять точки данных линиями</b> (доступно только для типа <b>График</b>) - чтобы игнорировать пустые ячейки и отображать соединительную линию между точками данных.</li>
                    </ul>
                </li>
                <li><b>Показывать данные в скрытых строках и столбцах</b> - установите этот флажок, если в спарклайны требуется включать значения из скрытых ячеек.</li>
            </ul>
            <p><img alt="Окно Спарклайн - дополнительные параметры" src="../images/sparkline_advancedsettings2.png" /></p>
            <p>На вкладке <b>Параметры оси</b> можно указать следующие параметры <b>Горизонтальной/Вертикальной оси</b>:</p>
            <ul>
                <li>
                    В разделе <b>Горизонтальная ось</b> доступны следующие параметры:
                    <ul>
                        <li><b>Показывать ось</b> - установите флажок, чтобы отобразить горизонтальную ось. Если исходные данные содержат отрицательные значения, эта опция помогает показать их более наглядно.</li>
                        <li><b>В обратном порядке</b> - установите флажок, чтобы отобразить данные в обратной последовательности.</li>
                    </ul>
                </li>
                <li>
                    В разделе <b>Вертикальная ось</b> доступны следующие параметры:
                    <ul>
                        <li>
                            <b>Минимум/Максимум</b>
                            <ul>
                                <li>
                                    <b>Автоматическое для каждого</b> - эта опция выбрана по умолчанию. Она позволяет использовать собственные минимальные/максимальные значения для каждого спарклайна. Минимальные/максимальные значения берутся из отдельных рядов данных, которые используются для построения каждого спарклайна. Максимальное значение для каждого спарклайна будет расположено вверху ячейки, а минимальное - внизу.
                                    <p><span class="big big-autoforeach"></span></p>
                                </li>
                                <li>
                                    <b>Одинаковое для всех</b> - эта опция позволяет использовать одно и то же минимальное и максимальное значение для всей группы спарклайнов. Минимальное и максимальное значения берутся из всего диапазона данных, используемого для построения группы спарклайнов. Минимальные и максимальные значения для каждого спарклайна масштабируются относительно наибольшего/наименьшего значения внутри диапазона. При выборе этой опции проще сравнивать между собой несколько спарклайнов.
                                    <p><span class="big big-sameforall"></span></p>
                                </li>
                                <li><b>Фиксированное</b> - эта опция позволяет задать пользовательское минимальное и максимальное значение. Значения меньше или больше указанных не будут отображаться в спарклайнах.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ul>
            <h3>Удаление спарклайнов</h3>
            <p>Чтобы удалить спарклайны, выберите ячейки со спарклайнами, которые нужно удалить, и щелкните правой кнопкой мыши. В контекстном меню выберите <b>Спарклайны</b> и нажмите <b>Очистить выбранные спарклайны</b> или <b>Очистить выбранные группы спарклайнов</b>.</p>
</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПРАВСИМВ/ПРАВБ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПРАВСИМВ/ПРАВБ</h1>
			<p>Функция <b>ПРАВСИМВ/ПРАВБ</b> - это одна из функций для работы с текстом и данными. Извлекает подстроку из заданной строки, начиная с крайнего правого символа, согласно заданному количеству символов. Функция <b>ПРАВСИМВ</b> предназначена для языков, использующих однобайтовую кодировку (SBCS), в то время как <b>ПРАВБ</b> - для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.</p>
			<p>Синтаксис функции <b>ПРАВСИМВ/ПРАВБ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ПРАВСИМВ(текст;[число_знаков])</em></b></p>
			 <p style="text-indent: 150px;"><b><em>ПРАВБ(текст;[число_знаков])</em></b></p>
			<p><em>где</em></p>
				<p style="text-indent: 50px;"><b><em>текст</em></b> - строка, из которой требуется извлечь подстроку,</p> 
				<p style="text-indent: 50px;"><b><em>число_знаков</em></b> - количество символов подстроки. Необязательный аргумент. Если он опущен, ему присваивается значение 1.</p>
			<p>Эти данные можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>ПРАВСИМВ/ПРАВБ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>ПРАВСИМВ/ПРАВБ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ПРАВСИМВ/ПРАВБ" src="../images/right.png" /></p>
		</div>
	</body>
</html>
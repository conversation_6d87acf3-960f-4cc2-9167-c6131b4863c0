<!DOCTYPE html>
<html>
	<head>
		<title>Функция ОТРЕЗОК</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ОТРЕЗОК</h1>
			<p>Функция <b>ОТРЕЗОК</b> - это одна из статистических функций. Анализирует значения первого и второго массивов для вычисления точки пересечения.</p>
			<p>Синтаксис функции <b>ОТРЕЗОК</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ОТРЕЗОК(массив1;массив2)</em></b></p> 
			<p>где <b><em>массив1(2)</em></b> - выбранные диапазоны ячеек с одинаковым количеством элементов (столбцов и строк).</p>
			<p>Чтобы применить функцию <b>ОТРЕЗОК</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>ОТРЕЗОК</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ОТРЕЗОК" src="../images/intercept.png" /></p>
		</div>
	</body>
</html>
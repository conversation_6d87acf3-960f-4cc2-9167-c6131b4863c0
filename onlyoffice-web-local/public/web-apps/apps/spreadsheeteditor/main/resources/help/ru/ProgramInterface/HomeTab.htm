<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Главная</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора электронных таблиц - Вкладка Главная" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Вкладка Главная</h1>
            <p>Вкладка <b>Главная</b> в <a href="https://www.onlyoffice.com/ru/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редакторе электронных таблиц</b></a> открывается по умолчанию при открытии электронной таблицы. Она позволяет форматировать ячейки и данные в них, применять фильтры, вставлять функции. Здесь также доступны некоторые другие опции, такие как цветовые схемы, функция <b>Форматировать как шаблон таблицы</b> и т.д.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора электронных таблиц:</p>
                <p><img alt="Вкладка Главная" src="../images/interface/hometab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора электронных таблиц:</p>
                <p><img alt="Вкладка Главная" src="../images/interface/desktop_hometab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>задавать <a href="../UsageInstructions/FontTypeSizeStyle.htm" onclick="onhyperlinkclick(this)">тип, размер, стиль и цвета шрифта</a>,</li>
                <li><a href="../UsageInstructions/AlignText.htm" onclick="onhyperlinkclick(this)">выравнивать данные</a> в ячейках,</li>
                <li>добавлять <a href="../UsageInstructions/AddBorders.htm" onclick="onhyperlinkclick(this)">границы ячеек</a> и <a href="../UsageInstructions/MergeCells.htm" onclick="onhyperlinkclick(this)">объединять ячейки</a>,</li>
                <li>вставлять <a href="../UsageInstructions/InsertFunction.htm" onclick="onhyperlinkclick(this)">функции</a> и создавать <a href="../UsageInstructions/UseNamedRanges.htm" onclick="onhyperlinkclick(this)">именованные диапазоны</a>,</li>
                <li>выполнять <a href="../UsageInstructions/SortData.htm" onclick="onhyperlinkclick(this)">сортировку и фильтрацию</a> данных,</li>
                <li>изменять <a href="../UsageInstructions/ChangeNumberFormat.htm" onclick="onhyperlinkclick(this)">формат представления чисел</a>,</li>
                <li>добавлять или удалять <a href="../UsageInstructions/InsertDeleteCells.htm" onclick="onhyperlinkclick(this)">ячейки, строки, столбцы</a>,</li>
                <li><a href="../UsageInstructions/ClearFormatting.htm" onclick="onhyperlinkclick(this)">копировать и очищать</a> форматирование ячеек,</li>
                <li>использовать <a href="../UsageInstructions/ConditionalFormatting.htm" onclick="onhyperlinkclick(this)"> условное форматирование</a>,</li>
                <li><a href="../UsageInstructions/SortData.htm#tabletemplate" onclick="onhyperlinkclick(this)">применять шаблон таблицы</a> к выделенному диапазону ячеек.</li>
            </ul>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
<head>
    <title>Проверка данных</title>
    <meta charset="utf-8" />
    <meta name="description" content="Установите параметры проверки данных для управления дальнейшим редактированием электронной таблицы" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Проверка данных</h1>
        <p>Редактор электронных таблиц ONLYOFFICE предоставляет функцию <b>Проверка данных</b>, которая позволяет настраивать параметры данных, вводимые в ячейки.</p>
        <p>Чтобы получить доступ к функции проверки данных, выберите ячейку, диапазон ячеек или всю электронную таблицу, к которой вы хотите применить эту функцию, на верхней панели инструментов перейдите на вкладку <b>Данные</b> и нажмите кнопку <span class = "icon icon-dataval_icon"></span> <b>Проверка данных</b>. Открытое окно <b>Проверка данных</b> содержит три вкладки: <em>Настройки</em>, <em>Подсказка по вводу</em> и <em>Сообщение об ошибке</em>.</p>
        <h2>Настройки</h2>
        <p>На вкладке <b>Настройки</b> вы можете указать тип данных, которые можно вводить:</p>
        <p class="note"><b>Примечание</b>: Установите флажок <b>Распространить изменения на все другие ячейки с тем же условием</b>, чтобы использовать те же настройки для выбранного диапазона ячеек или всего листа.</p>
        <p><img alt="Проверка данных - вкладка настройки" src="../images/dataval_settings.png" /></p>
        <ul>
            <li>
                выберите нужный вариант в выпадающем списке <b>Разрешить</b>:
                <ul>
                    <li><b>Любое значение</b>: без ограничений по типу данных.</li>
                    <li><b>Целое число</b>: разрешены только целые числа.</li>
                    <li><b>Десятичное число</b>: разрешены только числа с десятичной запятой.</li>
                    <li>
                        <b>Список</b>: разрешены только варианты из выпадающего списка, который вы создали. Снимите флажок <b>Показывать раскрывающийся список в ячейке</b>, чтобы скрыть стрелку раскрывающегося списка.
                        <p><img alt="Список - настройки" src="../images/dataval_list.png" /></p>
                    </li>
                    <li><b>Дата</b>: разрешены только ячейки с форматом даты.</li>
                    <li><b>Время</b>: разрешены только ячейки с форматом времени.</li>
                    <li><b>Длина текста</b>: устанавливает лимит символов.</li>
                    <li><b>Другое</b>: устанавливает желаемый параметр проверки, заданный в виде формулы.</li>
                </ul>
                <p class="note"><b>Примечание</b>: Установите флажок <b>Распространить изменения на все другие ячейки с тем же условием</b>, чтобы использовать те же настройки для выбранного диапазона ячеек или всего листа.</p>
            </li>
            <li>
                укажите условие проверки в выпадающем списке <b>Данные</b>:
                <ul>
                    <li><b>между</b>: данные в ячейках должны быть в пределах диапазона, установленного правилом проверки.</li>
                    <li><b>не между</b>: данные в ячейках не должны находиться в пределах диапазона, установленного правилом проверки.</li>
                    <li><b>равно</b>: данные в ячейках должны быть равны значению, установленному правилом проверки.</li>
                    <li><b>не равно</b>: данные в ячейках не должны быть равны значению, установленному правилом проверки.</li>
                    <li><b>больше</b>: данные в ячейках должны превышать значения, установленные правилом проверки.</li>
                    <li><b>меньше</b>: данные в ячейках должны быть меньше значений, установленных правилом проверки.</li>
                    <li><b>больше или равно</b>: данные в ячейках должны быть больше или равны значению, установленному правилом проверки.</li>
                    <li><b>меньше или равно</b>: данные в ячейках должны быть меньше или равны значению, установленному правилом проверки.</li>
                </ul>
            </li>
            <li>
                создайте правило проверки в зависимости от разрешенного типа данных:
                <div class="tg-wrap">
                    <table>
                        <thead>
                            <tr>
                                <th>Условие проверки</th>
                                <th>Правило проверки</th>
                                <th>Описание</th>
                                <th>Доступно</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td rowspan="3">Между / не между</td>
                                <td>Минимум / Максимум</td>
                                <td>Устанавливает диапазон значений</td>
                                <td>Целое число / Десятичное число / Длина текста</td>
                            </tr>
                            <tr>
                                <td>Дата начала / Дата окончания</td>
                                <td>Устанавливает диапазон дат</td>
                                <td>Дата</td>
                            </tr>
                            <tr>
                                <td>Время начала / Время окончание</td>
                                <td>Устанавливает временной диапазон</td>
                                <td>Время</td>
                            </tr>
                            <tr>
                                <td rowspan="4"> <br> <br>Равно / не равно</td>
                                <td>Сравнение</td>
                                <td>Устанавливает значение для сравнения</td>
                                <td>Целое число / Десятичное число</td>
                            </tr>
                            <tr>
                                <td>Дата</td>
                                <td>Устанавливает дату для сравнения</td>
                                <td>Дата</td>
                            </tr>
                            <tr>
                                <td>Пройденное время</td>
                                <td>Устанавливает время для сравнения</td>
                                <td>Время</td>
                            </tr>
                            <tr>
                                <td>Длина</td>
                                <td>Устанавливает значение длины текста для сравнения</td>
                                <td>Длина текста</td>
                            </tr>
                            <tr>
                                <td rowspan="3">Больше / больше или равно</td>
                                <td>Минимум</td>
                                <td>Устанавливает нижний предел</td>
                                <td>Целое число / Десятичное число / Длина текста</td>
                            </tr>
                            <tr>
                                <td>Дата начала</td>
                                <td>Устанавливает дату начала</td>
                                <td>Дата</td>
                            </tr>
                            <tr>
                                <td>Время начала</td>
                                <td>Устанавливает время начала</td>
                                <td>Время</td>
                            </tr>
                            <tr>
                                <td rowspan="3">Меньше / меньше или равно</td>
                                <td>Максимум</td>
                                <td>Устанавливает верхний предел</td>
                                <td>Целое число / Десятичное число / Длина текста</td>
                            </tr>
                            <tr>
                                <td>Дата окончания</td>
                                <td>Устанавливает дату окончания</td>
                                <td>Время</td>
                            </tr>
                            <tr>
                                <td>Дата окончания</td>
                                <td>Устанавливает дату окончания</td>
                                <td>Время</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                А также:
                <ul>
                    <li><b>Источник</b>: укажите ячейку или диапазон ячеек данных для типа данных <em>Список</em>.</li>
                    <li><b>Формула</b>: введите требуемую формулу или ячейку, содержащую формулу, чтобы создать настраиваемое правило проверки для типа данных <em>Другое</em>.</li>
                </ul>
            </li>
        </ul>
        <h2>Подсказка по вводу</h2>
        <p>Вкладка <b>Подсказка по вводу</b> позволяет создавать настраиваемое сообщение, отображаемое при наведении курсором мыши на ячейку.</p>
        <p><img alt="Проверка данных - Подсказка по вводу" src="../images/dataval_inputmessage.png" /></p>
        <ul>
            <li>Укажите <b>Заголовок</b> и текст вашей <b>Подсказки по вводу</b>.</li>
            <li>Уберите флажок с <b>Отображать подсказку, если ячейка является текущей</b>, чтобы отключить отображение сообщения. Оставьте его, чтобы отображать сообщение.</li>
        </ul>
        <p><img alt="Подсказки по вводу - пример" src="../images/dataval_inputmessage_example.png" /></p>
        <h2>Сообщение об ошибке</h2>
        <p>Вкладка <b>Сообщение об ошибке</b> позволяет указать, какое сообщение будет отображаться, когда данные, введенные пользователями, не соответствуют правилам проверки.</p>
        <p><img alt="Проверка данных - параметры сообщений об ошибке" src="../images/dataval_erroralert.png" /></p>
        <ul>
            <li><b>Стиль</b>: выберите одну из доступных опций оповещения: <em>Стоп</em>, <em>Предупреждение</em> или <em>Сообщение</em>.</li>
            <li><b>Заголовок</b>: укажите заголовок сообщения об ошибке.</li>
            <li><b>Сообщение об ошибке</b>: введите текст сообщения об ошибке.</li>
            <li>Снимите флажок с <b>Выводить сообщение об ошибке</b>, чтобы отключить отображение сообщения об ошибке.</li>
        </ul>
        <p><img alt="Сообщение об ошибке - пример" src="../images/dataval_erroralert_example.png" /></p>
    </div>
</body>
</html>
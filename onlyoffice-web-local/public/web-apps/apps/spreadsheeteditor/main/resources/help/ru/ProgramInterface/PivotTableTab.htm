<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Сводная таблица</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора электронных таблиц - Вкладка Сводная таблица" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вкладка Сводная таблица</h1>
            <p>Вкладка <b>Сводная таблица</b> в <a href="https://www.onlyoffice.com/ru/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редакторе электронных таблиц</b></a> позволяет <a href="../UsageInstructions/PivotTables.htm" onclick="onhyperlinkclick(this)">создавать и редактировать</a> сводные таблицы.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора электронных таблиц:</p>
                <p><img alt="Вкладка Сводная таблица" src="../images/interface/pivottabletab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора электронных таблиц:</p>
                <p><img alt="Вкладка Макет" src="../images/interface/desktop_pivottabletab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>создать новую сводную таблицу,</li>
                <li>выбрать нужный макет сводной таблицы,</li>
                <li>обновить сводную таблицу при изменении данных в исходном наборе,</li>
                <li>выделить всю сводную таблицу одним кликом,</li>
                <li>выделить некоторые строки или столбцы, применив к ним особое форматирование,</li>
                <li>выбрать один из готовых стилей таблиц.</li>
            </ul>
        </div>
	</body>
</html>
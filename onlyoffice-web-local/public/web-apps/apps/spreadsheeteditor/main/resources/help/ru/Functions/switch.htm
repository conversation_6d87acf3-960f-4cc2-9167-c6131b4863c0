<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПЕРЕКЛЮЧ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ПЕРЕКЛЮЧ</h1>
            <p>Функция <b>ПЕРЕКЛЮЧ</b> - это одна из логических функций. Вычисляет значение (которое называют выражением) на основе списка значений и возвращает результат, соответствующий первому совпадающему значению. Если совпадения не обнаружены, может быть возвращено необязательное стандартное значение.</p>
            <p>Синтаксис функции <b>ПЕРЕКЛЮЧ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ПЕРЕКЛЮЧ(выражение;значение1;результат1;[по_умолчанию или значение2;результат2];…[по_умолчанию или значение3;результат3])</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>выражение</em></b> - это значение, которое сравнивается со значениями <b><em>значение1 ...значение126</em></b>.</p>
            <p style="text-indent: 50px;"><b><em>значение1 ...значение126</em></b> - это значение, с которым сравнивается аргумент <b><em>выражение</em></b>.</p>
            <p style="text-indent: 50px;"><b><em>результат1 ...результат126</em></b> - это результат, возвращаемый, если аргумент <b><em>значение1 ...значение126</em></b> совпадает с аргументом <b><em>выражение</em></b>.</p>
            <p style="text-indent: 50px;"><b><em>по_умолчанию</em></b> - это результат, возвращаемый, если совпадения не обнаружены. Если аргумент <b><em>по_умолчанию</em></b> не задан и совпадений не обнаружено, функция возвращает ошибку #Н/Д.</p>
            <p class="note"><b>Примечание</b>: можно ввести до <b>254</b> аргументов, то есть до 126 пар значений и результатов.</p>
            <p>Чтобы применить функцию <b>ПЕРЕКЛЮЧ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Логические</b>,</li>
                <li>щелкните по функции <b>ПЕРЕКЛЮЧ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ПЕРЕКЛЮЧ" src="../images/switch.png" /></p>
        </div>
	</body>
</html>
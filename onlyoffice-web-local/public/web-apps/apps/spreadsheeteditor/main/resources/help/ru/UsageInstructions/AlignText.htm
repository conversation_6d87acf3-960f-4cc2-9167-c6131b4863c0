<!DOCTYPE html>
<html>
	<head>
		<title>Выравнивание данных в ячейках</title>
		<meta charset="utf-8" />
		<meta name="description" content="Все о выравнивании данных в ячейках" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Выравнивание данных в ячейках</h1>
			<p>Данные внутри ячейки можно выравнивать горизонтально или вертикально или даже поворачивать. Для этого выделите ячейку, диапазон ячеек мышью или весь рабочий лист, нажав сочетание клавиш <b>Ctrl+A</b>. Можно также выделить несколько ячеек или диапазонов ячеек, которые не являются смежными, удерживая клавишу <b>Ctrl</b> при выделении ячеек и диапазонов с помощью мыши. Затем выполните одну из следующих операций, используя значки, расположенные на вкладке <b>Главная</b> верхней панели инструментов.</p>
			<ul>
				<li>
					Примените один из типов горизонтального выравнивания данных внутри ячейки:
					<ul>
						<li>нажмите на значок <b>По левому краю</b> <div class = "icon icon-alignleft"></div> для выравнивания данных по левому краю ячейки (правый край остается невыровненным);</li>
						<li>нажмите на значок <b>По центру</b> <div class = "icon icon-aligncenter"></div> для выравнивания данных по центру ячейки (правый и левый края остаются невыровненными);</li>
						<li>нажмите на значок <b>По правому краю</b> <div class = "icon icon-alignright"></div> для выравнивания данных по правому краю ячейки (левый край остается невыровненным);</li>
						<li>нажмите на значок <b>По ширине</b> <div class = "icon icon-justify"></div> для выравнивания данных как по левому, так и по правому краю ячейки (выравнивание осуществляется за счет добавления дополнительных интервалов там, где это необходимо).</li>
					</ul>
				</li>
				<li>
					Измените вертикальное выравнивание данных внутри ячейки:
					<ul>
						<li>нажмите на значок <b>По верхнему краю</b> <div class = "icon icon-aligntop"></div> для выравнивания данных по верхнему краю ячейки;</li>
						<li>нажмите на значок <b>По середине</b> <div class = "icon icon-alignmiddle"></div> для выравнивания данных по центру ячейки;</li>
						<li>нажмите на значок <b>По нижнему краю</b> <div class = "icon icon-alignbottom"></div> для выравнивания данных по нижнему краю ячейки.</li>
					</ul>
				</li>
				<li>
					Измените угол наклона данных внутри ячейки, щелкнув по значку <b>Ориентация</b> <div class = "icon icon-orientation"></div> и выбрав одну из опций:
					<ul>
						<li>используйте опцию <b>Горизонтальный текст</b> <div class = "icon icon-horizontaltext"></div>, чтобы расположить текст по горизонтали (эта опция используется по умолчанию),</li>
						<li>используйте опцию <b>Текст против часовой стрелки</b> <div class = "icon icon-anglecounterclockwise"></div>, чтобы расположить текст в ячейке от левого нижнего угла к правому верхнему,</li>
						<li>используйте опцию <b>Текст по часовой стрелке</b> <div class = "icon icon-angleclockwise"></div>, чтобы расположить текст в ячейке от левого верхнего угла к правому нижнему углу,</li>
						<li>используйте опцию <b>Вертикальный текст</b> <div class = "icon icon-verticaltext"></div>, чтобы расположить текст вертикально,</li>
						<li>используйте опцию <b>Повернуть текст вверх</b> <div class = "icon icon-rotateup"></div>, чтобы расположить текст в ячейке снизу вверх,</li>
						<li>
							используйте опцию <b>Повернуть текст вниз</b> <div class = "icon icon-rotatedown"></div>, чтобы расположить текст в ячейке сверху вниз.
						</li>
					</ul>
				</li>
			</ul>
			    <p><img alt="Параметры ячейки" src="../images/cellsettings_rightsidebar.png" /></p>
			<ul>
				<li>
					Чтобы добавить отступ для текста в ячейке, в разделе <b>Параметры ячейки</b> правой боковой панели введите значение <b>Отступа</b>, на которое содержимое ячейки будет перемещено вправо.
					<p class="note">Если вы измените ориентацию текста, отступы будут сброшены. Если вы измените отступы для повернутого текста, ориентация текста будет сброшена. Отступы можно установить только если выбрана горизонтальная или вертикальная ориентация текста.</p>
				</li>
				<li>
					Чтобы повернуть текст на точно заданный угол, нажмите на значок <b>Параметры ячейки</b> <div class = "icon icon-cellsettings_rightpanel"></div> на правой боковой панели и используйте раздел <b>Ориентация</b>.  Введите в поле <b>Угол</b> нужное значение в градусах или скорректируйте его, используя стрелки справа.
				</li>
				<li>
					Расположите данные в ячейке в соответствии с шириной столбца, щелкнув по значку <b>Перенос текста</b> <div class = "icon icon-wraptext"></div>.
					<p class="note">При изменении ширины столбца перенос текста настраивается автоматически.</p>
				</li>
				<li>Чтобы расположить данные в ячейке в соответствии с шириной ячейки, установите флажок <b>Автоподбор ширины</b> на правой боковой панели. Содержимое ячейки будет уменьшено в размерах так, чтобы оно могло полностью уместиться внутри.</li>
			</ul>
		</div>
	</body>
</html>
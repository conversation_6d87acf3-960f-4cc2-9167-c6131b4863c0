<!DOCTYPE html>
<html>
	<head>
		<title>Функция ГИПЕРССЫЛКА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ГИПЕРССЫЛКА</h1>
			<p>Функция <b>ГИПЕРССЫЛКА</b> это одна из поисковых функций. Она создает ярлык, который позволяет перейти к другому месту в текущей книге или открыть документ, расположенный на сетевом сервере, в интрасети или в Интернете.</p>
			<p>Синтаксис функции <b>ГИПЕРССЫЛКА</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ГИПЕРССЫЛКА(адрес;[имя])</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>адрес</em></b> - путь и имя открываемого документа. <span class="onlineDocumentFeatures">В <em>онлайн-версии</em> путь может быть только URL-адресом.</span> Аргумент <b><em>адрес</em></b> также может указывать на определенное место в текущей рабочей книге, например, на определенную ячейку или именованный диапазон. Значение аргумента может быть задано в виде текстовой строки, заключенной в кавычки, или в виде ссылки на ячейку, содержащей текстовую строку.</p> 
				<p style="text-indent: 50px;"><b><em>имя</em></b> - текст, отображаемый в ячейке. Необязательный аргумент. Если этот аргумент опущен, в ячейке отображается значение аргумента <b><em>адрес</em></b>.</p>
				
			<p>Чтобы применить функцию <b>ГИПЕРССЫЛКА</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
			<li>щелкните по функции <b>ГИПЕРССЫЛКА</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
            <p>Чтобы перейти по ссылке, щелкните по ней. Чтобы выделить ячейку со ссылкой, не переходя по этой ссылке, щелкните и удерживайте кнопку мыши.</p>
			<p style="text-indent: 150px;"><img alt="Функция ГИПЕРССЫЛКА" src="../images/hyperlinkfunction.png" /></p>
		</div>
	</body>
</html>
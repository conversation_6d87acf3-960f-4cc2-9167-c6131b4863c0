<!DOCTYPE html>
<html>
	<head>
		<title>Изменение формата представления чисел</title>
		<meta charset="utf-8" />
		<meta name="description" content="Измените формат представления чисел, чтобы отображать их наиболее подходящим образом" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Изменение формата представления чисел</h1>
            <h3>Применение числового формата</h3>
			<p>Можно легко изменить числовой формат, то есть то, каким образом выглядят введенные числа в электронной таблице. Для этого: </p>
			<ol>
				<li>выделите ячейку, диапазон ячеек мышью или весь рабочий лист, нажав сочетание клавиш <b>Ctrl+A</b>,
                <p class="note"><b>Примечание</b>: можно также выделить несколько ячеек или диапазонов ячеек, которые не являются смежными, удерживая клавишу <b>Ctrl</b> при выделении ячеек и диапазонов с помощью мыши.</p>
                </li>
				<li>разверните список <b>Числовой формат</b> <img alt="Список Числовой формат" src="../images/generalformat.png" />, расположенный на вкладке <b>Главная</b> верхней панели инструментов, или щелкните по выделенным ячейкам правой кнопкой мыши и используйте пункт контекстного меню <b>Числовой формат</b>. Выберите формат представления чисел, который надо применить:
					<ul>
						<li><b>Общий</b> - используется для отображения введенных данных как обычных чисел, самым компактным образом без дополнительных знаков,</li>
						<li><b>Числовой</b> - используется для отображения чисел с 0-30 знаками после десятичной запятой, где между каждой группой из трех цифр перед десятичной запятой вставляется разделитель тысяч,</li>
						<!--<li><b>Целочисленный</b> - используется для отображения чисел в виде целых чисел,</li>-->
						<li><b>Научный</b> (экспоненциальный) - используется для представления очень длинных чисел за счет преобразования в строку типа <em>d.dddE+ddd</em> или <em>d.dddE-ddd</em>, где каждый символ <em>d</em> обозначает цифру от 0 до 9,</li>
                        <li>
                            <b>Финансовый</b> - используется для отображения денежных значений с используемым по умолчанию обозначением денежной единицы и двумя десятичными знаками. Чтобы применить другое обозначение денежной единицы или количество десятичных знаков, следуйте <a href="../UsageInstructions/ChangeNumberFormat.htm#customize" onclick="onhyperlinkclick(this)">приведенным ниже инструкциям</a>. В отличие от <b>Денежного</b> формата, в <b>Финансовом</b> формате обозначения денежной единицы выравниваются по левому краю ячейки, нулевые значения представляются как тире, а отрицательные значения отображаются в скобках.
                            <p><img alt="Финансовый и денежный форматы представления чисел" src="../images/accounting_currency.png" /></p>
                            <p class="note"><b>Примечание</b>: чтобы быстро применить к выделенным данным <b>Финансовый</b> формат, можно также щелкнуть по значку <b>Финансовый формат</b> <span class="icon icon-accountingstyle"></span> на вкладке <b>Главная</b> верхней панели инструментов и выбрать нужное обозначение денежной единицы: <b>&#36;</b> Доллар, <b>&euro;</b> Евро, <b>&pound</b> Фунт, <b>&#8381;</b> Рубль, <b>&yen</b> Йена, <b>kn</b> Хорватская куна.</p>
                        </li>
                        <li><b>Денежный</b> - используется для отображения денежных значений с используемым по умолчанию обозначением денежной единицы и двумя десятичными знаками. Чтобы применить другое обозначение денежной единицы или количество десятичных знаков, следуйте <a href="../UsageInstructions/ChangeNumberFormat.htm#customize" onclick="onhyperlinkclick(this)">приведенным ниже инструкциям</a>. В отличие от <b>Финансового</b> формата, в <b>Денежном</b> формате обозначение денежной единицы помещается непосредственно рядом с числом, а отрицательные значения отображаются с отрицательным знаком (-).</li>
						<li><b>Дата</b> - используется для отображения дат<!--. Можно выбрать один из следующих форматов представления дат: <em>MM-dd-yy</em>, <em>MM-dd-yyyy</em>, <em>dd-MM-yy</em>, <em>dd-MM-yyyy</em>, <em>dd-MMM-yyyy</em>, <em>dd-MMM</em>, <em>MMM-yy</em>-->,</li>
						<li><b>Время</b> - используется для отображения времени<!--. Можно выбрать один из следующих форматов представления времени: <em>HH:mm</em>, <em>HH:MM:ss</em>, <em>hh:mm tt</em>, <em>hh:mm:ss tt</em>, <em>[h]:mm:ss</em>-->,</li>
						<li><b>Процентный</b> - используется для отображения данных в виде процентов со знаком процента <b>%</b>,
						<p class="note"><b>Примечание</b>: чтобы быстро применить к данным процентный формат, можно также использовать значок <b>Процентный формат</b> <span class="icon icon-percentstyle"></span> на вкладке <b>Главная</b> верхней панели инструментов.</p>
						</li>
                        <li><b>Дробный</b> - используется для отображения чисел в виде обыкновенных, а не десятичных дробей.</li>
						<li><b>Текстовый</b> - используется для отображения числовых значений, при котором они рассматриваются как обычный текст, с максимально доступной точностью.</li>
                        <li><b>Другие форматы</b> - используется для настройки уже примененных числовых форматов с указанием дополнительных параметров (см. описание ниже).</li>
                        <li><b>Особый</b> - используется для создания собственного формата:
                        <ul>
                            <li>выберите ячейку, диапазон ячеек или весь лист для значений, которые вы хотите отформатировать,</li>
                            <li>выберите пункт <b>Особый</b> в меню <b>Другие форматы</b>,</li>
                            <li>введите требуемые коды и проверьте результат в области предварительного просмотра или выберите один из шаблонов и / или объедините их. Если вы хотите создать формат на основе существующего, сначала примените существующий формат, а затем отредактируйте коды по своему усмотрению,</li>
                            <li>нажмите <b>OK</b>.</li>
                        </ul>
                        </li>
					</ul>
				</li>
				<li>при необходимости измените количество десятичных разрядов:
				    <ul>
				        <li>используйте значок <b>Увеличить разрядность</b> <div class = "icon icon-increasedecimal"></div>, расположенный на вкладке <b>Главная</b> верхней панели инструментов, чтобы увеличить количество знаков, отображаемых после десятичной запятой,</li>
				        <li>используйте значок <b>Уменьшить разрядность</b> <div class = "icon icon-decreasedecimal"></div>, расположенный на вкладке <b>Главная</b> верхней панели инструментов, чтобы уменьшить количество знаков, отображаемых после десятичной запятой.</li>
				    </ul>
				 </li>
			</ol>
            <p class="note">
                <b>Примечание</b>: чтобы изменить числовой формат, можно также использовать <a href="../HelpfulHints/KeyboardShortcuts.htm#dataformats" onclick="onhyperlinkclick(this)">сочетания клавиш</a>.
            </p>
            <h3 id="customize">Настройка числового формата</h3>
            <p>Настроить числовой формат можно следующим образом:</p>
            <ol>
                <li>выделите ячейки, для которых требуется настроить числовой формат,</li>
                <li>разверните список <b>Числовой формат</b> <img alt="Список Числовой формат" src="../images/generalformat.png" />, расположенный на вкладке <b>Главная</b> верхней панели инструментов, или щелкните по выделенным ячейкам правой кнопкой мыши и используйте пункт контекстного меню <b>Числовой формат</b>,</li>
                <li>выберите опцию <b>Другие форматы</b>,</li>
                <li>
                    в открывшемся окне <b>Числовой формат</b> настройте доступные параметры. Опции различаются в зависимости от того, какой числовой формат применен к выделенным ячейкам. Чтобы изменить числовой формат, можно использовать список <b>Категория</b>.
                    <p><img alt="Окно Числовой формат" src="../images/numberformatwindow.png" /></p>
                    <ul>
                        <li>для <b>Числового</b> формата можно задать количество <b>Десятичных знаков</b>, указать, надо ли <b>Использовать разделитель разрядов</b>, и выбрать один из доступных <b>Форматов</b> для отображения отрицательных значений.</li>
                        <li>для <b>Научного</b> и <b>Процентного</b> форматов, можно задать количество <b>Десятичных знаков</b>.</li>
                        <li>для <b>Финансового</b> и <b>Денежного</b> форматов, можно задать количество <b>Десятичных знаков</b>, выбрать одно из доступных <b>Обозначений</b> денежных единиц и один из доступных <b>Форматов</b> для отображения отрицательных значений.</li>
                        <li>для формата <b>Дата</b> можно выбрать один из доступных форматов представления дат: <em>15.4</em>, <em>15.4.06</em>, <em>15.04.06</em>, <em>15.4.2006</em>, <em>15.4.06 0:00</em>, <em>15.4.06 12:00 AM</em>, <em>A</em>, <em>апреля 15 2006</em>, <em>15-апр</em>, <em>15-апр-06</em>, <em>апр-06</em>, <em>Апрель-06</em>, <em>A-06</em>, <em>06-апр</em>, <em>15-апр-2006</em>, <em>2006-апр-15</em>, <em>06-апр-15</em>, <em>15.апр</em>, <em>15.апр.06</em>, <em>апр.06</em>, <em>Апрель.06</em>, <em>А.06</em>, <em>06.апр</em>, <em>15.апр.2006</em>, <em>2006.апр.15</em>, <em>06.апр.15</em>, <em>15 апр</em>, <em>15 апр 06</em>, <em>апр 06</em>, <em>Апрель 06</em>, <em>А 06</em>, <em>06 апр</em>, <em>15 апр 2006</em>, <em>2006 апр 15</em>, <em>06 апр 15</em>, <em>06.4.15</em>, <em>06.04.15</em>, <em>2006.4.15</em>.</li>
                        <li>для формата <b>Время</b> можно выбрать один из доступных форматов представления времени: <em>12:48:58 PM</em>, <em>12:48</em>, <em>12:48 PM</em>, <em>12:48:58</em>, <em>48:57,6</em>, <em>36:48:58</em>.</li>
                        <li>для <b>Дробного</b> формата можно выбрать один из доступных форматов: <em>До одной цифры (1/3)</em>, <em>До двух цифр (12/25)</em>, <em>До трех цифр (131/135)</em>, <em>Половинными долями (1/2)</em>, <em>Четвертыми долями (2/4)</em>, <em>Восьмыми долями (4/8)</em>, <em>Шестнадцатыми долями (8/16)</em>, <em>Десятыми долями (5/10)</em>, <em>Сотыми долями (50/100)</em>.</li>
                    </ul>
                </li>
                <li>нажмите кнопку <b>OK</b>, чтобы применить изменения.</li>
            </ol>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПРОПНАЧ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПРОПНАЧ</h1>
			<p>Функция <b>ПРОПНАЧ</b> - это одна из функций для работы с текстом и данными. Преобразует первую букву каждого слова в прописную (верхний регистр), а все остальные буквы - в строчные (нижний регистр).</p>
			<p>Синтаксис функции <b>ПРОПНАЧ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ПРОПНАЧ(текст)</em></b></p> 
			<p>где <b><em>текст</em></b> - это данные, введенные вручную или находящиеся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>ПРОПНАЧ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>ПРОПНАЧ</b>,</li>
			<li>введите требуемый аргумент,
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ПРОПНАЧ" src="../images/proper.png" /></p>
		</div>
	</body>
</html>
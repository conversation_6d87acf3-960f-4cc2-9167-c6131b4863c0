<!DOCTYPE html>
<html>
	<head>
		<title>Функция СТОЛБЕЦ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СТОЛБЕЦ</h1>
			<p>Функция <b>СТОЛБЕЦ</b> - это одна из поисковых функций. Она возвращает номер столбца ячейки.</p>
			<p>Синтаксис функции <b>СТОЛБЕЦ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СТОЛБЕЦ([ссылка])</em></b></p> 
			<p>где <b><em>ссылка</em></b> - ссылка на ячейку.</p>
			<p class="note"><b>Примечание</b>: <b><em>ссылка</em></b> - необязательный аргумент. Если он опущен, функция возвращает номер столбца той ячейки, которая выделена для отображения результата функции <b>СТОЛБЕЦ</b>. </p>
			<p>Чтобы применить функцию <b>СТОЛБЕЦ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
			<li>щелкните по функции <b>СТОЛБЕЦ</b>,</li>
			<li>введите требуемый аргумент,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СТОЛБЕЦ" src="../images/column.png" /></p>
		</div>
	</body>
</html>
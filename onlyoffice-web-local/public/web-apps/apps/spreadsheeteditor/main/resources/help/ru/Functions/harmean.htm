<!DOCTYPE html>
<html>
	<head>
		<title>Функция СРГАРМ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СРГАРМ</h1>
			<p>Функция <b>СРГАРМ</b> - это одна из статистических функций. Вычисляет среднее гармоническое для списка значений.</p>
			<p>Синтаксис функции <b>СРГАРМ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СРГАРМ(список_аргументов)</em></b></p> 
			<p>где <b><em>список_аргументов</em></b> - это до 30 числовых значений больше 0, введенных вручную или находящихся в ячейках, на которые даются ссылки.</p>
			<p>Чтобы применить функцию <b>СРГАРМ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>СРГАРМ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой или выделите мышью диапазон ячеек,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СРГАРМ" src="../images/harmean.png" /></p>
		</div>
	</body>
</html>
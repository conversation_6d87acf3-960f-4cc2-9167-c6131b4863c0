<!DOCTYPE html>
<html>
	<head>
		<title>Функция ВЕРОЯТНОСТЬ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ВЕРОЯТНОСТЬ</h1>
			<p>Функция <b>ВЕРОЯТНОСТЬ</b> - это одна из статистических функций. Возвращает вероятность того, что значения из интервала находятся внутри нижнего и верхнего пределов.</p>
			<p>Синтаксис функции <b>ВЕРОЯТНОСТЬ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ВЕРОЯТНОСТЬ(x_интервал;интервал_вероятностей;[нижний_предел];[верхний_предел])</em></b></p> 
			<p><em>где</em></p>
				<p style="text-indent: 50px;"><b><em>x_интервал</em></b> - выбранный диапазон ячеек, содержащих числовые значения, с которыми связаны вероятности.</p>
				<p style="text-indent: 50px;"><b><em>интервал_вероятностей</em></b> - множество вероятностей, соответствующих значениям в аргументе <b><em>x_интервал</em></b>, выбранный диапазон ячеек, содержащих числовые значения больше 0, но меньше 1. Сумма значений в аргументе <b><em>интервал_вероятностей</em></b> должна равняться 1, в противном случае функция возвращает ошибку <b>#ЧИСЛО!</b>.</p>
				<p class="note"><b>Примечание</b>: аргументы <b><em>x_интервал</em></b> и <b><em>интервал_вероятностей</em></b> должны содержать одинаковое количество элементов.</p>
				<p style="text-indent: 50px;"><b><em>нижний_предел</em></b> - нижняя граница значения; числовое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
				<p style="text-indent: 50px;"><b><em>верхний_предел</em></b> - верхняя граница значения; числовое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка. Необязательный аргумент. Если он опущен, функция ВЕРОЯТНОСТЬ возвращает вероятность, равную значению аргумента <b><em>нижний_предел</em></b>.</p>
			<p>Чтобы применить функцию <b>ВЕРОЯТНОСТЬ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>ВЕРОЯТНОСТЬ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ВЕРОЯТНОСТЬ" src="../images/prob.png" /></p>
		</div>
	</body>
</html>
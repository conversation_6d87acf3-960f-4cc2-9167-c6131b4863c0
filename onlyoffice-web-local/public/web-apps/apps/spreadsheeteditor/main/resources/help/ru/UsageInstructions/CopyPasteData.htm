<!DOCTYPE html>
<html>
	<head>
		<title>Вырезание / копирование / вставка данных</title>
		<meta charset="utf-8" />
		<meta name="description" content="Вырезайте/копируйте/вставляйте данные, используя сочетания клавиш" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Вырезание / копирование / вставка данных</h1>
      <h3>Использование основных операций с буфером обмена</h3>
      <p>Для вырезания, копирования и вставки данных в текущей электронной таблице используйте контекстное меню или соответствующие значки, доступные на любой вкладке верхней панели инструментов:</p>
			<ul>
				<li><b>Вырезание</b> - выделите данные и используйте опцию контекстного меню <b>Вырезать</b> или значок <b>Вырезать</b> <span class="icon icon-cut"></span> на верхней панели инструментов, чтобы удалить выделенные данные и отправить их в буфер обмена компьютера. <span class="onlineDocumentFeatures">Вырезанные данные можно затем вставить в другое место этой же электронной таблицы.</span></li>
				<li><b>Копирование</b> - выделите данные, затем или используйте значок <b>Копировать</b> <span class="icon icon-copy"></span> на верхней панели инструментов, или щелкните правой кнопкой мыши и выберите в меню пункт <b>Копировать</b>, чтобы отправить выделенные данные в буфер обмена компьютера. <span class="onlineDocumentFeatures">Скопированные данные можно затем вставить в другое место этой же электронной таблицы.</span></li>
				<li><b>Вставка</b> - выберите место, затем или используйте значок <b>Вставить</b> <span class="icon icon-paste"></span> на верхней панели инструментов, или щелкните правой кнопкой мыши и выберите в меню пункт <b>Вставить</b>, чтобы вставить ранее скопированные или вырезанные данные из буфера обмена компьютера в текущей позиции курсора. <span class="onlineDocumentFeatures">Данные могут быть ранее скопированы из этой же электронной таблицы.</span></li>
			</ul>
            <p><span class="onlineDocumentFeatures">В <em>онлайн-версии</em> для копирования данных из другой электронной таблицы или какой-то другой программы или вставки данных в них используются только сочетания клавиш,</span> <span class="desktopDocumentFeatures">в <em>десктопной версии</em> для любых операций копирования и вставки можно использовать как кнопки на панели инструментов или опции контекстного меню, так и сочетания клавиш:</span></p>
			<ul>
			<li>сочетание клавиш <b>Ctrl+X</b> для вырезания;</li>
			<li>сочетание клавиш <b>Ctrl+C</b> для копирования;</li>
			<li>сочетание клавиш <b>Ctrl+V</b> для вставки.</li>
			</ul>
      <p class="note">
        <b>Примечание</b>: вместо того, чтобы вырезать и вставлять данные на одном и том же рабочем листе, можно выделить нужную ячейку/диапазон ячеек,
        установить указатель мыши у границы выделения (при этом он будет выглядеть так: <span class="icon icon-arrow"></span>)
        и перетащить выделение мышью в нужное место.  
      </p>
            <p>Чтобы включить / отключить автоматическое появление кнопки <b>Специальная вставка</b> после вставки, перейдите на вкладку <b>Файл</b> > <b>Дополнительные параметры</b> и поставьте / снимите галочку <b>Показывать кнопку Параметры вставки при вставке содержимого</b>.</p>
            <h3>Использование функции Специальная вставка</h3>
            <p class="note"><b>Примечание</b>: Во время совместной работы, <b>Специальная вставка</b> доступна только в <b>Строгом</b> режиме редактирования.</p>
            <p>После вставки скопированных данных рядом с правым нижним углом вставленной ячейки/диапазона ячеек появляется кнопка <b>Специальная вставка</b> <span class="icon icon-pastespecialbutton"></span>. Нажмите на эту кнопку, чтобы выбрать нужный параметр вставки. </p>
            <p>При вставке ячейки/диапазона ячеек с отформатированными данными доступны следующие параметры:</p>
            <ul>
                <li><em>Вставить (Ctrl+P)</em> - позволяет вставить все содержимое ячейки, включая форматирование данных. Эта опция выбрана по умолчанию.</li>
                <li>
                    Следующие опции можно использовать, если скопированные данные содержат формулы:
                    <ul>
                        <li><em>Вставить только формулу (Ctrl+F)</em> - позволяет вставить формулы, не вставляя форматирование данных.</li>
                        <li><em>Формула + формат чисел (Ctrl+O)</em> - позволяет вставить формулы вместе с форматированием, примененным к числам.</li>
                        <li><em>Формула + все форматирование (Ctrl+K)</em> - позволяет вставить формулы вместе со всем форматированием данных.</li>
                        <li><em>Формула без границ (Ctrl+B)</em> - позволяет вставить формулы вместе со всем форматированием данных, кроме границ ячеек.</li>
                        <li><em>Формула + ширина столбца (Ctrl+W)</em> - позволяет вставить формулы вместе со всем форматированием данных и установить ширину столбцов исходных ячеек для диапазона ячеек, в который вы вставляете данные.</li>
                        <li><em>Транспонировать (Ctrl+T)</em> - позволяет вставить данные, изменив столбцы на строки, а строки на столбцы. Эта опция доступна для обычных диапазонов данных, но не для форматированных таблиц.</li>
                    </ul>
                </li>
                <li>
                    Следующие опции позволяют вставить результат, возвращаемый скопированной формулой, не вставляя саму формулу:
                    <ul>
                        <li><em>Вставить только значение (Ctrl+V)</em> - позволяет вставить результаты формул, не вставляя форматирование данных.</li>
                        <li><em>Значение + формат чисел (Ctrl+A)</em> - позволяет вставить результаты формул вместе с форматированием, примененным к числам.</li>
                        <li><em>Значение + все форматирование (Ctrl+E)</em> - позволяет вставить результаты формул вместе со всем форматированием данных.</li>
                    </ul>
                </li>
                <li><em>Вставить только форматирование (Ctrl+R)</em> - позволяет вставить только форматирование ячеек, не вставляя содержимое ячеек.</li>
                <li><em>Вставить связь (Ctrl+N)</em> - позволяет вставить внутреннюю ссылку на ячейку или файл на текущем портале.
                    <p><img alt="Параметры вставки" src="../images/pastespecial.png" /></p>
                </li>
                <li>
                    <em>Специальная вставка</em> - открывает диалоговое окно <b>Специальная вставка</b>, которое содержит следующие опции:
                    <ol>
                        <li>
                            <b>Параметры вставки</b>
                            <ul>
                                <li><em>Формулы</em> - позволяет вставить формулы, не вставляя форматирование данных.</li>
                                <li><em>Значения</em> - позволяет вставить формулы вместе с форматированием, примененным к числам.</li>
                                <li><em>Форматы</em> - позволяет вставить формулы вместе со всем форматированием данных.</li>
                                <li><em>Комментарии</em> - позволяет вставить только комментарии, добавленные к ячейкам выделенного диапазона.</li>
                                <li><em>Ширина столбцов</em> - позволяет установить ширину столбцов исходных ячеек для диапазона ячеек.</li>
                                <li><em>Без рамки</em> - позволяет вставить формулы без форматирования границ.</li>
                                <li><em>Формулы и форматирование</em> - позволяет вставить формулы вместе со всем форматированием данных.</li>
                                <li><em>Формулы и ширина столбцов</em> - позволяет вставить формулы вместе со всем форматированием данных и установить ширину столбцов исходных ячеек для диапазона ячеек, в который вы вставляете данные.</li>
                                <li><em>Формулы и форматы чисел</em> - позволяет вставить формулы вместе с форматированием, примененным к числам.</li>
                                <li><em>Значения и форматы чисел</em> - позволяет вставить результаты формул вместе с форматированием, примененным к числам.</li>
                                <li><em>Значения и форматирование</em> - позволяет вставить результаты формул вместе со всем форматированием данных.</li>
                            </ul>
                        </li>
                        <li>
                            <b>Операция</b>
                            <ul>
                                <li><em>Сложение</em> - позволяет автоматически произвести операцию сложения для числовых значений в каждой вставленной ячейке.</li>
                                <li><em>Вычитание</em> - позволяет автоматически произвести операцию вычитания для числовых значений в каждой вставленной ячейке.</li>
                                <li><em>Умножение</em> - позволяет автоматически произвести операцию умножения для числовых значений в каждой вставленной ячейке.</li>
                                <li><em>Деление</em> - позволяет автоматически произвести операцию деления для числовых значений в каждой вставленной ячейке.</li>
                            </ul>
                        </li>
                        <li><b>Транспонировать</b> - позволяет вставить данные, изменив столбцы на строки, а строки на столбцы.</li>
                        <li><b>Пропускать пустые ячейки</b> - позволяет не вставлять форматирование и значения пустых ячеек.</li>
                    </ol>
                    <p><img alt="Окно Специальная вставка" src="../images/pastespecial_window.png" /></p>
                </li>
            </ul>
            <p>При вставке содержимого отдельной ячейки или текста в автофигурах доступны следующие параметры:</p>
            <ul>
                <li><em>Исходное форматирование (Ctrl+K)</em> - позволяет сохранить исходное форматирование скопированных данных.</li>
                <li><em>Форматирование конечных ячеек (Ctrl+M)</em> - позволяет применить форматирование, которое уже используется для ячейки/автофигуры, в которую вы вставляете данные.</li>
            </ul>
            <h5 id="delimiteddata">Вставка данных с разделителями</h5>
            <p>При вставке текста с разделителями, скопированного из файла <b>.txt</b>, доступны следующие параметры:</p>
            <p class="note">Текст с разделителями может содержать несколько записей, где каждая запись соответствует одной табличной строке. Запись может включать в себя несколько текстовых значений, разделенных с помощью разделителей (таких как запятая, точка с запятой, двоеточие, табуляция, пробел или другой символ). Файл должен быть сохранен как простой текст в формате <b>.txt</b>.</p>
            <ul>
                <li><em>Сохранить только текст (Ctrl+T)</em> - позволяет вставить текстовые значения в один столбец, где содержимое каждой ячейки соответствует строке в исходном текстовом файле.</li>
                <li>
                    <em>Использовать мастер импорта текста</em> - позволяет открыть <b>Мастер импорта текста</b>, с помощью которого можно легко распределить текстовые значения на несколько столбцов, где каждое текстовое значение, отделенное разделителем, будет помещено в отдельной ячейке.
                    <p>Когда откроется окно <b>Мастер импорта текста</b>, из выпадающего списка <b>Разделитель</b> выберите разделитель текста, который используется в данных с разделителем. Данные, разделенные на столбцы, будут отображены в расположенном ниже поле <b>Просмотр</b>. Если вас устраивает результат, нажмите кнопку <b>OK</b>.</p>
                </li>
            </ul>
            <p><img alt="Мастер импорта текста" src="../images/textimportwizard.png" /></p>
            <p>Если вы вставили данные с разделителями из источника, который не является простым текстовым файлом (например, текст, скопированный с веб-страницы и т.д.), или если вы применили функцию <em>Сохранить только текст</em>, а теперь хотите распределить данные из одного столбца по нескольким столбцам, вы можете использовать опцию <b>Текст по столбцам</b>. </p>
            <p>Чтобы разделить данные на несколько столбцов:</p>
            <ol>
                <li>Выделите нужную ячейку или столбец, содержащий данные с разделителями.</li>
                <li>Перейдите на вкладку <b>Данные</b>.</li>
                <li>Нажмите кнопку <b>Текст по столбцам</b> на верхней панели инструментов. Откроется <b>Мастер распределения текста по столбцам</b>.</li>
                <li>В выпадающем списке <b>Разделитель</b> выберите разделитель, который используется в данных с разделителем.</li>
                <li>
                    Нажмите кнопку <b>Дополнительно</b>, чтобы открыть окно <b>Дополнительные параметры</b>, в котором можно указать <b>Десятичный разделитель</b> и <b>Разделитель разрядов тысяч</b>.
                    <p><img alt="Окно настроек разделителей" src="../images/separator.png" /></p>
                </li>
                <li>Просмотрите результат в расположенном ниже поле и нажмите кнопку <b>OK</b>.</li>
            </ol>
            <p>После этого каждое текстовое значение, отделенное разделителем, будет помещено в отдельной ячейке.</p>
            <p class="note">Если в ячейках справа от столбца, который требуется разделить, содержатся какие-то данные, эти данные будут перезаписаны.</p>
      <h3>Использование функции автозаполнения</h3>
      <p>
        Для быстрого заполнения множества ячеек одними и теми же данными воспользуйтесь функцией <b>Автозаполнение</b>:        
      </p>
      <ol>
        <li>выберите ячейку/диапазон ячеек, содержащие нужные данные,</li>
        <li>
          поместите указатель мыши рядом с маркером заполнения в правом нижнем углу ячейки. При этом указатель будет выглядеть как черный крест:          
          <p>
              <div class = "big big-autofill"></div>
          </p>
        </li>
        <li>перетащите маркер заполнения по соседним ячейкам, которые вы хотите заполнить выбранными данными.</li>
      </ol>
      <p class="note">
        <b>Примечание</b>: если вам нужно создать последовательный ряд цифр (например 1, 2, 3, 4...; 2, 4, 6, 8... и т.д.) 
        или дат, можно ввести хотя бы два начальных значения и быстро продолжить ряд, выделив эти ячейки и перетащив мышью маркер заполнения.        
      </p>
            <h3>Заполнение ячеек в столбце текстовыми значениями</h3>
            <p>Если столбец электронной таблицы содержит какие-то текстовые значения, можно легко заменить любое значение в этом столбце или заполнить следующую пустую ячейку, выбрав одно из уже существующих текстовых значений.</p>
            <p>Щелкните по нужной ячейке правой кнопкой мыши и выберите в контекстном меню пункт <b>Выбрать из списка</b>.</p>
            <p><img alt="Выбор из списка" src="../images/selectfromlist.png" /></p>
            <p>Выберите одно из доступных текстовых значений для замены текущего значения или заполнения пустой ячейки.</p>
    </div>
	</body>
</html>
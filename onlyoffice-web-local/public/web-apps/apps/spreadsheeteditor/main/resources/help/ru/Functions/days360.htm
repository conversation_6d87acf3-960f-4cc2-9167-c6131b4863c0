<!DOCTYPE html>
<html>
	<head>
		<title>Функция ДНЕЙ360</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ДНЕЙ360</h1>
			<p>Функция <b>ДНЕЙ360</b> - это одна из функций даты и времени. Возвращает количество дней между двумя датами (начальной и конечной) на основе 360-дневного года с использованием одного из методов вычислений (американского или европейского).</p>
			<p>Синтаксис функции <b>ДНЕЙ360</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ДНЕЙ360(нач_дата;кон_дата;[метод])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>нач_дата</em></b> и <b><em>кон_дата</em></b> - значения, которые являются двумя датами, количество дней между которыми требуется вычислить.</p>
			<p style="text-indent: 50px;"><b><em>метод</em></b> - необязательное логическое значение: <b>ИСТИНА</b> или <b>ЛОЖЬ</b>. Если этот аргумент имеет значение <b>ИСТИНА</b>, вычисление выполняется с помощью европейского метода, согласно которому начальная и конечная даты, которые приходятся на 31-й день месяца, полагаются равными 30-му дню того же месяца.<br />
				Если этот аргумент имеет значение <b>ЛОЖЬ</b> или опущен, вычисление выполняется с помощью американского метода, согласно которому, если начальная дата является последним днем месяца, она полагается равной 30-му дню того же месяца. Если конечная дата является последним днем месяца, а начальная дата меньше, чем 30-е число, то конечная дата полагается равной первому дню следующего месяца. В противном случае конечная дата полагается равной 30-му дню того же месяца.
			</p> 
			<p>Чтобы применить функцию <b>ДНЕЙ360</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>ДНЕЙ360</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ДНЕЙ360" src="../images/days360.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ВЗЯТЬ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Функция ВЗЯТЬ</h1>
			<p>Функция <b>ВЗЯТЬ</b> - это одна из поисковых функций. Она возвращает строки или столбцы из начала или конца массива.</p>
			<p class="note">Обратите внимание, что это формула массива. Чтобы узнать больше, обратитесь к статье <a href="../UsageInstructions/InsertArrayFormulas.htm" onclick="onhyperlinkclick(this)">Вставка формул массива</a>.</p>
			<p>Синтаксис функции <b>ВЗЯТЬ</b>:</p>
			<p style="text-indent: 150px;"><b><em>ВЗЯТЬ(массив, строки, [столбцы])</em></b></p>
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>массив</em></b> - ссылка на диапазон ячеек, из которого берутся строки или столбцы.</p>
			<p style="text-indent: 50px;"><b><em>строки</em></b> - определяет количество строк, которые нужно взять. При отрицательном значении, строки будут браться с конца диапазона.</p>
			<p style="text-indent: 50px;"><b><em>столбцы</em></b> - определяет количество столбцов, которые нужно взять. При отрицательном значении, столбцы будут браться с конца диапазона.</p>
			<p>Чтобы применить функцию <b>ВЗЯТЬ</b>,</p>
			<ol>
				<li>выделите ячейку, в которой требуется отобразить результат,</li>
				<li>
					щелкните по значку <b>Вставить функцию</b> <div class="icon icon-insertfunction"></div> расположенному на верхней панели инструментов,
					<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
					<br />или щелкните по значку <div class="icon icon-function"></div> перед строкой формул,
				</li>
				<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
				<li>щелкните по функции <b>ВЗЯТЬ</b>,</li>
				<li>введите требуемые аргументы через точку с запятой,</li>
				<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<!--<p style="text-indent: 150px;"><img alt="TAKE Function" src="../images/take.png" /></p>-->
		</div>
	</body>
</html>
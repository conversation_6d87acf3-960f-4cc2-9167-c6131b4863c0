<!DOCTYPE html>
<html>
	<head>
		<title>Функция ГАММАОБР</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ГАММАОБР</h1>
			<p>Функция <b>ГАММАОБР</b> - это одна из статистических функций. Возвращает значение, обратное гамма-распределению.</p>
			<p>Синтаксис функции <b>ГАММАОБР</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ГАММАОБР(вероятность;альфа;бета)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>вероятность</em></b> - вероятность, связанная с гамма-распределением. Числовое значение больше 0 и меньше 1.</p>
			<p style="text-indent: 50px;"><b><em>альфа</em></b> - первый параметр распределения; числовое значение больше 0.</p>
			<p style="text-indent: 50px;"><b><em>бета</em></b> - второй параметр распределения; числовое значение больше 0. Если аргумент <b><em>бета</em></b> равен 1, функция ГАММАОБР возвращает стандартное гамма-распределение.</p>
			<p>Эти аргументы можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
 			<p>Чтобы применить функцию <b>ГАММАОБР</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>ГАММАОБР</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ГАММАОБР" src="../images/gammainv.png" /></p>
		</div>
	</body>
</html>
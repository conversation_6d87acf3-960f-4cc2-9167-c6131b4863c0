<!DOCTYPE html>
<html>
    <head>
        <title>Вставка символов и знаков</title>
        <meta charset="utf-8" />
        <meta name="description" content="Советы по совместному редактированию" />
        <link type="text/css" rel="stylesheet" href="../editor.css" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
    </head>
    <body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вставка символов и знаков</h1>
            <p>При работе может возникнуть необходимость поставить символ, которого нет на вашей клавиатуре. Для вставки таких символов используйте функцию <span class="icon icon-insert_symbol_icon"></span> <b>Вставить символ</b>.</p>
            <p>Для этого выполните следующие шаги:</p>
            <ul>
                <li>установите курсор, куда будет помещен символ,</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>
                    щелкните по значку <div class = "icon icon-insert_symbol_icon"></div> <b>Символ</b>,
                    <p><img alt="Таблица символов панель слева" src="../images/insert_symbol_window.png" /></p>
                </li>
                <li>в открывшемся окне выберите необходимый символ,</li>
                <li>
                    <p>чтобы быстрее найти нужный символ, используйте раздел <b>Набор</b>. В нем все символы распределены по группам, например, выберите «Символы валют», если нужно вставить знак валют.</p>
                    <p>Если же данный символ отсутствует в наборе, выберите другой шрифт. Во многих из них также есть символы, отличные от стандартного набора.</p>
                    <p>Или же впишите в строку шестнадцатеричный <b>Код знака из Юникод</b> нужного вам символа. Данный код можно найти в <b>Таблице символов</b>.</p>
                    <p>Также можно использовать вкладку <b>Специальные символы</b> для выбора специального символа из списка.</p>
                    <p><img alt="Таблица символов панель слева " src="../images/insert_symbol_window2.png" /></p>
                    <p>Для быстрого доступа к нужным символам также используйте <b>Ранее использовавшиеся символы</b>, где хранятся несколько последних использованных символов,</p>
                </li>
                <li>нажмите <b>Вставить</b>. Выбранный символ будет добавлен в документ.</li>
            </ul>

            <h2>Вставка символов ASCII</h2>
            <p>Для добавления символов также используется таблица символов ASCII.</p>
            <p>Для этого зажмите клавишу ALT и при помощи цифровой клавиатуры введите код знака.</p>
            <p class="note"><b>Обратите внимание</b>: убедитесь, что используете цифровую клавиатуру, а не цифры на основной клавиатуре. Чтобы включить цифровую клавиатуру, нажмите клавишу Num Lock.</p>
            <p>Например, для добавления символа параграфа (§) нажмите и удерживайте клавишу ALT, введите цифры 789, а затем отпустите клавишу ALT.</p>

            <h2>Вставка символов при помощи таблицы символов</h2>
            <p>С помощью таблицы символов Windows так же можно найти символы, которых нет на клавиатуре. Чтобы открыть данную таблицу, выполните одно из следующих действий:</p>
            <ul>
                <li>В строке Поиск напишите «Таблица символов» и откройте ее</li>
                <li>
                    Одновременно нажмите клавиши Win+R, в появившемся окне введите <code>charmap.exe</code> и щелкните ОК.
                    <p><img alt="Таблица символов окно" src="../images/insert_symbols_windows.png" /></p>
                </li>
            </ul>
            <p>В открывшемся окне <b>Таблица символов</b> выберите один из представленных <b>Набор символов</b>, их <b>Группировку</b> и <b>Шрифт</b>. Далее щелкните на нужные символы, скопируйте их в буфер обмена и вставьте в нужное место в документе.</p>
        </div>
    </body>
</html>
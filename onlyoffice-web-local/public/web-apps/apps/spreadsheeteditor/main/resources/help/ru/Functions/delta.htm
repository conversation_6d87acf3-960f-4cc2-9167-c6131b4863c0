<!DOCTYPE html>
<html>
	<head>
		<title>Функция ДЕЛЬТА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ДЕЛЬТА</h1>
			<p>Функция <b>ДЕЛЬТА</b> - это одна из инженерных функций. Используется для проверки равенства двух чисел. Функция возвращает 1, если числа равны, в противном случае возвращает 0.</p>
			<p>Синтаксис функции <b>ДЕЛЬТА</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ДЕЛЬТА(число1;[число2])</em></b></p> 
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>число1</em></b> - первое число.</p>
            <p style="text-indent: 50px;"><b><em>число2</em></b> - второе число. Это необязательный аргумент. Если он опущен, аргумент <b><em>число2</em></b> полагается равным 0.</p>
			<p>Числовые значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
      <p>Чтобы применить функцию <b>ДЕЛЬТА</b>,</p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Инженерные</b>,
        </li>
        <li>
          щелкните по функции <b>ДЕЛЬТА</b>,
          <li>введите требуемые аргументы через точку с запятой,</li>
          <li>
            нажмите клавишу <b>Enter</b>.
          </li>
        </ol>
      <p>Результат будет отображен в выделенной ячейке.</p>
			<p style="text-indent: 150px;"><div class = "smb smb-delta"></div></p>
		</div>
	</body>
</html>
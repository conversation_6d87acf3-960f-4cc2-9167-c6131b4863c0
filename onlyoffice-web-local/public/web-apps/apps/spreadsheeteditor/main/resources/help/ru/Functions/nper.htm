<!DOCTYPE html>
<html>
	<head>
		<title>Функция КПЕР</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция КПЕР</h1>
			<p>Функция <b>КПЕР</b> - это одна из финансовых функций. Вычисляет количество периодов выплаты для инвестиции исходя из заданной процентной ставки и постоянной периодичности платежей.</p>
			<p>Синтаксис функции <b>КПЕР</b>:</p> 
			<p style="text-indent: 150px;"><b><em>КПЕР(ставка;плт;пс;[бс];[тип])</em></b></p> 
			<p><em>где</em></p>
				<p style="text-indent: 50px;"><b><em>ставка</em></b> - процентная ставка.</p> 
				<p style="text-indent: 50px;"><b><em>плт</em></b> - сумма отдельного платежа.</p>
				<p style="text-indent: 50px;"><b><em>пс</em></b> - текущая сумма платежей.</p> 
				<p style="text-indent: 50px;"><b><em>бс</em></b> - будущая стоимость инвестиции. Необязательный аргумент. Если он опущен, аргументу <b><em>бс</em></b> присваивается значение 0.</p>
				<p style="text-indent: 50px;"><b><em>тип</em></b> - срок выплаты. Необязательный аргумент. Если его значение равно 0 или он опущен, предполагается, что платеж должен быть произведен в конце периода. Если значение аргумента <b><em>тип</em></b> равно 1, платеж должен быть произведен в начале периода. </p>
			<p class="note"><b>Примечание:</b> выплачиваемые денежные средства (например, сберегательные вклады) представляются отрицательными числами; получаемые денежные средства (например, дивиденды) представляются положительными числами.</p>
			<p>Эти числовые значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>КПЕР</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Финансовые</b>,</li>
			<li>щелкните по функции <b>КПЕР</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция КПЕР" src="../images/nper.png" /></p>
		</div>
	</body>
</html>
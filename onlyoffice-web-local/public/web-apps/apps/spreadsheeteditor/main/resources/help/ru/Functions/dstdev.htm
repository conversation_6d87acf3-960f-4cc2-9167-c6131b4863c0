<!DOCTYPE html>
<html>
	<head>
		<title>Функция ДСТАНДОТКЛ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ДСТАНДОТКЛ</h1>
            <p>Функция <b>ДСТАНДОТКЛ</b> - это одна из функций для работы с базами данных. Оценивает стандартное отклонение на основе выборки из генеральной совокупности, используя числа в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям.</p>
            <p>Синтаксис функции <b>ДСТАНДОТКЛ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ДСТАНДОТКЛ(база_данных;поле;условия)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>база_данных</em></b> - диапазон ячеек, составляющих базу данных. Должен содержать заголовки столбцов в первой строке.</p>
            <p style="text-indent: 50px;"><b><em>поле</em></b> - аргумент, определяющий, какое поле (то есть столбец) надо использовать. Этот аргумент можно задать как номер нужного столбца или как заголовок столбца, заключенный в кавычки.</p>
            <p style="text-indent: 50px;"><b><em>условия</em></b> - диапазон ячеек, содержащих условия. Должен содержать название хотя бы одного поля (заголовок столбца) и хотя бы одну расположенную под ним ячейку, в которой задается условие, применяемое к этому полю в базе данных. Диапазон ячеек <b><em>условия</em></b> не должен перекрывать диапазон <b><em>база_данных</em></b>.</p>
            <p>Чтобы применить функцию <b>ДСТАНДОТКЛ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Базы данных</b>,</li>
                <li>щелкните по функции <b>ДСТАНДОТКЛ</b>,</li>
                <li>введите требуемые аргументы вручную через точку с запятой или выделите диапазон ячеек мышью,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ДСТАНДОТКЛ" src="../images/dstdev.png" /></p>
        </div>
	</body>
</html>
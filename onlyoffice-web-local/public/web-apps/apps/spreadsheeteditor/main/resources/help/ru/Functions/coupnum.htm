<!DOCTYPE html>
<html>
  <head>
    <title>Функция ЧИСЛКУПОН</title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
      <script type="text/javascript" src="../callback.js"></script>
      <script type="text/javascript" src="../search/js/page-search.js"></script>
  </head>
  <body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
      <h1>Функция ЧИСЛКУПОН</h1>
      <p>
        Функция <b>ЧИСЛКУПОН</b> - это одна из финансовых функций. Используется для вычисления количества выплат процентов между датой покупки ценной бумаги и датой погашения.
      </p>
      <p>
        Синтаксис функции <b>ЧИСЛКУПОН</b>:
      </p>
      <p style="text-indent: 150px;">
        <b>
          <em>ЧИСЛКУПОН(дата_согл;дата_вступл_в_силу;частота;[базис])</em>
        </b>
      </p>
      <p>
        <em>где</em>
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>дата_согл</em>
        </b> - это дата приобретения ценной бумаги.
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>дата_вступл_в_силу</em>
        </b> - это дата истечения срока действия ценной бумаги (дата погашения).
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>частота</em>
        </b> - это количество выплат процентов за год. Возможные значения этого аргумента: 1 в случае, если проценты выплачиваются ежегодно, 2 в случае, если проценты выплачиваются каждые полгода, 4 в случае, если проценты выплачиваются ежеквартально.
      </p>
      <p style="text-indent: 50px;"><b><em>базис</em></b> - это используемый способ расчета количества дней; числовое значение от 0 до 4. Это необязательный аргумент. Он может принимать одно из следующих значений:</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Числовое значение</b></td>
                    <td><b>Способ расчета</b></td>
                </tr>
                <tr>
                    <td>0</td>
                    <td>Американский метод (NASD) 30/360</td>
                </tr>
                <tr>
                    <td>1</td>
                    <td>Фактический метод</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Фактический/360 метод</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Фактический/365 метод</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>Европейский метод 30/360</td>
                </tr>
            </table>
            <p class="note"><b>Примечание:</b> даты должны быть введены с помощью функции ДАТА.</p>
      <p>Значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
      <p>
        Чтобы применить функцию <b>ЧИСЛКУПОН</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Финансовые</b>,
        </li>
        <li>
          щелкните по функции <b>ЧИСЛКУПОН</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выделенной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ЧИСЛКУПОН" src="../images/coupnum.png" /></p>
		</div>
	</body>
</html>
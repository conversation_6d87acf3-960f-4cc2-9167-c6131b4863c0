<!DOCTYPE html>
<html>
	<head>
		<title>Функция ОКРУГЛВВЕРХ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ОКРУГЛВВЕРХ</h1>
			<p>Функция <b>ОКРУГЛВВЕРХ</b> - это одна из математических и тригонометрических функций. Округляет число в большую сторону до заданного количества десятичных разрядов.</p>
			<p>Синтаксис функции <b>ОКРУГЛВВЕРХ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ОКРУГЛВВЕРХ(x;число_разрядов)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>x</em></b> - число, которое требуется округлить в большую сторону.</p> 
			<p style="text-indent: 50px;"><b><em>число_разрядов</em></b> - количество десятичных разрядов, до которого требуется округлить число.</p> 
			<p>Эти числовые значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>ОКРУГЛВВЕРХ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>ОКРУГЛВВЕРХ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ОКРУГЛВВЕРХ" src="../images/roundup.png" /></p>
		</div>
	</body>
</html>
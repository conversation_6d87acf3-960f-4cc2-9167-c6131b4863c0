<!DOCTYPE html>
<html>
	<head>
		<title>Функция НОМНЕДЕЛИ.ISO</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция НОМНЕДЕЛИ.ISO</h1>
            <p>Функция <b>НОМНЕДЕЛИ.ISO</b> - это одна из функций даты и времени. Возвращает номер недели в году для определенной даты в соответствии со стандартами ISO (число от 1 до 54).</p>
            <p>Синтаксис функции <b>НОМНЕДЕЛИ.ISO</b>:</p>
            <p style="text-indent: 150px;"><b><em>НОМНЕДЕЛИ.ISO(дата)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>дата</em></b> - дата, для которой требуется найти номер недели в соответствии со стандартами ISO. Может быть ссылкой на ячейку, содержащей дату, или датой, возвращаемой функцией <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функцией даты и времени.</p>
            <p>Чтобы применить функцию <b>НОМНЕДЕЛИ.ISO</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Дата и время</b>,</li>
                <li>щелкните по функции <b>НОМНЕДЕЛИ.ISO</b>,</li>
                <li>введите требуемый аргумент,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция НОМНЕДЕЛИ.ISO" src="../images/isoweeknum.png" /></p>
        </div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПРПЛТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПРПЛТ</h1>
			<p>Функция <b>ПРПЛТ</b> - это одна из финансовых функций. Используется для вычисления суммы платежей по процентам для инвестиции исходя из указанной процентной ставки и постоянной периодичности платежей.</p>
			<p>Синтаксис функции <b>ПРПЛТ</b>:</p>
      <p style="text-indent: 150px;">
        <b>
          <em>ПРПЛТ(ставка;период;кпер;пс;[бс];[тип])</em>
        </b>
      </p>
      <p>
        <em>где</em>
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>ставка</em>
        </b> - это процентная ставка по инвестиции.
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>период</em>
        </b> - это период времени, за который требуется вычислить размер процентов. Этот аргумент может принимать значения от <b>
          <em>1</em>
        </b> до <b>
          <em>кпер</em>
        </b>.
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>кпер</em>
        </b> - это количество платежных периодов.
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>пс</em>
        </b> - это текущая стоимость выплат.
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>бс</em>
        </b> - это будущая стоимость (то есть денежные средства, оставшиеся после последней выплаты). Это необязательный аргумент. Если он опущен, аргумент 
        <b>
          <em>бс</em>
        </b> полагается равным 0.
      </p>               
				<p style="text-indent: 50px;"><b><em>тип</em></b> - это срок выплат. Если этот аргумент равен 0 или опущен, то 
        предполагается, что выплаты производятся в конце периода. Если аргумент <b><em>тип</em></b> равен 1, то выплаты производятся в начале периода.</p>
            <p class="note">
                <b>Примечание:</b> выплачиваемые денежные средства (например, сберегательные вклады) представляются отрицательными числами; получаемые денежные средства (например, дивиденды) представляются положительными числами.
                Единицы измерения аргументов "ставка" и "кпер" должны быть согласованы между собой: используйте N%/12 для аргумента "ставка"
                и N*12 для аргумента "кпер", если речь идет о ежемесячных платежах, N%/4 для аргумента "ставка" и N*4 для аргумента "кпер",
                если речь идет о ежеквартальных платежах, N% для аргумента "ставка" и N для аргумента "кпер", если речь идет о ежегодных платежах.
            </p>
      <p>Числовые значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
      <p>
        Чтобы применить функцию <b>ПРПЛТ</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Финансовые</b>,
        </li>
        <li>
          щелкните по функции <b>ПРПЛТ</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выделенной ячейке.</p>      
			<p style="text-indent: 150px;"><img alt="Функция ПРПЛТ" src="../images/ipmt.png" /></p>
		</div>
	</body>
</html>
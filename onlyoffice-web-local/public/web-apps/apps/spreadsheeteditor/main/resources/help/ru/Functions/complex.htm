<!DOCTYPE html>
<html>
	<head>
		<title>Функция КОМПЛЕКСН</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция КОМПЛЕКСН</h1>
			<p>Функция <b>КОМПЛЕКСН</b> - это одна из инженерных функций. Используется для преобразования действительной и мнимой части в комплексное число, выраженное в формате a + bi или a + bj.</p>
			<p>Синтаксис функции <b>КОМПЛЕКСН</b>:</p> 
			<p style="text-indent: 150px;"><b><em>КОМПЛЕКСН(действительная_часть;мнимая_часть;[мнимая_единица])</em></b></p> 
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>действительная_часть</em></b> - это действительная часть комплексного числа.</p>
            <p style="text-indent: 50px;"><b><em>мнимая_часть</em></b> - это мнимая часть комплексного числа.</p>
            <p style="text-indent: 50px;"><b><em>мнимая_единица</em></b> -  это указатель мнимой части комплексного числа. Он может быть равен либо "i", либо "j" (строчными буквами). Это необязательный аргумент. Если он опущен, то параметр <b><em>мнимая_единица</em></b> полагается равным "i".</p>
            <p>Значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>КОМПЛЕКСН</b>,</p>			
        <ol>
          <li>выделите ячейку, в которой требуется отобразить результат,</li>
          <li>
            щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
            <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
            <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
          </li>
          <li>
            выберите из списка группу функций <b>Инженерные</b>,
          </li>
          <li>
            щелкните по функции <b>КОМПЛЕКСН</b>,
          </li>
            <li>введите требуемые аргументы через точку с запятой,</li>
            <li>
              нажмите клавишу <b>Enter</b>.
            </li>
          </ol>
        <p>Результат будет отображен в выделенной ячейке.</p>        
			<p style="text-indent: 150px;"><img alt="Функция КОМПЛЕКСН" src="../images/complex.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция РИМСКОЕ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция РИМСКОЕ</h1>
			<p>Функция <b>РИМСКОЕ</b> - это одна из математических и тригонометрических функций. Преобразует число в римское.</p>
			<p>Синтаксис функции <b>РИМСКОЕ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>РИМСКОЕ(число;[форма])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>число</em></b> - числовое значение, большее или равное 1 и меньшее, чем 3999, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p> 
			<p style="text-indent: 50px;"><b><em>форма</em></b> - форма записи римского числа. Может иметь одно из следующих значений:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Значение</b></td>
					<td><b>Тип</b></td>
				</tr>
				<tr>
					<td>0</td>
					<td>Классический</td>
				</tr>
				<tr>
					<td>1</td>
					<td>Более наглядный</td>
				</tr>
				<tr>
					<td>2</td>
					<td>Более наглядный</td>
				</tr>
				<tr>
					<td>3</td>
					<td>Более наглядный</td>
				</tr>
				<tr>
					<td>4</td>
					<td>Упрощенный</td>
				</tr>
				<tr>
					<td>ИСТИНА</td>
					<td>Классический</td>
				</tr>
				<tr>
					<td>ЛОЖЬ</td>
					<td>Упрощенный</td>
				</tr>
			</table>
			<p>Чтобы применить функцию <b>РИМСКОЕ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>РИМСКОЕ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция РИМСКОЕ" src="../images/roman.png" /></p>
		</div>
	</body>
</html>
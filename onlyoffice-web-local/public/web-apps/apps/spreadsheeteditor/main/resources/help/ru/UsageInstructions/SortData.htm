<!DOCTYPE html>
<html>
	<head>
		<title>Сортировка и фильтрация данных</title>
		<meta charset="utf-8" />
		<meta name="description" content="Сортируйте данные в электронной таблице по возрастанию или по убыванию и применяйте фильтры" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Сортировка и фильтрация данных</h1>
			<h3>Сортировка данных</h3>
			<p>Данные в электронной таблице можно быстро отсортировать, используя одну из доступных опций:</p>
			<ul>
			<li><b>По возрастанию</b> используется для сортировки данных в порядке возрастания - от A до Я по алфавиту или от наименьшего значения к наибольшему для числовых данных.</li>
			<li><b>По убыванию</b> используется для сортировки данных в порядке убывания - от Я до A по алфавиту или от наибольшего значения к наименьшему для числовых данных.</li>
			</ul>
            <p class="note"><b>Примечание</b>: параметры <b>сортировки</b> доступны как на вкладке <b>Главная</b>, так и на вкладке <b>Данные</b>.</p>
			<p>Для сортировки данных:</p>
			<ol>
				<li>выделите диапазон ячеек, который требуется отсортировать (можно выделить отдельную ячейку в диапазоне, чтобы отсортировать весь диапазон),</li>
				<li>щелкните по значку <b>Сортировка по возрастанию</b> <div class = "icon icon-sortatoz"></div>, расположенному на вкладке <b>Главная</b> или <b>Данные</b> верхней панели инструментов, для сортировки данных в порядке возрастания,
					<br />ИЛИ<br />
					щелкните по значку <b>Сортировка по убыванию</b> <div class = "icon icon-sortztoa"></div>, расположенному на вкладке <b>Главная</b> или <b>Данные</b> верхней панели инструментов, для сортировки данных в порядке убывания.
				</li>
			</ol>
            <p class="note"><b>Примечание</b>: если вы выделите отдельный столбец/строку в диапазоне ячеек или часть строки/столбца, вам будет предложено выбрать, хотите ли вы расширить выделенный диапазон, чтобы включить смежные ячейки, или отсортировать только выделенные данные.</p>
			<p>Данные также можно сортировать, используя команды <b>контекстного меню</b>. Щелкните правой кнопкой мыши по выделенному диапазону ячеек, выберите в меню команду <b>Сортировка</b>, а затем выберите из подменю опцию <b>По возрастанию</b> или <b>По убыванию</b>.</p>
            <p>С помощью контекстного меню данные можно также отсортировать по цвету:</p>
            <ol>
                <li>щелкните правой кнопкой мыши по ячейке, содержащей цвет, по которому требуется отсортировать данные,</li>
                <li>выберите в меню команду <b>Сортировка</b>,</li>
                <li>
                    выберите из подменю нужную опцию:
                    <ul>
                        <li><b>Сначала ячейки с выделенным цветом</b> - чтобы отобразить записи с таким же цветом фона ячеек в верхней части столбца,</li>
                        <li><b>Сначала ячейки с выделенным шрифтом</b> - чтобы отобразить записи с таким же цветом шрифта в верхней части столбца.</li>
                    </ul>
                </li>
            </ol>
            
            <h3 id="filter">Фильтрация данных</h3>
			<p>Чтобы отобразить только те строки, которые соответствуют определенным критериям, и скрыть остальные, воспользуйтесь <b>Фильтром</b>.</p>
            <p class="note"><b>Примечание</b>: параметры <b>фильтрации</b> доступны как на вкладке <b>Главная</b>, так и на вкладке <b>Данные</b>.</p>
            <b>Чтобы включить фильтр:</b>
            <ol>
                <li>Выделите диапазон ячеек, содержащих данные, которые требуется отфильтровать (можно выделить отдельную ячейку в диапазоне, чтобы отфильтровать весь диапазон),</li>
                <li>
                    Щелкните по значку <b>Фильтр</b> <div class = "icon icon-sortandfilter"></div>, расположенному на вкладке <b>Главная</b> или <b>Данные</b> верхней панели инструментов.
                    <p>В первой ячейке каждого столбца выделенного диапазона ячеек появится кнопка со стрелкой <span class = "icon icon-dropdownarrow"></span>. Это означает, что фильтр включен.</p>
                </li>
            </ol>
            <p><b>Чтобы применить фильтр:</b></p>
            <ol>
                <li>
                    Нажмите на кнопку со стрелкой <div class = "icon icon-dropdownarrow"></div>. Откроется список команд <b>фильтра</b>:
                    <p><img alt="Окно фильтра" src="../images/filterwindow.png" /></p>
                    <p class="note"><b>Примечание</b>: можно изменить размер окна фильтра путем перетаскивания его правой границы вправо или влево, чтобы отображать данные максимально удобным образом.</p>
                </li>
                <li>
                    <p>Настройте параметры фильтра. Можно действовать одним из трех следующих способов: выбрать данные, которые надо отображать, отфильтровать данные по определенным критериям или отфильтровать данные по цвету.</p>
                    <ul>
                        <li>
                            <b>Выбор данных, которые надо отображать</b>
                            <p>Снимите флажки рядом с данными, которые требуется скрыть. Для удобства все данные в списке команд <b>фильтра</b> отсортированы в порядке возрастания.</p>
                            <p>Количество уникальных значений в отфильтрованном диапазоне отображено справа от каждого значения в окне фильтра.</p>
                            <p class="note"><b>Примечание</b>: флажок <b>{Пустые}</b> соответствует пустым ячейкам. Он доступен, если в выделенном диапазоне есть хотя бы одна пустая ячейка.</p>
                            <p>Чтобы облегчить этот процесс, используйте поле поиска. Введите в этом поле свой запрос полностью или частично <!--и нажмите клавишу Enter--> - в списке ниже будут отображены значения, содержащие эти символы. Также будут доступны следующие две опции:</p>
                            <ul>
                                <li><b>Выделить все результаты поиска</b> - выбрана по умолчанию. Позволяет выделить все значения в списке, соответствующие вашему запросу.</li>
                                <li><b>Добавить выделенный фрагмент в фильтр</b> - если установить этот флажок, выбранные значения не будут скрыты после применения фильтра.</li>
                            </ul>
                            <p>После того как вы выберете все нужные данные, нажмите кнопку <b>OK</b> в списке команд <b>фильтра</b>, чтобы применить фильтр.</p>
                        </li>
                        <li>
                            <b>Фильтрация данных по определенным критериям</b>
                            <p>В зависимости от данных, содержащихся в выбранном столбце, в правой части окна <b>фильтра</b> можно выбрать команду <b>Числовой фильтр</b> или <b>Текстовый фильтр</b>, а затем выбрать одну из опций в подменю:</p>
                            <ul>
                                <li>Для <b>Числового фильтра</b> доступны следующие опции: <em>Равно...</em>, <em>Не равно...</em>, <em>Больше...</em>, <em>Больше или равно...</em>, <em>Меньше...</em>, <em>Меньше или равно...</em>, <em>Между</em>, <em>Первые 10</em>, <em>Выше среднего</em>, <em>Ниже среднего</em>, <em>Пользовательский...</em>.</li>
                                <li>Для <b>Текстового фильтра</b> доступны следующие опции: <em>Равно...</em>, <em>Не равно...</em>, <em>Начинается с...</em>, <em>Не начинается с...</em>, <em>Оканчивается на...</em>, <em>Не оканчивается на...</em>, <em>Содержит...</em>, <em>Не содержит...</em>, <em>Пользовательский...</em>.</li>
                            </ul>
                            <p>После выбора одной из вышеуказанных опций (кроме опций <em>Первые 10</em> и <em>Выше/Ниже среднего</em>), откроется окно <b>Пользовательский фильтр</b>. В верхнем выпадающем списке будет выбран соответствующий критерий. Введите нужное значение в поле справа.</p>
                            <p>Для добавления еще одного критерия используйте переключатель <b>И</b>, если требуется, чтобы данные удовлетворяли обоим критериям, или выберите переключатель <b>Или</b>, если могут удовлетворяться один или оба критерия. Затем выберите из нижнего выпадающего списка второй критерий и введите нужное значение справа.</p>
                            <p>Нажмите кнопку <b>OK</b>, чтобы применить фильтр.</p>
                            <p><img alt="Окно пользовательского фильтра" src="../images/customfilterwindow.png" /></p>
                            <p>При выборе опции <em>Пользовательский...</em>  из списка опций <b>Числового/Текстового фильтра</b>, первое условие не выбирается автоматически, вы можете выбрать его сами.</p>
                            <p>При выборе опции <em>Первые 10</em> из списка опций <b>Числового фильтра</b>, откроется новое окно:</p>
                            <p><img alt="Окно Наложение условия по списку" src="../images/topten.png" /></p>
                            <p>В первом выпадающем списке можно выбрать, надо ли отобразить <b>Наибольшие</b> или <b>Наименьшие</b> значения. Во втором поле можно указать, сколько записей из списка или какой процент от общего количества записей требуется отобразить (можно ввести число от 1 до 500). В третьем выпадающем списке можно задать единицы измерения: <b>Элемент</b> или <b>Процент</b>. Когда нужные параметры будут заданы, нажмите кнопку <b>OK</b>, чтобы применить фильтр.</p>
                            <p>При выборе опции <em>Выше/Ниже среднего</em> из списка опций <b>Числового фильтра</b>, фильтр будет применен сразу.</p>
                        </li>
                        <li>
                            <b>Фильтрация данных по цвету</b>
                            <p>Если в диапазоне ячеек, который требуется отфильтровать, есть ячейки, которые вы отформатировали, изменив цвет их фона или шрифта (вручную или с помощью готовых стилей), можно использовать одну из следующих опций:</p>
                            <ul>
                                <li><b>Фильтр по цвету ячеек</b> - чтобы отобразить только записи с определенным цветом фона ячеек и скрыть остальные,</li>
                                <li><b>Фильтр по цвету шрифта</b> - чтобы отобразить только записи с определенным цветом шрифта в ячейках и скрыть остальные.</li>
                            </ul>
                            <p>Когда вы выберете нужную опцию, откроется палитра, содержащая цвета, использованные в выделенном диапазоне ячеек. Выберите один из цветов, чтобы применить фильтр.</p>
                            <p><img alt="Фильтрация данных по цвету" src="../images/filterbycolor.png" /></p>
                        </li>
                    </ul>
                    <p>В первой ячейке столбца появится кнопка <b>Фильтр</b> <span class="icon icon-filterbutton"></span>. Это означает, что фильтр применен. Количество отфильтрованных записей будет отображено в строке состояния  (например, <em>отфильтровано записей: 25 из 80</em>).</p>
                    <p class="note"><b>Примечание</b>: когда фильтр применен, строки, отсеянные в результате фильтрации, нельзя изменить при автозаполнении, форматировании, удалении видимого содержимого. Такие действия влияют только на видимые строки, а строки, скрытые фильтром, остаются без изменений. При копировании и вставке отфильтрованных данных можно скопировать и вставить только видимые строки. Это не эквивалентно строкам, скрытым вручную, которые затрагиваются всеми аналогичными действиями.</p>
                </li>
            </ol>
            <h3>Сортировка отфильтрованных данных</h3>
            <p>Можно задать <b>порядок сортировки</b> данных, для которых включен или применен фильтр. Нажмите на кнопку со стрелкой <span class="icon icon-dropdownarrow"></span> или кнопку <b>Фильтр</b> <span class="icon icon-filterbutton"></span> и выберите одну из опций в списке команд <b>фильтра</b>:</p>
            <ul>
                <li><b>Сортировка по возрастанию</b> - позволяет сортировать данные в порядке возрастания, отобразив в верхней части столбца наименьшее значение,</li>
                <li><b>Сортировка по убыванию</b> - позволяет сортировать данные в порядке убывания, отобразив в верхней части столбца наибольшее значение,</li>
                <li><b>Сортировка по цвету ячеек</b> - позволяет выбрать один из цветов и отобразить записи с таким же цветом фона ячеек в верхней части столбца,</li>
                <li><b>Сортировка по цвету шрифта</b> - позволяет выбрать один из цветов и отобразить записи с таким же цветом шрифта в верхней части столбца.</li>
            </ul>
            <p>Последние две команды можно использовать, если в диапазоне ячеек, который требуется отсортировать, есть ячейки, которые вы отформатировали, изменив цвет их фона или шрифта (вручную или с помощью готовых стилей).</p>
            <p>Направление сортировки будет обозначено с помощью стрелки в кнопках фильтра.</p>
            <ul>
                <li>если данные отсортированы по возрастанию, кнопка со стрелкой в первой ячейке столбца выглядит так: <div class = "icon icon-lowesttohighest"></div>, а кнопка <b>Фильтр</b> выглядит следующим образом: <div class = "icon icon-lowesttohighest1"></div>.</li>
                <li>если данные отсортированы по убыванию, кнопка со стрелкой в первой ячейке столбца выглядит так: <div class = "icon icon-highesttolowest"></div>, а кнопка <b>Фильтр</b> выглядит следующим образом: <div class = "icon icon-highesttolowest1"></div>.</li>
            </ul>
            <p>Данные можно также быстро отсортировать по цвету с помощью команд контекстного меню:</p>
            <ol>
                <li>щелкните правой кнопкой мыши по ячейке, содержащей цвет, по которому требуется отсортировать данные,</li>
                <li>выберите в меню команду <b>Сортировка</b>,</li>
                <li>
                    выберите из подменю нужную опцию:
                    <ul>
                        <li><b>Сначала ячейки с выделенным цветом</b> - чтобы отобразить записи с таким же цветом фона ячеек в верхней части столбца,</li>
                        <li><b>Сначала ячейки с выделенным шрифтом</b> - чтобы отобразить записи с таким же цветом шрифта в верхней части столбца.</li>
                    </ul>
                </li>
            </ol>
            <h3>Фильтр по содержимому выделенной ячейки</h3>
            <p>Данные можно также быстро фильтровать по содержимому выделенной ячейки с помощью команд <b>контекстного меню</b>. Щелкните правой кнопкой мыши по ячейке, выберите в меню команду <b>Фильтр</b>, а затем выберите одну из доступных опций:</p>
            <ul>
                <li><b>Фильтр по значению выбранной ячейки</b> - чтобы отобразить только записи с таким же значением, как и в выделенной ячейке.</li>
                <li><b>Фильтр по цвету ячейки</b> - чтобы отобразить только записи с таким же цветом фона ячеек, как и у выделенной ячейки.</li>
                <li><b>Фильтр по цвету шрифта</b> - чтобы отобразить только записи с таким же цветом шрифта, как и у выделенной ячейки.</li>
            </ul>						
            <h3 id="tabletemplate">Форматирование по шаблону таблицы</h3>
			<p>Чтобы облегчить работу с данными, в <b>редакторе электронных таблиц</b> предусмотрена возможность применения к выделенному диапазону ячеек шаблона таблицы с автоматическим включением фильтра. Для этого:</p>
			<ol>
				<li>выделите диапазон ячеек, которые требуется отформатировать,</li>
				<li>щелкните по значку <b>Форматировать как шаблон таблицы</b> <div class = "icon icon-tabletemplate"></div>, расположенному на вкладке <b>Главная</b> верхней панели инструментов,</li>
				<li>в галерее выберите требуемый шаблон,</li>
				<li>в открывшемся всплывающем окне проверьте диапазон ячеек, которые требуется отформатировать как таблицу,</li>
				<li>установите флажок <b>Заголовок</b>, если требуется, чтобы заголовки таблицы входили в выделенный диапазон ячеек; в противном случае строка заголовка будет добавлена наверху, в то время как выделенный диапазон ячеек сместится на одну строку вниз,</li>
				<li>нажмите кнопку <b>OK</b>, чтобы применить выбранный шаблон.</li>
			</ol>
			<p>Шаблон будет применен к выделенному диапазону ячеек, и вы сможете редактировать заголовки таблицы и применять фильтр для работы с данными. Для получения дополнительной информации о работе с форматированными таблицами, обратитесь к <a href="../UsageInstructions/FormattedTables.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</p>
            <h3>Повторное применение фильтра</h3>
            <p>Если отфильтрованные данные были изменены, можно обновить фильтр, чтобы отобразить актуальный результат:</p>
            <ol>
                <li>нажмите на кнопку <b>Фильтр</b> <div class = "icon icon-filterbutton"></div> в первой ячейке столбца, содержащего отфильтрованные данные,</li>
                <li>в открывшемся списке команд фильтра выберите опцию <b>Применить повторно</b>.</li>
            </ol>
            <p>Можно также щелкнуть правой кнопкой мыши по ячейке в столбце, содержащем отфильтрованные данные, и выбрать из контекстного меню команду <b>Применить повторно</b>.</p>
            <h3>Очистка фильтра</h3>
			<p>Для очистки фильтра:</p>
            <ol>
                <li>нажмите на кнопку <b>Фильтр</b> <div class = "icon icon-filterbutton"></div> в первой ячейке столбца, содержащего отфильтрованные данные,</li>
                <li>в открывшемся списке команд фильтра выберите опцию <b>Очистить</b>.</li>
            </ol>
            <p>Можно также поступить следующим образом:</p>
				<ol>
					<li>выделите диапазон ячеек, которые содержат отфильтрованные данные,</li>
					<li>щелкните по значку <b>Очистить фильтр</b> <div class = "icon icon-clearfilter"></div>, расположенному на вкладке <b>Главная</b> или <b>Данные</b> верхней панели инструментов.</li>
				</ol>
      <p>
        Фильтр останется включенным, но все примененные параметры фильтра будут удалены, а кнопки <b>Фильтр</b> <span class="icon icon-filterbutton"></span>
        в первых ячейках столбцов изменятся на кнопки со стрелкой <span class="icon icon-dropdownarrow"></span>.
      </p>
      <h3>Удаление фильтра</h3>
      <p>Для удаления фильтра:</p>
      <ol>
        <li>выделите диапазон ячеек, содержащих отфильтрованные данные,</li>
        <li>
          щелкните по значку <b>Фильтр</b> <div class = "icon icon-sortandfilter"></div>, расположенному на вкладке <b>Главная</b> или <b>Данные</b> верхней панели инструментов.</li>
      </ol>
      <p>Фильтр будет отключен, а кнопки со стрелкой <span class="icon icon-dropdownarrow"></span> исчезнут из первых ячеек столбцов.</p>
      <h3>Сортировка данных по нескольким столбцам/строкам</h3>
      <p>Для сортировки данных по нескольким столбцам/строкам можно создать несколько уровней сортировки, используя функцию <b>Настраиваемая сортировка</b>.</p>
            <ol>
                <li>выделите диапазон ячеек, который требуется отсортировать (можно выделить отдельную ячейку в диапазоне, чтобы отсортировать весь диапазон),</li>
                <li>щелкните по значку <b>Настраиваемая сортировка</b> <div class = "icon icon-customsort"></div>, расположенному на вкладке <b>Данные</b> верхней панели инструментов,</li>
                <li>
                    откроется окно <b>Сортировка</b>. По умолчанию включена сортировка по столбцам.
                    <p><img alt="Окно настраиваемой сортировки" src="../images/customsortwindow.png" /></p>
                    <p>Чтобы изменить ориентацию сортировки (то есть сортировать данные по строкам, а не по столбцам) нажмите кнопку <b>Параметры</b> наверху. Откроется окно <b>Параметры сортировки</b>:</p>
                    <p><img alt="Окно Параметры сортировки" src="../images/sortoptionswindow.png" /></p>
                    <ol>
                        <li>установите флажок <b>Мои данные содержат заголовки</b>, если это необходимо,</li>
                        <li>выберите нужную <b>Ориентацию</b>: <b>Сортировать сверху вниз</b>, чтобы сортировать данные по столбцам, или <b>Сортировать слева направо</b>, чтобы сортировать данные по строкам,</li>
                        <li>нажмите кнопку <b>OK</b>, чтобы применить изменения и закрыть окно.</li>
                    </ol>
                </li>
                <li>
                    задайте первый уровень сортировки в поле <b>Сортировать по</b>:
                    <p><img alt="Окно настраиваемой сортировки" src="../images/customsortwindow2.png" /></p>
                    <ul>
                        <li>в разделе <b>Столбец</b> / <b>Строка</b> выберите первый столбец / строку, который требуется отсортировать,</li>
                        <li>в списке <b>Сортировка</b> выберите одну из следующих опций: <b>Значения</b>, <b>Цвет ячейки</b> или <b>Цвет шрифта</b>,</li>
                        <li>
                            в списке <b>Порядок</b> укажите нужный порядок сортировки. Доступные параметры различаются в зависимости от опции, выбранной в списке <b>Сортировка</b>:
                            <ul>
                                <li>если выбрана опция <b>Значения</b>, выберите опцию <b>По возрастанию</b> / <b>По убыванию</b>, если диапазон ячеек содержит числовые значения, или опцию <b>От А до Я</b> / <b>От Я до А</b>, если диапазон ячеек содержит текстовые значения,</li>
                                <li>если выбрана опция <b>Цвет ячейки</b>, выберите нужный цвет ячейки и выберите опцию <b>Сверху</b> / <b>Снизу</b> для столбцов или <b>Слева</b> / <b>Справа</b> для строк,</li>
                                <li>если выбрана опция <b>Цвет шрифта</b>, выберите нужный цвет шрифта и выберите опцию <b>Сверху</b> / <b>Снизу</b> для столбцов или <b>Слева</b> / <b>Справа</b> для строк.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>добавьте следующий уровень сортировки, нажав кнопку <b>Добавить уровень</b>, выберите второй столбец / строку, который требуется отсортировать, и укажите другие параметры сортировки в поле <b>Затем по</b>, как описано выше. В случае необходимости добавьте другие уровни таким же способом.</li>
                <li>управляйте добавленными уровнями, используя кнопки в верхней части окна: <b>Удалить уровень</b>, <b>Копировать уровень</b> или измените порядок уровней, используя кнопки со стрелками <b>Переместить уровень вверх</b> / <b>Переместить уровень вниз</b>,</li>
                <li>нажмите кнопку <b>OK</b>, чтобы применить изменения и закрыть окно.</li>
            </ol>
            <p>Данные будут отсортированы в соответствии с заданными уровнями сортировки.</p>
    </div>
	</body>
</html>
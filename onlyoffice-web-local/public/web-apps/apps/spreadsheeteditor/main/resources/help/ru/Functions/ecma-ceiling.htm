<!DOCTYPE html>
<html>
	<head>
		<title>Функция ECMA.ОКРВВЕРХ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ECMA.ОКРВВЕРХ</h1>
            <p>Функция <b>ECMA.ОКРВВЕРХ</b> - это одна из математических и тригонометрических функций. Используется, чтобы округлить число в большую сторону до ближайшего числа, кратного заданной значимости. Отрицательные числа округляются в сторону нуля.</p>
            <p>Синтаксис функции <b>ECMA.ОКРВВЕРХ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ECMA.ОКРВВЕРХ(x;точность)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>x</em></b> - число, которое требуется округлить,</p>
            <p style="text-indent: 50px;"><b><em>точность</em></b> - величина, до кратного которой требуется округлить число.</p>
            <p>Эти аргументы - числовые значения, введенные вручную или находящиеся в ячейке, на которую дается ссылка.</p>
            <p>Чтобы применить функцию <b>ECMA.ОКРВВЕРХ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Математические</b>,</li>
                <li>щелкните по функции <b>ECMA.ОКРВВЕРХ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ECMA.ОКРВВЕРХ" src="../images/ecma-ceiling.png" /></p>
        </div>
	</body>
</html>
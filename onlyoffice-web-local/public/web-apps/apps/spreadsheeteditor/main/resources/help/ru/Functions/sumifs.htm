<!DOCTYPE html>
<html>
	<head>
		<title>Функция СУММЕСЛИМН</title>
		<meta charset="utf-8" />
        <meta name="description" content="Математическая и тригонометрическая функция СУММЕСЛИМН суммирует все числа в выбранном диапазоне ячеек в соответствии с условиями и возвращает результат." />
        <meta name="keywords" content="суммеслимн, эксель суммеслимн, суммеслимн в excel примеры, суммеслимн как работает, суммеслимн эксель, формула суммеслимн, формула суммеслимн в excel">
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СУММЕСЛИМН</h1>
			<p>Функция <b>СУММЕСЛИМН</b> - это одна из математических и тригонометрических функций. Суммирует все числа в выбранном диапазоне ячеек в соответствии с несколькими условиями и возвращает результат.</p>
			<p>Синтаксис функции <b>СУММЕСЛИМН</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СУММЕСЛИМН(диапазон_суммирования;диапазон_условия1;условие1;[диапазон_условия2;условие2]; ...)</em></b></p> 
			<p><em>где</em></p> 
            <p style="text-indent: 50px;"><b><em>диапазон_суммирования</em></b> - диапазон ячеек, который требуется просуммировать.</p>
			<p style="text-indent: 50px;"><b><em>диапазон_условия1</em></b> - первый выбранный диапазон ячеек, к которому применяется <em>условие1</em>.</p>
			<p style="text-indent: 50px;"><b><em>условие1</em></b> - первое условие, которое должно выполняться. Оно применяется к <em>диапазону_условия1</em> и определяет, какие ячейки в <em>диапазоне_суммирования</em> требуется просуммировать. Это значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
            <p style="text-indent: 50px;"><b><em>диапазон_условия2, условие2 ...</em></b> - дополнительные диапазоны ячеек и соответствующие условия. Это необязательные аргументы.</p>
            <p class="note"><b>Примечание:</b> при указании условий можно использовать подстановочные знаки. Вопросительный знак "?" может замещать любой отдельный символ, а звездочку "*" можно использовать вместо любого количества символов.</p>
			<h2>Как работает функция СУММЕСЛИМН</h2>
            <p>Чтобы применить функцию <b>СУММЕСЛИМН</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>СУММЕСЛИМН</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СУММЕСЛИМН" src="../images/sumifs.png" /></p>
		</div>
	</body>
</html>
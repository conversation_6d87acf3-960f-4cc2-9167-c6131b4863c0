<!DOCTYPE html>
<html>
	<head>
		<title>Функция ТТЕСТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ТТЕСТ</h1>
            <p>Функция <b>ТТЕСТ</b> - это одна из статистических функций. Возвращает вероятность, соответствующую критерию Стьюдента. Функция <b>ТТЕСТ</b> позволяет определить вероятность того, что две выборки взяты из генеральных совокупностей, которые имеют одно и то же среднее.</p>
            <p>Синтаксис функции <b>ТТЕСТ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ТТЕСТ(массив1;массив2;хвосты;тип)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>массив1</em></b> - первый диапазон числовых значений.</p>
            <p style="text-indent: 50px;"><b><em>массив2</em></b> - второй диапазон числовых значений.</p>
            <p style="text-indent: 50px;"><b><em>хвосты</em></b> - число хвостов распределения. Если значение этого аргумента равно 1, используется одностороннее распределение. Если значение этого аргумента равно 2, используется двустороннее распределение.</p>
            <p style="text-indent: 50px;"><b><em>тип</em></b> - числовое значение, определяющее вид выполняемого t-теста. Может использоваться одно из следующих значений:</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Числовое значение</b></td>
                    <td><b>Вид t-теста</b></td>
                </tr>
                <tr>
                    <td>1</td>
                    <td>Парный</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Двухвыборочный с равными дисперсиями (гомоскедастический)</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Двухвыборочный с неравными дисперсиями (гетероскедастический)</td>
                </tr>
            </table>
            <p>Эти значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
            <p>Чтобы применить функцию <b>ТТЕСТ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>ТТЕСТ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой или выделите мышью диапазон ячеек,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ТТЕСТ" src="../images/ttest.png" /></p>
        </div>
	</body>
</html>
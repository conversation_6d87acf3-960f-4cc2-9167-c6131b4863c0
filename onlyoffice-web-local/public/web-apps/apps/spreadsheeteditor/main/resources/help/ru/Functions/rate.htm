<!DOCTYPE html>
<html>
	<head>
		<title>Функция СТАВКА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СТАВКА</h1>
			<p>Функция <b>СТАВКА</b> - это одна из финансовых функций. Используется для вычисления размера процентной ставки по инвестиции исходя из постоянной периодичности платежей.</p>
			<p>Синтаксис функции <b>СТАВКА</b>:</p>
      <p style="text-indent: 150px;">
        <b>
          <em>СТАВКА(кпер;плт;пс;[бс];[тип];[прогноз])</em>
        </b>
      </p>
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>кпер</em></b> - это количество платежных периодов.</p>
      <p style="text-indent: 50px;"><b><em>плт</em></b> - это сумма отдельного платежа.</p>
      <p style="text-indent: 50px;"><b><em>пс</em></b> - это текущая стоимость выплат.</p>
			<p style="text-indent: 50px;"><b><em>бс</em></b> - это значение будущей стоимости, то есть средства, оставшиеся после последней выплаты. Это необязательный аргумент. Если он опущен, аргумент <b><em>бс</em></b> полагается равным 0.</p>
      <p style="text-indent: 50px;">
        <b>
          <em>тип</em>
        </b> - это срок выплат. Это необязательный аргумент. Если он равен 0 или опущен, предполагается, что платеж должен быть произведен в конце периода. Если аргумент <b><em>тип</em></b> равен 1, платеж должен быть произведен в начале периода.</p>
      <p style="text-indent: 50px;">
        <b>
          <em>прогноз</em>
        </b> - это приблизительная оценка будущей ставки. Это необязательный аргумент. Если он опущен, аргумент <b>
          <em>прогноз</em>
        </b> полагается равным 10%.
      </p>
            <p class="note">
                <b>Примечание:</b> выплачиваемые денежные средства (например, сберегательные вклады) представляются отрицательными числами; получаемые денежные средства (например, дивиденды) представляются положительными числами.
                Единицы измерения аргументов "прогноз" и "кпер" должны быть согласованы между собой: используйте N%/12 для аргумента "прогноз"
                и N*12 для аргумента "кпер", если речь идет о ежемесячных платежах, N%/4 для аргумента "прогноз" и N*4 для аргумента "кпер",
                если речь идет о ежеквартальных платежах, N% для аргумента "прогноз" и N для аргумента "кпер", если речь идет о ежегодных платежах.
            </p>
			<p>Числовые значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>СТАВКА</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Финансовые</b>,</li>
			<li>щелкните по функции <b>СТАВКА</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СТАВКА" src="../images/rate.png" /></p>
		</div>
	</body>
</html>
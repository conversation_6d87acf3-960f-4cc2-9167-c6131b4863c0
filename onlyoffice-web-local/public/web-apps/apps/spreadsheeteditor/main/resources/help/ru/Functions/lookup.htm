<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПРОСМОТР</title>
		<meta charset="utf-8" />
        <meta name="description" content="Функция ПРОСМОТР - это одна из поисковых функций. Узнайте, как использовать формулу ПРОСМОТР в таблицах Excel и совместимых файлах с помощью ONLYOFFICE." />
        <meta name="keywords" content="функция просмотр в excel, просмотр excel, просмотр функция, excel функция просмотр">
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПРОСМОТР</h1>
			<p>Функция <b>ПРОСМОТР</b> - это одна из поисковых функций. Она возвращает значение из выбранного диапазона (строки или столбца с данными, отсортированными в порядке возрастания).</p>
			<p>Синтаксис функции <b>ПРОСМОТР</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ПРОСМОТР(искомое_значение;просматриваемый_вектор;[вектор_результатов])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>искомое_значение</em></b> - искомое значение,</p>
			<p style="text-indent: 50px;"><b><em>просматриваемый_вектор</em></b> - одна строка или столбец с данными, отсортированными в порядке возрастания,</p>
			<p style="text-indent: 50px;"><b><em>вектор_результатов</em></b> - одна строка или столбец с данными. Должен иметь такой же размер, что и <b><em>просматриваемый_вектор</em></b>.</p>
			<p>Функция выполняет поиск <b><em>искомого значения</em></b> в <b><em>векторе поиска</em></b> и возвращает значение, находящееся в той же самой позиции в <b><em>векторе результатов</em></b>.</p>
			<p class="note"><b>Примечание</b>: если <b>искомое значение</b> меньше, чем все значения в <b>просматриваемом векторе</b>, функция возвращает ошибку <b>#Н/Д</b>. Если значение, строго соответствующее <b>искомому значению</b>, отсутствует, то функция выбирает в <b>просматриваемом векторе</b> наибольшее значение, которое меньше искомого значения или равно ему.</p>
			<h2>Как работает функция ПРОСМОТР</h2>
            <p>Чтобы применить функцию <b>ПРОСМОТР</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
			<li>щелкните по функции <b>ПРОСМОТР</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><img alt="Функция ПРОСМОТР" src="../images/lookup.gif" /></p>
		</div>
	</body>
</html>
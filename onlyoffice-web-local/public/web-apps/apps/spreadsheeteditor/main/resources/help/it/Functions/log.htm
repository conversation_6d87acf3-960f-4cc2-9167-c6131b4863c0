﻿<!DOCTYPE html>
<html>
	<head>
		<title>Funzione LOG</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
	</head>
	<body>
		<div class="mainpart">
			<h1>Funzione LOG</h1>
			<p>La funzione <b>LOG</b> è una delle funzioni matematiche e trigonometriche. Si usa per restituire il logaritmo di un numero in una base specificata.</p>
			<p>La sintassi della funzione <b>LOG</b> è:</p> 
			<p style="text-indent: 150px;"><b><em>LOG(number [,base])</em></b></p>
			<p><em>dove</em></p> 
			<p style="text-indent: 50px;"><b><em>number</em></b> è un valore numerico maggiore di 0</p>
			<p style="text-indent: 50px;"><b><em>base</em></b> è la base usata per calcolare il logaritmo di un numero. è un parametro opzionle. Se non è specificato, la funzione usa la <b><em>base</em></b> di 10.</p>
			<p>I valori numerici possono essere inseriti a mano o inclusi nelle celle alle quali fate riferimento.</p>
			<p>Per applicare la funzione <b>LOG</b>,</p>
			<ol>
			<li>selezionate la cella dove desiderate visualizzare il risultato,</li>
			<li>cliccate sull'icona <b>Inserisci funzione</b> <div class = "icon icon-insertfunction"></div> sulla barra degli strumenti superiore,
				<br />o cliccate con il tasto destro del mouse sulla cella scelta e selezionate l'opzione <b>Inserisci funzione</b> dal menu contestuale,
				<br />o cliccate sull'icona <div class = "icon icon-function"></div> prima della barra della formula,
			</li>
			<li>selezionate il gruppo di funzioni <b>Matematiche e trigonametriche</b> dall'elenco,</li>
			<li>cliccate sulla funzione <b>LOG</b>,</li>
			<li>inserite gli argomenti richiesti separati da virgola,</li>
			<li>premete il tasto <b>Enter</b>.</li>
			</ol>
			<p>Il risultato sarà visualizzato nella cella scelta.</p>
			<p style="text-indent: 150px;"><img alt="Funzione LOG" src="../images/log.png" /></p>
		</div>
	</body>
</html>
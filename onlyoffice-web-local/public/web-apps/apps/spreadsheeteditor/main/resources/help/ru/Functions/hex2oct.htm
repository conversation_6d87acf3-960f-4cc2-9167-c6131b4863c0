<!DOCTYPE html>
<html>
	<head>
		<title>Функция ШЕСТН.В.ВОСЬМ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ШЕСТН.В.ВОСЬМ</h1>
			<p>Функция <b>ШЕСТН.В.ВОСЬМ</b> - это одна из инженерных функций. Преобразует шестнадцатеричное число в восьмеричное.</p>
			<p>Синтаксис функции <b>ШЕСТН.В.ВОСЬМ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ШЕСТН.В.ВОСЬМ(число;[разрядность])</em></b></p>
			<p><em>где</em></p>  
			    <p style="text-indent: 50px;"><b><em>число</em></b> -  это шестнадцатеричное число, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p> 
				<p style="text-indent: 50px;"><b><em>разрядность</em></b> - это количество отображаемых разрядов. Если этот аргумент опущен, используется минимальное количество разрядов.</p> 
			<p class="note"><b>Примечание:</b> если аргумент не распознан как шестнадцатеричное число, содержит более 10 символов, возвращаемое восьмеричное число требует больше разрядов, чем вы указали, или указанное значение аргумента <b><em>разрядность</em></b> меньше или равно 0, то функция возвращает ошибку <b>#ЧИСЛО!</b>.</p>
			<p>Чтобы применить функцию <b>ШЕСТН.В.ВОСЬМ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
        <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
        <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
      </li>
			<li>выберите из списка группу функций <b>Инженерные</b>,</li>
			<li>щелкните по функции <b>ШЕСТН.В.ВОСЬМ</b>,
      <li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
      </ol>
  <p>Результат будет отображен в выделенной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ШЕСТН.В.ВОСЬМ" src="../images/hex2oct.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция РУБЛЬ.ДЕС</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция РУБЛЬ.ДЕС</h1>
			<p>Функция <b>РУБЛЬ.ДЕС</b> - это одна из финансовых функций. Преобразует цену в рублях, представленную в виде дроби, в цену в рублях, выраженную десятичным числом.</p>
			<p>Синтаксис функции <b>РУБЛЬ.ДЕС</b>:</p> 
			<p style="text-indent: 150px;"><b><em>РУБЛЬ.ДЕС(дроб_руб;дроб)</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>дроб_руб</em></b> - это целая и дробная части, разделенные десятичным разделителем.</p> 
				<p style="text-indent: 50px;"><b><em>дроб</em></b> - это целое число, которое вы хотите использовать в качестве знаменателя для дробной части значения <em>дроб_руб</em>.</p> 
            <p class="note"><b>Примечание:</b> например, значение аргумента <em>дроб_руб</em>, выраженное в виде <b>1.03</b>, интерпретируется как
            <b>1 + 3/n</b>, где <b>n</b> - это значение аргумента <em>дроб</em>.</p>
      <p>Числовые значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
      <p>
        Чтобы применить функцию <b>РУБЛЬ.ДЕС</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Финансовые</b>,
        </li>
        <li>
          щелкните по функции <b>РУБЛЬ.ДЕС</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выделенной ячейке.</p>      
			<p style="text-indent: 150px;"><img alt="Функция РУБЛЬ.ДЕС" src="../images/dollarde.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ГАММАНЛОГ.ТОЧН</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ГАММАНЛОГ.ТОЧН</h1>
			<p>Функция <b>ГАММАНЛОГ.ТОЧН</b> - это одна из статистических функций. Возвращает натуральный логарифм гамма-функции.</p>
			<p>Синтаксис функции <b>ГАММАНЛОГ.ТОЧН</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ГАММАНЛОГ.ТОЧН(x)</em></b></p> 
			<p>где <b><em>x</em></b> - числовое значение больше 0, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>ГАММАНЛОГ.ТОЧН</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>ГАММАНЛОГ.ТОЧН</b>,</li>
			<li>введите требуемый аргумент,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ГАММАНЛОГ.ТОЧН" src="../images/gammaln-precise.png" /></p>
		</div>
	</body>
</html>
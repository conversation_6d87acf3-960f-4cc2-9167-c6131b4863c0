<!DOCTYPE html>
<html>
	<head>
		<title>Функция ГАММАРАСП</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ГАММАРАСП</h1>
			<p>Функция <b>ГАММАРАСП</b> - это одна из статистических функций. Возвращает гамма-распределение.</p>
			<p>Синтаксис функции <b>ГАММАРАСП</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ГАММАРАСП(x;альфа;бета;интегральная)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>x</em></b> - значение, для которого вычисляется функция. Числовое значение больше 0.</p>
			<p style="text-indent: 50px;"><b><em>альфа</em></b> - первый параметр распределения; числовое значение больше 0.</p>
			<p style="text-indent: 50px;"><b><em>бета</em></b> - второй параметр распределения; числовое значение больше 0. Если аргумент <b><em>бета</em></b> равен 1, функция ГАММАРАСП возвращает стандартное гамма-распределение.</p>
			<p style="text-indent: 50px;"><b><em>интегральная</em></b> - логическое значение (ИСТИНА или ЛОЖЬ), определяющее форму функции. Если этот аргумент имеет значение ИСТИНА, функция ГАММАРАСП возвращает интегральную функцию распределения. Если этот аргумент имеет значение ЛОЖЬ, функция ГАММАРАСП возвращает функцию плотности распределения.</p>
            <p>Эти аргументы можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
 			<p>Чтобы применить функцию <b>ГАММАРАСП</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>ГАММАРАСП</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ГАММАРАСП" src="../images/gammadist.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция СОВПАД</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СОВПАД</h1>
			<p>Функция <b>СОВПАД</b> - это одна из функций для работы с текстом и данными. Она используется для сравнения данных в двух ячейках. Функция возвращает значение ИСТИНА, если данные совпадают, и ЛОЖЬ, если нет.</p>
			<p>Синтаксис функции <b>СОВПАД</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СОВПАД(текст1;текст2)</em></b></p> 
			<p>где <b><em>текст1(2)</em></b> - это данные, введенные вручную или находящиеся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>СОВПАД</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>СОВПАД</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,
			<p class="note"><b>Примечание</b>: функция СОВПАД <b>учитывает регистр</b>.</p>
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><em>Например:</em></p>
			<p>Здесь два аргумента: <em>текст1</em> = <b>A1</b>; <em>текст2</em> = <b>B1</b>, где <b>A1</b> имеет значение "<b>Мой Пароль</b>", <b>B1</b> имеет значение "<b>мой пароль</b>". Следовательно, функция возвращает значение <b>ЛОЖЬ</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция СОВПАД: ЛОЖЬ" src="../images/exactfalse.png" /></p>
			<p>Если изменить данные в ячейке <b>A1</b>, преобразовав все заглавные буквы в строчные, функция возвращает значение <b>ИСТИНА</b>:</p>
			<p style="text-indent: 150px;"><img alt="Функция СОВПАД: ИСТИНА" src="../images/exacttrue.png" /></p>
		</div>
	</body>
</html>
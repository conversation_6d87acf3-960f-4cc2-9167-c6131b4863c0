<!DOCTYPE html>
<html>
	<head>
		<title>Функция СУММКВРАЗН</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СУММКВРАЗН</h1>
			<p>Функция <b>СУММКВРАЗН</b> - это одна из математических и тригонометрических функций. Возвращает сумму квадратов разностей соответствующих элементов в двух массивах.</p>
			<p>Синтаксис функции <b>СУММКВРАЗН</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СУММКВРАЗН(массив-1;массив-2)</em></b></p> 
			<p>где <b><em>массив-1</em></b> и <b><em>массив-2</em></b> - выбранные диапазоны ячеек с одинаковым количеством столбцов и строк.</p>
			<p>Чтобы применить функцию <b>СУММКВРАЗН</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>СУММКВРАЗН</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция SUMXMY2" src="../images/sumxmy2.png" /></p>
		</div>
	</body>
</html>
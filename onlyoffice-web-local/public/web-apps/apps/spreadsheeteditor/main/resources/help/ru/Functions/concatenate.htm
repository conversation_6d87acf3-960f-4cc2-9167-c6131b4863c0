<!DOCTYPE html>
<html>
	<head>
		<title>Функция СЦЕПИТЬ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СЦЕПИТЬ</h1>
			<p>Функция <b>СЦЕПИТЬ</b> - это одна из функций для работы с текстом и данными. Она используется для объединения данных из двух или более ячеек в одну.</p>
			<p>Синтаксис функции <b>СЦЕПИТЬ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СЦЕПИТЬ(текст1;текст2; ...)</em></b></p> 
			<p>где <b><em>текст1(2)</em></b> - это до 265 значений данных, введенных вручную или находящихся в ячейках, на которые даются ссылки.</p>
			<p>Чтобы применить функцию <b>СЦЕПИТЬ</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>СЦЕПИТЬ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><em>Например:</em></p>
			<p>Здесь три аргумента: <em>текст1</em> = <b>A1</b> (Елена), <em>текст2</em> = <b>" "</b> (пробел), <em>текст3</em> = <b>B1</b> (Петрова). Следовательно, функция объединит имя и фамилию в одной ячейке и вернет результат <b>Елена Петрова</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция СЦЕПИТЬ" src="../images/concatenate.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЛИСТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ЛИСТ</h1>
            <p>Функция <b>ЛИСТ</b> - это одна из информационных функций. Возвращает номер листа, на который имеется ссылка.</p>
            <p>Синтаксис функции <b>ЛИСТ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ЛИСТ(значение)</em></b></p>
            <p>где <b><em>значение</em></b> - это название листа или ссылка, для которой необходимо установить номер листа. Если аргумент <b><em>значение</em></b> опущен, возвращается номер текущего листа.</p>
            <p>Чтобы применить функцию <b>ЛИСТ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Информационные</b>,</li>
                <li>щелкните по функции <b>ЛИСТ</b>,</li>
                <li>введите требуемый аргумент,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ЛИСТ" src="../images/sheet.png" /></p>
        </div>
	</body>
</html>
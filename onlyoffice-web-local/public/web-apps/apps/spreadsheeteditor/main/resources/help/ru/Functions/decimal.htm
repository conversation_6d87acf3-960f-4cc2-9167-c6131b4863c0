<!DOCTYPE html>
<html>
	<head>
		<title>Функция ДЕС</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ДЕС</h1>
			<p>Функция <b>ДЕС</b> - это одна из математических и тригонометрических функций. Преобразует текстовое представление числа с указанным основанием в десятичное число.</p>
			<p>Синтаксис функции <b>ДЕС</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ДЕС(текст;основание)</em></b></p> 
			<p><em>где</em></p> 
            <p style="text-indent: 50px;"><b><em>текст</em></b> - текстовое представление числа, которое требуется преобразовать. Длина строки не должна превышать 255 символов.</p>
            <p style="text-indent: 50px;"><b><em>основание</em></b> -  основание системы счисления, в которой представлено число. Целое число, большее или равное 2 и меньшее или равное 36.</p>
            <p>Эти числовые значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>ДЕС</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Математические</b>,</li>
                <li>щелкните по функции <b>ДЕС</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ДЕС" src="../images/decimal.png" /></p>
		</div>
	</body>
</html>
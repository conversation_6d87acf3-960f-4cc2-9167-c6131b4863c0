<!DOCTYPE html>
<html>
	<head>
		<title>Функция ДДОБ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ДДОБ</h1>
            <p>Функция <b>ДДОБ</b> - это одна из финансовых функций. Используется для вычисления величины амортизации имущества за указанный отчетный период методом двойного убывающего остатка.</p>
            <p>Синтаксис функции <b>ДДОБ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ДДОБ(нач_стоимость;ост_стоимость;время_эксплуатации;период;[коэффициент])</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>нач_стоимость</em></b> - это стоимость имушества.</p>
            <p style="text-indent: 50px;"><b><em>ост_стоимость</em></b> - это остаточная стоимость имущества в конце срока службы.</p>
            <p style="text-indent: 50px;"><b><em>время_эксплуатации</em></b> - это общее количество периодов в течение срока службы имущества.</p>
            <p style="text-indent: 50px;"><b><em>период</em></b> - это период, за который вы хотите вычислить величину амортизации. Значение этого аргумента должно быть выражено в тех же единицах измерения, что и значение аргумента <em>время_эксплуатации</em>.</p>
            <p style="text-indent: 50px;"><b><em>коэффициент</em></b> - это коэффициент уменьшения размера амортизации. Это необязательный аргумент. Если он опущен, то аргумент <b><em>коэффициент</em></b> полагается равным 2.</p>
          <p class="note"><b>Примечание:</b> все значения должны быть заданы положительными числами.</p>
          <p>Значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
          <p>
            Чтобы применить функцию <b>ДДОБ</b>,
          </p>
          <ol>
            <li>выделите ячейку, в которой требуется отобразить результат,</li>
            <li>
              щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
              <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
              <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
            </li>
            <li>
              выберите из списка группу функций <b>Финансовые</b>,
            </li>
            <li>
              щелкните по функции <b>ДДОБ</b>,
            </li>
            <li>введите требуемые аргументы через точку с запятой,</li>
            <li>
              нажмите клавишу <b>Enter</b>.
            </li>
          </ol>
          <p>Результат будет отображен в выделенной ячейке.</p>          
          <p style="text-indent: 150px;"><img alt="Функция ДДОБ" src="../images/ddb.png" /></p>
        </div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция МОПРЕД</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция МОПРЕД</h1>
			<p>Функция <b>МОПРЕД</b> - это одна из математических и тригонометрических функций. Возвращает определитель матрицы (матрица хранится в массиве).</p>
			<p>Синтаксис функции <b>МОПРЕД</b>:</p> 
			<p style="text-indent: 150px;"><b><em>МОПРЕД(массив)</em></b></p> 
			<p>где <b><em>массив</em></b> - массив чисел.</p>
			<p class="note"><b>Примечание</b>: в том случае, если любая из ячеек массива содержит пустое или нечисловое значение, функция возвращает ошибку <b>#Н/Д</b>.<br />
				Если количество строк в массиве не равно количеству столбцов, функция возвращает ошибку <b>#ЗНАЧ!</b>.</p>
			<p>Чтобы применить функцию <b>МОПРЕД</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>МОПРЕД</b>,</li>
			<li>выделите мышью диапазон ячеек или введите требуемый аргумент вручную следующим образом A1:B2,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция МОПРЕД" src="../images/mdeterm.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ОКРВНИЗ.ТОЧН</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ОКРВНИЗ.ТОЧН</h1>
			<p>Функция <b>ОКРВНИЗ.ТОЧН</b> - это одна из математических и тригонометрических функций. Возвращает число, округленное с недостатком до ближайшего целого или до ближайшего числа, кратного заданной значимости. Число всегда округляется с недостатком вне зависимости от его знака.</p>
			<p>Синтаксис функции <b>ОКРВНИЗ.ТОЧН</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ОКРВНИЗ.ТОЧН(x;[точность])</em></b></p> 
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>x</em></b> - число, которое требуется округлить в меньшую сторону.</p>
			<p style="text-indent: 50px;"><b><em>точность</em></b> - величина, до кратного которой требуется округлить число. Необязательный параметр. Если он опущен, используется значение по умолчанию, равное 1. Если он равен нулю, функция возвращает 0.</p>
            <p>Эти числовые значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>ОКРВНИЗ.ТОЧН</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Математические</b>,</li>
                <li>щелкните по функции <b>ОКРВНИЗ.ТОЧН</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ОКРВНИЗ.ТОЧН" src="../images/floorprecise.png" /></p>
		</div>
	</body>
</html>
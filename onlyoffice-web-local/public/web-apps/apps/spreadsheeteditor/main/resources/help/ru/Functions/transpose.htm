<!DOCTYPE html>
<html>
	<head>
		<title>Функция ТРАНСП</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ТРАНСП</h1>
			<p>Функция <b>ТРАНСП</b> - это одна из поисковых функций. <!--Преобразует столбцы в строки, а строки - в столбцы--> Возвращает первый элемент массива.</p>
			<p>Синтаксис функции <b>ТРАНСП</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ТРАНСП(массив)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><em><b>массив</b></em> - это ссылка на диапазон ячеек.</p>
      <p>
        Чтобы применить функцию <b>ТРАНСП</b>,
      </p>
      <ol>
        <li>выделите <!--диапазон ячеек--> ячейку, в которой требуется отобразить результат,</li>
        <!--<p class="note"> количество строк в выделенном диапазоне должно совпадать с количеством столбцов в исходном диапазоне, 
        а количество столбцов в выделенном диапазоне должно совпадать с количеством строк в исходном диапазоне.</p>-->
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Поиск и ссылки</b>,
        </li>
        <li>
          щелкните по функции <b>ТРАНСП</b>,</li>
          <li>
            выделите мышью диапазон ячеек или введите его вручную, например A1:B2,
            </li>
          <li>
            нажмите <!--сочетание клавиш <b>Ctrl+Shift+Enter</b>-->клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выбранном диапазоне ячеек.</p>
			<p style="text-indent: 150px;"><img alt="Функция ТРАНСП" src="../images/transpose.png" /></p>
		</div>
	</body>
</html>
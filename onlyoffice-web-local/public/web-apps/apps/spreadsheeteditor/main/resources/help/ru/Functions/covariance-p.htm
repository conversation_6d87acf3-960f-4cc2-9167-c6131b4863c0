<!DOCTYPE html>
<html>
	<head>
		<title>Функция КОВАРИАЦИЯ.Г</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция КОВАРИАЦИЯ.Г</h1>
            <p>Функция <b>КОВАРИАЦИЯ.Г</b> - это одна из статистических функций. Возвращает ковариацию совокупности, то есть среднее произведений отклонений для каждой пары точек в двух наборах данных. Ковариация используется для определения связи между двумя наборами данных.</p>
            <p>Синтаксис функции <b>КОВАРИАЦИЯ.Г</b>:</p>
            <p style="text-indent: 150px;"><b><em>КОВАРИАЦИЯ.Г(массив1;массив2)</em></b></p>
            <p>где <b><em>массив1(2)</em></b> - выбранные диапазоны ячеек, содержащие одно и то же количество значений.</p>
            <p class="note"><b>Примечание:</b> если аргумент <b><em>массив1(2)</em></b> содержит текст, логические значения или пустые ячейки, эти значения игнорируются, но ячейки с нулевыми значениями учитываются.</p>
            <p>Чтобы применить функцию <b>КОВАРИАЦИЯ.Г</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>КОВАРИАЦИЯ.Г</b>,</li>
                <li>введите требуемые аргументы вручную через точку с запятой или выделите диапазон ячеек мышью,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция КОВАРИАЦИЯ.Г" src="../images/covariance-p.png" /></p>
        </div>
	</body>
</html>
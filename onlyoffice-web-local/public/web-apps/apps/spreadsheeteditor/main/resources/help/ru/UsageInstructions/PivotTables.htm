<!DOCTYPE html>
<html>
	<head>
		<title>Создание и редактирование сводных таблиц</title>
		<meta charset="utf-8" />
        <meta name="description" content="Создавайте и редактируйте сводные таблицы. Применяйте фильтры, меняйте оформление сводных таблиц в электронной таблице." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Создание и редактирование сводных таблиц</h1>
            <p>Сводные таблицы позволяют группировать и систематизировать данные из больших наборов данных для получения сводной информации. Вы можете упорядочивать данные множеством разных способов, чтобы отображать только нужную информацию и сфокусироваться на важных аспектах.</p>
            <h3>Создание новой сводной таблицы</h3>
            <p>Для создания сводной таблицы:</p>
            <ol>
                <li>Подготовьте исходный набор данных, который требуется использовать для создания сводной таблицы. Он должен включать заголовки столбцов. Набор данных не должен содержать пустых строк или столбцов.</li>
                <li>Выделите любую ячейку в исходном диапазоне данных.</li>
                <li>
                    Перейдите на вкладку <b>Сводная таблица</b> верхней панели инструментов и нажмите на кнопку <b>Вставить таблицу</b> <div class = "icon icon-insert_pivot"></div>.
                    <p>Если вы хотите создать сводную таблицу на базе <a href="FormattedTables.htm" onclick="onhyperlinkclick(this)">форматированной таблицы</a>, также можно использовать опцию <span class="icon icon-insertpivot"></span> <b>Вставить сводную таблицу</b> на вкладке <b>Параметры таблицы</b> правой боковой панели.</p>
                </li>
                <li>
                    Откроется окно <b>Создать сводную таблицу</b>.
                    <p><img alt="Окно Создать сводную таблицу" src="../images/create_pivot.png" /></p>
                    <ul>
                        <li>
                            <b>Диапазон исходных данных</b> уже указан. В этом случае будут использоваться все данные из исходного диапазона. Если вы хотите изменить диапазон данных (например, включить только часть исходных данных), нажмите на кнопку <div class = "icon icon-changerange"></div>. В окне <b>Выбор диапазона данных</b> введите нужный диапазон данных в формате <em>Лист1!$A$1:$E$10</em>. Вы также можете выделить нужный диапазон данных на листе с помощью мыши. Когда все будет готово, нажмите кнопку <b>OK</b>.
                        </li>
                        <li>
                            Укажите, где требуется разместить сводную таблицу.
                            <ul>
                                <li>Опция <b>Новый лист</b> выбрана по умолчанию. Она позволяет разместить сводную таблицу на новом рабочем листе.</li>
                                <li>
                                    Также можно выбрать опцию <b>Существующий лист</b> и затем выбрать определенную ячейку. В этом случае выбранная ячейка будет правой верхней ячейкой созданной сводной таблицы. Чтобы выбрать ячейку, нажмите на кнопку <div class = "icon icon-changerange"></div>.
                                    <p><img alt="Окно Выбор диапазона данных" src="../images/pivot_selectdata2.png" /></p>
                                    <p>В окне <b>Выбор диапазона данных</b> введите адрес ячейки в формате <em>Лист1!$G$2</em>. Также можно щелкнуть по нужной ячейке на листе. Когда все будет готово, нажмите кнопку <b>OK</b>. </p>
                                </li>
                            </ul>
                        </li>
                        <li>Когда местоположение таблицы будет выбрано, нажмите кнопку <b>OK</b> в окне <b>Создать таблицу</b>.</li>
                    </ul>
                </li>
            </ol>
            <p>Пустая сводная таблица будет вставлена в выбранном местоположении.</p>
            <p>Откроется вкладка <b>Параметры сводной таблицы</b> на правой боковой панели. Эту вкладку можно скрыть или показать, нажав на значок <span class="icon icon-pivot_settings"></span>.</p>
            <p><img alt="Вкладка Параметры сводной таблицы" src="../images/right_pivot.png" /></p>

            <h4>Выбор полей для отображения</h4>
            <p>Раздел <b>Выбрать поля</b> содержит названия полей, соответствующие заголовкам столбцов в исходном наборе данных. Каждое поле содержит значения из соответствующего столбца исходной таблицы. Ниже доступны следующие четыре поля: <b>Фильтры</b>, <b>Столбцы</b>, <b>Строки</b> и <b>Значения</b>.</p>
            <p>Отметьте галочками поля, которые требуется отобразить в сводной таблице. Когда вы отметите поле, оно будет добавлено в один из доступных разделов на правой боковой панели в зависимости от типа данных и будет отображено в сводной таблице. Поля, содержащие текстовые значения, будут добавлены в раздел <b>Строки</b>; поля, содержащие числовые значения, будут добавлены в раздел  <b>Значения</b>.</p>
            <p>Вы можете просто перетаскивать поля в нужный раздел, а также перетаскивать поля между разделами, чтобы быстро перестроить сводную таблицу. Чтобы удалить поле из текущего раздела, перетащите его за пределы этого раздела.</p>
            <p>Чтобы добавить поле в нужный раздел, также можно нажать на черную стрелку справа от поля в разделе <b>Выбрать поля</b> и выбрать нужную опцию из меню: <b>Добавить в фильтры</b>, <b>Добавить в строки</b>, <b>Добавить в столбцы</b>, <b>Добавить в значения</b>.</p>
            <p><img alt="Вкладка Параметры сводной таблицы" src="../images/pivot_selectfields.png" /></p>
            <p>Ниже приводятся примеры использования разделов <b>Фильтры</b>, <b>Столбцы</b>, <b>Строки</b> и <b>Значения</b>.</p>
            <ul>
                <li>
                    При добавлении поля в раздел <b>Фильтры</b> над сводной таблицей будет добавлен отдельный фильтр. Он будет применен ко всей сводной таблице. Если нажать на кнопку со стрелкой <div class = "icon icon-dropdownarrow"></div> в добавленном фильтре, вы увидите значения из выбранного поля. Если снять галочки с некоторых значений в окне фильтра и нажать кнопку <b>OK</b>, значения, с которых снято выделение, не будут отображаться в сводной таблице.
                    <p><img alt="Фильтры сводной таблицы" src="../images/pivot_filter_field.png" /></p>
                </li>
                <li>
                    При добавлении поля в раздел <b>Столбцы</b>, сводная таблица будет содержать столько же столбцов, сколько значений содержится в выбранном поле. Также будет добавлен столбец <b>Общий итог</b>.
                    <p><img alt="Столбцы сводной таблицы" src="../images/pivot_columns.png" /></p>
                </li>
                <li>
                    При добавлении поля в раздел <b>Строки</b>, сводная таблица будет содержать столько же строк, сколько значений содержится в выбранном поле. Также будет добавлена строка <b>Общий итог</b>.
                    <p><img alt="Строки сводной таблицы" src="../images/pivot_rows.png" /></p>
                </li>
                <li>
                    При добавлении поля в раздел <b>Значения</b> в сводной таблице будет отображаться суммирующее значение для всех числовых значений из выбранных полей. Если поле содержит текстовые значения, будет отображаться количество значений. Функцию, которая используется для вычисления суммирующего значения, можно изменить в настройках поля.
                    <p><img alt="Значения сводной таблицы" src="../images/pivot_values.png" /></p>
                </li>
            </ul>

            <h4>Упорядочивание полей и изменение их свойств</h4>
            <p>Когда поля будут добавлены в нужные разделы, ими можно управлять, чтобы изменить макет и формат сводной таблицы. Нажмите на черную стрелку справа от поля в разделе <b>Фильтры</b>, <b>Столбцы</b>, <b>Строки</b> или <b>Значения</b>, чтобы открыть контекстное меню поля.</p>
            <p><img alt="Меню сводной таблицы" src="../images/pivot_menu.png" /></p>
            <p>С его помощью можно:</p>
            <ul>
                <li><b>Переместить</b> выбранное поле <b>Вверх</b>, <b>Вниз</b>, <b>В начало</b> или <b>В конец</b> текущего раздела, если в текущий раздел добавлено несколько полей.</li>
                <li><b>Переместить</b> выбранное поле в другой раздел - в <b>Фильтры</b>, <b>Столбцы</b>, <b>Строки</b> или <b>Значения</b>. Опция, соответствующая текущему разделу, будет неактивна.</li>
                <li><b>Удалить</b> выбранное поле из текущего раздела.</li>
                <li>Изменить <b>параметры</b> выбранного поля.</li>
            </ul>
            <p><b>Параметры полей</b> из раздела <b>Фильтры</b>, <b>Столбцы</b> и <b>Строки</b> выглядят одинаково:</p>
            <p><img alt="Настройки поля из раздела Фильтры" src="../images/pivot_filter_field_layout.png" /></p>
            <p>На вкладке <b>Макет</b> содержатся следующие опции:</p>
            <ul>
                <li>Опция <b>Имя источника</b> позволяет посмотреть имя поля, соответствующее заголовку столбца из исходного набора данных.</li>
                <li>Опция <b>Пользовательское имя</b> позволяет изменить имя выбранного поля, отображаемое в сводной таблице.</li>
                <li>
                    В разделе <b>Форма отчета</b> можно изменить способ отображения выбранного поля в сводной таблице:
                    <ul>
                        <li>
                            Выберите нужный макет для выбранного поля в сводной таблице:
                            <ul>
                                <li>
                                    В форме <b>В виде таблицы</b> отображается один столбец для каждого поля и выделяется место для заголовков полей.
                                    <!--<p><img alt="Pivot table Tabular form" src="../images/pivot_tabular.png" /></p>-->
                                </li>
                                <li>
                                    В форме <b>Структуры</b> отображается один столбец для каждого поля и выделяется место для заголовков полей. В ней также можно отображать промежуточные итоги над каждой группой.
                                    <!--<p><img alt="Pivot table Outline form" src="../images/pivot_outline.png" /></p>-->
                                </li>
                                <li>
                                    В <b>Компактной</b> форме элементы из разных полей раздела строк отображаются в одном столбце.
                                    <!--<p><img alt="Pivot table Compact form" src="../images/pivot_compact.png" /></p>-->
                                </li>
                            </ul>
                        </li>
                        <li>Опция <b>Повторять метки элементов в каждой строке</b> позволяет визуально группировать строки или столбцы при наличии нескольких полей в табличной форме.</li>
                        <li>Опция <b>Добавлять пустую строку после каждой записи</b> позволяет добавлять пустые строки после элементов выбранного поля.</li>
                        <li>Опция <b>Показывать промежуточные итоги</b> позволяет выбрать, надо ли отображать промежуточные итоги для выбранного поля. Можно выбрать одну из опций: <em>Показывать в заголовке группы</em> или <em>Показывать в нижней части группы</em>.</li>
                        <li>Опция <b>Показывать элементы без данных</b> позволяет показать или скрыть пустые элементы в выбранном поле.</li>
                    </ul>
                </li>
            </ul>
            <p><img alt="Настройки поля из раздела Фильтры" src="../images/pivot_filter_field_subtotals.png" /></p>
            <p>На вкладке <b>Промежуточные итоги</b> можно выбрать <b>Функции для промежуточных итогов</b>. Отметьте галочкой нужную функцию в списке: <em>Сумма</em>, <em>Количество</em>, <em>Среднее</em>, <em>Макс</em>, <em>Мин</em>, <em>Произведение</em>, <em>Количество чисел</em>, <em>Стандотклон</em>, <em>Стандотклонп</em>, <em>Дисп</em>, <em>Диспр</em>.</p>
            <p><b>Параметры поля значений</b></p>
            <p><img alt="Настройки поля из раздела Значения" src="../images/pivot_values_field_settings.png" /></p>
            <ul>
                <li>Опция <b>Имя источника</b> позволяет посмотреть имя поля, соответствующее заголовку столбца из исходного набора данных.</li>
                <li>Опция <b>Пользовательское имя</b> позволяет изменить имя выбранного поля, отображаемое в сводной таблице.</li>
                <li>В списке <b>Операция</b> можно выбрать функцию, используемую для вычисления суммирующего значения всех значений из этого поля. По умолчанию для числовых значений используется функция <em>Сумма</em>, а для текстовых значений - функция <em>Количество</em>. Доступны следующие функции: <em>Сумма</em>, <em>Количество</em>, <em>Среднее</em>, <em>Макс</em>, <em>Мин</em>, <em>Произведение</em>.</li>
            </ul>

            <h3>Группировка и разгруппировка данных</h3>
            <p>Данные в сводных таблицах можно сгруппировать в соответствии с индивидуальными требованиями. Группировка может быть выполнена по датам и основным числам.</p>
            <h4>Группировка дат</h4>
            <p>Чтобы сгруппировать даты, создайте сводную таблицу, содержащую необходимые даты. Щелкните правой кнопкой мыши по любой ячейке в сводной таблице с датой, в контекстном меню выберите параметр <b>Сгруппировать</b> и установите необходимые параметры в открывшемся окне.</p>
            <p><img alt="Группировка дат" src="../images/group_dates.png" /></p>
            <ul>
                <li><b>Начиная с</b> - по умолчанию выбирается первая дата в исходных данных. Чтобы ее изменить, введите в это поле нужную дату. Отключите это поле, чтобы игнорировать начальную точку.</li>
                <li><b>Заканчивая в</b> -  по умолчанию выбирается последняя дата в исходных данных. Чтобы ее изменить, введите в это поле нужную дату. Отключите это поле, чтобы игнорировать конечную точку.</li>
                <li><b>По</b> - параметры <b>Секунды</b>, <b>Минуты</b> и <b>Часы</b> группируют данные в соответствии со временем, указанным в исходных данных. Параметр <b>Месяцы</b> исключает дни и оставляет только месяцы. Опция <b>Кварталы</b> работает при следующем условии: четыре месяца составляют квартал <em>Кв1</em>, <em>Кв2</em> и т.д. опция <b>Годы</b> группирует даты по годам, указанным в исходных данных. Комбинируйте варианты, чтобы добиться желаемого результата.</li>
                <li><b>Количество дней</b> - устанавливает необходимое значение для определения диапазона дат.</li>
                <li>По завершении нажмите <b>ОК</b>.</li>
            </ul>
            <h4>Группировка чисел</h4>
            <p>Чтобы сгруппировать числа, создайте сводную таблицу, включающую набор необходимых чисел. Щелкните правой кнопкой мыши любую ячейку в сводной таблице с номером, в контекстном меню выберите опцию <b>Сгруппировать</b> и установите необходимые параметры в открывшемся окне.</p>
            <p><img alt="Группировка чисел" src="../images/group_numbers.png" /></p>
            <ul>
                <li><b>Начиная с</b> - по умолчанию выбирается наименьшее число в исходных данных. Чтобы изменить его, введите в это поле нужное число. Отключите это поле, чтобы игнорировать наименьшее число.</li>
                <li><b>Заканчивая в</b> - по умолчанию выбирается наибольшее число в исходных данных. Чтобы изменить его, введите в это поле нужный номер. Отключите это поле, чтобы игнорировать наибольшее число.</li>
                <li><b>По</b> - установить необходимый интервал для группировки номеров. Например, <em>«2»</em> сгруппирует набор чисел от 1 до 10 как и <em>«1-2»</em>, <em>«3-4»</em> и т.д.</li>
                <li>По завершении нажмите <b>ОК</b>.</li>
            </ul>
            <h4>Разгруппировка данных</h4>
            <p>Чтобы разгруппировать ранее сгруппированные данные,</p>
            <ol>
                <li>щелкните правой кнопкой мыши любую ячейку в группе,</li>
                <li>выберите опцию <b>Разгруппировать</b> в контекстном меню.</li>
            </ol>

            <h3>Изменение оформления сводных таблиц</h3>
            <p>Опции, доступные на верхней панели инструментов, позволяют изменить способ отображения сводной таблицы. Эти параметры применяются ко всей сводной таблице.</p>
            <p>Чтобы активировать инструменты редактирования на верхней панели инструментов, выделите мышью хотя бы одну ячейку в сводной таблице.</p>
            <p><img alt="Верхняя панель инструментов сводной таблицы" src="../images/pivot_top.png" /></p>
            <ul>
                <li>
                    В выпадающем списке <b>Макет отчета</b> можно выбрать нужный макет для сводной таблицы:
                    <ul>
                        <li>
                            <em>Показать в сжатой форме</em> - позволяет отображать элементы из разных полей раздела строк в одном столбце.
                            <p><img alt="Сжатая форма сводной таблицы" src="../images/pivot_compact.png" /></p>
                        </li>
                        <li>
                            <em>Показать в форме структуры</em> - позволяет отображать сводную таблицу в классическом стиле. В этой форме отображается один столбец для каждого поля и выделяется место для заголовков полей. В ней также можно отображать промежуточные итоги над каждой группой.
                            <p><img alt="Сводная таблица в форме структуры" src="../images/pivot_outline.png" /></p>
                        </li>
                        <li>
                            <em>Показать в табличной форме</em> - позволяет отображать сводную таблицу в традиционном табличном формате. В этой форме отображается один столбец для каждого поля и выделяется место для заголовков полей.
                            <p><img alt="Сводная таблица в табличной форме" src="../images/pivot_tabular.png" /></p>
                        </li>
                        <li><em>Повторять все метки элементов</em> - позволяет визуально группировать строки или столбцы при наличии нескольких полей в табличной форме.</li>
                        <li><em>Не повторять все метки элементов</em> - позволяет скрыть метки элементов при наличии нескольких полей в табличной форме.</li>
                    </ul>
                </li>
                <li>
                    В выпадающем списке <b>Пустые строки</b> можно выбрать, надо ли отображать пустые строки после элементов:
                    <ul>
                        <li><em>Вставлять пустую строку после каждого элемента</em> - позволяет добавить пустые строки после элементов.</li>
                        <li><em>Удалить пустую строку после каждого элемента</em> - позволяет убрать добавленные пустые строки.</li>
                    </ul>
                </li>
                <li>
                    В выпадающем списке <b>Промежуточные итоги</b> можно выбрать, надо ли отображать промежуточные итоги в сводной таблице:
                    <ul>
                        <li><em>Не показывать промежуточные итоги</em> - позволяет скрыть промежуточные итоги для всех элементов.</li>
                        <li><em>Показывать все промежуточные итоги в нижней части группы</em> - позволяет отобразить промежуточные итоги под строками, для которых производится промежуточное суммирование.</li>
                        <li><em>Показывать все промежуточные итоги в верхней части группы</em> - позволяет отобразить промежуточные итоги над строками, для которых производится промежуточное суммирование.</li>
                    </ul>
                </li>
                <li>
                    В выпадающем списке <b>Общие итоги</b> можно выбрать, надо ли отображать общие итоги в сводной таблице:
                    <ul>
                        <li><em>Отключить для строк и столбцов</em> - позволяет скрыть общие итоги как для строк, так и для столбцов.</li>
                        <li><em>Включить для строк и столбцов</em> - позволяет отобразить общие итоги как для строк, так и для столбцов.</li>
                        <li><em>Включить только для строк</em> - позволяет отобразить общие итоги только для строк.</li>
                        <li><em>Включить только для столбцов</em> - позволяет отобразить общие итоги только для столбцов.</li>
                    </ul>
                    <p class="note"><b>Примечание</b>: аналогичные настройки также доступны в окне дополнительных параметров сводной таблицы в разделе <b>Общие итоги</b> вкладки <b>Название и макет</b>.</p>
                </li>
            </ul>
            <p>Кнопка <span class="icon icon-pivotselecticon"></span> <b>Выделить</b> позволяет выделить всю сводную таблицу.</p>
            <p>Если вы изменили данные в исходном наборе данных, выделите сводную таблицу и нажмите кнопку <span class="icon icon-pivot_refresh"></span> <b>Обновить</b>, чтобы обновить сводную таблицу.</p>

            <h3>Изменение стиля сводных таблиц</h3>
            <p>Вы можете изменить оформление сводных таблиц в электронной таблице с помощью инструментов редактирования стиля, доступных на верхней панели инструментов.</p>
            <p>Чтобы активировать инструменты редактирования на верхней панели инструментов, выделите мышью хотя бы одну ячейку в сводной таблице.</p>
            <p><img alt="Вкладка Сводная таблица" src="../images/pivottoptoolbar.png" /></p>
            <p>Параметры строк и столбцов позволяют выделить некоторые строки или столбцы при помощи особого форматирования, или выделить разные строки и столбцы с помощью разных цветов фона для их четкого разграничения. Доступны следующие опции:</p>
            <ul>
                <li><b>Заголовки строк</b> - позволяет выделить заголовки строк при помощи особого форматирования.</li>
                <li><b>Заголовки столбцов</b> - позволяет выделить заголовки столбцов при помощи особого форматирования.</li>
                <li><b>Чередовать строки</b> - включает чередование цвета фона для четных и нечетных строк.</li>
                <li><b>Чередовать столбцы</b> - включает чередование цвета фона для четных и нечетных столбцов.</li>
            </ul>
            <p>
                Список шаблонов позволяет выбрать один из готовых стилей сводных таблиц. Каждый шаблон сочетает в себе определенные параметры форматирования, такие как цвет фона, стиль границ, чередование строк или столбцов и т.д.
                Набор шаблонов отображается по-разному в зависимости от параметров, выбранных для строк и столбцов. Например, если вы отметили опции <b>Заголовки строк</b> и <b>Чередовать столбцы</b>, отображаемый список шаблонов будет содержать только шаблоны с выделенными заголовками строк и включенным чередованием столбцов.
            </p>
            <h3>Фильтрация, сортировка и создание срезов в сводных таблицах</h3>
            <p>Вы можете фильтровать сводные таблицы по подписям или значениям и использовать дополнительные параметры сортировки.</p>
            <h4>Фильтрация</h4>
            <p>Нажмите на кнопку со стрелкой <span class="icon icon-dropdownarrow"></span> в <b>Названиях строк</b> или <b>Названиях столбцов</b> сводной таблицы. Откроется список команд <b>фильтра</b>:</p>
            <p><img alt="Окно фильтра" src="../images/pivot_filterwindow.png" /></p>
            <p>Настройте параметры фильтра. Можно действовать одним из следующих способов: выбрать данные, которые надо отображать, или отфильтровать данные по определенным критериям.</p>
            <ul>
                <li>
                    <b>Выбор данных, которые надо отображать</b>
                    <p>Снимите флажки рядом с данными, которые требуется скрыть. Для удобства все данные в списке команд <b>фильтра</b> отсортированы в порядке возрастания.</p>
                    <p class="note"><b>Примечание</b>: флажок <b>(пусто)</b> соответствует пустым ячейкам. Он доступен, если в выделенном диапазоне есть хотя бы одна пустая ячейка.</p>
                    <p>Чтобы облегчить этот процесс, используйте поле поиска. Введите в этом поле свой запрос полностью или частично - в списке ниже будут отображены значения, содержащие эти символы. Также будут доступны следующие две опции:</p>
                    <ul>
                        <li><b>Выделить все результаты поиска</b> - выбрана по умолчанию. Позволяет выделить все значения в списке, соответствующие вашему запросу.</li>
                        <li><b>Добавить выделенный фрагмент в фильтр</b> - если установить этот флажок, выбранные значения не будут скрыты после применения фильтра.</li>
                    </ul>
                    <p>После того как вы выберете все нужные данные, нажмите кнопку <b>OK</b> в списке команд <b>фильтра</b>, чтобы применить фильтр.</p>
                </li>
                <li>
                    <b>Фильтрация данных по определенным критериям</b>
                    <p>В правой части окна <b>фильтра</b> можно выбрать команду <b>Фильтр подписей</b> или <b>Фильтр значений</b>, а затем выбрать одну из опций в подменю:</p>
                    <ul>
                        <li>
                            Для <b>Фильтра подписей</b> доступны следующие опции:
                            <ul>
                                <li>Для текстовых значений: <em>Равно...</em>, <em>Не равно...</em>, <em>Начинается с...</em>, <em>Не начинается с...</em>, <em>Оканчивается на...</em>, <em>Не оканчивается на...</em>, <em>Содержит...</em>, <em>Не содержит...</em>.</li>
                                <li>Для числовых значений: <em>Больше...</em>, <em>Больше или равно...</em>, <em>Меньше...</em>, <em>Меньше или равно...</em>, <em>Между</em>, <em>Не между</em>.</li>
                            </ul>
                        </li>
                        <li>Для <b>Фильтра значений</b> доступны следующие опции: <em>Равно...</em>, <em>Не равно...</em>, <em>Больше...</em>, <em>Больше или равно...</em>, <em>Меньше...</em>, <em>Меньше или равно...</em>, <em>Между</em>, <em>Не между</em>, <em>Первые 10</em>.</li>
                    </ul>
                    <p>После выбора одной из вышеуказанных опций (кроме опций <em>Первые 10</em>), откроется окно <b>Фильтра подписей/Значений</b>. В первом и втором выпадающих списках будут выбраны соответствующее поле и критерий. Введите нужное значение в поле справа.</p>
                    <p>Нажмите кнопку <b>OK</b>, чтобы применить фильтр.</p>
                    <p><img alt="Окно Фильтра значений" src="../images/pivot_filter.png" /></p>
                    <p>При выборе опции <em>Первые 10</em> из списка опций <b>Фильтра значений</b> откроется новое окно:</p>
                    <p><img alt="Окно Наложение условия по списку" src="../images/pivot_topten.png" /></p>
                    <p>В первом выпадающем списке можно выбрать, надо ли отобразить <b>Наибольшие</b> или <b>Наименьшие</b> значения. Во втором поле можно указать, сколько записей из списка или какой процент от общего количества записей требуется отобразить (можно ввести число от 1 до 500). В третьем выпадающем списке можно задать единицы измерения: <b>Элемент</b> или <b>Процент</b>. В четвертом выпадающем списке отображается имя выбранного поля. Когда нужные параметры будут заданы, нажмите кнопку <b>OK</b>, чтобы применить фильтр.</p>
                </li>
            </ul>
            <p>Кнопка <b>Фильтр</b> <span class="icon icon-filterbutton"></span> появится в <b>Названиях строк</b> или <b>Названиях столбцов</b> сводной таблицы. Это означает, что фильтр применен. <!--The number of filtered records will be displayed at the status bar (e.g. <em>25 of 80 records filtered</em>).--></p>

            <h4>Сортировка</h4>
            <p>Данные сводной таблицы можно сортировать, используя параметры <b>сортировки</b>. Нажмите на кнопку со стрелкой <span class="icon icon-dropdownarrow"></span> в <b>Названиях строк</b> или <b>Названиях столбцов</b> сводной таблицы и выберите опцию <b>Сортировка по возрастанию</b> или <b>Сортировка по убыванию</b> в подменю.</p>
            <p>Опция <b>Дополнительные параметры сортировки...</b> позволяет открыть окно <b>Сортировать</b>, в котором можно выбрать нужный порядок сортировки - <em>По возрастанию (от А до Я)</em> или <em>По убыванию (от Я до А)</em> - а затем выбрать определенное поле, которое требуется отсортировать.</p>
            <p><img alt="Параметры сортировки сводной таблицы" src="../images/pivot_sort.png" /></p>

            <h4>Создание срезов</h4>
            <p>Чтобы упростить фильтрацию данных и отображать только то, что необходимо, вы можете добавить срезы. Чтобы узнать больше о срезах, пожалуйста, обратитесь к <a href="Slicers.htm" onclick="onhyperlinkclick(this)">руководству по созданию срезов</a>.</p>

            <h3>Изменение дополнительных параметров сводной таблицы</h3>
            <p id="pivot_advanced">Чтобы изменить дополнительные параметры сводной таблицы, нажмите ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно 'Сводная таблица - Дополнительные параметры':</p>
            <p><img alt="Дополнительные параметры сводной таблицы" src="../images/pivot_advanced.png" /></p>
            <p>На вкладке <b>Название и макет</b> можно изменить общие свойства сводной таблицы.</p>
            <ul>
                <li>С помощью опции <b>Название</b> можно изменить название сводной таблицы.</li>
                <li>
                    В разделе <b>Общие итоги</b> можно выбрать, надо ли отображать общие итоги в сводной таблице. Опции <em>Показывать для строк</em> и <em>Показывать для столбцов</em> отмечены по умолчанию. Вы можете снять галочку или с одной из них, или с них обеих, чтобы скрыть соответствующие общие итоги из сводной таблицы.
                    <p class="note"><b>Примечание</b>: аналогичные настройки также доступны на верхней панели инструментов в меню <b>Общие итоги</b>.</p>
                </li>
                <li>
                    В разделе <b>Отображать поля в области фильтра отчета</b> можно настроить фильтры отчета, которые появляются при добавлении полей в раздел <b>Фильтры</b>:
                    <ul>
                        <li>Опция <em>Вниз, затем вправо</em> используется для организации столбцов. Она позволяет отображать фильтры отчета по столбцам.</li>
                        <li>Опция <em>Вправо, затем вниз</em> используется для организации строк. Она позволяет отображать фильтры отчета по строкам.</li>
                        <li>Опция <em>Число полей фильтра отчета в столбце</em> позволяет выбрать количество фильтров для отображения в каждом столбце. По умолчанию задано значение <em>0</em>. Вы можете выбрать нужное числовое значение.</li>
                    </ul>
                </li>
                <li>Опция <b>Показывать заголовки полей для строк и столбцов</b> позволяет выбрать, надо ли отображать заголовки полей в сводной таблице. Эта опция выбрана по умолчанию. Снимите с нее галочку, если хотите скрыть заголовки полей из сводной таблицы.</li>
                <li>Опция <b>Автоматически изменять ширину столбцов при обновлении</b> позволяет включить/отключить автоматическую корректировку ширины столбцов. Эта опция выбрана по умолчанию.</li>
            </ul>
            <p><img alt="Дополнительные параметры сводной таблицы" src="../images/pivot_advanced2.png" /></p>
            <p>На вкладке <b>Источник данных</b> можно изменить данные, которые требуется использовать для создания сводной таблицы.</p>
            <p>Проверьте выбранный <b>Диапазон данных</b> и измените его в случае необходимости. Для этого нажмите на кнопку <span class="icon icon-changerange"></span>. </p>
            <p><img alt="Окно Выбор диапазона данных" src="../images/pivot_selectdata.png" /></p>
            <p>В окне <b>Выбор диапазона данных</b> введите нужный диапазон данных в формате <em>Лист1!$A$1:$E$10</em>. Также можно выбрать нужный диапазон ячеек на рабочем листе с помощью мыши. Когда все будет готово, нажмите кнопку <b>OK</b>.</p>
            <p><img alt="Дополнительные параметры сводной таблицы" src="../images/pivot_advanced3.png" /></p>
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит сводная таблица.</p>
            <h3>Удаление сводной таблицы</h3>
            <p>Для удаления сводной таблицы:</p>
            <ol>
                <li>Выделите всю сводную таблицу с помощью кнопки <div class = "icon icon-pivotselecticon"></div> <b>Выделить</b> на верхней панели инструментов.</li>
                <li>Нажмите клавишу <b>Delete</b>.</li>
            </ol>
        </div>
	</body>
</html>
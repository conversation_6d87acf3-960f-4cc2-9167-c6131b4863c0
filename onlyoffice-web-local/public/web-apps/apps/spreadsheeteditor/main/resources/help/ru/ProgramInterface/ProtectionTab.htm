<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Защита</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора электронных таблиц - Вкладка Защита" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вкладка Защита</h1>
            <p>Вкладка <b>Защита</b> в <a href="https://www.onlyoffice.com/ru/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редакторе электронных таблиц</b></a> позволяет предотвратить несанкционированный доступ путем шифрования и защиты рабочей книги или листов.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора электронных таблиц:</p>
                <p><img alt="Вкладка Защита" src="../images/interface/protectiontab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора электронных таблиц:</p>
                <p><img alt="Вкладка Защита" src="../images/interface/desktop_protectiontab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li><a href="../UsageInstructions/Password.htm" onclick="onhyperlinkclick(this)">Зашифровывать</a> электронную таблицу при помощи пароля,</li>
                <li><a href="../UsageInstructions/ProtectWorkbook.htm" onclick="onhyperlinkclick(this)">Защищать структуру книги</a> с помощью пароля или без него,</li>
                <li><a href="../UsageInstructions/ProtectSheet.htm" onclick="onhyperlinkclick(this)">Защищать лист</a>, ограничив возможности редактирования листа с помощью пароля или без него,</li>
                <li><a href="../UsageInstructions/AllowEditRanges.htm" onclick="onhyperlinkclick(this)">Разрешать редактировать диапазоны</a> заблокированных ячеек с паролем или без него,</li>
                <li>Включать и отключать следующие <a href="../UsageInstructions/ProtectSpreadsheet.htm" onclick="onhyperlinkclick(this)">опции</a>: <b>Заблокированная ячейка</b>, <b>Скрытые формулы</b>, <b>Заблокированная фигура</b>, <b>Заблокировать текст</b>.</li>
            </ul>
		</div>
	</body>
</html>

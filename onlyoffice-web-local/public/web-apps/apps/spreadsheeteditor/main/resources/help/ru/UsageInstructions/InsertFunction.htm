<!DOCTYPE html>
<html>
	<head>
		<title>Вставка функций</title>
		<meta charset="utf-8" />
		<meta name="description" content="Вставьте функцию для выполнения основных вычислений" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Вставка функций</h1>
			<p>Важная причина использования электронных таблиц - это возможность выполнять основные расчеты. Некоторые из них выполняются автоматически при выделении диапазона ячеек на рабочем листе:</p>
      <ul>
        <li>
          <b>Среднее</b> - используется для того, чтобы проанализировать выбранный диапазон ячеек и рассчитать среднее значение.
        </li>
        <li>
          <b>Количество</b> - используется для того, чтобы подсчитать количество выбранных ячеек, содержащих значения, без учета пустых ячеек.
        </li>
          <li><b>Мин</b> - используется для того, чтобы проанализировать выбранный диапазон ячеек и найти наименьшее число.</li>
          <li><b>Макс</b> - используется для того, чтобы проанализировать выбранный диапазон ячеек и найти наибольшее число.</li>
        <li>
          <b>Сумма</b> - используется для того, чтобы сложить все числа в выбранном диапазоне без учета пустых или содержащих текст ячеек.          
        </li>
      </ul>
      <p>Результаты этих расчетов отображаются в правом нижнем углу строки состояния. Вы можете управлять строкой состояния, щелкнув по ней правой кнопкой мыши и выбрав только те функции, которые требуется отображать.</p>
      <p>
        <img alt="Основные расчеты" src="../images/basiccalculations.png" />
      </p>
      <p>
        Для выполнения любых других расчетов можно ввести нужную формулу вручную, используя общепринятые математические операторы, или вставить заранее определенную формулу - <b>Функцию</b>.
      </p>
      <p>Возможности работы с <b>Функциями</b> доступны как на вкладке <b>Главная</b>, так и на вкладке <b>Формула</b>. Также можно использовать сочетание клавиш <em>Shift+F3</em>. На вкладке <b>Главная</b>, вы можете использовать кнопку <b>Вставить функцию</b> <span class="icon icon-insertfunction"></span>, чтобы добавить одну из часто используемых функций (СУММ, СРЗНАЧ, МИН, МАКС, СЧЁТ) или открыть окно <b>Вставить функцию</b>, содержащее все доступные функции, распределенные по категориям. Используйте поле поиска, чтобы найти нужную функцию по имени.</p>
            <p><img alt="Вставка функций" src="../images/insertfunctionwindow.png" /></p>
            <p>На вкладке <b>Формула</b> можно использовать следующие кнопки:</p>
            <p><img alt="Вкладка Формула" src="../images/interface/formulatab.png" /></p>
            <ul>
                <li><b>Функция</b> - чтобы открыть окно <b>Вставить функцию</b>, содержащее все доступные функции, распределенные по категориям.</li>
                <li><b>Автосумма</b> - чтобы быстро получить доступ к функциям СУММ, МИН, МАКС, СЧЁТ. При выборе функции из этой группы она автоматически выполняет вычисления для всех ячеек в столбце, расположенных выше выделенной ячейки, поэтому вам не потребуется вводить аргументы.</li>
                <li><b>Последние использованные</b> - чтобы быстро получить доступ к 10 последним использованным функциям.</li>
                <li><b>Финансовые</b>, <b>Логические</b>, <b>Текст и данные</b>, <b>Дата и время</b>, <b>Поиск и ссылки</b>, <b>Математические</b> - чтобы быстро получить доступ к функциям, относящимся к определенной категории.</li>
                <li><b>Другие функции</b> - чтобы получить доступ к функциям из следующих групп: <b>Базы данных</b>, <b>Инженерные</b>, <b>Информационные</b> и <b>Статистические</b>.</li>
                <li><b>Именованные диапазоны</b> - чтобы открыть <b>Диспетчер имен</b>, или присвоить новое имя, или вставить имя в качестве аргумента функции. Для получения дополнительной информации обратитесь к <a href="UseNamedRanges.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</li>
                <li><b>Пересчет</b> - чтобы принудительно выполнить пересчет функций.</li>
            </ul>
            
      <p id="insertfunction">Для вставки функции:</p>
			<ol>
				<li>Выделите ячейку, в которую требуется вставить функцию.</li>
                <li>Действуйте одним из следующих способов:
                    <ul>
                        <li>перейдите на вкладку <b>Формула</b> и используйте кнопки на верхней панели инструментов, чтобы получить доступ к функциям из определенной группы, затем щелкните по нужной функции, чтобы открыть окно <b>Аргументы функции</b>. Также можно выбрать в меню опцию <b>Дополнительно</b> или нажать кнопку <div class = "icon icon-functionicon"></div> <b>Функция</b> на верхней панели инструментов, чтобы открыть окно <b>Вставить функцию</b>.</li>
                        <li>перейдите на вкладку <b>Главная</b>, щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, выберите одну из часто используемых функций (СУММ, СРЗНАЧ, МИН, МАКС, СЧЁТ) или выберите опцию <b>Дополнительно</b>, чтобы открыть окно <b>Вставить функцию</b>.</li>
                        <li>щелкните правой кнопкой мыши по выделенной ячейке и выберите в контекстном меню команду <b>Вставить функцию</b>.</li>
                        <li>щелкните по значку <div class = "icon icon-function"></div> перед строкой формул.</li>
                    </ul>
                </li>
				<li>В открывшемся окне <b>Вставить функцию</b> введите имя функции в поле поиска или выберите нужную группу функций, а затем выберите из списка требуемую функцию и нажмите <b>OK</b>.
                    <p>Когда вы выберете нужную функцию, откроется окно <b>Аргументы функции</b>:</p>
                    <p><img alt="Аргументы функции" src="../images/functionarguments.png" /></p>
                </li>
                <li>В открывшемся окне <b>Аргументы функции</b> введите нужные значения для каждого аргумента.
                    <p>Аргументы функции можно вводить вручную или нажав на кнопку <span class="icon icon-changerange"></span> и выбрав ячейку или диапазон ячеек, который надо добавить в качестве аргумента.</p>
                    <p class="note"><b>Примечание</b>: в общих случаях, в качестве аргументов функций можно использовать числовые значения, логические значения (ИСТИНА, ЛОЖЬ), текстовые значения (они должны быть заключены в кавычки), ссылки на ячейки, ссылки на диапазоны ячеек, <a href="UseNamedRanges.htm#usingnames" onclick="onhyperlinkclick(this)">имена, присвоенные диапазонам,</a> и другие функции.</p>
                    <p>Результат функции будет отображен ниже.</p>
                </li>
                <li>Когда все аргументы будут указаны, нажмите кнопку <b>OK</b> в окне <b>Аргументы функции</b>.</li>
			</ol>
            <p>Чтобы ввести функцию вручную с помощью клавиатуры,</p>
            <ol>
                <li>Выделите ячейку.</li>
                <li>
                    Введите знак "равно" (=).
                    <p>Каждая формула должна начинаться со знака "равно" (=).</p>
                </li>
                <li>
                    Введите имя функции.
                    <p>Как только вы введете начальные буквы, появится список <b>Автозавершения формул</b>. По мере ввода в нем отображаются элементы (формулы и имена), которые соответствуют введенным символам. При наведении курсора на формулу отображается всплывающая подсказка с ее описанием. Можно выбрать нужную формулу из списка и вставить ее, щелкнув по ней или нажав клавишу <b>Tab</b>.</p>
                </li>
                <li>
                    Введите аргументы функции или вручную, или выделив мышью диапазон ячеек, который надо добавить в качестве аргумента. Если функция требует несколько аргументов, их надо вводить через точку с запятой.
                    <p>Аргументы должны быть заключены в круглые скобки. При выборе функции из списка открывающая скобка '(' добавляется автоматически. При вводе аргументов также отображается всплывающая подсказка с синтаксисом формулы. </p>
                    <p>
                        <img alt="Всплывающая подсказка" src="../images/functiontooltip.png" />
                    </p>
                </li>
                <li>Когда все аргументы будут указаны, добавьте закрывающую скобку ')' и нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>При вводе новых данных или изменении значений, используемых в качестве аргументов, пересчет функций по умолчанию выполняется автоматически. Вы можете принудительно выполнить пересчет функций с помощью кнопки <b>Пересчет</b> на вкладке <b>Формула</b>. Нажатие на саму кнопку <span class="icon icon-calculationicon"></span> <b>Пересчет</b> позволяет выполнить пересчет всей рабочей книги, также можно нажать на стрелку под этой кнопкой и выбрать в меню нужный вариант: <b>Пересчет книги</b> или <b>Пересчет рабочего листа</b>. <!--Используя кнопку <b>Пересчет</b>, также можно выбрать предпочтительный режим пересчета: автоматически или вручную.--></p>
            <p>Также можно использовать следующие сочетания клавиш: <b>F9</b> для пересчета рабочей книги, <b>Shift +F9</b> для пересчета текущего рабочего листа.</p>
            <p>Ниже приводится список доступных функций, сгруппированных по категориям:</p> 
                 <table class="helptable">
					<tr class="headerlite">
						<td width="25%"><b>Категория функций</b></td>
						<td width="40%"><b>Описание</b></td>
					<td width="35%"><b>Функции</b></td>
					</tr>
					<tr>
						<td>Функции для работы с текстом и данными</td>
						<td>Используются для корректного отображения текстовых данных в электронной таблице.</td>
					<td><a href="../Functions/asc.htm" onclick="onhyperlinkclick(this)">ASC</a>; <a href="../Functions/char.htm" onclick="onhyperlinkclick(this)">СИМВОЛ</a>; <a href="../Functions/clean.htm" onclick="onhyperlinkclick(this)">ПЕЧСИМВ</a>; <a href="../Functions/code.htm" onclick="onhyperlinkclick(this)">КОДСИМВ</a>; <a href="../Functions/concatenate.htm" onclick="onhyperlinkclick(this)">СЦЕПИТЬ</a>; <a href="../Functions/concat.htm" onclick="onhyperlinkclick(this)">СЦЕП</a>; <a href="../Functions/dollar.htm" onclick="onhyperlinkclick(this)">РУБЛЬ</a>; <a href="../Functions/exact.htm" onclick="onhyperlinkclick(this)">СОВПАД</a>; <a href="../Functions/find.htm" onclick="onhyperlinkclick(this)">НАЙТИ</a>; <a href="../Functions/find.htm" onclick="onhyperlinkclick(this)">НАЙТИБ</a>; <a href="../Functions/fixed.htm" onclick="onhyperlinkclick(this)">ФИКСИРОВАННЫЙ</a>; <a href="../Functions/left.htm" onclick="onhyperlinkclick(this)">ЛЕВСИМВ</a>; <a href="../Functions/left.htm" onclick="onhyperlinkclick(this)">ЛЕВБ</a>; <a href="../Functions/len.htm" onclick="onhyperlinkclick(this)">ДЛСТР</a>; <a href="../Functions/len.htm" onclick="onhyperlinkclick(this)">ДЛИНБ</a>; <a href="../Functions/lower.htm" onclick="onhyperlinkclick(this)">СТРОЧН</a>; <a href="../Functions/mid.htm" onclick="onhyperlinkclick(this)">ПСТР</a>; <a href="../Functions/mid.htm" onclick="onhyperlinkclick(this)">ПСТРБ</a>; <a href="../Functions/numbervalue.htm" onclick="onhyperlinkclick(this)">ЧЗНАЧ</a>; <a href="../Functions/proper.htm" onclick="onhyperlinkclick(this)">ПРОПНАЧ</a>; <a href="../Functions/replace.htm" onclick="onhyperlinkclick(this)">ЗАМЕНИТЬ</a>; <a href="../Functions/replace.htm" onclick="onhyperlinkclick(this)">ЗАМЕНИТЬБ</a>; <a href="../Functions/rept.htm" onclick="onhyperlinkclick(this)">ПОВТОР</a>; <a href="../Functions/right.htm" onclick="onhyperlinkclick(this)">ПРАВСИМВ</a>; <a href="../Functions/right.htm" onclick="onhyperlinkclick(this)">ПРАВБ</a>; <a href="../Functions/search.htm" onclick="onhyperlinkclick(this)">ПОИСК</a>; <a href="../Functions/search.htm" onclick="onhyperlinkclick(this)">ПОИСКБ</a>; <a href="../Functions/substitute.htm" onclick="onhyperlinkclick(this)">ПОДСТАВИТЬ</a>; <a href="../Functions/t.htm" onclick="onhyperlinkclick(this)">T</a>; <a href="../Functions/text.htm" onclick="onhyperlinkclick(this)">ТЕКСТ</a>; <a href="../Functions/textjoin.htm" onclick="onhyperlinkclick(this)">ОБЪЕДИНИТЬ</a>; <a href="../Functions/trim.htm" onclick="onhyperlinkclick(this)">СЖПРОБЕЛЫ</a>; <a href="../Functions/unichar.htm" onclick="onhyperlinkclick(this)">ЮНИСИМВ</a>; <a href="../Functions/unicode.htm" onclick="onhyperlinkclick(this)">UNICODE</a>; <a href="../Functions/upper.htm" onclick="onhyperlinkclick(this)">ПРОПИСН</a>; <a href="../Functions/value.htm" onclick="onhyperlinkclick(this)">ЗНАЧЕН</a>; <a href="../Functions/textbefore.htm" onclick="onhyperlinkclick(this)">ТЕКСТДО</a>; <a href="../Functions/textafter.htm" onclick="onhyperlinkclick(this)">ТЕКСТПОСЛЕ</a>; <a href="../Functions/textsplit.htm" onclick="onhyperlinkclick(this)">ТЕКСТРАЗД</a></td>
					</tr>
                     <tr>
                         <td>Статистические функции</td>
                         <td>Используются для анализа данных: нахождения среднего значения, наибольшего или наименьшего значения в диапазоне ячеек.</td>
                         <td><a href="../Functions/avedev.htm" onclick="onhyperlinkclick(this)">СРОТКЛ</a>; <a href="../Functions/average.htm" onclick="onhyperlinkclick(this)">СРЗНАЧ</a>; <a href="../Functions/averagea.htm" onclick="onhyperlinkclick(this)">СРЗНАЧА</a>; <a href="../Functions/averageif.htm" onclick="onhyperlinkclick(this)">СРЗНАЧЕСЛИ</a>; <a href="../Functions/averageifs.htm" onclick="onhyperlinkclick(this)">СРЗНАЧЕСЛИМН</a>; <a href="../Functions/betadist.htm" onclick="onhyperlinkclick(this)">БЕТАРАСП</a>; <a href="../Functions/beta-dist.htm" onclick="onhyperlinkclick(this)">БЕТА.РАСП</a>; <a href="../Functions/beta-inv.htm" onclick="onhyperlinkclick(this)">БЕТА.ОБР</a>; <a href="../Functions/betainv.htm" onclick="onhyperlinkclick(this)">БЕТАОБР</a>; <a href="../Functions/binomdist.htm" onclick="onhyperlinkclick(this)">БИНОМРАСП</a>; <a href="../Functions/binom-dist.htm" onclick="onhyperlinkclick(this)">БИНОМ.РАСП</a>; <a href="../Functions/binom-dist-range.htm" onclick="onhyperlinkclick(this)">БИНОМ.РАСП.ДИАП</a>; <a href="../Functions/binom-inv.htm" onclick="onhyperlinkclick(this)">БИНОМ.ОБР</a>; <a href="../Functions/chidist.htm" onclick="onhyperlinkclick(this)">ХИ2РАСП</a>; <a href="../Functions/chiinv.htm" onclick="onhyperlinkclick(this)">ХИ2ОБР</a>; <a href="../Functions/chisq-dist.htm" onclick="onhyperlinkclick(this)">ХИ2.РАСП</a>; <a href="../Functions/chisq-dist-rt.htm" onclick="onhyperlinkclick(this)">ХИ2.РАСП.ПХ</a>; <a href="../Functions/chisq-inv.htm" onclick="onhyperlinkclick(this)">ХИ2.ОБР</a>; <a href="../Functions/chisq-inv-rt.htm" onclick="onhyperlinkclick(this)">ХИ2.ОБР.ПХ</a>; <a href="../Functions/chitest.htm" onclick="onhyperlinkclick(this)">ХИ2ТЕСТ</a>; <a href="../Functions/chisq-test.htm" onclick="onhyperlinkclick(this)">ХИ2.ТЕСТ</a>; <a href="../Functions/confidence.htm" onclick="onhyperlinkclick(this)">ДОВЕРИТ</a>; <a href="../Functions/confidence-norm.htm" onclick="onhyperlinkclick(this)">ДОВЕРИТ.НОРМ</a>; <a href="../Functions/confidence-t.htm" onclick="onhyperlinkclick(this)">ДОВЕРИТ.СТЬЮДЕНТ</a>; <a href="../Functions/correl.htm" onclick="onhyperlinkclick(this)">КОРРЕЛ</a>; <a href="../Functions/count.htm" onclick="onhyperlinkclick(this)">СЧЁТ</a>; <a href="../Functions/counta.htm" onclick="onhyperlinkclick(this)">СЧЁТЗ</a>; <a href="../Functions/countblank.htm" onclick="onhyperlinkclick(this)">СЧИТАТЬПУСТОТЫ</a>; <a href="../Functions/countif.htm" onclick="onhyperlinkclick(this)">СЧЁТЕСЛИ</a>; <a href="../Functions/countifs.htm" onclick="onhyperlinkclick(this)">СЧЁТЕСЛИМН</a>; <a href="../Functions/covar.htm" onclick="onhyperlinkclick(this)">КОВАР</a>; <a href="../Functions/covariance-p.htm" onclick="onhyperlinkclick(this)">КОВАРИАЦИЯ.Г</a>; <a href="../Functions/covariance-s.htm" onclick="onhyperlinkclick(this)">КОВАРИАЦИЯ.В</a>; <a href="../Functions/critbinom.htm" onclick="onhyperlinkclick(this)">КРИТБИНОМ</a>; <a href="../Functions/devsq.htm" onclick="onhyperlinkclick(this)">КВАДРОТКЛ</a>; <a href="../Functions/expon-dist.htm" onclick="onhyperlinkclick(this)">ЭКСП.РАСП</a>; <a href="../Functions/expondist.htm" onclick="onhyperlinkclick(this)">ЭКСПРАСП</a>; <a href="../Functions/f-dist.htm" onclick="onhyperlinkclick(this)">F.РАСП</a>; <a href="../Functions/fdist.htm" onclick="onhyperlinkclick(this)">FРАСП</a>; <a href="../Functions/f-dist-rt.htm" onclick="onhyperlinkclick(this)">F.РАСП.ПХ</a>; <a href="../Functions/f-inv.htm" onclick="onhyperlinkclick(this)">F.ОБР</a>; <a href="../Functions/finv.htm" onclick="onhyperlinkclick(this)">FРАСПОБР</a>; <a href="../Functions/f-inv-rt.htm" onclick="onhyperlinkclick(this)">F.ОБР.ПХ</a>; <a href="../Functions/fisher.htm" onclick="onhyperlinkclick(this)">ФИШЕР</a>; <a href="../Functions/fisherinv.htm" onclick="onhyperlinkclick(this)">ФИШЕРОБР</a>; <a href="../Functions/forecast.htm" onclick="onhyperlinkclick(this)">ПРЕДСКАЗ</a>; <a href="../Functions/forecast-ets.htm" onclick="onhyperlinkclick(this)">ПРЕДСКАЗ.ETS</a>; <a href="../Functions/forecast-ets-confint.htm" onclick="onhyperlinkclick(this)">ПРЕДСКАЗ.ЕTS.ДОВИНТЕРВАЛ</a>; <a href="../Functions/forecast-ets-seasonality.htm" onclick="onhyperlinkclick(this)">ПРЕДСКАЗ.ETS.СЕЗОННОСТЬ</a>; <a href="../Functions/forecast-ets-stat.htm" onclick="onhyperlinkclick(this)">ПРЕДСКАЗ.ETS.СТАТ</a>; <a href="../Functions/forecast-linear.htm" onclick="onhyperlinkclick(this)">ПРЕДСКАЗ.ЛИНЕЙН</a>; <a href="../Functions/frequency.htm" onclick="onhyperlinkclick(this)">ЧАСТОТА</a>; <a href="../Functions/ftest.htm" onclick="onhyperlinkclick(this)">ФТЕСТ</a>; <a href="../Functions/f-test.htm" onclick="onhyperlinkclick(this)">F.ТЕСТ</a>; <a href="../Functions/gamma.htm" onclick="onhyperlinkclick(this)">ГАММА</a>; <a href="../Functions/gamma-dist.htm" onclick="onhyperlinkclick(this)">ГАММА.РАСП</a>; <a href="../Functions/gammadist.htm" onclick="onhyperlinkclick(this)">ГАММАРАСП</a>; <a href="../Functions/gamma-inv.htm" onclick="onhyperlinkclick(this)">ГАММА.ОБР</a>; <a href="../Functions/gammainv.htm" onclick="onhyperlinkclick(this)">ГАММАОБР</a>; <a href="../Functions/gammaln.htm" onclick="onhyperlinkclick(this)">ГАММАНЛОГ</a>; <a href="../Functions/gammaln-precise.htm" onclick="onhyperlinkclick(this)">ГАММАНЛОГ.ТОЧН</a>; <a href="../Functions/gauss.htm" onclick="onhyperlinkclick(this)">ГАУСС</a>; <a href="../Functions/geomean.htm" onclick="onhyperlinkclick(this)">СРГЕОМ</a>; <a href="../Functions/growth.htm" onclick="onhyperlinkclick(this)">РОСТ</a>; <a href="../Functions/harmean.htm" onclick="onhyperlinkclick(this)">СРГАРМ</a>; <a href="../Functions/hypgeomdist.htm" onclick="onhyperlinkclick(this)">ГИПЕРГЕОМЕТ</a>; <a href="../Functions/hypgeom-dist.htm" onclick="onhyperlinkclick(this)">ГИПЕРГЕОМ.РАСП</a>; <a href="../Functions/intercept.htm" onclick="onhyperlinkclick(this)">ОТРЕЗОК</a>; <a href="../Functions/kurt.htm" onclick="onhyperlinkclick(this)">ЭКСЦЕСС</a>; <a href="../Functions/large.htm" onclick="onhyperlinkclick(this)">НАИБОЛЬШИЙ</a>; <a href="../Functions/linest.htm" onclick="onhyperlinkclick(this)">ЛИНЕЙН</a>; <a href="../Functions/logest.htm" onclick="onhyperlinkclick(this)">ЛГРФПРИБЛ</a>; <a href="../Functions/loginv.htm" onclick="onhyperlinkclick(this)">ЛОГНОРМОБР</a>; <a href="../Functions/lognorm-dist.htm" onclick="onhyperlinkclick(this)">ЛОГНОРМ.РАСП</a>; <a href="../Functions/lognorm-inv.htm" onclick="onhyperlinkclick(this)">ЛОГНОРМ.ОБР</a>; <a href="../Functions/lognormdist.htm" onclick="onhyperlinkclick(this)">ЛОГНОРМРАСП</a>; <a href="../Functions/max.htm" onclick="onhyperlinkclick(this)">МАКС</a>; <a href="../Functions/maxa.htm" onclick="onhyperlinkclick(this)">МАКСА</a>; <a href="../Functions/maxifs.htm" onclick="onhyperlinkclick(this)">МАКСЕСЛИ</a>; <a href="../Functions/median.htm" onclick="onhyperlinkclick(this)">МЕДИАНА</a>; <a href="../Functions/min.htm" onclick="onhyperlinkclick(this)">МИН</a>; <a href="../Functions/mina.htm" onclick="onhyperlinkclick(this)">МИНА</a>; <a href="../Functions/minifs.htm" onclick="onhyperlinkclick(this)">МИНЕСЛИ</a>; <a href="../Functions/mode.htm" onclick="onhyperlinkclick(this)">МОДА</a>; <a href="../Functions/mode-mult.htm" onclick="onhyperlinkclick(this)">МОДА.НСК</a>; <a href="../Functions/mode-sngl.htm" onclick="onhyperlinkclick(this)">МОДА.ОДН</a>; <a href="../Functions/negbinomdist.htm" onclick="onhyperlinkclick(this)">ОТРБИНОМРАСП</a>; <a href="../Functions/negbinom-dist.htm" onclick="onhyperlinkclick(this)">ОТРБИНОМ.РАСП</a>; <a href="../Functions/normdist.htm" onclick="onhyperlinkclick(this)">НОРМРАСП</a>; <a href="../Functions/norm-dist.htm" onclick="onhyperlinkclick(this)">НОРМ.РАСП</a>; <a href="../Functions/norminv.htm" onclick="onhyperlinkclick(this)">НОРМОБР</a>; <a href="../Functions/norm-inv.htm" onclick="onhyperlinkclick(this)">НОРМ.ОБР</a>; <a href="../Functions/normsdist.htm" onclick="onhyperlinkclick(this)">НОРМСТРАСП</a>; <a href="../Functions/norm-s-dist.htm" onclick="onhyperlinkclick(this)">НОРМ.СТ.РАСП</a>; <a href="../Functions/normsinv.htm" onclick="onhyperlinkclick(this)">НОРМСТОБР</a>; <a href="../Functions/norm-s-inv.htm" onclick="onhyperlinkclick(this)">НОРМ.СТ.ОБР</a>; <a href="../Functions/pearson.htm" onclick="onhyperlinkclick(this)">ПИРСОН</a>; <a href="../Functions/percentile.htm" onclick="onhyperlinkclick(this)">ПЕРСЕНТИЛЬ</a>; <a href="../Functions/percentile-exc.htm" onclick="onhyperlinkclick(this)">ПРОЦЕНТИЛЬ.ИСКЛ</a>; <a href="../Functions/percentile-inc.htm" onclick="onhyperlinkclick(this)">ПРОЦЕНТИЛЬ.ВКЛ</a>; <a href="../Functions/percentrank.htm" onclick="onhyperlinkclick(this)">ПРОЦЕНТРАНГ</a>; <a href="../Functions/percentrank-exc.htm" onclick="onhyperlinkclick(this)">ПРОЦЕНТРАНГ.ИСКЛ</a>; <a href="../Functions/percentrank-inc.htm" onclick="onhyperlinkclick(this)">ПРОЦЕНТРАНГ.ВКЛ</a>; <a href="../Functions/permut.htm" onclick="onhyperlinkclick(this)">ПЕРЕСТ</a>; <a href="../Functions/permutationa.htm" onclick="onhyperlinkclick(this)">ПЕРЕСТА</a>; <a href="../Functions/phi.htm" onclick="onhyperlinkclick(this)">ФИ</a>; <a href="../Functions/poisson.htm" onclick="onhyperlinkclick(this)">ПУАССОН</a>; <a href="../Functions/poisson-dist.htm" onclick="onhyperlinkclick(this)">ПУАССОН.РАСП</a>; <a href="../Functions/prob.htm" onclick="onhyperlinkclick(this)">ВЕРОЯТНОСТЬ</a>; <a href="../Functions/quartile.htm" onclick="onhyperlinkclick(this)">КВАРТИЛЬ</a>; <a href="../Functions/quartile-exc.htm" onclick="onhyperlinkclick(this)">КВАРТИЛЬ.ИСКЛ</a>; <a href="../Functions/quartile-inc.htm" onclick="onhyperlinkclick(this)">КВАРТИЛЬ.ВКЛ</a>; <a href="../Functions/rank.htm" onclick="onhyperlinkclick(this)">РАНГ</a>; <a href="../Functions/rank-avg.htm" onclick="onhyperlinkclick(this)">РАНГ.СР</a>; <a href="../Functions/rank-eq.htm" onclick="onhyperlinkclick(this)">РАНГ.РВ</a>; <a href="../Functions/rsq.htm" onclick="onhyperlinkclick(this)">КВПИРСОН</a>; <a href="../Functions/skew.htm" onclick="onhyperlinkclick(this)">СКОС</a>; <a href="../Functions/skew-p.htm" onclick="onhyperlinkclick(this)">СКОС.Г</a>; <a href="../Functions/slope.htm" onclick="onhyperlinkclick(this)">НАКЛОН</a>; <a href="../Functions/small.htm" onclick="onhyperlinkclick(this)">НАИМЕНЬШИЙ</a>; <a href="../Functions/standardize.htm" onclick="onhyperlinkclick(this)">НОРМАЛИЗАЦИЯ</a>; <a href="../Functions/stdev.htm" onclick="onhyperlinkclick(this)">СТАНДОТКЛОН</a>; <a href="../Functions/stdev-s.htm" onclick="onhyperlinkclick(this)">СТАНДОТКЛОН.В</a>; <a href="../Functions/stdeva.htm" onclick="onhyperlinkclick(this)">СТАНДОТКЛОНА</a>; <a href="../Functions/stdevp.htm" onclick="onhyperlinkclick(this)">СТАНДОТКЛОНП</a>; <a href="../Functions/stdev-p.htm" onclick="onhyperlinkclick(this)">СТАНДОТКЛОН.Г</a>; <a href="../Functions/stdevpa.htm" onclick="onhyperlinkclick(this)">СТАНДОТКЛОНПА</a>; <a href="../Functions/steyx.htm" onclick="onhyperlinkclick(this)">СТОШYX</a>; <a href="../Functions/tdist.htm" onclick="onhyperlinkclick(this)">СТЬЮДРАСП</a>; <a href="../Functions/t-dist.htm" onclick="onhyperlinkclick(this)">СТЬЮДЕНТ.РАСП</a>; <a href="../Functions/t-dist-2t.htm" onclick="onhyperlinkclick(this)">СТЬЮДЕНТ.РАСП.2Х</a>; <a href="../Functions/t-dist-rt.htm" onclick="onhyperlinkclick(this)">СТЬЮДЕНТ.РАСП.ПХ</a>; <a href="../Functions/t-inv.htm" onclick="onhyperlinkclick(this)">СТЬЮДЕНТ.ОБР</a>; <a href="../Functions/t-inv-2t.htm" onclick="onhyperlinkclick(this)">СТЬЮДЕНТ.ОБР.2Х</a>; <a href="../Functions/tinv.htm" onclick="onhyperlinkclick(this)">СТЬЮДРАСПОБР</a>; <a href="../Functions/trend.htm" onclick="onhyperlinkclick(this)">ТЕНДЕНЦИЯ</a>; <a href="../Functions/trimmean.htm" onclick="onhyperlinkclick(this)">УРЕЗСРЕДНЕЕ</a>; <a href="../Functions/ttest.htm" onclick="onhyperlinkclick(this)">ТТЕСТ</a>; <a href="../Functions/t-test.htm" onclick="onhyperlinkclick(this)">СТЬЮДЕНТ.ТЕСТ</a>; <a href="../Functions/var.htm" onclick="onhyperlinkclick(this)">ДИСП</a>; <a href="../Functions/vara.htm" onclick="onhyperlinkclick(this)">ДИСПА</a>; <a href="../Functions/varp.htm" onclick="onhyperlinkclick(this)">ДИСПР</a>; <a href="../Functions/var-p.htm" onclick="onhyperlinkclick(this)">ДИСП.Г</a>; <a href="../Functions/var-s.htm" onclick="onhyperlinkclick(this)">ДИСП.В</a>; <a href="../Functions/varpa.htm" onclick="onhyperlinkclick(this)">ДИСПРА</a>; <a href="../Functions/weibull.htm" onclick="onhyperlinkclick(this)">ВЕЙБУЛЛ</a>; <a href="../Functions/weibull-dist.htm" onclick="onhyperlinkclick(this)">ВЕЙБУЛЛ.РАСП</a>; <a href="../Functions/ztest.htm" onclick="onhyperlinkclick(this)">ZТЕСТ</a>; <a href="../Functions/z-test.htm" onclick="onhyperlinkclick(this)">Z.ТЕСТ</a></td>
                     </tr>
                   <tr>
						<td>Математические функции</td>
						<td>Используются для выполнения базовых математических и тригонометрических операций, таких как сложение, умножение, деление, округление и т.д.</td>
                        <td><a href="../Functions/abs.htm" onclick="onhyperlinkclick(this)">ABS</a>; <a href="../Functions/acos.htm" onclick="onhyperlinkclick(this)">ACOS</a>; <a href="../Functions/acosh.htm" onclick="onhyperlinkclick(this)">ACOSH</a>; <a href="../Functions/acot.htm" onclick="onhyperlinkclick(this)">ACOT</a>; <a href="../Functions/acoth.htm" onclick="onhyperlinkclick(this)">ACOTH</a>; <a href="../Functions/aggregate.htm" onclick="onhyperlinkclick(this)">АГРЕГАТ</a>; <a href="../Functions/arabic.htm" onclick="onhyperlinkclick(this)">АРАБСКОЕ</a>; <a href="../Functions/asin.htm" onclick="onhyperlinkclick(this)">ASIN</a>; <a href="../Functions/asinh.htm" onclick="onhyperlinkclick(this)">ASINH</a>; <a href="../Functions/atan.htm" onclick="onhyperlinkclick(this)">ATAN</a>; <a href="../Functions/atan2.htm" onclick="onhyperlinkclick(this)">ATAN2</a>; <a href="../Functions/atanh.htm" onclick="onhyperlinkclick(this)">ATANH</a>; <a href="../Functions/base.htm" onclick="onhyperlinkclick(this)">ОСНОВАНИЕ</a>; <a href="../Functions/ceiling.htm" onclick="onhyperlinkclick(this)">ОКРВВЕРХ</a>; <a href="../Functions/ceiling-math.htm" onclick="onhyperlinkclick(this)">ОКРВВЕРХ.МАТ</a>; <a href="../Functions/ceiling-precise.htm" onclick="onhyperlinkclick(this)">ОКРВВЕРХ.ТОЧН</a>; <a href="../Functions/combin.htm" onclick="onhyperlinkclick(this)">ЧИСЛКОМБ</a>; <a href="../Functions/combina.htm" onclick="onhyperlinkclick(this)">ЧИСЛКОМБА</a>; <a href="../Functions/cos.htm" onclick="onhyperlinkclick(this)">COS</a>; <a href="../Functions/cosh.htm" onclick="onhyperlinkclick(this)">COSH</a>; <a href="../Functions/cot.htm" onclick="onhyperlinkclick(this)">COT</a>; <a href="../Functions/coth.htm" onclick="onhyperlinkclick(this)">COTH</a>; <a href="../Functions/csc.htm" onclick="onhyperlinkclick(this)">CSC</a>; <a href="../Functions/csch.htm" onclick="onhyperlinkclick(this)">CSCH</a>; <a href="../Functions/decimal.htm" onclick="onhyperlinkclick(this)">ДЕС</a>; <a href="../Functions/degrees.htm" onclick="onhyperlinkclick(this)">ГРАДУСЫ</a>; <a href="../Functions/ecma-ceiling.htm" onclick="onhyperlinkclick(this)">ECMA.ОКРВВЕРХ</a>; <a href="../Functions/even.htm" onclick="onhyperlinkclick(this)">ЧЁТН</a>; <a href="../Functions/exp.htm" onclick="onhyperlinkclick(this)">EXP</a>; <a href="../Functions/fact.htm" onclick="onhyperlinkclick(this)">ФАКТР</a>; <a href="../Functions/factdouble.htm" onclick="onhyperlinkclick(this)">ДВФАКТР</a>; <a href="../Functions/floor.htm" onclick="onhyperlinkclick(this)">ОКРВНИЗ</a>; <a href="../Functions/floor-precise.htm" onclick="onhyperlinkclick(this)">ОКРВНИЗ.ТОЧН</a>; <a href="../Functions/floor-math.htm" onclick="onhyperlinkclick(this)">ОКРВНИЗ.МАТ</a>; <a href="../Functions/gcd.htm" onclick="onhyperlinkclick(this)">НОД</a>; <a href="../Functions/int.htm" onclick="onhyperlinkclick(this)">ЦЕЛОЕ</a>; <a href="../Functions/iso-ceiling.htm" onclick="onhyperlinkclick(this)">ISO.ОКРВВЕРХ</a>; <a href="../Functions/lcm.htm" onclick="onhyperlinkclick(this)">НОК</a>; <a href="../Functions/ln.htm" onclick="onhyperlinkclick(this)">LN</a>; <a href="../Functions/log.htm" onclick="onhyperlinkclick(this)">LOG</a>; <a href="../Functions/log10.htm" onclick="onhyperlinkclick(this)">LOG10</a>; <a href="../Functions/mdeterm.htm" onclick="onhyperlinkclick(this)">МОПРЕД</a>; <a href="../Functions/minverse.htm" onclick="onhyperlinkclick(this)">МОБР</a>; <a href="../Functions/mmult.htm" onclick="onhyperlinkclick(this)">МУМНОЖ</a>; <a href="../Functions/mod.htm" onclick="onhyperlinkclick(this)">ОСТАТ</a>; <a href="../Functions/mround.htm" onclick="onhyperlinkclick(this)">ОКРУГЛТ</a>; <a href="../Functions/multinomial.htm" onclick="onhyperlinkclick(this)">МУЛЬТИНОМ</a>; <a href="../Functions/munit.htm" onclick="onhyperlinkclick(this)">МЕДИН</a>; <a href="../Functions/odd.htm" onclick="onhyperlinkclick(this)">НЕЧЁТ</a>; <a href="../Functions/pi.htm" onclick="onhyperlinkclick(this)">ПИ</a>; <a href="../Functions/power.htm" onclick="onhyperlinkclick(this)">СТЕПЕНЬ</a>; <a href="../Functions/product.htm" onclick="onhyperlinkclick(this)">ПРОИЗВЕД</a>; <a href="../Functions/quotient.htm" onclick="onhyperlinkclick(this)">ЧАСТНОЕ</a>; <a href="../Functions/radians.htm" onclick="onhyperlinkclick(this)">РАДИАНЫ</a>; <a href="../Functions/rand.htm" onclick="onhyperlinkclick(this)">СЛЧИС</a>; <a href="../Functions/randarray.htm" onclick="onhyperlinkclick(this)">СЛУЧМАССИВ</a>; <a href="../Functions/randbetween.htm" onclick="onhyperlinkclick(this)">СЛУЧМЕЖДУ</a>; <a href="../Functions/roman.htm" onclick="onhyperlinkclick(this)">РИМСКОЕ</a>; <a href="../Functions/round.htm" onclick="onhyperlinkclick(this)">ОКРУГЛ</a>; <a href="../Functions/rounddown.htm" onclick="onhyperlinkclick(this)">ОКРУГЛВНИЗ</a>; <a href="../Functions/roundup.htm" onclick="onhyperlinkclick(this)">ОКРУГЛВВЕРХ</a>; <a href="../Functions/sec.htm" onclick="onhyperlinkclick(this)">SEC</a>; <a href="../Functions/sech.htm" onclick="onhyperlinkclick(this)">SECH</a>; <a href="../Functions/seriessum.htm" onclick="onhyperlinkclick(this)">РЯД.СУММ</a>; <a href="../Functions/sign.htm" onclick="onhyperlinkclick(this)">ЗНАК</a>; <a href="../Functions/sin.htm" onclick="onhyperlinkclick(this)">SIN</a>; <a href="../Functions/sinh.htm" onclick="onhyperlinkclick(this)">SINH</a>; <a href="../Functions/sqrt.htm" onclick="onhyperlinkclick(this)">КОРЕНЬ</a>; <a href="../Functions/sqrtpi.htm" onclick="onhyperlinkclick(this)">КОРЕНЬПИ</a>; <a href="../Functions/subtotal.htm" onclick="onhyperlinkclick(this)">ПРОМЕЖУТОЧНЫЕ.ИТОГИ</a>; <a href="../Functions/sum.htm" onclick="onhyperlinkclick(this)">СУММ</a>; <a href="../Functions/sumif.htm" onclick="onhyperlinkclick(this)">СУММЕСЛИ</a>; <a href="../Functions/sumifs.htm" onclick="onhyperlinkclick(this)">СУММЕСЛИМН</a>; <a href="../Functions/sumproduct.htm" onclick="onhyperlinkclick(this)">СУММПРОИЗВ</a>; <a href="../Functions/sumsq.htm" onclick="onhyperlinkclick(this)">СУММКВ</a>; <a href="../Functions/sumx2my2.htm" onclick="onhyperlinkclick(this)">СУММРАЗНКВ</a>; <a href="../Functions/sumx2py2.htm" onclick="onhyperlinkclick(this)">СУММСУММКВ</a>; <a href="../Functions/sumxmy2.htm" onclick="onhyperlinkclick(this)">СУММКВРАЗН</a>; <a href="../Functions/tan.htm" onclick="onhyperlinkclick(this)">TAN</a>; <a href="../Functions/tanh.htm" onclick="onhyperlinkclick(this)">TANH</a>; <a href="../Functions/trunc.htm" onclick="onhyperlinkclick(this)">ОТБР</a>;</td>
					</tr>
                     <tr>
                         <td>Функции даты и времени</td>
                         <td>Используются для корректного отображения даты и времени в электронной таблице.</td>
                         <td><a href="../Functions/date.htm" onclick="onhyperlinkclick(this)">ДАТА</a>; <a href="../Functions/datedif.htm" onclick="onhyperlinkclick(this)">РАЗНДАТ</a>; <a href="../Functions/datevalue.htm" onclick="onhyperlinkclick(this)">ДАТАЗНАЧ</a>; <a href="../Functions/day.htm" onclick="onhyperlinkclick(this)">ДЕНЬ</a>; <a href="../Functions/days.htm" onclick="onhyperlinkclick(this)">ДНИ</a>; <a href="../Functions/days360.htm" onclick="onhyperlinkclick(this)">ДНЕЙ360</a>; <a href="../Functions/edate.htm" onclick="onhyperlinkclick(this)">ДАТАМЕС</a>; <a href="../Functions/eomonth.htm" onclick="onhyperlinkclick(this)">КОНМЕСЯЦА</a>; <a href="../Functions/hour.htm" onclick="onhyperlinkclick(this)">ЧАС</a>; <a href="../Functions/isoweeknum.htm" onclick="onhyperlinkclick(this)">НОМНЕДЕЛИ.ISO</a>; <a href="../Functions/minute.htm" onclick="onhyperlinkclick(this)">МИНУТЫ</a>; <a href="../Functions/month.htm" onclick="onhyperlinkclick(this)">МЕСЯЦ</a>; <a href="../Functions/networkdays.htm" onclick="onhyperlinkclick(this)">ЧИСТРАБДНИ</a>; <a href="../Functions/networkdays-intl.htm" onclick="onhyperlinkclick(this)">ЧИСТРАБДНИ.МЕЖД</a>; <a href="../Functions/now.htm" onclick="onhyperlinkclick(this)">ТДАТА</a>; <a href="../Functions/second.htm" onclick="onhyperlinkclick(this)">СЕКУНДЫ</a>; <a href="../Functions/time.htm" onclick="onhyperlinkclick(this)">ВРЕМЯ</a>; <a href="../Functions/timevalue.htm" onclick="onhyperlinkclick(this)">ВРЕМЗНАЧ</a>; <a href="../Functions/today.htm" onclick="onhyperlinkclick(this)">СЕГОДНЯ</a>; <a href="../Functions/weekday.htm" onclick="onhyperlinkclick(this)">ДЕНЬНЕД</a>; <a href="../Functions/weeknum.htm" onclick="onhyperlinkclick(this)">НОМНЕДЕЛИ</a>; <a href="../Functions/workday.htm" onclick="onhyperlinkclick(this)">РАБДЕНЬ</a>; <a href="../Functions/workday-intl.htm" onclick="onhyperlinkclick(this)">РАБДЕНЬ.МЕЖД</a>; <a href="../Functions/year.htm" onclick="onhyperlinkclick(this)">ГОД</a>; <a href="../Functions/yearfrac.htm" onclick="onhyperlinkclick(this)">ДОЛЯГОДА</a></td>
                     </tr>
                     <tr>
                         <td>Инженерные функции</td>
                         <td>Используются для выполнения инженерных расчетов: преобразования чисел из одной системы счисления в другую, работы с комплексными числами и т.д.</td>
                         <td><a href="../Functions/besseli.htm" onclick="onhyperlinkclick(this)">БЕССЕЛЬ.I</a>; <a href="../Functions/besselj.htm" onclick="onhyperlinkclick(this)">БЕССЕЛЬ.J</a>; <a href="../Functions/besselk.htm" onclick="onhyperlinkclick(this)">БЕССЕЛЬ.K</a>; <a href="../Functions/bessely.htm" onclick="onhyperlinkclick(this)">БЕССЕЛЬ.Y</a>; <a href="../Functions/bin2dec.htm" onclick="onhyperlinkclick(this)">ДВ.В.ДЕС</a>; <a href="../Functions/bin2hex.htm" onclick="onhyperlinkclick(this)">ДВ.В.ШЕСТН</a>; <a href="../Functions/bin2oct.htm" onclick="onhyperlinkclick(this)">ДВ.В.ВОСЬМ</a>; <a href="../Functions/bitand.htm" onclick="onhyperlinkclick(this)">БИТ.И</a>; <a href="../Functions/bitlshift.htm" onclick="onhyperlinkclick(this)">БИТ.СДВИГЛ</a>; <a href="../Functions/bitor.htm" onclick="onhyperlinkclick(this)">БИТ.ИЛИ</a>; <a href="../Functions/bitrshift.htm" onclick="onhyperlinkclick(this)">БИТ.СДВИГП</a>; <a href="../Functions/bitxor.htm" onclick="onhyperlinkclick(this)">БИТ.ИСКЛИЛИ</a>; <a href="../Functions/complex.htm" onclick="onhyperlinkclick(this)">КОМПЛЕКСН</a>; <a href="../Functions/convert.htm" onclick="onhyperlinkclick(this)">ПРЕОБР</a>; <a href="../Functions/dec2bin.htm" onclick="onhyperlinkclick(this)">ДЕС.В.ДВ</a>; <a href="../Functions/dec2hex.htm" onclick="onhyperlinkclick(this)">ДЕС.В.ШЕСТН</a>; <a href="../Functions/dec2oct.htm" onclick="onhyperlinkclick(this)">ДЕС.В.ВОСЬМ</a>; <a href="../Functions/delta.htm" onclick="onhyperlinkclick(this)">ДЕЛЬТА</a>; <a href="../Functions/erf.htm" onclick="onhyperlinkclick(this)">ФОШ</a>; <a href="../Functions/erf-precise.htm" onclick="onhyperlinkclick(this)">ФОШ.ТОЧН</a>; <a href="../Functions/erfc.htm" onclick="onhyperlinkclick(this)">ДФОШ</a>; <a href="../Functions/erfc-precise.htm" onclick="onhyperlinkclick(this)">ДФОШ.ТОЧН</a>; <a href="../Functions/gestep.htm" onclick="onhyperlinkclick(this)">ПОРОГ</a>; <a href="../Functions/hex2bin.htm" onclick="onhyperlinkclick(this)">ШЕСТН.В.ДВ</a>; <a href="../Functions/hex2dec.htm" onclick="onhyperlinkclick(this)">ШЕСТН.В.ДЕС</a>; <a href="../Functions/hex2oct.htm" onclick="onhyperlinkclick(this)">ШЕСТН.В.ВОСЬМ</a>; <a href="../Functions/imabs.htm" onclick="onhyperlinkclick(this)">МНИМ.ABS</a>; <a href="../Functions/imaginary.htm" onclick="onhyperlinkclick(this)">МНИМ.ЧАСТЬ</a>; <a href="../Functions/imargument.htm" onclick="onhyperlinkclick(this)">МНИМ.АРГУМЕНТ</a>; <a href="../Functions/imconjugate.htm" onclick="onhyperlinkclick(this)">МНИМ.СОПРЯЖ</a>; <a href="../Functions/imcos.htm" onclick="onhyperlinkclick(this)">МНИМ.COS</a>; <a href="../Functions/imcosh.htm" onclick="onhyperlinkclick(this)">МНИМ.COSH</a>; <a href="../Functions/imcot.htm" onclick="onhyperlinkclick(this)">МНИМ.COT</a>; <a href="../Functions/imcsc.htm" onclick="onhyperlinkclick(this)">МНИМ.CSC</a>; <a href="../Functions/imcsch.htm" onclick="onhyperlinkclick(this)">МНИМ.CSCH</a>; <a href="../Functions/imdiv.htm" onclick="onhyperlinkclick(this)">МНИМ.ДЕЛ</a>; <a href="../Functions/imexp.htm" onclick="onhyperlinkclick(this)">МНИМ.EXP</a>; <a href="../Functions/imln.htm" onclick="onhyperlinkclick(this)">МНИМ.LN</a>; <a href="../Functions/imlog10.htm" onclick="onhyperlinkclick(this)">МНИМ.LOG10</a>; <a href="../Functions/imlog2.htm" onclick="onhyperlinkclick(this)">МНИМ.LOG2</a>; <a href="../Functions/impower.htm" onclick="onhyperlinkclick(this)">МНИМ.СТЕПЕНЬ</a>; <a href="../Functions/improduct.htm" onclick="onhyperlinkclick(this)">МНИМ.ПРОИЗВЕД</a>; <a href="../Functions/imreal.htm" onclick="onhyperlinkclick(this)">МНИМ.ВЕЩ</a>; <a href="../Functions/imsec.htm" onclick="onhyperlinkclick(this)">МНИМ.SEC</a>; <a href="../Functions/imsech.htm" onclick="onhyperlinkclick(this)">МНИМ.SECH</a>; <a href="../Functions/imsin.htm" onclick="onhyperlinkclick(this)">МНИМ.SIN</a>; <a href="../Functions/imsinh.htm" onclick="onhyperlinkclick(this)">МНИМ.SINH</a>; <a href="../Functions/imsqrt.htm" onclick="onhyperlinkclick(this)">МНИМ.КОРЕНЬ</a>; <a href="../Functions/imsub.htm" onclick="onhyperlinkclick(this)">МНИМ.РАЗН</a>; <a href="../Functions/imsum.htm" onclick="onhyperlinkclick(this)">МНИМ.СУММ</a>; <a href="../Functions/imtan.htm" onclick="onhyperlinkclick(this)">МНИМ.TAN</a>; <a href="../Functions/oct2bin.htm" onclick="onhyperlinkclick(this)">ВОСЬМ.В.ДВ</a>; <a href="../Functions/oct2dec.htm" onclick="onhyperlinkclick(this)">ВОСЬМ.В.ДЕС</a>; <a href="../Functions/oct2hex.htm" onclick="onhyperlinkclick(this)">ВОСЬМ.В.ШЕСТН</a></td>
                     </tr>
                     <tr>
                         <td>Функции для работы с базами данных</td>
                         <td>Используются для выполнения вычислений по значениям определенного поля базы данных, соответствующих заданным критериям.</td>
                         <td><a href="../Functions/daverage.htm" onclick="onhyperlinkclick(this)">ДСРЗНАЧ</a>; <a href="../Functions/dcount.htm" onclick="onhyperlinkclick(this)">БСЧЁТ</a>; <a href="../Functions/dcounta.htm" onclick="onhyperlinkclick(this)">БСЧЁТА</a>; <a href="../Functions/dget.htm" onclick="onhyperlinkclick(this)">БИЗВЛЕЧЬ</a>; <a href="../Functions/dmax.htm" onclick="onhyperlinkclick(this)">ДМАКС</a>; <a href="../Functions/dmin.htm" onclick="onhyperlinkclick(this)">ДМИН</a>; <a href="../Functions/dproduct.htm" onclick="onhyperlinkclick(this)">БДПРОИЗВЕД</a>; <a href="../Functions/dstdev.htm" onclick="onhyperlinkclick(this)">ДСТАНДОТКЛ</a>; <a href="../Functions/dstdevp.htm" onclick="onhyperlinkclick(this)">ДСТАНДОТКЛП</a>; <a href="../Functions/dsum.htm" onclick="onhyperlinkclick(this)">БДСУММ</a>; <a href="../Functions/dvar.htm" onclick="onhyperlinkclick(this)">БДДИСП</a>; <a href="../Functions/dvarp.htm" onclick="onhyperlinkclick(this)">БДДИСПП</a></td>
                     </tr>
                     <tr>
                         <td>Финансовые функции</td>
                         <td>Используются для выполнения финансовых расчетов: вычисления чистой приведенной стоимости, суммы платежа и т.д.</td>
                         <td><a href="../Functions/accrint.htm" onclick="onhyperlinkclick(this)">НАКОПДОХОД</a>; <a href="../Functions/accrintm.htm" onclick="onhyperlinkclick(this)">НАКОПДОХОДПОГАШ</a>; <a href="../Functions/amordegrc.htm" onclick="onhyperlinkclick(this)">АМОРУМ</a>; <a href="../Functions/amorlinc.htm" onclick="onhyperlinkclick(this)">АМОРУВ</a>; <a href="../Functions/coupdaybs.htm" onclick="onhyperlinkclick(this)">ДНЕЙКУПОНДО</a>; <a href="../Functions/coupdays.htm" onclick="onhyperlinkclick(this)">ДНЕЙКУПОН</a>; <a href="../Functions/coupdaysnc.htm" onclick="onhyperlinkclick(this)">ДНЕЙКУПОНПОСЛЕ</a>; <a href="../Functions/coupncd.htm" onclick="onhyperlinkclick(this)">ДАТАКУПОНПОСЛЕ</a>; <a href="../Functions/coupnum.htm" onclick="onhyperlinkclick(this)">ЧИСЛКУПОН</a>; <a href="../Functions/couppcd.htm" onclick="onhyperlinkclick(this)">ДАТАКУПОНДО</a>; <a href="../Functions/cumipmt.htm" onclick="onhyperlinkclick(this)">ОБЩПЛАТ</a>; <a href="../Functions/cumprinc.htm" onclick="onhyperlinkclick(this)">ОБЩДОХОД</a>; <a href="../Functions/db.htm" onclick="onhyperlinkclick(this)">ФУО</a>; <a href="../Functions/ddb.htm" onclick="onhyperlinkclick(this)">ДДОБ</a>; <a href="../Functions/disc.htm" onclick="onhyperlinkclick(this)">СКИДКА</a>; <a href="../Functions/dollarde.htm" onclick="onhyperlinkclick(this)">РУБЛЬ.ДЕС</a>; <a href="../Functions/dollarfr.htm" onclick="onhyperlinkclick(this)">РУБЛЬ.ДРОБЬ</a>; <a href="../Functions/duration.htm" onclick="onhyperlinkclick(this)">ДЛИТ</a>; <a href="../Functions/effect.htm" onclick="onhyperlinkclick(this)">ЭФФЕКТ</a>; <a href="../Functions/fv.htm" onclick="onhyperlinkclick(this)">БС</a>; <a href="../Functions/fvschedule.htm" onclick="onhyperlinkclick(this)">БЗРАСПИС</a>; <a href="../Functions/intrate.htm" onclick="onhyperlinkclick(this)">ИНОРМА</a>; <a href="../Functions/ipmt.htm" onclick="onhyperlinkclick(this)">ПРПЛТ</a>; <a href="../Functions/irr.htm" onclick="onhyperlinkclick(this)">ВСД</a>; <a href="../Functions/ispmt.htm" onclick="onhyperlinkclick(this)">ПРОЦПЛАТ</a>; <a href="../Functions/mduration.htm" onclick="onhyperlinkclick(this)">МДЛИТ</a>; <a href="../Functions/mirr.htm" onclick="onhyperlinkclick(this)">МВСД</a>; <a href="../Functions/nominal.htm" onclick="onhyperlinkclick(this)">НОМИНАЛ</a>; <a href="../Functions/nper.htm" onclick="onhyperlinkclick(this)">КПЕР</a>; <a href="../Functions/npv.htm" onclick="onhyperlinkclick(this)">ЧПС</a>; <a href="../Functions/oddfprice.htm" onclick="onhyperlinkclick(this)">ЦЕНАПЕРВНЕРЕГ</a>; <a href="../Functions/oddfyield.htm" onclick="onhyperlinkclick(this)">ДОХОДПЕРВНЕРЕГ</a>; <a href="../Functions/oddlprice.htm" onclick="onhyperlinkclick(this)">ЦЕНАПОСЛНЕРЕГ</a>; <a href="../Functions/oddlyield.htm" onclick="onhyperlinkclick(this)">ДОХОДПОСЛНЕРЕГ</a>; <a href="../Functions/pduration.htm" onclick="onhyperlinkclick(this)">ПДЛИТ</a>; <a href="../Functions/pmt.htm" onclick="onhyperlinkclick(this)">ПЛТ</a>; <a href="../Functions/ppmt.htm" onclick="onhyperlinkclick(this)">ОСПЛТ</a>; <a href="../Functions/price.htm" onclick="onhyperlinkclick(this)">ЦЕНА</a>; <a href="../Functions/pricedisc.htm" onclick="onhyperlinkclick(this)">ЦЕНАСКИДКА</a>; <a href="../Functions/pricemat.htm" onclick="onhyperlinkclick(this)">ЦЕНАПОГАШ</a>; <a href="../Functions/pv.htm" onclick="onhyperlinkclick(this)">ПС</a>; <a href="../Functions/rate.htm" onclick="onhyperlinkclick(this)">СТАВКА</a>; <a href="../Functions/received.htm" onclick="onhyperlinkclick(this)">ПОЛУЧЕНО</a>; <a href="../Functions/rri.htm" onclick="onhyperlinkclick(this)">ЭКВ.СТАВКА</a>; <a href="../Functions/sln.htm" onclick="onhyperlinkclick(this)">АПЛ</a>; <a href="../Functions/syd.htm" onclick="onhyperlinkclick(this)">АСЧ</a>; <a href="../Functions/tbilleq.htm" onclick="onhyperlinkclick(this)">РАВНОКЧЕК</a>; <a href="../Functions/tbillprice.htm" onclick="onhyperlinkclick(this)">ЦЕНАКЧЕК</a>; <a href="../Functions/tbillyield.htm" onclick="onhyperlinkclick(this)">ДОХОДКЧЕК</a>; <a href="../Functions/vdb.htm" onclick="onhyperlinkclick(this)">ПУО</a>; <a href="../Functions/xirr.htm" onclick="onhyperlinkclick(this)">ЧИСТВНДОХ</a>; <a href="../Functions/xnpv.htm" onclick="onhyperlinkclick(this)">ЧИСТНЗ</a>; <a href="../Functions/yield.htm" onclick="onhyperlinkclick(this)">ДОХОД</a>; <a href="../Functions/yielddisc.htm" onclick="onhyperlinkclick(this)">ДОХОДСКИДКА</a>; <a href="../Functions/yieldmat.htm" onclick="onhyperlinkclick(this)">ДОХОДПОГАШ</a></td>
                     </tr>
                     <tr>
                         <td>Поисковые функции</td>
                         <td>Используются для упрощения поиска информации по списку данных.</td>
                         <td><a href="../Functions/address.htm" onclick="onhyperlinkclick(this)">АДРЕС</a>; <a href="../Functions/choose.htm" onclick="onhyperlinkclick(this)">ВЫБОР</a>; <a href="../Functions/column.htm" onclick="onhyperlinkclick(this)">СТОЛБЕЦ</a>; <a href="../Functions/columns.htm" onclick="onhyperlinkclick(this)">ЧИСЛСТОЛБ</a>; <a href="../Functions/formulatext.htm" onclick="onhyperlinkclick(this)">Ф.ТЕКСТ</a>; <a href="../Functions/hlookup.htm" onclick="onhyperlinkclick(this)">ГПР</a>; <a href="../Functions/hyperlink.htm" onclick="onhyperlinkclick(this)">ГИПЕРССЫЛКА</a>; <a href="../Functions/index.htm" onclick="onhyperlinkclick(this)">ИНДЕКС</a>; <a href="../Functions/indirect.htm" onclick="onhyperlinkclick(this)">ДВССЫЛ</a>; <a href="../Functions/lookup.htm" onclick="onhyperlinkclick(this)">ПРОСМОТР</a>; <a href="../Functions/match.htm" onclick="onhyperlinkclick(this)">ПОИСКПОЗ</a>; <a href="../Functions/offset.htm" onclick="onhyperlinkclick(this)">СМЕЩ</a>; <a href="../Functions/row.htm" onclick="onhyperlinkclick(this)">СТРОКА</a>; <a href="../Functions/rows.htm" onclick="onhyperlinkclick(this)">ЧСТРОК</a>; <a href="../Functions/transpose.htm" onclick="onhyperlinkclick(this)">ТРАНСП</a>; <a href="../Functions/unique.htm" onclick="onhyperlinkclick(this)">УНИК</a>; <a href="../Functions/vlookup.htm" onclick="onhyperlinkclick(this)">ВПР</a>; <a href="../Functions/xlookup.htm" onclick="onhyperlinkclick(this)">ПРОСМОТРX</a>; <a href="../Functions/hstack.htm" onclick="onhyperlinkclick(this)">ГСТОЛБИК</a>; <a href="../Functions/vstack.htm" onclick="onhyperlinkclick(this)">ВСТОЛБИК</a>; <a href="../Functions/torow.htm" onclick="onhyperlinkclick(this)">ПОСТРОК</a>; <a href="../Functions/tocol.htm" onclick="onhyperlinkclick(this)">ПОСТОЛБЦ</a>; <a href="../Functions/wraprows.htm" onclick="onhyperlinkclick(this)">СВЕРНСТРОК</a>; <a href="../Functions/wrapcols.htm" onclick="onhyperlinkclick(this)">СВЕРНСТОЛБЦ</a>; <a href="../Functions/drop.htm" onclick="onhyperlinkclick(this)">СБРОСИТЬ</a>; <a href="../Functions/take.htm" onclick="onhyperlinkclick(this)">ВЗЯТЬ</a>; <a href="../Functions/chooserows.htm" onclick="onhyperlinkclick(this)">ВЫБОРСТРОК</a>; <a href="../Functions/choosecols.htm" onclick="onhyperlinkclick(this)">ВЫБОРСТОЛБЦ</a></td>
                     </tr>
					<tr>
						<td>Информационные функции</td>
                        <td>Используются для предоставления информации о данных в выделенной ячейке или диапазоне ячеек.</td>
					<td><a href="../Functions/cell.htm" onclick="onhyperlinkclick(this)">ЯЧЕЙКА</a>; <a href="../Functions/error-type.htm" onclick="onhyperlinkclick(this)">ТИП.ОШИБКИ</a>; <a href="../Functions/isblank.htm" onclick="onhyperlinkclick(this)">ЕПУСТО</a>; <a href="../Functions/iserr.htm" onclick="onhyperlinkclick(this)">ЕОШ</a>; <a href="../Functions/iserror.htm" onclick="onhyperlinkclick(this)">ЕОШИБКА</a>; <a href="../Functions/iseven.htm" onclick="onhyperlinkclick(this)">ЕЧЁТН</a>; <a href="../Functions/isformula.htm" onclick="onhyperlinkclick(this)">ЕФОРМУЛА</a>; <a href="../Functions/islogical.htm" onclick="onhyperlinkclick(this)">ЕЛОГИЧ</a>; <a href="../Functions/isna.htm" onclick="onhyperlinkclick(this)">ЕНД</a>; <a href="../Functions/isnontext.htm" onclick="onhyperlinkclick(this)">ЕНЕТЕКСТ</a>; <a href="../Functions/isnumber.htm" onclick="onhyperlinkclick(this)">ЕЧИСЛО</a>; <a href="../Functions/isodd.htm" onclick="onhyperlinkclick(this)">ЕНЕЧЁТ</a>; <a href="../Functions/isref.htm" onclick="onhyperlinkclick(this)">ЕССЫЛКА</a>; <a href="../Functions/istext.htm" onclick="onhyperlinkclick(this)">ЕТЕКСТ</a>; <a href="../Functions/n.htm" onclick="onhyperlinkclick(this)">Ч</a>; <a href="../Functions/na.htm" onclick="onhyperlinkclick(this)">НД</a>; <a href="../Functions/sheet.htm" onclick="onhyperlinkclick(this)">ЛИСТ</a>; <a href="../Functions/sheets.htm" onclick="onhyperlinkclick(this)">ЛИСТЫ</a>; <a href="../Functions/type.htm" onclick="onhyperlinkclick(this)">ТИП</a></td>
					</tr>
					<tr>
						<td>Логические функции</td>
                        <td>Используются для выполнения проверки, является ли условие истинным или ложным.</td>
					<td><a href="../Functions/and.htm" onclick="onhyperlinkclick(this)">И</a>; <a href="../Functions/false.htm" onclick="onhyperlinkclick(this)">ЛОЖЬ</a>; <a href="../Functions/if.htm" onclick="onhyperlinkclick(this)">ЕСЛИ</a>; <a href="../Functions/iferror.htm" onclick="onhyperlinkclick(this)">ЕСЛИОШИБКА</a>; <a href="../Functions/ifna.htm" onclick="onhyperlinkclick(this)">ЕСНД</a>; <a href="../Functions/ifs.htm" onclick="onhyperlinkclick(this)">ЕСЛИМН</a>; <a href="../Functions/not.htm" onclick="onhyperlinkclick(this)">НЕ</a>; <a href="../Functions/or.htm" onclick="onhyperlinkclick(this)">ИЛИ</a>; <a href="../Functions/switch.htm" onclick="onhyperlinkclick(this)">ПЕРЕКЛЮЧ</a>; <a href="../Functions/true.htm" onclick="onhyperlinkclick(this)">ИСТИНА</a>; <a href="../Functions/xor.htm" onclick="onhyperlinkclick(this)">ИСКЛИЛИ</a></td>
					</tr>
			</table>
		</div>
	</body>
</html>
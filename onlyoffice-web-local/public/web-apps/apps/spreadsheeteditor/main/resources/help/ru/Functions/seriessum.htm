<!DOCTYPE html>
<html>
	<head>
		<title>Функция РЯД.СУММ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция РЯД.СУММ</h1>
			<p>Функция <b>РЯД.СУММ</b> - это одна из математических и тригонометрических функций. Возвращает сумму степенного ряда.</p>
			<p>Синтаксис функции <b>РЯД.СУММ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>РЯД.СУММ(переменная;показатель_степени;шаг;коэффициенты)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>переменная</em></b> - значение переменной степенного ряда.</p>
				<p style="text-indent: 50px;"><b><em>показатель_степени</em></b> - показатель степени <b><em>переменной</em></b> для первого члена степенного ряда.</p>
				<p style="text-indent: 50px;"><b><em>шаг</em></b> - шаг, на который увеличивается <b><em>показатель_степени</em></b> для каждого следующего члена степенного ряда.</p>
				<p style="text-indent: 50px;"><b><em>коэффициенты</em></b> - коэффициенты при соответствующих степенях <b><em>переменной</em></b>. Количеством <b><em>коэффициентов</em></b> определяется количество членов степенного ряда.</p>
				<p>Эти числовые значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			
			
			<p>Чтобы применить функцию <b>РЯД.СУММ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>РЯД.СУММ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция РЯД.СУММ" src="../images/seriessum.png" /></p>
		</div>
	</body>
</html>
﻿<!DOCTYPE html>
<html>
	<head>
		<title>Set font type, size, style, and colors</title>
		<meta charset="utf-8" />
		<meta name="description" content="Change the following formatting parameters: font type, size, style, and colors" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Set font type, size, style, and colors</h1>
			<p>You can select the font type and its size, apply one of the decoration styles and change the font and background colors using the corresponding icons situated at the <b>Home</b> tab of the top toolbar.</p>
			<p class="note"><b>Note</b>: in case you want to apply the formatting to the data already present in the spreadsheet, select them with the mouse or <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">using the keyboard</a> and apply the formatting. If you need to apply the formatting to multiple non-adjacent cells or cell ranges, hold down the <b>Ctrl</b> key while selecting cells/ranges with the mouse.</p>
			<table>
				<tr>
                    <td width="10%">Font</td>
                    <td width="15%"><div class = "big big-fontfamily"></div></td>
					<td>Is used to select one of the fonts from the list of the available ones. <span class="desktopDocumentFeatures">If a required font is not available in the list, you can download and install it on your operating system, after that the font will be available for use in the <em>desktop version</em>.</span></td>
				</tr>
				<tr>
					<td>Font size</td>
					<td><div class = "icon icon-fontsize"></div></td>
					<td>Is used to select among the preset font size values from the dropdown list (the default values are: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 and 96). It's also possible to manually enter a custom value to the font size field and then press <em>Enter</em>.</td>
				</tr>
				<tr>
					<td>Increment font size</td>
					<td><div class = "icon icon-larger"></div></td>
					<td>Is used to change the font size making it larger one point each time the icon is clicked.</td>
				</tr>
				<tr>
					<td>Decrement font size</td>
					<td><div class = "icon icon-smaller"></div></td>
					<td>Is used to change the font size making it smaller one point each time the icon is clicked.</td>
				</tr>
				<tr>
					<td>Bold</td>
					<td><div class = "icon icon-bold"></div></td>
					<td>Is used to make the font bold giving it more weight.</td>
				</tr>
				<tr>
					<td>Italic</td>
					<td><div class = "icon icon-italic"></div></td>
					<td>Is used to make the font italicized giving it some right side tilt.</td>
				</tr>
				<tr>
					<td>Underline</td>
					<td><div class = "icon icon-underline"></div></td>
					<td>Is used to make the text underlined with the line going under the letters.</td>
				</tr>
                <tr>
                    <td>Strikeout</td>
                    <td><div class = "icon icon-strike"></div></td>
                    <td>Is used to make the text struck out with the line going through the letters.</td>
                </tr>
                <tr>
                    <td>Subscript/Superscript</td>
                    <td><div class = "icon icon-subscripticon"></div></td>
                    <td>Allows to choose the <b>Superscript</b> or <b>Subscript</b> option. The <b>Superscript</b> option is used to make the text smaller and place it to the upper part of the text line, e.g. as in fractions. The <b>Subscript</b> option is used to make the text smaller and place it to the lower part of the text line, e.g. as in chemical formulas.</td>
                </tr>
                <tr>
					<td>Font color</td>
					<td><div class = "icon icon-fontcolor"></div></td>
					<td>Is used to change the color of the letters/characters in cells.</td>
				</tr>
				<tr>
					<td>Background color</td>
					<td><div class = "icon icon-backgroundcolor"></div></td>
					<td>Is used to change the color of the cell background. Using this icon you can apply a <em>solid color</em> fill. The cell background color can also be changed using the <b>Fill</b> section at the <a href="../UsageInstructions/AddBorders.htm" onclick="onhyperlinkclick(this)">Cell settings</a> tab of the right sidebar.</td>
				</tr>
				<tr>
					<td>Change color scheme</td>
					<td><div class = "icon icon-changecolorscheme"></div></td>
					<td>Is used to change the default color palette for worksheet elements (font, background, chats and chart elements) selecting one of the available ones: <b>Office</b>, <b>Grayscale</b>, <b>Apex</b>, <b>Aspect</b>, <b>Civic</b>, <b>Concourse</b>, <b>Equity</b>, <b>Flow</b>, <b>Foundry</b>, <b>Median</b>, <b>Metro</b>, <b>Module</b>, <b>Odulent</b>, <b>Oriel</b>, <b>Origin</b>, <b>Paper</b>, <b>Solstice</b>, <b>Technic</b>, <b>Trek</b>, <b>Urban</b>, or <b>Verve</b>.</td>
				</tr>
			</table>
			<p><p class="note"><b>Note</b>: it's also possible to apply one of the formatting presets selecting the cell you wish to format and choosing the desired preset from the list at the <b>Home</b> tab of the top toolbar:<br /><br />
				<img alt="Formatting Presets" src="../images/presets.png" />
			   </p>
			<p>To change the font color or use a <em>solid color</em> fill as the cell background,</p>
			<ol>
			<li>select characters/cells with the mouse or the whole worksheet using the <b>Ctrl+A</b> key combination,</li>
				<li>click the corresponding icon at the top toolbar,</li>
				<li>select any color in the available palettes
					<p><img alt="Palette" src="../images/palette.png" /></p>
					<ul>
						<li><b>Theme Colors</b> - the colors that correspond to the selected color scheme of the spreadsheet.</li>
						<li><b>Standard Colors</b> - the default colors set.</li>
						<li><b>Custom Color</b> - click this caption if there is no needed color in the available palettes. Select the necessary colors range moving the vertical color slider and set the specific color dragging the color picker within the large square color field. Once you select a color with the color picker, the appropriate RGB and sRGB color values will be displayed in the fields on the right. You can also specify a color on the base of the RGB color model entering the necessary numeric values into the <b>R</b>, <b>G</b>, <b>B</b> (red, green, blue) fields or enter the sRGB hexadecimal code into the field marked with the <b>#</b> sign. The selected color appears in the <b>New</b> preview box. If the object was previously filled with any custom color, this color is displayed in the <b>Current</b> box so you can compare the original and modified colors. When the color is specified, click the <b>Add</b> button:
						<p><img alt="Palette - Custom Color" src="../../../../../../common/main/resources/help/it/images/palette_custom.png" /></p>
						<p>The custom color will be applied to the selected text/cell and added to the <b>Custom color</b> palette.</p>
						</li>
						</ul>
					</li>
			</ol>
			<p>To clear the background color of a certain cell,</p>
			<ol>
				<li>select a cell, or a range of cells with the mouse or the whole worksheet using the <b>Ctrl+A</b> key combination,</li>
				<li>click the <b>Background color</b> <div class = "icon icon-backgroundcolor"></div> icon at the <b>Home</b> tab of the top toolbar,</li>
				<li>select the <div class = "icon icon-nofill"></div> icon.</li>
			</ol>
		</div>
	</body>
</html>
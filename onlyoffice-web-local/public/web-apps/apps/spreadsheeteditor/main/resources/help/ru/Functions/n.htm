<!DOCTYPE html>
<html>
	<head>
		<title>Функция Ч</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция Ч</h1>
			<p>Функция <b>Ч</b> - это одна из информационных функций. Она преобразует значение в число.</p>
			<p>Синтаксис функции <b>Ч</b>:</p> 
			<p style="text-indent: 150px;"><b><em>Ч(значение)</em></b></p> 
			<p>где <b><em>значение</em></b> - это проверяемое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка. Ниже представлены возможные значения и результат их преобразования:</p>
			<table>
				<tr>
					<td><b>Значение</b></td>
					<td><b>Число</b></td>
				</tr>
				<tr>
					<td>число</td>
					<td>число</td>
				</tr>
				<tr>
					<td>дата</td>
					<td>дата в числовом формате</td>
				</tr>
				<tr>
					<td>логическое значение ИСТИНА</td>
					<td>1</td>
				</tr>
				<tr>
					<td>логическое значение ЛОЖЬ</td>
					<td>0</td>
				</tr>
				<tr>
					<td>ошибка</td>
					<td>значение ошибки</td>
				</tr>
				<tr>
					<td>Другое</td>
					<td>0</td>
				</tr>
			</table>
			<p>Чтобы применить функцию <b>Ч</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Информационные</b>,</li>
			<li>щелкните по функции <b>Ч</b>,</li>
			<li>введите требуемый аргумент,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция Ч" src="../images/n.png" /></p>
		</div>
	</body>
</html>
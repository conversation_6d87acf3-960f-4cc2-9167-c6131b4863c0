<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЧИСТРАБДНИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЧИСТРАБДНИ</h1>
			<p>Функция <b>ЧИСТРАБДНИ</b> - это одна из функций даты и времени. Возвращает количество рабочих дней между двумя датами (начальной и конечной). Выходные и праздничные дни в это число не включаются.</p>
			<p>Синтаксис функции <b>ЧИСТРАБДНИ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЧИСТРАБДНИ(нач_дата;кон_дата;[праздники])</em></b></p> 
			<p>где</p> 
				<p style="text-indent: 50px;"><b><em>нач_дата</em></b> - первая дата периода, введенная с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени.</p>
				<p style="text-indent: 50px;"><b><em>кон_дата</em></b> - последняя дата периода, введенная с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени.</p>
            <p style="text-indent: 50px;"><b><em>праздники</em></b> - необязательный аргумент, указывающий, какие даты, кроме выходных, являются нерабочими. Их можно ввести с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени или указать ссылку на диапазон ячеек, содержащих даты.</p>
			<p>Чтобы применить функцию <b>ЧИСТРАБДНИ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>ЧИСТРАБДНИ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ЧИСТРАБДНИ" src="../images/networkdays.png" /></p>
		</div>
	</body>
</html>
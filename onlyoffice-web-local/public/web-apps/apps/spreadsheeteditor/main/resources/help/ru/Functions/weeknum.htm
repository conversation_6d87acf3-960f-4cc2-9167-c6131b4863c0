<!DOCTYPE html>
<html>
	<head>
		<title>Функция НОМНЕДЕЛИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция НОМНЕДЕЛИ</h1>
			<p>Функция <b>НОМНЕДЕЛИ</b> - это одна из функций даты и времени. Возвращает порядковый номер той недели в течение года, на которую приходится заданная дата.</p>
			<p>Синтаксис функции <b>НОМНЕДЕЛИ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>НОМНЕДЕЛИ(дата_в_числовом_формате;[тип])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>дата_в_числовом_формате</em></b> - число, представляющее дату, по которой определяется неделя, введенное с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени.</p>
			<p style="text-indent: 50px;"><b><em>тип</em></b> - числовое значение, определяющее, какой тип значения должен быть возвращен. Может иметь одно из следующих значений:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Числовое значение</b></td>
					<td><b>Последовательность дней</b></td>
				</tr>
				<tr>
					<td>1 или опущено</td>
					<td>с воскресенья по субботу</td>
				</tr>
				<tr>
					<td>2</td>
					<td>с понедельника по воскресенье</td>
				</tr>
			</table>
			<p>Чтобы применить функцию <b>НОМНЕДЕЛИ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>НОМНЕДЕЛИ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция НОМНЕДЕЛИ" src="../images/weeknum.png" /></p>
		</div>
	</body>
</html>
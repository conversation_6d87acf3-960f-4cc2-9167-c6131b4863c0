<!DOCTYPE html>
<html>
	<head>
		<title>Функция СТЬЮДРАСП</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СТЬЮДРАСП</h1>
			<p>Функция <b>СТЬЮДРАСП</b> - это одна из статистических функций. Возвращает процентные точки (вероятность) для t-распределения Стьюдента, где числовое значение (<b><em>x</em></b>) - это вычисляемое значение t, для которого должны быть вычислены вероятности. T-распределение используется для проверки гипотез при малом объеме выборки. Данную функцию можно использовать вместо таблицы критических значений t-распределения.</p>
			<p>Синтаксис функции <b>СТЬЮДРАСП</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СТЬЮДРАСП(x;степени_свободы;хвосты)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>x</em></b> - значение, для которого вычисляется функция. Числовое значение больше 0.</p>
			<p style="text-indent: 50px;"><b><em>степени_свободы</em></b> - число степеней свободы; целое число большее или равное 1.</p>
			<p style="text-indent: 50px;"><b><em>хвосты</em></b> - числовое значение, определяющее количество возвращаемых хвостов распределения. Если оно равно 1, функция <b>СТЬЮДРАСП</b> возвращает одностороннее распределение. Если оно равно 2, функция <b>СТЬЮДРАСП</b> возвращает двустороннее распределение.</p>
			<p>Эти аргументы можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
 			<p>Чтобы применить функцию <b>СТЬЮДРАСП</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>СТЬЮДРАСП</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СТЬЮДРАСП" src="../images/tdist.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПРЕДСКАЗ.ЕTS.ДОВИНТЕРВАЛ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПРЕДСКАЗ.ЕTS.ДОВИНТЕРВАЛ</h1>
			<p>Функция <b>ПРЕДСКАЗ.ЕTS.ДОВИНТЕРВАЛ</b> - это одна из статистических функций. Возвращает доверительный интервал для прогнозной величины на указанную дату.</p>
			<p>Синтаксис функции <b>ПРЕДСКАЗ.ЕTS.ДОВИНТЕРВАЛ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ПРЕДСКАЗ.ЕTS.ДОВИНТЕРВАЛ(целевая_дата;значения;временная_шкала;[вероятность];[сезонность];[заполнение_данных];[агрегирование])</em></b></p> 
			<p><em>где</em></p>
				<p style="text-indent: 50px;"><b><em>целевая_дата</em></b> - дата, для которой предсказывается новое значение. Должна быть позже, чем последняя дата диапазона <b><em>временная_шкала</em></b>.</p>
				<p style="text-indent: 50px;"><b><em>значения</em></b> - диапазон ретроспективных данных, на основе которых прогнозируется новое значение.</p>
                <p style="text-indent: 50px;"><b><em>временная_шкала</em></b> - диапазон значений даты/времени, которые соответствуют ретроспективным данным. Диапазон <b><em>временная_шкала</em></b> должен быть такого же размера, что и <b><em>значения</em></b>. Значения даты/времени должны отстоять друг от друга на одинаковый интервал (хотя функция может обработать до 30% отсутствующих значений в соответствии с указанным значением аргумента <b><em>заполнение_данных</em></b> и агрегировать повторяющиеся значения в соответствии с указанным значением аргумента <b><em>агрегирование</em></b>).</p>
                <p style="text-indent: 50px;"><b><em>вероятность</em></b> - числовое значение от 0 до 1 (не включая эти числа), определяющее степень достоверности для расчета доверительного интервала. Это необязательный аргумент. Если он опущен, используется значение по умолчанию 0.95.</p>
                <p style="text-indent: 50px;"><b><em>сезонность</em></b> - числовое значение, указывающее, какой метод должен использоваться для определения сезонности. Это необязательный аргумент. Допустимые значения приведены в таблице ниже.</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Числовое значение</b></td>
                    <td><b>Поведение</b></td>
                </tr>
                <tr>
                    <td>1 или опущено</td>
                    <td>Сезонность определяется автоматически. В качестве длины сезонного шаблона используются положительные целые числа.</td>
                </tr>
                <tr>
                    <td>0</td>
                    <td>Фактор сезонности не используется, прогноз будет линейным.</td>
                </tr>
                <tr>
                    <td>целое число, большее или равное 2</td>
                    <td>В качестве длины сезонного шаблона используется указанное число.</td>
                </tr>
            </table>
                <p style="text-indent: 50px;"><b><em>заполнение_данных</em></b> - числовое значение, указывающее, как обрабатывать отсутствующие данные в диапазоне <b><em>временная_шкала</em></b>. Это необязательный аргумент. Допустимые значения приведены в таблице ниже.</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Числовое значение</b></td>
                    <td><b>Поведение</b></td>
                </tr>
                <tr>
                    <td>1 или опущено</td>
                    <td>Отсутствующие значения вычисляются как среднее между соседними точками.</td>
                </tr>
                <tr>
                    <td>0</td>
                    <td>Отсутствующие значения рассматриваются как нулевые.</td>
                </tr>
            </table>
            <p style="text-indent: 50px;"><b><em>агрегирование</em></b> - числовое значение, указывающее, с помощью какой функции надо агрегировать одинаковые значения времени в диапазоне <b><em>временная_шкала</em></b>. Это необязательный аргумент. Допустимые значения приведены в таблице ниже.</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Числовое значение</b></td>
                    <td><b>Функция</b></td>
                </tr>
                <tr>
                    <td>1 или опущено</td>
                    <td><a href="../Functions/average.htm" onclick="onhyperlinkclick(this)">СРЗНАЧ</a></td>
                </tr>
                <tr>
                    <td>2</td>
                    <td><a href="../Functions/count.htm" onclick="onhyperlinkclick(this)">СЧЁТ</a></td>
                </tr>
                <tr>
                    <td>3</td>
                    <td><a href="../Functions/counta.htm" onclick="onhyperlinkclick(this)">СЧЁТЗ</a></td>
                </tr>
                <tr>
                    <td>4</td>
                    <td><a href="../Functions/max.htm" onclick="onhyperlinkclick(this)">МАКС</a></td>
                </tr>
                <tr>
                    <td>5</td>
                    <td><a href="../Functions/median.htm" onclick="onhyperlinkclick(this)">МЕДИАНА</a></td>
                </tr>
                <tr>
                    <td>6</td>
                    <td><a href="../Functions/min.htm" onclick="onhyperlinkclick(this)">МИН</a></td>
                </tr>
                <tr>
                    <td>7</td>
                    <td><a href="../Functions/sum.htm" onclick="onhyperlinkclick(this)">СУММ</a></td>
                </tr>
            </table> 
			<p>Чтобы применить функцию <b>ПРЕДСКАЗ.ЕTS.ДОВИНТЕРВАЛ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>ПРЕДСКАЗ.ЕTS.ДОВИНТЕРВАЛ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ПРЕДСКАЗ.ЕTS.ДОВИНТЕРВАЛ" src="../images/forecast-ets-confint.png" /></p>
		</div>
	</body>
</html>
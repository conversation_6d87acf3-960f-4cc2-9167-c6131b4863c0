<!DOCTYPE html>
<html>
	<head>
		<title>Защита электронной таблицы</title>
		<meta charset="utf-8" />
        <meta name="description" content="Предотвратите несанкционированный доступ путем шифрования и защиты рабочей книги или листов" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Защита электронной таблицы</h1>
            <p><a href="https://www.onlyoffice.com/ru/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактор электронных таблиц</b></a> предоставляет возможность защитить электронную таблицу, когда вы хотите ограничить доступ или возможности редактирования для других пользователей. <b>Редактор электронных таблиц</b> предлагает различные уровни защиты для управления доступом к файлу и возможностями редактирования внутри книги и листах. Используйте вкладку <b>Защита</b>, чтобы настраивать доступные параметры защиты по своему усмотрению.</p>
            <p><img alt="Вкладка Защита" src="../images/interface/protectiontab.png" /></p>
            <p>Доступные варианты защиты:</p>
            <p><a href="../UsageInstructions/Password.htm" onclick="onhyperlinkclick(this)"><b>Шифрование</b></a> - для управления доступом к файлу и предотвращения его открытия другими пользователями.</p>
            <p><a href="../UsageInstructions/ProtectWorkbook.htm#delimiteddata" onclick="onhyperlinkclick(this)"><b>Защита книги</b></a> - для контроля над действиями пользователей с книгой и предотвращения нежелательных изменений в структуре книги.</p>
            <p><a href="../UsageInstructions/ProtectSheet.htm" onclick="onhyperlinkclick(this)"><b>Защита листа</b></a> - для контроля над действиями пользователей в листе и предотвращения нежелательных изменений данных.</p>
            <p><a href="../UsageInstructions/AllowEditRanges.htm" onclick="onhyperlinkclick(this)"><b>Разрешение редактировать диапазоны</b></a> - для указания диапазона ячеек, с которыми пользователь может работать на защищенном листе.</p>
            <p>Использование флажков на вкладке <b>Защита</b> для быстрой блокировки или разблокировки содержимого защищенного листа.</p>
            <p class="note"><b>Примечание</b>: эти опции не вступят в силу, пока вы не включите защиту листа.</p>
            <p>Ячейки, фигуры и текст внутри фигуры заблокированы на листе по умолчанию. Снимите соответствующий флажок, чтобы разблокировать их. Разблокированные объекты по-прежнему можно редактировать, когда лист защищен. Параметры <b>Заблокированная фигура</b> и <b>Заблокировать текст</b> становятся активными при выборе фигуры. Параметр <b>Заблокированная фигура</b> применяется как к фигурам, так и к другим объектам, таким как диаграммы, изображения и текстовые поля. Параметр <b>Заблокировать текст</b> блокирует текст внутри всех графических объектов, кроме диаграмм.</p>
            <p>Установите флажок напротив параметра <b>Скрытые формулы</b>, чтобы скрыть формулы в выбранном диапазоне или ячейке, когда лист защищен. Скрытая формула не будет отображаться в строке формул при нажатии на ячейку.<p>
        </div>
	</body>
</html>

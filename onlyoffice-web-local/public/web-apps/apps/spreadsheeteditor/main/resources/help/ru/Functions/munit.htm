<!DOCTYPE html>
<html>
	<head>
		<title>Функция МЕДИН</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Функция МЕДИН</h1>
			<p>Функция <b>МЕДИН</b> - одна из математических и тригонометрических функций. Она возващает матрицу единиц для указанного измерения.</p>
			<p>Синтаксис функции <b>МЕДИН</b>:</p>
			<p style="text-indent: 150px;"><b><em>МЕДИН(измерение)</em></b></p>
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>измерение</em></b> - это обязательный аргумет. Это целое число, определяющее размер единичной матрицы, которую вы хотите вернуть в виде массива. Измерение должно быть больше нуля.</p>
			<p>Чтобы применить функцию <b>МЕДИН</b>,</p>
			<ol>
				<li>выделите ячейку, в которой требуется отобразить результат,</li>
				<li>
					щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
					<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
					<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
				</li>
				<li>выберите из списка группу функций <b>Математические и тригонометрические</b>,</li>
				<li>щелкните по функции <b>МЕДИН</b>,</li>
				<li>введите требуемый аргумент,</li>
				<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p class="note">Чтобы вернуть диапазон значений, выберите необходимый диапазон ячеек, введите формулу и нажмите комбинацию клавиш <b>Ctrl + Shift + Enter</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция МЕДИН" src="../images/munit.png" /></p>
		</div>
	</body>
</html>
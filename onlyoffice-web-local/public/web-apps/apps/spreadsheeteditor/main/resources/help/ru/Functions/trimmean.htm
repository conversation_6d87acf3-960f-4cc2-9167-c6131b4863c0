<!DOCTYPE html>
<html>
	<head>
		<title>Функция УРЕЗСРЕДНЕЕ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция УРЕЗСРЕДНЕЕ</h1>
            <p>Функция <b>УРЕЗСРЕДНЕЕ</b> - это одна из статистических функций. Возвращает среднее внутренности множества данных. Функция <b>УРЕЗСРЕДНЕЕ</b> вычисляет среднее, отбрасывания заданный процент данных с экстремальными значениями.</p>
            <p>Синтаксис функции <b>УРЕЗСРЕДНЕЕ</b>:</p>
            <p style="text-indent: 150px;"><b><em>УРЕЗСРЕДНЕЕ(массив;доля)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>массив</em></b> - диапазон усекаемых и усредняемых числовых значений.</p>
            <p style="text-indent: 50px;"><b><em>доля</em></b> - доля точек данных, исключаемых из вычислений. Числовое значение, большее или равное 0, но меньшее, чем 1. Количество исключаемых точек данных округляется в меньшую сторону до ближайшего целого, кратного 2. Например, если аргумент <b><em>массив</em></b> содержит 30 значений, а аргумент <b><em>доля</em></b> равен 0.1, то 10 процентов от 30 точек равняется 3. Это значение округляется в меньшую сторону до 2, следовательно, исключается по одному значению с каждого конца множества: 1 с начала и 1 с конца множества. </p>
            <p>Эти значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
            <p>Чтобы применить функцию <b>УРЕЗСРЕДНЕЕ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>УРЕЗСРЕДНЕЕ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой или выделите мышью диапазон ячеек,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция УРЕЗСРЕДНЕЕ" src="../images/trimmean.png" /></p>
        </div>
	</body>
</html>
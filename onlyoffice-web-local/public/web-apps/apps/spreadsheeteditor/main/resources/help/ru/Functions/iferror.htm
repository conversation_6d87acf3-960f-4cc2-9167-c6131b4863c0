<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЕСЛИОШИБКА</title>
		<meta charset="utf-8" />
        <meta name="description" content="Функция ЕСЛИОШИБКА - это одна из логических функций. Узнайте, как использовать формулу ЕСЛИОШИБКА в таблицах Excel и совместимых файлах с помощью ONLYOFFICE." />
        <meta name="keywords" content="еслиошибка, еслиошибка excel, excel еслиошибка, функция еслиошибка, эксель еслиошибка">
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЕСЛИОШИБКА</h1>
			<p>Функция <b>ЕСЛИОШИБКА</b> - это одна из логических функций. Используется для проверки формулы на наличие ошибок в первом аргументе. Функция возвращает результат формулы, если ошибки нет, или определенное значение, если она есть.</p>
			<p>Синтаксис функции <b>ЕСЛИОШИБКА</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЕСЛИОШИБКА(значение;значение_если_ошибка)</em></b></p> 
			<p>где <b><em>значение</em></b> и <b><em>значение_если_ошибка</em></b> - это значения, введенные вручную или находящиеся в ячейке, на которую дается ссылка.</p>
			<h2>Как работает функция ЕСЛИОШИБКА</h2>
            <p>Чтобы применить функцию <b>ЕСЛИОШИБКА</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Логические</b>,</li>
			<li>щелкните по функции <b>ЕСЛИОШИБКА</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><em>Например:</em></p>
			<p>Здесь два аргумента: <em>значение</em> = <b>A1/B1</b>, <em>значение_если_ошибка</em> = <b>"ошибка"</b>, где <b>A1</b> имеет значение <b>12</b>, <b>B1</b> имеет значение <b>3</b>. Данная формула не содержит ошибок в первом аргументе. Следовательно, функция возвращает результат вычисления. Если изменить значение <b>B1</b> с <b>3</b> на <b>0</b>, то, поскольку на ноль делить нельзя, функция возвращает значение <b>ошибка</b>.</p>
			<p><img alt="Функция ЕСЛИОШИБКА" src="../images/iferror.gif" /></p>
		</div>
	</body>
</html>
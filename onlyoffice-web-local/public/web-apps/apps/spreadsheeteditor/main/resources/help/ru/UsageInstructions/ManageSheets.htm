<!DOCTYPE html>
<html>
	<head>
		<title>Управление листами</title>
		<meta charset="utf-8" />
		<meta name="description" content="Управляйте листами в онлайн-редакторе электронных таблиц: вставляйте, удаляйте, переименовывайте, копируйте и перемещайте." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Управление листами</h1>
			<p>По умолчанию новая созданная электронная таблица содержит один лист. Проще всего добавить новый лист, нажав на кнопку <span class="icon icon-plus"></span> справа от кнопок <b>Навигации по листам</b> в левом нижнем углу.</p>
			<p>Есть другой способ <b>добавления нового листа</b>:</p>
			<ol>
				<li>щелкните правой кнопкой мыши по ярлычку листа, после которого требуется вставить новый лист,</li>
				<li>выберите из контекстного меню команду <b>Вставить</b>.</li>
			</ol>
			<p>Новый лист будет вставлен после выбранного.</p>
			<p><b>Для активации нужного листа</b> используйте ярлычки листов, расположенные в левом нижнем углу каждой электронной таблицы.</p>
			<p class="note"><b>Примечание</b>: если электронная таблица содержит много листов, для поиска нужного воспользуйтесь кнопками <b>Навигации по листам</b>, расположенными в левом нижнем углу.</p>
			<p><b>Для удаления ненужного листа:</b></p>
			<ol>
				<li>щелкните правой кнопкой мыши по ярлычку листа, который требуется удалить,</li>
				<li>выберите из контекстного меню команду <b>Удалить</b>.</li>
			</ol>
			<p>Выбранный лист будет удален из текущей электронной таблицы.</p>
			<p><b>Для переименования существующего листа:</b></p>
			<ol>
				<li>щелкните правой кнопкой мыши по ярлычку листа, который требуется переименовать,</li>
				<li>выберите из контекстного меню команду <b>Переименовать</b>,</li>
				<li>в диалоговом окне введите <b>Название листа</b> и нажмите на кнопку <b>OK</b>.</li>
			</ol>
			<p>Имя выбранного листа будет изменено.</p>
			<p><b>Для копирования существующего листа:</b></p>
			<ol>
				<li>щелкните правой кнопкой мыши по ярлычку листа, который требуется скопировать,</li>
				<li>выберите из контекстного меню команду <b>Копировать</b>,</li>
				<li>выберите лист, перед которым требуется вставить скопированный, или используйте опцию <b>Скопировать в конец</b>, чтобы вставить скопированный лист после всех имеющихся,</li>
				<li>нажмите на кнопку <b>OK</b>, чтобы подтвердить выбор.</li>
			</ol>
			<p>Выбранный лист будет скопирован и вставлен в выбранное место.</p>
			<p><b>Для перемещения существующего листа:</b></p>
			<ol>
				<li>щелкните правой кнопкой мыши по ярлычку листа, который требуется переместить,</li>
				<li>выберите из контекстного меню команду <b>Переместить</b>,</li>
				<li>выберите лист, перед которым требуется вставить выбранный, или используйте опцию <b>Переместить в конец</b>, чтобы вставить выбранный лист после всех имеющихся,</li>
				<li>нажмите на кнопку <b>OK</b>, чтобы подтвердить выбор.</li>
			</ol>
			<p>Вы также можете вручную <b>Перетащить</b> лист из одной книги в другую. Для этого выберите лист, который хотите переместить, и перетащите его на панель листов другой книги. Например, вы можете перетащить лист из книги онлайн-редактора в десктопный редактор:</p>
			<p><img alt="Перетащить лист gif" src="../images/move_sheet.gif" /></p>
			<p>В этом случае лист из исходной электронной таблицы будет <b>удален</b>.</p>
            <p class="note">В настоящее время нельзя перетаскивать листы в новую книгу в десктопных редакторах на Linux.</p>
			<p>Если электронная таблица содержит много листов, то, чтобы упростить работу, можно скрыть некоторые из них, ненужные в данный момент. Для этого:</p>
			<ol>
				<li>щелкните правой кнопкой мыши по ярлычку листа, который требуется скрыть,</li>
				<li>выберите из контекстного меню команду <b>Скрыть</b>,</li>
			</ol>
			<p><b>Для отображения скрытого листа</b> щелкните правой кнопкой мыши по любому ярлычку листа, откройте список <b>Скрытый</b> и выберите лист, который требуется отобразить.</p>
			<p>Чтобы легко различать листы, можно <b>присвоить ярлычкам листов разные цвета</b>. Для этого:</p>
			<ol>
				<li>щелкните правой кнопкой мыши по нужному ярлычку листа,</li>
				<li>выберите из контекстного меню пункт <b>Цвет ярлычка</b>,</li>
				<li>
					выберите любой цвет на доступных палитрах
					<p><img alt="Палитра" src="../images/palette.png" /></p>
					<ul>
						<li><b>Цвета темы</b> - цвета, соответствующие выбранной цветовой схеме электронной таблицы.</li>
						<li><b>Стандартные цвета</b> - набор стандартных цветов.</li>
						<li>
							<b>Пользовательский цвет</b> - щелкните по этой надписи, если в доступных палитрах нет нужного цвета. Выберите нужный цветовой диапазон, перемещая вертикальный ползунок цвета, и определите конкретный цвет, перетаскивая инструмент для выбора цвета внутри большого квадратного цветового поля. Как только Вы выберете какой-то цвет, в полях справа отобразятся соответствующие цветовые значения RGB и sRGB. Также можно задать цвет на базе цветовой модели RGB, введя нужные числовые значения в полях <b>R</b>, <b>G</b>, <b>B</b> (красный, зеленый, синий), или указать шестнадцатеричный код sRGB в поле, отмеченном знаком <b>#</b>. Выбранный цвет появится в окне предварительного просмотра <b>Новый</b>. Если к объекту был ранее применен какой-то пользовательский цвет, этот цвет отображается в окне <b>Текущий</b>, так что вы можете сравнить исходный и измененный цвета. Когда цвет будет задан, нажмите на кнопку <b>Добавить</b>:
							<p><img alt="Палитра - Пользовательский цвет" src="../../../../../../common/main/resources/help/ru/images/palette_custom.png" /></p>
							<p>Пользовательский цвет будет применен к выбранному ярлычку и добавлен в палитру <b>Пользовательский цвет</b>.</p>
						</li>
					</ul>
				</li>
			</ol>
			<p>Вы можете <b>работать с несколькими листами одновременно</b>:</p>
			<ol>
				<li>выделите первый лист, который требуется включить в группу,</li>
				<li>нажмите и удерживайте клавишу <em>Shift</em>, чтобы выделить несколько смежных листов, которые требуется сгруппировать, или используйте клавишу <em>Ctrl</em>, чтобы выделить несколько несмежных листов, которые требуется сгруппировать,</li>
				<li>щелкните правой кнопкой мыши по ярлычку одного из выделенных листов, чтобы открыть контекстное меню,</li>
				<li>
					выберите нужный пункт меню:
					<p><img alt="Управление несколькими листами" src="../images/severalsheets.png" /></p>
					<ul>
						<li><b>Вставить</b> - чтобы вставить такое же количество новых пустых листов, которое содержится в выделенной группе,</li>
						<li><b>Удалить</b> - чтобы удалить все выделенные листы одновременно (нельзя удалить все листы в рабочей книге, так как она должна содержать хотя бы один видимый лист),</li>
						<li><b>Переименовать</b> - эту опцию можно применить только к каждому отдельно взятому листу,</li>
						<li><b>Копировать</b> - чтобы создать копии всех выделенных листов одновременно и вставить их в выбранное место,</li>
						<li><b>Переместить</b> - чтобы переместить все выделенные листы одновременно и вставить их в выбранное место,</li>
						<li><b>Скрыть</b> - чтобы скрыть все выделенные листы одновременно (нельзя скрыть все листы в рабочей книге, так как она должна содержать хотя бы один видимый лист),</li>
						<li><b>Цвет ярлычка</b> - чтобы присвоить один и тот же цвет всем ярлычкам выделенных листов одновременно,</li>
						<li><b>Выделить все листы</b> - чтобы выделить все листы в текущей рабочей книге,</li>
						<li>
							<b>Разгруппировать листы</b> - чтобы разгруппировать выделенные листы.
							<p class="note">разгруппировать листы также можно, дважды щелкнув по листу, включенному в группу, или щелкнув по любому листу, не включенному в группу.</p>
						</li>
					</ul>
				</li>
			</ol>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция НД</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция НД</h1>
			<p>Функция <b>НД</b> - это одна из информационных функций. Она возвращает значение ошибки #Н/Д. Эта функция не требует аргумента.</p>
			<p>Синтаксис функции <b>НД</b>:</p> 
			<p style="text-indent: 150px;"><b><em>НД()</em></b></p> 
			<p>Чтобы применить функцию <b>НД</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Информационные</b>,</li>
			<li>щелкните по функции <b>НД</b>,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция НД" src="../images/na.png" /></p>
		</div>
	</body>
</html>
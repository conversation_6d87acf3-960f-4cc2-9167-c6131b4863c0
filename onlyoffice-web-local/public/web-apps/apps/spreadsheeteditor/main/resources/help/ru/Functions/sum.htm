<!DOCTYPE html>
<html>
	<head>
		<title>Функция СУММ</title>
		<meta charset="utf-8" />
        <meta name="description" content="Функция СУММ - это одна из математических и тригонометрических функций, которая возвращает результат сложения всех чисел в выбранном диапазоне ячеек." />
        <meta name="keywords" content="сумм, формула суммы, формула суммы в эксель, сумм excel, сумм excel, формула суммы в excel, сумма эксель, excel сумма, функция сумм в excel, для чего используется функция сумм, для чего используется функция сумм, функция сумм в excel, функция суммы в эксель, эксель сумм, функция сложения в excel, как в экселе сделать формулу сумма, эксель сумм, формула сумма эксель, функция сумм, excel формула суммы, функция суммы в excel">
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СУММ</h1>
			<p>Функция <b>СУММ</b> - это одна из математических и тригонометрических функций. Возвращает результат сложения всех чисел в выбранном диапазоне ячеек.</p>
			<p>Синтаксис функции <b>СУММ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СУММ(список_аргументов)</em></b></p> 
			<p>где <b><em>список_аргументов</em></b> - это ряд числовых значений, введенных вручную или находящихся в ячейках, на которые дается ссылка.</p>
			<h2>Как работает функция СУММ</h2>
            <p>Чтобы применить функцию <b>СУММ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>, или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул и выберите функцию <b>СУММ</b> из группы функций <b>Математические</b>,</li>
			<li>введите требуемые аргументы через точку с запятой или выделите мышью диапазон ячеек,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><div class = "smb smb-sum"></div></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция СТРОЧН</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СТРОЧН</h1>
			<p>Функция <b>СТРОЧН</b> - это одна из функций для работы с текстом и данными. Используется для преобразования букв в выбранной ячейке из верхнего регистра в нижний.</p>
			<p>Синтаксис функции <b>СТРОЧН</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СТРОЧН(текст)</em></b></p> 
			<p>где <b><em>текст</em></b> - это данные, находящиеся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>СТРОЧН</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>СТРОЧН</b>,</li>
			<li>введите требуемый аргумент,
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СТРОЧН" src="../images/lower.png" /></p>
		</div>
	</body>
</html>
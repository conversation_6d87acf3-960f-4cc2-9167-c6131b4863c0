<!DOCTYPE html>
<html>
	<head>
		<title>Функция Ф.ТЕКСТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция Ф.ТЕКСТ</h1>
			<p>Функция <b>Ф.ТЕКСТ</b> - это одна из поисковых функций. Возвращает формулу в виде строки (то есть текстовой строки, отображаемой в строке формул при выборе ячейки, содержащей формулу).</p>
			<p>Синтаксис функции <b>Ф.ТЕКСТ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>Ф.ТЕКСТ(ссылка)</em></b></p> 
			<p>где <b><em>ссылка</em></b> - это ссылка на ячейку или диапазон ячеек.</p>
            <p>Если диапазон ячеек, на который дается ссылка, содержит несколько формул, функция <b>Ф.ТЕКСТ</b> возвращает значение левой верхней ячейки диапазона. Если диапазон ячеек, на который дается ссылка, не содержит формул, функция <b>Ф.ТЕКСТ</b> возвращает значение ошибки <b>Н/Д</b>.</p>
			<p>Чтобы применить функцию <b>Ф.ТЕКСТ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
                <li>щелкните по функции <b>Ф.ТЕКСТ</b>,</li>
                <li>введите требуемый аргумент,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция Ф.ТЕКСТ" src="../images/formulatext.png" /></p>
		</div>
	</body>
</html>
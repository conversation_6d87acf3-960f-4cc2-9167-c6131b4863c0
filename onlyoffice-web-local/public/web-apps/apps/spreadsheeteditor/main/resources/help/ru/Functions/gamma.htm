<!DOCTYPE html>
<html>
	<head>
		<title>Функция ГАММА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ГАММА</h1>
			<p>Функция <b>ГАММА</b> - это одна из статистических функций. Возвращает значение гамма-функции.</p>
			<p>Синтаксис функции <b>ГАММА</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ГАММА(число)</em></b></p> 
			<p>где <b><em>число</em></b> - числовое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
            <p class="note"><b>Примечание:</b> если аргумент <b><em>число</em></b> - отрицательное целое число или 0, функция возвращает значение ошибки <b>#ЧИСЛО!</b>. </p> 
			<p>Чтобы применить функцию <b>ГАММА</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>ГАММА</b>,</li>
                <li>введите требуемый аргумент,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><div class = "smb smb-gamma"></div></p>
		</div>
	</body>
</html>
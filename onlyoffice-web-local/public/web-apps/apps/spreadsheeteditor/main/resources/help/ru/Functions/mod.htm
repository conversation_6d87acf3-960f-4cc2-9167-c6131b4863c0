<!DOCTYPE html>
<html>
	<head>
		<title>Функция ОСТАТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ОСТАТ</h1>
			<p>Функция <b>ОСТАТ</b> - это одна из математических и тригонометрических функций. Она возвращает остаток от деления числа на заданный делитель.</p>
			<p>Синтаксис функции <b>ОСТАТ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ОСТАТ(x;y)</em></b></p> 
			<p>где</p>
				<p style="text-indent: 50px;"><b><em>x</em></b> - число, которое требуется разделить и получить остаток.</p> 
				<p style="text-indent: 50px;"><b><em>y</em></b> - число, на которое требуется разделить.</p> 
			<p>Эти числовые значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p class="note"><b>Примечание</b>: если <b>y</b> равен <b>0</b>, функция возвращает ошибку <b>#ДЕЛ/0!</b>.</p> 
			<p>Чтобы применить функцию <b>ОСТАТ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>ОСТАТ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ОСТАТ" src="../images/mod.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ГПР</title>
		<meta charset="utf-8" />
        <meta name="description" content="Функция ГПР - это одна из поисковых функций. Узнайте, как использовать формулу ГПР в таблицах Excel и совместимых файлах с помощью ONLYOFFICE." />
        <meta name="keywords" content="гпр, гпр excel, функция гпр, гпр функция, эксель гпр, формула гпр пример, гпр расшифровка, гпр эксель, формула гпр, гпр excel пример, гпр в excel, гпр пример, функция гпр в excel, функция гпр эксель, как работает функция гпр">
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ГПР</h1>
			<p>Функция <b>ГПР</b> - это одна из поисковых функций. Она используется для выполнения горизонтального поиска значения в верхней строке таблицы или массива и возвращает значение, которое находится в том же самом столбце в строке с заданным номером.</p>
			<p>Синтаксис функции <b>ГПР</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ГПР(искомое_значение;таблица;номер_строки;[интервальный_просмотр])</em></b></p> 
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>искомое_значение</em></b> - это значение, которое необходимо найти,</p>
			<p style="text-indent: 50px;"><b><em>таблица</em></b> - это две или более строки с данными, отсортированными в порядке возрастания,</p>
			<p style="text-indent: 50px;"><b><em>номер_строки</em></b> - это номер строки в том же самом столбце <b><em>таблицы</em></b>; числовое значение, большее или равное 1, но меньшее, чем количество строк в <b>таблице</b>,</p>
			<p style="text-indent: 50px;"><b><em>интервальный_просмотр</em></b> - необязательный аргумент. Это логическое значение: ИСТИНА или ЛОЖЬ. Введите значение ЛОЖЬ для поиска точного соответствия. Введите значение ИСТИНА для поиска приблизительного соответствия, в этом случае при отсутствии значения, строго соответствующего <b><em>искомому значению</em></b>, функция выбирает следующее наибольшее значение, которое меньше, чем <b><em>искомое значение</em></b>. Если этот аргумент отсутствует, функция находит приблизительное соответствие.</p>
			<p class="note"><b>Примечание</b>: если аргумент <b><em>интервальный_просмотр</em></b> имеет значение ЛОЖЬ, но точное соответствие не найдено, функция возвращает ошибку <b>#Н/Д</b>.</p>
			<h2>Как работает функция ГПР</h2>
            <p>Чтобы применить функцию <b>ГПР</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
			<li>щелкните по функции <b>ГПР</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><img alt="Функция ГПР" src="../images/hlookup.gif" /></p>
		</div>
	</body>
</html>
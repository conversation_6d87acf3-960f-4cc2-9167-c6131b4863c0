<!DOCTYPE html>
<html>
	<head>
		<title>Функция ТИП.ОШИБКИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ТИП.ОШИБКИ</h1>
			<p>Функция <b>ТИП.ОШИБКИ</b> - это одна из информационных функций. Она возвращает числовое представление одной из существующих ошибок. </p>
			<p>Синтаксис функции <b>ТИП.ОШИБКИ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ТИП.ОШИБКИ(значение)</em></b></p> 
			<p>где <b><em>значение</em></b> - это значение ошибки, введенное вручную или находящееся в ячейке, на которую дается ссылка. Возможно одно из следующих значений ошибок:</p>
			<table>
				<tr>
					<td><b>Значение ошибки</b></td>
					<td><b>Числовое представление</b></td>
				</tr>
				<tr>
					<td>#ПУСТО!</td>
					<td>1</td>
				</tr>
				<tr>
					<td>#ДЕЛ/0!</td>
					<td>2</td>
				</tr>
				<tr>
					<td>#ЗНАЧ!</td>
					<td>3</td>
				</tr>
				<tr>
					<td>#ССЫЛКА!</td>
					<td>4</td>
				</tr>
				<tr>
					<td>#ИМЯ?</td>
					<td>5</td>
				</tr>
				<tr>
					<td>#ЧИСЛО!</td>
					<td>6</td>
				</tr>
				<tr>
					<td>#Н/Д</td>
					<td>7</td>
				</tr>
				<tr>
					<td>#ОЖИДАНИЕ_ДАННЫХ</td>
					<td>8</td>
				</tr>
				<tr>
					<td>Другое</td>
					<td>#Н/Д</td>
				</tr>
			</table>
			<p>Чтобы применить функцию <b>ТИП.ОШИБКИ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Информационные</b>,</li>
			<li>щелкните по функции <b>ТИП.ОШИБКИ</b>,</li>
			<li>введите требуемый аргумент,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ТИП.ОШИБКИ" src="../images/error.type.png" /></p>
		</div>
	</body>
</html>
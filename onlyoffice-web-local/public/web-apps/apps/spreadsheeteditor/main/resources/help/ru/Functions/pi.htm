<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПИ</h1>
			<p>Функция <b>ПИ</b> - это одна из математических и тригонометрических функций. Функция возвращает математическую константу <b>пи</b>, равную <b>3,14159265358979</b>. Функция <b>не</b> требует аргумента.</p>
			<p>Синтаксис функции <b>ПИ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ПИ()</em></b></p> 
			<p>Чтобы применить функцию <b>ПИ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>ПИ</b>,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><div class = "smb smb-pi"></div></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Дополнительные параметры редактора электронных таблиц</title>
		<meta charset="utf-8" />
        <meta name="description" content="Дополнительные параметры редактора электронных таблиц" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
		<h1>Дополнительные параметры редактора электронных таблиц</h1>
			<p>Вы можете изменить дополнительные параметры редактора электронных таблиц. Для перехода к ним откройте вкладку <b>Файл</b> на верхней панели инструментов и выберите опцию <b>Дополнительные параметры</b>.</p>
			<p>Дополнительные параметры сгруппированы следующим образом:</p>
            <h3>Редактирование и сохранение</h3>
            <ol>
                <li><span class="onlineDocumentFeatures"><b>Автосохранение</b> - используется в <em>онлайн-версии</em> для включения/отключения опции автоматического сохранения изменений, внесенных при редактировании.</span></li>
                <li><span class="desktopDocumentFeatures"><b>Автовосстановление</b> - используется в <em>десктопной версии</em> для включения/отключения опции автоматического восстановления электронной таблицы в случае непредвиденного закрытия программы.</span></li>
                <li><b>Показывать кнопку Параметры вставки при вставке содержимого</b>. Соответствующая кнопка будет появляться при вставке содержимого в электронную таблицу.</li>
            </ol>

            <h3>Совместная работа</h3>
            <ol>
                <li class="onlineDocumentFeatures">
                    Подраздел <b>Режим совместного редактирования</b> позволяет задать предпочтительный режим просмотра изменений, вносимых в электронную таблицу при совместной работе.
                    <ul>
                        <li><b>Быстрый</b> (по умолчанию). Пользователи, участвующие в совместном редактировании электронной таблицы, будут видеть изменения в реальном времени, как только они внесены другими пользователями.</li>
                        <li><b>Строгий</b>. Все изменения, внесенные участниками совместной работы, будут отображаться только после того, как вы нажмете на кнопку <b>Сохранить</b> <div class="icon icon-saveupdate"></div> с оповещением о наличии новых изменений.</li>
                    </ul>
                </li>
                <li><b>Показывать изменения других пользователей</b>. Эта функция позволяет видеть изменения, которые вносят другие пользователи, в электонной таблице, открытой только на просмотр, в <b>Режиме просмотра в реальном времени</b>.</li>
                <li><b>Показывать комментарии в тексте</b>. Если отключить эту функцию, ячейки, к которым добавлены комментарии, будут помечаться на листе, только когда Вы нажмете на значок <b>Комментарии</b> <div class="icon icon-commentsicon"></div> на левой боковой панели.</li>
                <li><b>Показывать решенные комментарии</b>. Эта функция отключена по умолчанию, чтобы решенные комментарии были скрыты на листе. Просмотреть такие комментарии можно только при нажатии на значок <b>Комментарии</b> <div class="icon icon-commentsicon"></div> на левой боковой панели. Включите эту опцию, если требуется отображать решенные комментарии на листе.</li>
            </ol>

            <h3>Рабочая область</h3>
            <ol>
                <li>
                    Опция <b>Стиль ссылок R1C1</b> используется для включения/отключения <b>стиля ссылок R1C1</b>. По умолчанию эта опция отключена, и используется <b>стиль ссылок A1</b>.
                    <p>Когда используется <b>стиль ссылок A1</b>, столбцы обозначаются буквами, а строки - цифрами. Если выделить ячейку, расположенную в строке 3 и столбце 2, ее адрес, отображаемый в поле слева от строки формул, выглядит следующим образом: <b>B3</b>. Если включен <b>стиль ссылок R1C1</b>, и строки, и столбцы обозначаются числами. Если выделить ячейку на пересечении строки 3  и столбца 2, ее адрес будет выглядеть так: <b>R3C2</b>. Буква R указывает на номер строки, а буква C - на номер столбца.</p>
                    <p><img alt="Активная ячейка" src="../images/activecell.png" /></p>
                    <p>Если ссылаться на другие ячейки, используя <b>стиль ссылок R1C1</b>, ссылка на целевую ячейку формируется на базе расстояния от активной ячейки. Например, если выделить ячейку, расположенную в строке 5 и столбце 1, и ссылаться на ячейку в строке 3 и столбце 2, ссылка будет такой: <b>R[-2]C[1]</b>. Числа в квадратных скобках обозначают позицию ячейки, на которую дается ссылка, относительно позиции текущей ячейки, то есть, целевая ячейка находится на 2 строки выше и на 1 столбец правее от активной ячейки. Если выделить ячейку, расположенную в строке 1 и столбце 2, и ссылаться на ту же самую ячейку в строке 3 и столбце 2, ссылка будет такой: <b>R[2]C</b>, то есть целевая ячейка находится на 2 строки ниже активной ячейки и в том же самом столбце.</p>
                    <p><div class="big big-relativereference"></div></p>
                </li>
                <li>Опция <b>Использовать клавишу Alt для навигации по интерфейсу с помощью клавиатуры</b> используется для включения использования клавиши <em>Alt</em> / <em>Option</em> в сочетаниях клавиш.</li>
                <li>
                    Опция <b>Тема интерфейса</b> используется для изменения цветовой схемы интерфейса редактора.
                    <ul>
                        <li>Опция <b>Системная</b> позволяет редактору соответствовать системной теме интерфейса.</li>
                        <li><b>Светлая</b> цветовая гамма включает стандартные синий, белый и светло-серый цвета с меньшим контрастом элементов интерфейса, подходящие для работы в дневное время.</li>
                        <li><b>Классическая светлая</b> цветовая гамма включает стандартные синий, белый и светло-серый цвета.</li>
                        <li><b>Темная</b> цветовая гамма включает черный, темно-серый и светло-серый цвета, подходящие для работы в ночное время.</li>
                        <li><b>Контрастная темная</b> цветовая гамма включает черный, темно-серый и белый цвета с большим контрастом элементов интерфейса, выделяющих рабочую область файла.</li>
                        <li>
                            Опция <b>Включить темный режим</b> используется, чтобы сделать рабочую область темнее, если для редактора выбрана <b>Темная</b> или <b>Контрастная темная</b> тема интерфейса. Поставьте галочку <b>Включить темный режим</b>, чтобы активировать эту опцию.
                            <p class="note"><b>Примечание</b>: Помимо доступных тем интерфейса <b>Светлая</b>, <b>Классическая светлая</b>, <b>Темная</b> и <b>Контрастная темная</b>, в редакторах ONLYOFFICE теперь можно использовать собственную цветовую тему. Чтобы узнать, как это сделать, пожалуйста, следуйте <a target="_blank" href="https://helpcenter.onlyoffice.com/ru/installation/docs-developer-change-theme.aspx" onclick="onhyperlinkclick(this)">данному руководству</a>.</p>
                        </li>
                    </ul>
                </li>
                <li>Опция <b>Единица измерения</b> используется для определения единиц, которые должны использоваться для измерения параметров элементов, таких как ширина, высота, интервалы, поля и т.д. Можно выбрать опцию <b>Сантиметр</b>, <b>Пункт</b> или <b>Дюйм</b>.</li>
                <li>Опция <b>Стандартное значение масштаба</b> используется для установки стандартного значения масштаба путем его выбора из списка доступных вариантов от 50% до 500%.</li>
                <li>
                    Опция <b>Хинтинг шрифтов</b> используется для выбора типа отображения шрифта в онлайн-редакторе электронных таблиц:
                    <ul>
                        <li>Выберите опцию <b>Как Windows</b>, если Вам нравится отображение шрифтов в операционной системе Windows, то есть с использованием хинтинга шрифтов Windows.</li>
                        <li>Выберите опцию <b>Как OS X</b>, если Вам нравится отображение шрифтов в операционной системе Mac, то есть вообще без хинтинга шрифтов.</li>
                        <li>Выберите опцию <b>Собственный</b>, если хотите, чтобы текст отображался с хинтингом, встроенным в файлы шрифтов.</li>
                        <li>
                            <b>Режим кэширования по умолчанию</b> - используется для выбора режима кэширования символов шрифта. Не рекомендуется переключать без особых причин. Это может быть полезно только в некоторых случаях, например, при возникновении проблемы в браузере Google Chrome с включенным аппаратным ускорением.
                            <p>В редакторе электронных таблиц есть два режима кэширования:</p>
                            <ol>
                                <li>В <b>первом режиме кэширования</b> каждая буква кэшируется как отдельная картинка.</li>
                                <li>Во <b>втором режиме кэширования</b> выделяется картинка определенного размера, в которой динамически располагаются буквы, а также реализован механизм выделения и удаления памяти в этой картинке. Если памяти недостаточно, создается другая картинка, и так далее.</li>
                            </ol>
                            <p>Настройка <b>Режим кэширования по умолчанию</b> применяет два вышеуказанных режима кэширования по отдельности для разных браузеров:</p>
                            <ul>
                                <li>Когда настройка <b>Режим кэширования по умолчанию</b> включена, в Internet Explorer (v. 9, 10, 11) используется <b>второй режим кэширования</b>, в других браузерах используется <b>первый режим кэширования</b>.</li>
                                <li>Когда настройка <b>Режим кэширования по умолчанию</b> выключена, в Internet Explorer (v. 9, 10, 11) используется <b>первый режим кэширования</b>, в других браузерах используется <b>второй режим кэширования</b>.</li>
                            </ul>
                        </li>
                    </ul>
                </li>
                <li>
                    Опция <b>Настройки макросов</b> используется для настройки отображения макросов с уведомлением.
                    <ul>
                        <li>Выберите опцию <b>Отключить все</b>, чтобы отключить все макросы в электронной таблице;</li>
                        <li>Выберите опцию <b>Показывать уведомление</b>, чтобы получать уведомления о макросах в электронной таблице;</li>
                        <li>Выберите опцию <b>Включить все</b>, чтобы автоматически запускать все макросы в электронной таблице.</li>
                    </ul>
                </li>
            </ol>

            <h3>Региональные параметры</h3>
            <ol>
                <li>
                    Опция <b>Язык формул</b> используется для выбора языка отображения и ввода имен формул, аргументов и их описания.
                    <p><b>Язык формул</b> поддерживается на 32 языках:</p>
                    <p><em>белорусский</em>, <em>болгарский</em>, <em>каталонский</em>, <em>китайский</em>, <em>чешский</em>, <em>датский</em>, <em>голландский</em>, <em>английский</em>, <em>финский</em>, <em>французский</em>, <em>немецкий</em>, <em>греческий</em>, <em>венгерский</em>, <em>индонезийский</em>, <em>итальянский</em>, <em>японский</em>, <em>корейский</em>, <em>лаосский</em>, <em>латышский</em>, <em>норвежский</em>, <em>польский</em>, <em>португальский (Бразилия)</em>, <em>португальский (Португалия)</em>, <em>румынский</em>, <em>русский</em>, <em>словацкий</em>, <em>словенский</em>, <em>испанский</em>, <em>шведский</em>, <em>турецкий</em>, <em>украинский</em>, <em>вьетнамский</em>.</p>
                </li>
                <li>Опция <b>Регион</b> используется для выбора формата отображения денежных единиц и даты и времени по умолчанию.</li>
                <li>Опция <b>Использовать разделители на базе региональных настроек</b> выбрана по умолчанию, разделители соответствуют заданному региону. Если вы хотите использовать особые разделители, снимите этот флажок и введите нужные символы в расположенных ниже полях <b>Десятичный разделитель</b> и <b>Разделитель разрядов тысяч</b>.</li>
            </ol>

            <h3>Правописание</h3>
            <ol>
                <li>Опция <b>Язык словаря</b> используется для выбора предпочтительного словаря для проверки орфографии.</li>
                <li><b>Пропускать слова из ПРОПИСНЫХ БУКВ</b>. Слова, написанные прописными буквами, игнорируются при проверке орфографии.</li>
                <li><b>Пропускать слова с цифрами</b>. Слова, в которых есть цифры, игнорируются при проверке орфографии.</li>
                <li>Меню <b>Параметры автозамены...</b> позволяет открыть <a href="../UsageInstructions/MathAutoCorrect.htm" onclick="onhyperlinkclick(this)">параметры автозамены</a>, такие как замена текста при вводе, распознанные функции, автоформат при вводе и другие.</li>
            </ol>

            <h3>Вычисление</h3>
            <ol>
                <li>Опция <b>Использовать систему дат 1904</b> опция служит для вычисления дат с использованием 1 января 1904 года в качестве отправной точки. Это может пригодиться при работе с электронными таблицами, созданными в MS Excel 2008 для Mac и более ранних версиях MS Excel для Mac.</li>
            </ol>
            <p>Чтобы сохранить внесенные изменения, нажмите кнопку <b>Применить</b>.</p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЕСНД</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЕСНД</h1>
			<p>Функция <b>ЕСНД</b> - это одна из логических функций. Используется для проверки формулы на наличие ошибок в первом аргументе. Функция возвращает указанное вами значение, если формула возвращает значение ошибки #Н/Д; в ином случае возвращает результат формулы.</p>
			<p>Синтаксис функции <b>ЕСНД</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЕСНД(значение;значение_при_ошибке)</em></b></p> 
			<p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>значение</em></b> - аргумент, проверяемый на наличие ошибки со значением #Н/Д.</p>
            <p style="text-indent: 50px;"><b><em>значение_при_ошибке</em></b> - значение, которое будет возвращено, если в результате выполнения формулы будет получено значение ошибки #Н/Д.</p>
            <p>Эти аргументы можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
            <p>Чтобы применить функцию <b>ЕСНД</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Логические</b>,</li>
			<li>щелкните по функции <b>ЕСНД</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ЕСНД" src="../images/ifna.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Использование форматированных таблиц</title>
		<meta charset="utf-8" />
        <meta name="description" content="Применяйте к выделенному диапазону ячеек шаблон таблицы с автоматическим включением фильтра." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Использование форматированных таблиц</h1>
            <h3>Создание новой форматированной таблицы</h3>
            <p>Чтобы облегчить работу с данными, в <b>редакторе электронных таблиц</b> предусмотрена возможность применения к выделенному диапазону ячеек шаблона таблицы с автоматическим включением фильтра. Для этого:</p>
            <ol>
                <li>выделите диапазон ячеек, которые требуется отформатировать,</li>
                <li>щелкните по значку <b>Форматировать как шаблон таблицы</b> <div class = "icon icon-tabletemplate"></div>, расположенному на вкладке <b>Главная</b> верхней панели инструментов,</li>
                <li>в галерее выберите требуемый шаблон,</li>
                <li>в открывшемся всплывающем окне проверьте диапазон ячеек, которые требуется отформатировать как таблицу,</li>
                <li>установите флажок <b>Заголовок</b>, если требуется, чтобы заголовки таблицы входили в выделенный диапазон ячеек; в противном случае строка заголовка будет добавлена наверху, в то время как выделенный диапазон ячеек сместится на одну строку вниз,</li>
                <li>нажмите кнопку <b>OK</b>, чтобы применить выбранный шаблон.</li>
            </ol>
            <p>Шаблон будет применен к выделенному диапазону ячеек, и вы сможете редактировать заголовки таблицы и <a href="SortData.htm" onclick="onhyperlinkclick(this)">применять фильтр</a> для работы с данными.</p>
            <p>Форматированную таблицу также можно вставить с помощью кнопки <b>Таблица</b> на вкладке <b>Вставка</b>. В этом случае применяется шаблон таблицы по умолчанию.</p>
            <p class="note"><b>Примечание</b>: как только вы создадите новую форматированную таблицу, этой таблице будет автоматически присвоено <a href="UseNamedRanges.htm" onclick="onhyperlinkclick(this)">стандартное имя</a> (<em>Таблица1</em>, <em>Таблица2</em> и т.д.). Это имя можно изменить, сделав его более содержательным, и использовать для дальнейшей работы.</p>
            <p>Если вы введете новое значение в любой ячейке под последней строкой таблицы (если таблица не содержит строки итогов) или в ячейке справа от последнего столбца таблицы, форматированная таблица будет автоматически расширена, и в нее будет включена новая строка или столбец. Если вы не хотите расширять таблицу, нажмите на появившуюся кнопку <span class="icon icon-pastespecialbutton"></span> и выберите опцию <b>Отменить авторазвертывание таблицы</b>. Как только это действие будет отменено, в этом меню станет доступна опция <b>Повторить авторазвертывание таблицы</b>.</p>
            <p><img alt="Отменить авторазвертывание таблицы" src="../images/undoautoexpansion.png" /></p>
            <p class="note"><b>Примечание</b>: Чтобы включить/отключить авторазвертывание таблиц, выберите параметр <b>Отменить авторазвертывание таблиц</b> в контекстном окне специальной вставки или выберите на вкладке <b>Файл</b> -> <b>Дополнительные параметры</b> -> <b>Проверка орфографии</b> -> <b>Правописание</b> -> <b>Параметры автозамены...</b> -> <b>Автоформат при вводе</b>.</p>
            <h3>Выделение строк и столбцов</h3>
            <p>Чтобы выделить всю строку в форматированной таблице, наведите курсор мыши на левую границу строки таблицы, чтобы курсор превратился в черную стрелку <span class="icon icon-selectrow_cursor"></span>, затем щелкните левой кнопкой мыши.</p>
            <p><img alt="Выделение строки" src="../images/selectrow.png" /></p>
            <p>Чтобы выделить весь столбец в форматированной таблице, наведите курсор мыши на верхний край заголовка столбца, чтобы курсор превратился в черную стрелку <span class="icon icon-selectcolumn_cursor"></span>, затем щелкните левой кнопкой мыши. Если щелкнуть один раз, будут выделены данные столбца (как показано на изображении ниже); если щелкнуть дважды, будет выделен весь столбец, включая заголовок.</p>
            <p><img alt="Выделение столбца" src="../images/selectcolumn.png" /></p>
            <p>Чтобы выделить всю форматированную таблицу, наведите курсор мыши на левый верхний угол форматированной таблицы, чтобы курсор превратился в диагональную черную стрелку <span class="icon icon-selecttable_cursor"></span>, затем щелкните левой кнопкой мыши.</p>
            <p><img alt="Выделение таблицы" src="../images/selecttable.png" /></p>
            <h3>Редактирование форматированных таблиц</h3>
            <p>Некоторые параметры таблицы можно изменить с помощью вкладки <b>Параметры таблицы</b> на правой боковой панели. Чтобы ее открыть, выделите мышью хотя бы одну ячейку в таблице и щелкните по значку <b>Параметры таблицы</b> <span class="icon icon-table_settings_icon"></span> справа.</p>
            <p><img alt="Вкладка Параметры таблицы" src="../images/tablesettingstab.png" /></p>
            <p>Разделы <b>Строки</b> и <b>Столбцы</b>, расположенные наверху, позволяют выделить некоторые строки или столбцы при помощи особого форматирования, или выделить разные строки и столбцы с помощью разных цветов фона для их четкого разграничения. Доступны следующие опции:</p>
            <ul>
                <li><b>Заголовок</b> - позволяет отобразить строку заголовка.</li>
                <li>
                    <b>Итоговая</b> - добавляет строку <b>Итого</b> в нижней части таблицы.
                    <p class="note"><b>Примечание</b>: если выбрана эта опция, вы также можете выбрать функцию для вычисления суммарных значений. При выделении ячейки в строке <b>Итого</b>, справа от ячейки будет доступна <span class="icon icon-dropdownarrow"></span>. Нажмите ее и выберите нужную функцию из списка: <em>Среднее</em>, <em>Количество</em>, <em>Макс</em>, <em>Мин</em>, <em>Сумма</em>, <em>Стандотклон</em> или <em>Дисп</em>. Опция <em>Другие функции</em> позволяет открыть окно <b>Вставить функцию</b> и выбрать любую другую функцию. При выборе опции <em>Нет</em> в выделенной ячейке строки <b>Итого</b> не будет отображаться суммарное значение для этого столбца.</p>
                    <p><img alt="Итоговая строка" src="../images/summary.png" /></p>
                </li>
                <li><b>Чередовать</b> - включает чередование цвета фона для четных и нечетных строк.</li>
                <li><b>Кнопка фильтра</b> - позволяет отобразить кнопки со стрелкой <div class = "icon icon-dropdownarrow"></div> в каждой ячейке строки заголовка. Эта опция доступна только если выбрана опция <b>Заголовок</b>.</li>
                <li><b>Первый</b> - выделяет при помощи особого форматирования крайний левый столбец в таблице.</li>
                <li><b>Последний</b> - выделяет при помощи особого форматирования крайний правый столбец в таблице.</li>
                <li><b>Чередовать</b> - включает чередование цвета фона для четных и нечетных столбцов.</li>
            </ul>
            <p>
                Раздел <b>По шаблону</b> позволяет выбрать один из готовых стилей таблиц. Каждый шаблон сочетает в себе определенные параметры форматирования, такие как цвет фона, стиль границ, чередование строк или столбцов и т.д.
                Набор шаблонов отображается по-разному в зависимости от параметров, указанных в разделах <b>Строки</b> и/или <b>Столбцы</b> выше. Например, если Вы отметили опцию <b>Заголовок</b> в разделе <b>Строки</b> и опцию <b>Чередовать</b> в разделе <b>Столбцы</b>, отображаемый список шаблонов будет содержать только шаблоны со строкой заголовка и чередованием столбцов:
            </p>
            <p><span class="big big-templateslist"></span></p>
            <p>Если вы хотите очистить текущий стиль таблицы (цвет фона, границы и так далее), не удаляя при этом саму таблицу, примените шаблон <b>None</b> из списка шаблонов:</p>
            <p><span class="big big-nonetemplate"></span></p>
            <p>В разделе <b>Размер таблицы</b> можно изменить диапазон ячеек, к которому применено табличное форматирование. Нажмите на кнопку <b>Выбор данных</b> - откроется новое всплывающее окно. Измените ссылку на диапазон ячеек в поле ввода или мышью выделите новый диапазон на листе и нажмите кнопку <b>OK</b>.</p>
            <p class="note"><b>Примечание</b>: Заголовки должны оставаться в той же строке, а результирующий диапазон таблицы - частично перекрываться с исходным диапазоном.</p>
            <p><img alt="Изменение размера таблицы" src="../images/resizetable.png" /></p>
            <p>Раздел <b>Строки и столбцы</b> <span class="icon icon-rowsandcolumns"></span> позволяет выполнить следующие операции:</p>
            <ul>
                <li><b>Выбрать</b> строку, столбец, все данные в столбцах, исключая строку заголовка, или всю таблицу, включая строку заголовка.</li>
                <li><b>Вставить</b> новую строку выше или ниже выделенной, а также новый столбец слева или справа от выделенного.</li>
                <li><b>Удалить</b> строку, столбец (в зависимости от позиции курсора или выделения) или всю таблицу.</li>
            </ul>
            <p class="note"><b>Примечание</b>: Параметры раздела <b>Строки и столбцы</b> также доступны из <b>контекстного меню</b>.</p>
            <p>Опцию <span class="icon icon-removeduplicates"></span> <b>Удалить дубликаты</b> можно использовать, если вы хотите удалить повторяющиеся значения из форматированной таблицы. Для получения дополнительной информации по удалению дубликатов обратитесь к <a href="RemoveDuplicates.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</p>
            <p>Опцию <span class="icon icon-converttorange"></span> <b>Преобразовать в диапазон</b> можно использовать, если вы хотите преобразовать таблицу в обычный диапазон данных, удалив фильтр, но сохранив стиль таблицы (то есть цвета ячеек и шрифта и т.д.). Как только вы примените эту опцию, вкладка <b>Параметры таблицы</b> на правой боковой панели станет недоступна.</p>
            <p>Опция <span class="icon icon-insertslicer"></span> <b>Вставить срез</b> используется, чтобы создать срез для форматированной таблицы. Для получения дополнительной информации по работе со срезами обратитесь к <a href="Slicers.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</p>
            <p>Опция <span class="icon icon-insertpivot"></span> <b>Вставить сводную таблицу</b> используется, чтобы создать сводную таблицу на базе форматированной таблицы. Для получения дополнительной информации по работе со сводными таблицами обратитесь к <a href="PivotTables.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</p>
            <h3>Изменение дополнительных параметров форматированной таблицы</h3>
            <p>Чтобы изменить дополнительные параметры таблицы, нажмите ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно 'Таблица - Дополнительные параметры':</p>
            <p><img alt="Таблица - дополнительные параметры" src="../images/tableadvancedsettings.png" /></p>
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит таблица.</p>
            <p class="note"><b>Примечание</b>: Чтобы включить или отключить автоматическое расширение таблицы, перейдите в раздел <b>Дополнительные параметры</b> -> <b>Проверка орфографии</b> -> <b>Правописание</b> -> <b>Параметры автозамены</b> -> <b>Автоформат при вводе</b>.</p>
            <h3>Использование автозаполнение формул для добавления формул в форматированные таблицы.</h3>
            <p>В списке <b>Автозаполнение формул</b> отображаются все доступные параметры при применении формул к форматированным таблицам. Вы можете ссылаться на таблицу в своей формуле как внутри, так и вне таблицы. В качестве ссылок вместо адресов ячеек используются имена столбцов и элементов.</p>
            <p>Пример ниже показывает ссылку на таблицу в функции СУММ.</p>
            <ol>
                <li>
                    Выберите ячейку, начните вводить формулу, начиная со знака равно, за которым следует <em>имя таблицы</em>, затем выберите подходящий пункт из списка <b>Автозаполнение формул</b>.
                    <p><img alt="Табличные формулы" src="../images/tableformulas.png" />
                </li>
                <li>
                    Далее добавьте квадратную скобку [, чтобы открыть раскрывающийся список, содержащий столбцы и элементы, которые можно использовать в формуле. При наведении указателем мыши на ссылку в списке, появляется подсказка с ее описанием.
                    <p><img alt="Табличные формулы" src="../images/tableformulas1.png" />
                </li>
            </ol>
            <p class="note"><b>Примечание</b>: Каждая ссылка должна содержать открывающую и закрывающую скобки. Не забудьте проверить синтаксис формулы.</p>
        </div>   
	</body>
</html>
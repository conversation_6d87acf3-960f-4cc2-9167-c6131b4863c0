<!DOCTYPE html>
<html>
	<head>
		<title>Функция ТЕКСТДО</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Функция ТЕКСТДО</h1>
			<p>Функция <b>ТЕКСТДО</b> - это одна из функций для работы с текстом и данными. Возвращает текст перед символами-разделителями.</p>
			<p>Синтаксис функции <b>ТЕКСТДО</b>:</p>
			<p style="text-indent: 150px;"><b><em>ТЕКСТДО(text,delimiter,[instance_num], [match_mode], [match_end], [if_not_found])</em></b></p>
			<p>где</p>
			<p style="text-indent: 50px;"><b><em>text</em></b> - текст, в котором производится поиск. Использовать подстановочные знаки нельзя. Если <b>text</b> - пустая строка, функция возвращает пустой текст.</p>
			<p style="text-indent: 50px;"><b><em>delimiter</em></b>  - текст, помечающий точку, перед которой нужно извлечь текст.</p>
			<p style="text-indent: 50px;"><b><em>instance_num</em></b> - необязательный аргумент. Экземпляр разделителя, перед которым нужно извлечь текст. По умолчанию <b>instance_num</b> равен <b>1</b>. При отрицательном значении поиск начинается с конца.</p>
			<p style="text-indent: 50px;"><b><em>match_mode</em></b> - необязательный аргумент. Определяет, учитывается ли регистр в текстовом поиске. По умолчанию регистр учитывается. Используются следующие значения: <b>0</b> - с учетом регистра, <b>1</b> - без учета регистра.</p>
			<p style="text-indent: 50px;"><b><em>match_end</em></b> - необязательный аргумент. Рассматривает конец текста как разделитель. По умолчанию текст является точным совпадением. Используются следующие значения: <b>0</b> - <b><em>не</em></b> сопоставлять разделитель с концом текста, <b>1</b> - сопоставлять разделитель с концом текста.</p>
			<p style="text-indent: 50px;"><b><em>if_not_found</em></b> - необязательный аргумент. Задает значение, которое  возвращается, если совпадение не найдено.</p>
			<p>Чтобы применить функцию <b>ТЕКСТДО</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class="icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class="icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Текст и данные</b>,</li>
                <li>щелкните по функции <b>ТЕКСТДО</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ТЕКСТДО" src="../images/textbefore.png" /></p>
		</div>
	</body>
</html>
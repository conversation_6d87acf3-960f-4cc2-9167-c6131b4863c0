<!DOCTYPE html>
<html>
	<head>
		<title>Функция ОКРУГЛТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ОКРУГЛТ</h1>
			<p>Функция <b>ОКРУГЛТ</b> - это одна из математических и тригонометрических функций. Она используется, чтобы округлить число до кратного заданной значимости.</p>
			<p>Синтаксис функции <b>ОКРУГЛТ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ОКРУГЛТ(x;точность)</em></b></p> 
			<p>где <b><em>x</em></b> (число, которое требуется округлить), <b><em>точность</em></b> (величина, до кратного которой требуется округлить число) - это числовые значения, введенные вручную или находящиеся в ячейке, на которую дается ссылка.</p>
			<p class="note"><b>Примечание</b>: если значения <b><em>x</em></b> и <b><em>точность</em></b> имеют разные знаки, функция возвращает ошибку <b>#ЧИСЛО!</b>.</p> 
			<p>Чтобы применить функцию <b>ОКРУГЛТ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>ОКРУГЛТ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ОКРУГЛТ" src="../images/mround.png" /></p>
		</div>
	</body>
</html>
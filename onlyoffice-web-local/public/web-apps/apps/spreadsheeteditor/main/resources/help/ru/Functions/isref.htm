<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЕССЫЛКА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ЕССЫЛКА</h1>
            <p>Функция <b>ЕССЫЛКА</b> это одна из информационных функций. Она используется для проверки, является ли значение допустимой ссылкой на другую ячейку.</p>
            <p>Синтаксис функции <b>ЕССЫЛКА</b>:</p>
            <p style="text-indent: 150px;"><b><em>ЕССЫЛКА(значение)</em></b></p>
            <p>где <b><em>значение</em></b> - это проверяемое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
            <p>Чтобы применить функцию <b>ЕССЫЛКА</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Информационные</b>,</li>
                <li>щелкните по функции <b>ЕССЫЛКА</b>,</li>
                <li>введите требуемый аргумент,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке. Если значение является допустимой ссылкой, функция возвращает значение ИСТИНА.</p>
            <p style="text-indent: 150px;"><img alt="Функция ЕССЫЛКА" src="../images/isreftrue.png" /></p>
            <p>В противном случае функция возвращает значение ЛОЖЬ.</p>
            <p style="text-indent: 150px;"><img alt="Функция ЕССЫЛКА" src="../images/isreffalse.png" /></p>
        </div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Создание новой электронной таблицы или открытие существующей</title>
		<meta charset="utf-8" />
		<meta name="description" content="Создайте новую электронную таблицу, откройте недавно отредактированную, или вернитесь к списку существующих электронных таблиц" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Создание новой электронной таблицы или открытие существующей</h1>
            <p>В <b>редакторе таблиц</b> вы можете открыть электронную таблицу, которую вы недавно редактировали, переименовать её, создать новую или вернуться к списку существующих электронных таблиц.</p>
			<h3>Чтобы создать новую таблицу</h3>
            <div class="onlineDocumentFeatures">
                <p>В <em>онлайн-редакторе</em></p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Создать новую</b>. <!--Here you can choose whether to create a <b>blank</b> text document or use one of the available document <b>templates</b>: contract, letter, list, or plan.--></li>
                </ol>
            </div>
            <div class="desktopDocumentFeatures">
                <p>В <em>десктопном редакторе</em></p>
                <ol>
                    <li>в главном окне программы выберите пункт меню <b>Таблица</b> в разделе <b>Создать</b> на левой боковой панели - новый файл откроется в новой вкладке,</li>
                    <li>после внесения в таблицу необходимых изменений нажмите на значок <b>Сохранить</b> <div class = "icon icon-save"></div> в левом верхнем углу или откройте вкладку <b>Файл</b> и выберите пункт меню <b>Сохранить как</b>. </li>
                    <li>в окне проводника выберите местоположение файла на жестком диске, задайте название таблицы, выберите формат сохранения (XLSX, Шаблон таблицы (XLTX), ODS, OTS, CSV, PDF или PDFA) и нажмите кнопку <b>Сохранить</b>.</li>
                </ol>
            </div>

            <div class="desktopDocumentFeatures">
                <h3>Чтобы открыть существующую таблицу</h3>
                <p>В <em>десктопном редакторе</em></p>
                <ol>
                    <li>в главном окне программы выберите пункт меню <b>Открыть локальный файл</b> на левой боковой панели,</li>
                    <li>выберите нужную таблицу в окне проводника и нажмите кнопку <b>Открыть</b>.</li>
                </ol>
                <p>Можно также щелкнуть правой кнопкой мыши по нужному файлу в окне проводника, выбрать опцию <b>Открыть с помощью</b> и затем выбрать в меню нужное приложение. Если файлы офисных документов ассоциированы с приложением, электронные таблицы также можно открывать двойным щелчком мыши по названию файла в окне проводника.</p>
                <p>Все каталоги, к которым вы получали доступ с помощью десктопного редактора, будут отображены в разделе <b>Открыть локальный файл</b> в списке <b>Последние папки</b>, чтобы в дальнейшем вы могли быстро их открыть. Щелкните по нужной папке, чтобы выбрать один из находящихся в ней файлов.</p>
            </div>

            <h3>Чтобы открыть недавно отредактированную таблицу</h3>
            <div class="onlineDocumentFeatures">
                <p>В <em>онлайн-редакторе</em></p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Открыть последние</b>,</li>
                    <li>выберите нужную электронную таблицу из списка недавно отредактированных электронных таблиц.</li>
                </ol>
            </div>
            <div class="desktopDocumentFeatures">
                <p>В <em>десктопном редакторе</em></p>
                <ol>
                    <li>в главном окне программы выберите пункт меню <b>Последние файлы</b> на левой боковой панели,</li>
                    <li>выберите нужную электронную таблицу из списка недавно измененных документов.</li>
                </ol>
            </div>

            <h3>Чтобы переименовать открытую таблицу</h3>
            <div class="onlineDocumentFeatures">
                <p>В <em>онлайн-редакторе</em></p>
                <ol>
                    <li>щелкните по имени таблицы наверху страницы,</li>
                    <li>введите новое имя таблицы,</li>
                    <li>нажмите <b>Enter</b>, чтобы принять изменения.</li>
                </ol>
            </div>

            <p>Чтобы открыть папку, в которой сохранен файл, <span class="onlineDocumentFeatures"> в новой вкладке браузера в <em>онлайн-версии</em>,</span> <span class="desktopDocumentFeatures"> в окне проводника в <em>десктопной версии</em>,</span> нажмите на значок <span class = "icon icon-gotodocuments"></span> <b>Открыть расположение файла</b> в правой части шапки редактора. Можно также перейти на вкладку <b>Файл</b> на верхней панели инструментов и выбрать опцию <b>Открыть расположение файла</b>.</p>
            </div>
	</body>
</html>
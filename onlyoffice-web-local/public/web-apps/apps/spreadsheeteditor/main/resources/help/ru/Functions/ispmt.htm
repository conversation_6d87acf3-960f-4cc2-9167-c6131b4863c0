<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПРОЦПЛАТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПРОЦПЛАТ</h1>
			<p>Функция <b>ПРОЦПЛАТ</b> - это одна из финансовых функций. Используется для вычисления процентов, выплачиваемых за определенный инвестиционный период, исходя из постоянной периодичности платежей.</p>
			<p>Синтаксис функции <b>ПРОЦПЛАТ</b>:</p>
      <p style="text-indent: 150px;">
        <b>
          <em>ПРОЦПЛАТ(ставка;период;кпер;пс)</em>
        </b>
      </p>
      <p>
        <em>где</em>
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>ставка</em>
        </b> - это процентная ставка по инвестиции.
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>период</em>
        </b> - это период времени, за который требуется вычислить размер процентов. Этот аргумент может принимать значения от <b>
          <em>1</em>
        </b> до <b>
          <em>кпер</em>
        </b>.
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>кпер</em>
        </b> - это количество платежных периодов.
      </p>
      <p style="text-indent: 50px;">
        <b>
          <em>пс</em>
        </b> - это текущая стоимость выплат.
      </p>
            <p class="note">
                <b>Примечание:</b> выплачиваемые денежные средства (например, сберегательные вклады) представляются отрицательными числами; получаемые денежные средства (например, дивиденды) представляются положительными числами.
                Единицы измерения аргументов "ставка" и "кпер" должны быть согласованы между собой: используйте N%/12 для аргумента "ставка"
                и N*12 для аргумента "кпер", если речь идет о ежемесячных платежах, N%/4 для аргумента "ставка" и N*4 для аргумента "кпер",
                если речь идет о ежеквартальных платежах, N% для аргумента "ставка" и N для аргумента "кпер", если речь идет о ежегодных платежах.
            </p>
      <p>Числовые значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
      <p>
        Чтобы применить функцию <b>ПРОЦПЛАТ</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Финансовые</b>,
        </li>
        <li>
          щелкните по функции <b>ПРОЦПЛАТ</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выделенной ячейке.</p>      
			<p style="text-indent: 150px;"><img alt="Функция ПРОЦПЛАТ" src="../images/ispmt.png" /></p>
		</div>
	</body>
</html>
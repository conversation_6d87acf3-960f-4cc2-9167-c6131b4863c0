<!DOCTYPE html>
<html>
	<head>
		<title>Функция РУБЛЬ.ДРОБЬ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция РУБЛЬ.ДРОБЬ</h1>
			<p>Функция <b>РУБЛЬ.ДРОБЬ</b> - это одна из финансовых функций. Преобразует цену в рублях, представленную десятичным числом, в цену в рублях, выраженную в виде дроби.</p>
			<p>Синтаксис функции <b>РУБЛЬ.ДРОБЬ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>РУБЛЬ.ДРОБЬ(дес_руб;дроб)</em></b></p> 
			<p><em>где</em></p> 
            <p style="text-indent: 50px;"><b><em>дес_руб</em></b> - это десятичное число.</p>
      <p style="text-indent: 50px;">
        <b>
          <em>дроб</em>
        </b> - это целое число, которое вы хотите использовать в качестве знаменателя для полученной дроби.
      </p>
      <p class="note">
        <b>Примечание:</b> например, полученное значение <b>1.03</b> интерпретируется как
        <b>1 + 3/n</b>, где <b>n</b> - это значение аргумента <em>дроб</em>.
      </p>
      <p>Числовые значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
      <p>
        Чтобы применить функцию <b>РУБЛЬ.ДРОБЬ</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Финансовые</b>,
        </li>
        <li>
          щелкните по функции <b>РУБЛЬ.ДРОБЬ</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выделенной ячейке.</p>     
			<p style="text-indent: 150px;"><img alt="Функция РУБЛЬ.ДРОБЬ" src="../images/dollarfr.png" /></p>
		</div>
	</body>
</html>
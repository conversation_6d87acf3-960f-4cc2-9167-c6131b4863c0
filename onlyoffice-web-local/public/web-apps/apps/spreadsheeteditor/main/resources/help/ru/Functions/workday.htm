<!DOCTYPE html>
<html>
	<head>
		<title>Функция РАБДЕНЬ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция РАБДЕНЬ</h1>
			<p>Функция <b>РАБДЕНЬ</b> - это одна из функций даты и времени. Возвращает дату, которая идет на заданное число дней (количество_дней) до или после заданной начальной даты, без учета выходных и праздничных дней.</p>
			<p>Синтаксис функции <b>РАБДЕНЬ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>РАБДЕНЬ(нач_дата;количество_дней;[праздники])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>нач_дата</em></b> - первая дата периода, введенная с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени.</p>
			<p style="text-indent: 50px;"><b><em>количество_дней</em></b> - количество рабочих дней до или после <b><em>начальной даты</em></b>. Если аргумент <b><em>количество_дней</em></b> имеет отрицательное значение, функция РАБДЕНЬ возвращает дату, идущую перед заданной <b>начальной датой</b>. Если аргумент <b><em>количество_дней</em></b> имеет положительное значение, функция РАБДЕНЬ возвращает дату, идущую после заданной <b>начальной даты</b>.</p>
            <p style="text-indent: 50px;"><b><em>праздники</em></b> - необязательный аргумент, указывающий, какие даты, кроме выходных, являются нерабочими. Их можно ввести с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени или указать ссылку на диапазон ячеек, содержащих даты.</p>
			<p>Чтобы применить функцию <b>РАБДЕНЬ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>РАБДЕНЬ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция РАБДЕНЬ" src="../images/workday.png" /></p>
		</div>
	</body>
</html>
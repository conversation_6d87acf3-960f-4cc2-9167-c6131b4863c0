<!DOCTYPE html>
<html>
<head>
    <title>Условное Форматирование</title>
    <meta charset="utf-8" />
    <meta name="description" content="Условное форматирование позволяет применять к ячейкам различные стили форматирования (цвет, шрифт, украшение, градиент) для работы с данными в электронной таблице: выделять, сортировать и отображать данные, соответствующие необходимым критериям." />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Условное Форматирование</h1>
        <p><b>Условное форматирование</b> позволяет применять к ячейкам различные стили форматирования (цвет, шрифт, украшение, градиент) для работы с данными в электронной таблице: выделяйте или сортируйте, а затем отображайте данные, соответствующие необходимым критериям. Критерии определяются несколькими типами правил. Редактор электронных таблиц ONLYOFFICE поддерживает следующие правила условного форматирования:</p>
        <p><b>Значение равно</b>, <b>Наибольшее/Наименьшее</b>, <b>Среднее</b>, <b>Текст</b>, <b>Дата</b>, <b>Пустая ячейка/Ошибка</b>, <b>Повторяющееся/Уникальное</b>, <b>Гистограммы</b>, <b>Цветовые шкалы</b>, <b>Наборы знаков</b>, <b>Формула</b>.</p>
        <p>Если вы хотите выбрать одно из предустановленных условных форматирований или получить доступ ко всем доступным параметрам условного форматирования, перейдите на вкладку <b>Главная</b> и нажмите кнопку <b>Условное форматирование</b> <span class="icon icon-conditionalformatting_button"></span>.</p>
        <p><img alt="Условное форматирование вкладка Главное" src="../images/interface/hometab.png" /></p>
        <p>Все параметры <b>Условного форматирования</b> также доступны на вкладке <b>Параметры ячейки</b> правой боковой панели. Щелкните на стрелку, находящейся рядом с кнопкой <b>Условное форматирование</b>, чтобы открыть выпадающий список, содержащий все доступные параметры.</p>
        <p><img alt="Условное форматирование Правая боковая панель" src="../images/conditionalformatting_rightsidebar.png" /></p>
        <p>Щелкните правой кнопкой мыши по любой ячейке и выберите пункт <b>Условное форматирование</b> из контекстного меню, чтобы открыть окно <b>Новое правило форматирования</b>.</p>
        <p><img alt="Условное форматирование контекстное меню" src="../images/conditionalformatting_rightclick.png" /></p>
        <p>Чтобы применить правило условного форматирования, выберите диапазон ячеек, затем нажмите кнопку <b>Условное форматирование</b> <span class="icon icon-conditionalformatting_button"></span> на вкладке <b>Главная</b> верхней панели инструментов или щелкните на кнопку <b>Условное форматирование</b> на правой панели инструментов и выберите соответствующее правило из раскрывающегося списка.</p>
        <p><img alt="Условное форматирование Выпадающий список " src="../images/conditionalformatting_dropdown.png" />.
        <p>Откроется окно <b>Новое правило форматирования</b>, в котором вы сможете отформатировать критерии отображения.</p>
        <details class="details-example">
            <summary><b>Правило форматирования - Значение</b></summary>
            <p>Правило форматирования <b>Значение</b> используется для поиска и выделения ячеек, удовлетворяющих определенному условию сравнения:</p>
            <ul type="circle">
                <li>Больше</li>
                <li>Больше или равно</li>
                <li>Меньше</li>
                <li>Меньше или равно</li>
                <li>Равно</li>
                <li>Не равно</li>
                <li>Между</li>
                <li>Не между</li>
            </ul>
            <p><img alt="Новое правило Значение" src="../images/newrule_valueis.png" /></p>
            <ul type="disc">
                <li>В разделе <b>Правило</b> отображается выбранное правило и условие. Щелкните стрелку вниз, чтобы открыть список доступных правил и условий. Используйте поле <b>Выбор данных</b>, чтобы определить ячейки для сравнения. Вы можете выбрать одну ячейку, ряд ячеек или функцию в следующем формате: =<em>СУММ(A1:B5)</em>.</li>
                <li>В разделе <b>Формат</b> находится ряд параметров форматирования ячеек. Вы можете выбрать одну из доступных <b>Предустановок</b> формата.</li>
            </ul>
            <p><img alt="Предустановки формата" src="../images/format_presets.png" /></p>
            <p>или</p>
            <p>отформатируйте шрифт (Полужирный, Курсив, Подчеркивание, Зачеркивание), Цвет текста, Цвет заливки и Границ. А также выберите необходимый <b>Числовой формат</b> в выпадающем списке (Общий, Числовой, Научный, Финансовый, Денежный, Дата, Время, Процент). Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования. Чтобы удалить все форматирование, нажмите <b>Очистить</b>.</p>
            <p>Нажмите <b>ОК</b>.</p>
            <p>В приведенном ниже примере показаны предварительно заданные критерии форматирования <b>Больше</b> и <b>Между</b>. Горы высотой более 6960 имеют зеленый фон, а высотой от 5000 до 6500 - розовый.</p>
            <p><img alt="Значение" src="../images/conditionalformatting/valueis_example.png" /></p>
        </details>
        <details class="details-example">
            <summary><b>Правила форматирования - Наибольшее/Наименьшее</b></summary>
            <p>Правила форматирования <b>Наибольшее</b>/<b>Наименьшее</b> используется для поиска и выделения ячеек с наибольшим и наименьшим значением:</p>
            <ul type="circle">
                <li>Наибольшие 10 элементов</li>
                <li>Наибольшие 10%</li>
                <li>Наименьшие 10 элементов</li>
                <li>Наименьшие 10%</li>
            </ul>
            <p><img alt="Новое правило Наибольшее/Наименьшее" src="../images/newrule_topbottom.png" /></p>
            <ul type="disc">
                <li>В разделе <b>Правило</b> отображается выбранное правило и условие. Щелкните стрелку вниз, чтобы открыть список доступных правил и условий, количество отображаемых элементов (процентов) и выбрать, какие элементы (проценты) вы хотите выделить.</li>
                <li>В разделе <b>Формат</b> находится ряд параметров форматирования ячеек. Вы можете выбрать одну из доступных <b>Предустановок</b> формата</li>
            </ul>
            <p><img alt="Предустановки формата" src="../images/format_presets.png" /></p>
            <p>или</p>
            <p>отформатируйте шрифт (Полужирный, Курсив, Подчеркивание, Зачеркивание), Цвет текста, Цвет заливки и Границ. А также выберите необходимый <b>Числовой формат</b> в выпадающем списке (Общий, Числовой, Научный, Финансовый, Денежный, Дата, Время, Процент). Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования. Чтобы удалить все форматирование, нажмите <b>Очистить</b>.</p>
            <p>Нажмите <b>ОК</b>.</p>
            <p>В приведенном ниже примере показаны предварительно заданные критерии форматирования <b>Наибольшее 20%</b> и <b>Наименьшее 10</b>. Города, в которых были получены наибольшие 20% сборы, имеют красный фон, а те, в которых было продано наименьшее количество книг, - зеленый.</p>
            <p><img alt="Наибольшее Наименьшее" src="../images/conditionalformatting/topbottom_example.png" /></p>
        </details>
        <details class="details-example">
            <summary><b>Правило форматирования - Среднее</b></summary>
            <p>Правило форматирования <b>Среднее</b> используется для поиска и выделения ячеек, значение которых выше или ниже среднего или стандартного отклонения:</p>
            <ul type="circle">
                <li>Выше</li>
                <li>Ниже</li>
                <li>Равно или больше</li>
                <li>Меньше или равно</li>
                <li>На 1 стандартное отклонение выше</li>
                <li>На 1 стандартное отклонение ниже</li>
                <li>На 2 стандартное отклонение выше</li>
                <li>На 2 стандартное отклонение ниже</li>
                <li>На 3 стандартное отклонение выше</li>
                <li>На 4 стандартное отклонение ниже</li>
            </ul>
            <p><img alt="Новое правило Среднее" src="../images/newrule_average.png" /></p>
            <ul type="disc">
                <li>В разделе <b>Правило</b> отображается выбранное правило и условие. Щелкните стрелку вниз, чтобы открыть список доступных правил и условий.</li>
                <li>В разделе <b>Формат</b> находится ряд параметров форматирования ячеек. Вы можете выбрать одну из доступных <b>Предустановок</b> формата</li>
            </ul>
            <p><img alt="Предустановки формата" src="../images/format_presets.png" /></p>
            <p>или</p>
            <p>отформатируйте шрифт (Полужирный, Курсив, Подчеркивание, Зачеркивание), Цвет текста, Цвет заливки и Границ. А также выберите необходимый <b>Числовой формат</b> в выпадающем списке (Общий, Числовой, Научный, Финансовый, Денежный, Дата, Время, Процент). Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования. Чтобы удалить все форматирование, нажмите <b>Очистить</b>.</p>
            <p>Нажмите <b>ОК</b>.</p>
            <p>В приведенном ниже примере показаны предварительно заданные критерии форматирования <b>Выше</b>. Города, в которых присутствующих было выше среднего значения, имеют красный фон.</p>
            <p><img alt="Среднее" src="../images/conditionalformatting/average_example.png" /></p>
        </details>
        <details class="details-example">
            <summary><b>Правило форматирования - Текст</b></summary>
            <p>Правило форматирования <b>Текст</b> используется для поиска и выделения ячеек, содержащих определенный текст и удовлетворяющих одному из доступных условий форматирования:</p>
            <ul type="circle">
                <li>Содержит</li>
                <li>Не содержит</li>
                <li>Начинается с</li>
                <li>Заканчивается на</li>
            </ul>
            <p><img alt="Новое правило Текст" src="../images/newrule_text.png" /></p>
            <ul type="disc">
                <li>В разделе <b>Правило</b> отображается выбранное правило и условие. Щелкните стрелку вниз, чтобы открыть список доступных правил и условий. Используйте поле <b>Выбор данных</b>, чтобы определить ячейки для сравнения. Вы можете выбрать одну ячейку, ряд ячеек или функцию в следующем формате: =<em>СУММ(A1:B5)</em>.</li>
                <li>В разделе <b>Формат</b> находится ряд параметров форматирования ячеек. Вы можете выбрать одну из доступных <b>Предустановок</b> формата</li>
            </ul>
            <p><img alt="Предустановки формата" src="../images/format_presets.png" /></p>
            <p>или</p>
            <p>отформатируйте шрифт (Полужирный, Курсив, Подчеркивание, Зачеркивание), Цвет текста, Цвет заливки и Границ. А также выберите необходимый <b>Числовой формат</b> в выпадающем списке (Общий, Числовой, Научный, Финансовый, Денежный, Дата, Время, Процент). Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования. Чтобы удалить все форматирование, нажмите <b>Очистить</b>.</p>
            <p>Нажмите <b>ОК</b>.</p>
            <p>В приведенном ниже примере показаны предварительно заданные критерии форматирования <b>Содержит</b>. Чтобы выделить продажи для определенного региона или определенных товаров, ячейки, содержащие "Дания", имеют красный фон, а содержащие "Зерно", - желтый.</p>
            <p><img alt="Текст" src="../images/conditionalformatting/text_example.png" /></p>
        </details>
        <details class="details-example">
            <summary><b>Правило форматирования - Дата</b></summary>
            <p>Правило форматирования <b>Дата</b> используется для поиска и выделения ячеек, содержащих определенную дату и удовлетворяющих одному из доступных условий форматирования:</p>
            <ul type="circle">
                <li>Вчера</li>
                <li>Сегодня</li>
                <li>Завтра</li>
                <li>За последние 7 дней/li>
                <li>Прошлая неделя</li>
                <li>Эта неделя</li>
                <li>Следующая неделя</li>
                <li>Прошлый месяц</li>
                <li>Этот месяц</li>
                <li>Следующий месяц</li>
            </ul>
            <p><img alt="Новое правило Дата" src="../images/newrule_date.png" /></p>
            <ul type="disc">
                <li>В разделе <b>Правило</b> отображается выбранное правило и условие. Щелкните стрелку вниз, чтобы открыть список доступных правил и условий.</li>
                <li>В разделе <b>Формат</b> находится ряд параметров форматирования ячеек. Вы можете выбрать одну из доступных <b>Предустановок</b> формата</li>
            </ul>
            <p><img alt="Предустановки формата" src="../images/format_presets.png" /></p>
            <p>или</p>
            <p>отформатируйте шрифт (Полужирный, Курсив, Подчеркивание, Зачеркивание), Цвет текста, Цвет заливки и Границ. А также выберите необходимый <b>Числовой формат</b> в выпадающем списке (Общий, Числовой, Научный, Финансовый, Денежный, Дата, Время, Процент). Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования. Чтобы удалить все форматирование, нажмите <b>Очистить</b>.</p>
            <p>Нажмите <b>ОК</b>.</p>
            <p>В приведенном ниже примере показаны предварительно заданные критерии форматирования <b>Прошлый месяц</b>. Чтобы выделить продажи за определенный период времени, ячейки, содержащие даты за предыдущий месяц, имеют желтый фон.</p>
            <p><img alt="Дата" src="../images/conditionalformatting/date_example.png" /></p>
        </details>
        <details class="details-example">
            <summary><b>Правило форматирования - Пустая ячейка/Ошибка</b></summary>
            <p>Правило форматирования <b>Пустая ячейка/Ошибка</b> используется для поиска и выделения ячеек, содержащих или не содержащих пустые ячейки и ошибки:</p>
            <ul type="circle">
                <li>Содержит пустые ячейки</li>
                <li>Не содержит пустых ячеек</li>
                <li>Содержит ошибки</li>
                <li>Не содержит ошибок</li>
            </ul>
            <p><img alt="Новое правило Пустая ячейка/Ошибка" src="../images/newrule_blankerror.png" /></p>
            <ul type="disc">
                <li>В разделе <b>Правило</b> отображается выбранное правило и условие. Щелкните стрелку вниз, чтобы открыть список доступных правил и условий.</li>
                <li>В разделе <b>Формат</b> находится ряд параметров форматирования ячеек. Вы можете выбрать одну из доступных <b>Предустановок</b> формата</li>
            </ul>
            <p><img alt="Предустановки формата" src="../images/format_presets.png" /></p>
            <p>или</p>
            <p>отформатируйте шрифт (Полужирный, Курсив, Подчеркивание, Зачеркивание), Цвет текста, Цвет заливки и Границ. А также выберите необходимый <b>Числовой формат</b> в выпадающем списке (Общий, Числовой, Научный, Финансовый, Денежный, Дата, Время, Процент). Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования. Чтобы удалить все форматирование, нажмите <b>Очистить</b>.</p>
            <p>Нажмите <b>ОК</b>.</p>
            <p>В приведенном ниже примере показаны предварительно заданные критерии форматирования <b>Содержит пустые ячейки</b>. Ячейки, показывающие количество продаж, имеют голубой фон.</p>
            <p><img alt="Пустая ячейка/Ошибка" src="../images/conditionalformatting/blankerror_example.png" /></p>
        </details>
        <details class="details-example">
            <summary><b>Правило форматирования - Повторяющееся/Уникальное</b></summary>
            <p>Правило форматирования <b>Повторяющееся/Уникальное</b> используется для поиска и выделения ячеек, содержащих повторяющиеся значения в таблице или выбранном диапазоне:</p>
            <ul type="circle">
                <li>Повторяющееся</li>
                <li>Уникальное</li>
            </ul>
            <p><img alt="Новое правило Повторяющееся/Уникальное" src="../images/newrule_duplicateunique.png" /></p>
            <ul type="disc">
                <li>В разделе <b>Правило</b> отображается выбранное правило и условие. Щелкните стрелку вниз, чтобы открыть список доступных правил и условий.</li>
                <li>В разделе <b>Формат</b> находится ряд параметров форматирования ячеек. Вы можете выбрать одну из доступных <b>Предустановок</b> формата</li>
            </ul>
            <p><img alt="Предустановки формата" src="../images/format_presets.png" /></p>
            <p>или</p>
            <p>отформатируйте шрифт (Полужирный, Курсив, Подчеркивание, Зачеркивание), Цвет текста, Цвет заливки и Границ. А также выберите необходимый <b>Числовой формат</b> в выпадающем списке (Общий, Числовой, Научный, Финансовый, Денежный, Дата, Время, Процент). Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования. Чтобы удалить все форматирование, нажмите <b>Очистить</b>.</p>
            <p>Нажмите <b>ОК</b>.</p>
            <p>В приведенном ниже примере показаны предварительно заданные критерии форматирования <b>Повторяющееся</b>. Повторяющееся ячейки имеют желтый фон.</p>
            <p><img alt="Повторяющееся/Уникальное" src="../images/conditionalformatting/uniqueduplicates.gif" /></p>
        </details>
        <details class="details-example">
            <summary><b>Правило форматирования - Гистограммы</b></summary>
            <p>Правило форматирования <b>Гистограммы</b> используется для сравнения значений в виде гистограммы.</p>
            <p><span class="big big-databars_diagrams"></span></p>
            <p>Например, сравните высоту гор, отобразив их значение по умолчанию в метрах (зеленая диаграмма) и такое же значение в диапазоне от 0 до 100 процентов (желтая диаграмма); процентиль, когда экстремальные значения сдвигают данные (голубая диаграмма); только диаграммы вместо цифр (синяя диаграмма); данные в два столбца для просмотра как чисел, так и столбцов (красная диаграмма).</p>
            <p><img alt="Гистограммы" src="../images/conditionalformatting/databars.png" /></p>
        </details>
        <details class="details-example">
            <summary><b>Правило форматирования - Цветовые шкалы</b></summary>
            <p>Правило форматирования <b>Цветовые шкалы</b> используется для выделения значений таблице при помощи градиента.</p>
            <p><span class="big big-colorscales"></span></p>
            <p>В приведенном ниже примере показаны столбцы от «Молочные продукты» до «Напитки», в которых данные отображаются в виде двухцветной шкалы с вариациями от желтого до красного; в столбце «Всего продаж» данные отображаются в виде трехцветной шкалы от наименьшей суммы, которая окрашена в  красный цвет, до наибольшей суммы, которая окрашена в синий цвет..</p>
            <p><img alt="Градиент" src="../images/conditionalformatting/gradient.png" /></p>
        </details>
        <details class="details-example">
            <summary><b>Правило форматирования - Наборы значков</b></summary>
            <p>Правило форматирования <b>Наборы значков</b> используется для подсвечивания данных при помощи значка в ячейке, соответствующему критериям. Редактор электронных таблиц поддерживает различные наборы значков:</p>
            <ul type="circle">
                <li>Направления</li>
                <li>Фигуры</li>
                <li>Индикаторы</li>
                <li>Оценки</li>
            </ul>
            <p><span class="big big-iconsets"></span></p>
            <p>Ниже находятся примеры наиболее распространенных случаев использования условного форматирования набора значков.</p>
            <ul>
                <li>
                    Вместо чисел и процентных значений вы видите отформатированные ячейки с соответствующими стрелками, показывающими достигнута ли цель по продажам в столбце "Статус" и динамику будущих тенденций в столбце "Тенденция".
                    <p><img alt="Наборы значков" src="../images/conditionalformatting/iconsetrevenue.png" /></p>
                </li>
                <li>
                    Вместо ячеек с номерами рейтинга от 1 до 5 условное форматирование отображает соответствующие значки из легенды вверху для каждого велосипеда в списке рейтинга.
                    <p><img alt="Наборы значков" src="../images/conditionalformatting/iconsetbikerating.png" /></p>
                </li>
                <li>
                    Вместо того, чтобы вручную сравнивать данные месячной динамики прибыли, отформатированные ячейки имеют соответствующую красную или зеленую стрелку.
                    <p><img alt="Наборы значков" src="../images/conditionalformatting/iconsettrends.png" /></p>
                </li>
                <li>
                    Используйте систему светофоров (красные, желтые и зеленые круги), чтобы визуализировать динамику продаж.
                    <p><img alt="Наборы значков" src="../images/conditionalformatting/iconsettrafficlights.png" /></p>
                </li>
            </ul>
        </details>
        <details class="details-example">
            <summary><b>Форматирование при помощи формул</b></summary>
            <p>Форматирование при помощи <b>Формул</b> использует различные формулы для фильтрации данных в соответствии с конкретными задачами.</p>
            <p><img alt="Новое правило - Формула" src="../images/newrule_formula.png" /></p>
            <ul>
                <li>В разделе <b>Правило</b> отображается выбранное правило и условие. Щелкните стрелку вниз, чтобы открыть список доступных правил и условий. Используйте поле <b>Выбор данных</b>, чтобы определить ячейки для сравнения. Вы можете выбрать одну ячейку, ряд ячеек или функцию в следующем формате: =<em>СУММ(A1:B5)</em>.</li>
                <li>В разделе <b>Формат</b> находится ряд параметров форматирования ячеек. Вы можете выбрать одну из доступных <b>Предустановок</b> формата</li>
            </ul>
            <p><img alt="Предустановки формата" src="../images/format_presets.png" /></p>
            <p>или</p>
            <p>отформатируйте шрифт (Полужирный, Курсив, Подчеркивание, Зачеркивание), Цвет текста, Цвет заливки и Границ. А также выберите необходимый <b>Числовой формат</b> в выпадающем списке (Общий, Числовой, Научный, Финансовый, Денежный, Дата, Время, Процент). Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования. Чтобы удалить все форматирование, нажмите <b>Очистить</b>.</p>
            <p>Нажмите <b>ОК</b>.</p>
            <p>Примеры ниже иллюстрируют возможности форматирования при помощи формул.</p>
            <p>Заштрихуйте чередующиеся ряды,</p>
            <p><img alt="При помощи формул" src="../images/conditionalformatting/shading.png" /></p>
            <p>Сравните с эталонным значением (здесь это 55$) и выделите значение зеленым, если оно выше или красным, если оно ниже,</p>
            <p><img alt="При помощи формул" src="../images/conditionalformatting/comparison.png" /></p>
            <p>Выделите строки, соответствующие необходимым критериям (посмотрите, каких целей вы должны достичь в этом месяце, в данном случае это октябрь),</p>
            <p><img alt="При помощи формул" src="../images/conditionalformatting/shaderows.png" /></p>
            <p>Или выделите только уникальные строки</p>
            <p><img alt="При помощи формул" src="../images/conditionalformatting/shadeunique.png" /></p>
        </details>

        <h2>Создать Новое правило</h2>
        <p>Когда вам нужно создать новое правило условного форматирования, вы можете сделать это одним из следующих способов:</p>
        <ul>
            <li>Перейдите на вкладку <b>Главная</b> и нажмите кнопку <b>Условное форматирование</b> <div class = "icon icon-conditionalformatting_button"></div> и выберите <b>Новое правило</b> в выпадающем списке.</li>
            <li>Перейдите на вкладку <b>Параметры ячейки</b> правой боковой панели, щелкните на кнопку <b>Условное форматирование</b> и выберите <b>Новое правило</b> в выпадающем списке.</li>
            <li>Щелкните правой кнопкой мыши по ячейке и в контекстном меню выберите пункт <b>Условное форматирование</b>.</li>
        </ul>
        <p>Откроется окно <b>Новое правило форматирования</b>. Установите необходимые параметры для настройки правила, как описано выше, и нажмите <b>ОК</b>.</p>

        <h2>Управление правилами условного форматирования</h2>
        <p>После настройки правил условного форматирования, их можно редактировать, удалять и просматривать. Для этого нажмите кнопку <span class="icon icon-conditionalformatting_button"></span> <b>Условное форматирование</b>, которая находится на вкладке <b>Главная</b>, а также на правой боковой панели в разделе <b>Параметры ячейки</b>. Откроется окно условного форматирования:</p>
        <p><img alt="Условное форматирование" src="../images/managerules.png" /><p>
        <p><b>Показать правила форматирования для</b> позволяет выбрать правила, которые вы хотите отобразить:<p>
            <ul type="circle">
                <li>Текущий выделенный фрагмент</li>
                <li>Этот лист</li>
                <li>Эта таблица</li>
                <li>Эта сводная таблица</li>
            </ul>
        <p>Все правила, найденные в выбранном диапазоне, будут показаны в порядке приоритета (сверху вниз) в столбце <b>Правила</b>. В столбце <b>Применить к</b> отображается диапазон, к которому применяется правило. Вы можете изменить диапазон, щелкнув значок <span class="icon icon-selectdata_icon"></span> <b>Выбор данных</b>. Столбец <b>Формат</b> показывает примененное правило форматирования.</p>
        <ul>
            <li>Нажмите кнопку <b>Новое</b>, чтобы добавить новое правило.</li>
            <li>Нажмите кнопку <b>Изменить</b>, если вы хотите отредактировать существующее правило, откроется окно <b>Изменение правила форматирования</b>. Измените правило так, как считаете нужным, и нажмите <b>ОК</b>.</li>
            <li>Используйте стрелки вверх и вниз, чтобы изменить приоритет правила.</li>
            <li>Нажмите кнопку <b>Удалить</b>, чтобы удалить правило.</li>
            <li>Нажмите <b>ОК</b> для подтверждения.</li>
        </ul>

        <h2>Редактирование Условного форматирования</h2>
        <details class="details-example">
            <summary><b>Редактирования Значение, Наибольшее/Наименьшее, Среднее, Текст, Дата, Пустая ячейка/Ошибка, Повторяющееся/Уникальное, Формула</b></summary>
            <p>Окно <b>Изменение правила форматирования</b> для правил <b>Значение</b>, <b>Наибольшее/Наименьшее</b>, <b>Среднее</b>, <b>Текст</b>, <b>Дата</b>, <b>Пустая ячейка/Ошибка</b>, <b>Повторяющееся/Уникальное</b>, <b>Формула</b> содержит ряд стандартных настроек:</p>
            <p><img alt="Редактировать Правило - Значение" src="../images/editrule_valueis.png" /></p>
            <ul type="disc">
                <li>Щелкните <b>Правило</b>, чтобы изменить правило и условие форматирования, примененные ранее.</li>
                <li>В поле <b>Выбор данных</b> вы можете изменить диапазон ячеек, на который нужно ссылаться (для <i>Значение</i>, <i>Текст</i> и <i>Формула</i>).</li>
                <li><b>Форматирование</b> текста и ячеек (<i>Полужирный</i>, <i>Курсив</i>, <i>Подчеркнутый</i>, <i>Зачеркнутый</i>), <i>Цвет текста</i>, <i>Цвет заливки</i> и <i>Границы</i>).</li>
                <li>Щелкните раскрывающийся список <b>Общий</b>, чтобы выбрать соответствующий числовой формат (<i>Общий</i>, <i>Нумерной</i>, <i>Научный</i>, <i>Финансовый</i>, <i>Денежный</i>, <i>Дата</i>, <i>Время</i>, <i>Процент</i>).</li>
                <li>Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования.</li>
                <li>Нажмите <b>ОК</b> для подтверждения.</li>
            </ul>
        </details>
        <details class="details-example">
            <summary><b>Редактирование Гистограмм</b></summary>
            <p>Окно <b>Изменение правила форматирования</b> для <b>Гистограмм</b> содержит следующие параметры:</p>
            <p><img alt="Редактировать Правило - Гистограммы" src="../images/editrule_databar.png" /></p>
            <ul type="disc">
                <li>Щелкните <b>Правило</b>, чтобы изменить правило и условие форматирования, примененные ранее.</li>
                <li>
                    <b>Минимум</b>/<b>Максимум</b> определяют тип минимального и максимального значений для гистограмм. Типы максимального/минимального значения:
                    <ul>
                        <li>Минимум/Максимум</li>
                        <li>Числовой</li>
                        <li>Процентный</li>
                        <li>Формула</li>
                        <li>Процентиль</li>
                        <li>Автоматически</li>
                    </ul>
                    <p>Выберите <b>Автоматически</b>, чтобы установить минимальное значение равным нулю, а максимальное значение - наибольшим числом в диапазоне. Опция <b>Автоматически</b> устанавливается по умолчанию.</p>
                    <p>Щелкните поле <b>Выбор данных</b>, чтобы изменить диапазон ячеек для минимального максимального значений.</p>
                </li>
                <li>
                    <b>Внешний вид столбца</b>
                    <p>Настройте внешний вид <b>Гистограмм</b>, выбрав тип и цвет заливки и границы, а также направление.</p>
                    <ul>
                        <li>Выберите один из двух типов <b>Заливки</b>: <b>Сплошной</b> и <b>Градиент</b>. Рядом также можно выбрать цвет заливки столбцов для Положительных и Отрицательных значений.</li>
                        <li>Выберите один из двух типов <b>Границ</b>: <b>Сплошной</b> и <b>Нет</b>. Рядом также можно выбрать цвет границ столбцов для Положительных и Отрицательных значений.</li>
                        <li>Установите флажок напротив <b>Как положительное</b>, чтобы отображать положительные и отрицательные значения одним цветом. Если этот флажок установлен, настройки цвета для <b>Отрицательных</b> значений будут отключены.</li>
                        <li>Используйте <b>Направления столбца</b>, чтобы изменить направление диаграмм. По умолчанию выбрана опция <b>Контекст</b>, но вы также можете выбрать отображение <b>Слева направо</b> и <b>Справа налево</b> в зависимости от предпочтений представления данных.</li>
                        <li>Установите флажок напротив <b>Показывать только столбец</b>, чтобы скрыть значения на столбцах гистограммы.</li>
                    </ul>
                </li>
                <li>
                    <b>Оси</b>
                    <p>Выберите <b>Положение</b> оси панели данных относительно середины, чтобы разделить положительные и отрицательные значения. Выберите один из трех положений оси: <b>Автоматически</b>, <b>Середина ячейки</b> или <b>Нет</b>. Рядом также можно выбрать цвет оси.</p>
                </li>
                <li>Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования.</li>
                <li>Нажмите <b>ОК</b> для подтверждения.</li>
            </ul>
        </details>
        <details class="details-example">
            <summary><b>Редактирование Цветовых шкал</b></summary>
            <details class="details-example">
                <summary><b>Редактирование двухцветной шкалы</b></summary>
                <p>Окно <b>Изменение правила форматирования</b> для <b>Двухцветной шкалы</b> содержит следующие параметры:</p>
                <p><img alt="Редактировать форматирование - Двухцветная шкала" src="../images/editrule_2color.png" /></p>
                <ul type="disc">
                    <li>Щелкните <b>Правило</b>, чтобы изменить правило и условие форматирования, примененные ранее.</li>
                    <li>
                        <b>Мин. точка</b>/<b>Макс. точка</b> определяют тип значений для минимальной и максимальной точек цветовой шкалы. Типы максимального/минимального значения:
                        <ul>
                            <li>Минимум/Максимум</li>
                            <li>Числовой</li>
                            <li>Процентный</li>
                            <li>Формула</li>
                            <li>Процентиль</li>
                        </ul>
                        <p>По умолчанию выбрана опция <b>Минимум</b>/<b>Максимум</b>.</p>
                    </li>
                    <li>Щелкните поле <b>Выбор данных</b>, чтобы изменить диапазон ячеек для точек минимального максимального значений.</li>
                    <li>Измените цвет для каждой шкалы.</li>
                    <li>Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования.</li>
                    <li>Нажмите <b>ОК</b> для подтверждения.</li>
                </ul>
            </details>
            <details class="details-example">
                <summary><b>Редактирование трехцветной шкалы</b></summary>
                <p>Окно <b>Изменение правила форматирования</b> для <b>Трехцветной шкалы</b> содержит следующие параметры:</p>
                <p><img alt="Редактировать форматирование - Трехцветная шкала" src="../images/editrule_3color.png" /></p>
                <ul type="disc">
                    <li>Щелкните <b>Правило</b>, чтобы изменить правило и условие форматирования, примененные ранее.</li>
                    <li>
                        <b>Мин. точка</b>/<b>Средняя точка</b>/<b>Макс. точка</b> определяют тип значений для минимальной, среденй и максимальной точек цветовой шкалы.
                        <p>Типы значений минимальной/максимальной точки:</p>
                        <ul>
                            <li>Минимум/Максимум</li>
                            <li>Числовой</li>
                            <li>Процентный</li>
                            <li>Формула</li>
                            <li>Процентиль</li>
                        </ul>
                        <p>Типы значений средней точки:</p>
                        <ul>
                            <li>Числовой</li>
                            <li>Процентный</li>
                            <li>Формула</li>
                            <li>Процентиль</li>
                        </ul>
                        <p>По умолчанию для форматирования <b>Трехцветной шкалы</b> выбрана опция <b>Минимум</b>/<b>Процентиль</b>/<b>Максимум</b>.</p>
                    </li>
                    <li>Щелкните поле <b>Выбор данных</b>, чтобы изменить диапазон ячеек для точек минимального, среднего и максимального значений.</li>
                    <li>Измените цвет для каждой шкалы.</li>
                    <li>Поле <b>Просмотр</b> показывает, как ячейка будет выглядеть после форматирования.</li>
                    <li>Нажмите <b>ОК</b> для подтверждения.</li>
                </ul>
            </details>
        </details>
        <details class="details-example">
            <summary><b>Редактирование Наборов значков</b></summary>
            <p>Окно <b>Изменение правила форматирования</b> для <b>Наборов значков</b> содержит следующие параметры:</p>
            <p><img alt="Редактировать форматирование - Наборы значков" src="../images/editrule_iconsets.png" /></p>
            <ul type="disc">
                <li>Щелкните <b>Правило</b>, чтобы изменить правило и условие форматирования, примененные ранее;</li>
                <li>Нажмите <b>Стиль значка</b>, чтобы настроить стиль значков для созданного правила.</li>
                <li>Поставьте флажок напротив <b>Показать только значок</b>, чтобы отображать только значки в ячейках и скрывать значения.</li>
                <li>Нажмите <b>Значки в обратном порядке</b>, чтобы изменить порядок значков и расположить их от наименьшего к наибольшему значению. По умолчанию значки расположены от самого высокого до самого низкого значения.</li>
                <li>Установите правило для каждого значка и настройте операторы сравнения (<i>больше или равно</i>, <i>больше</i>), пороговые значения и тип значения (<i>Числовой</i>, <i>Процентный</i>, <i>Формула</i>, <i>Процентиль</i>), чтобы расположить значения в последовательности сверху вниз. По умолчанию значения разделены поровну.</li>
                <li>Нажмите <b>ОК</b> для подтверждения.</li>
            </ul>
        </details>
        <h2>Удаление Условного форматирования</h2>
        <p>Чтобы удалить все условные форматирования, перейдите на вкладку <b>Главная</b> и нажмите кнопку <span class="icon icon-conditionalformatting_button"></span> <b>Условное форматирование</b>  или на правой боковой панели во вкладке <b>Параметры ячейки</b> щелкните <b>Условное форматирование</b>, а затем раскрывающемся списке нажмите <b>Удалить правила</b> и выберите одно соответствующих действий:</p>
        <ul type="circle">
            <li>Из текущего выделенного фрагмента</li>
            <li>Из этого листа</li>
            <li>Из этой таблицы</li>
            <li>Из этой сводной таблицы</li>
        </ul>
    </div>
</body>
</html>
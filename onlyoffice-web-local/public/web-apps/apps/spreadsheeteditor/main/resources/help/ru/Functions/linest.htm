<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЛИНЕЙН</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЛИНЕЙН</h1>
			<p>Функция <b>ЛИНЕЙН</b> - одна из статистических функций. Она используется для вычисления статистики для ряда с использованием метода наименьших квадратов для вычисления прямой линии, которая наилучшим образом соответствует вашим данным, а затем возвращает массив, описывающий полученную линию; поскольку эта функция возвращает массив значений, ее необходимо вводить в виде формулы массива.</p>
			<p>Синтаксис функции <b>ЛИНЕЙН</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЛИНЕЙН( известные_значения_y, [известные_значения_x], [конст], [статистика] )</em></b></p> 
			<p>где</p>
			<p style="text-indent: 50px;"><b><em>известные_значения_y</em></b> - известный диапазон значений <em> y </em> в уравнении <em>y = mx + b</em>. Это обязательный аргумент.</p>
			<p style="text-indent: 50px;"><b><em>известные_значения_x</em></b> - известный диапазон значений <em> x </em> в уравнении <em>y = mx + b</em>. Это необязательный аргумент. Если он не указан, предполагается, что <em>известные_значения_x</em> является массивом <em>{1,2,3, ...}</em> с тем же количеством значений, что и <em>известные_значения_y</em>.</p>
			<p style="text-indent: 50px;"><b><em>конст</em></b> - логическое значение, указывающее, хотите ли вы установить <em>b</em> равным <em>0</em>. Это необязательный аргумент. Если установлено значение <em>ИСТИНА</em> или опущено, <em>b</em> рассчитывается как обычно. Если установлено значение <em>ЛОЖЬ</em>, <em>b</em> устанавливается равным <em>0</em>.</p>
			<p style="text-indent: 50px;"><b><em>статистика</em></b> - логическое значение, указывающее, хотите ли вы вернуть дополнительную статистику регрессии. Это необязательный аргумент. Если установлено значение <em>ИСТИНА</em>, функция возвращает дополнительную статистику регрессии. Если установлено значение <em>ЛОЖЬ</em> или опущено, функция не возвращает дополнительную статистику регрессии.</p>
			<p>Чтобы применить функцию <b>ЛИНЕЙН</b>,</p>
			<ol>
				<li>выберите ячейку, в которой вы хотите отобразить результат,</li>
				<li>
					щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
					<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
					<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
				</li>
				<li>выберите из списка группу функций <b>Статистические</b>,</li>
				<li>щелкните по функции <b>ЛИНЕЙН</b>,</li>
				<li>введите требуемые аргументы,</li>
				<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Первое значение итогового массива будет отображаться в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ЛИНЕЙН" src="../images/linest.png" /></p>
		</div>
	</body>
</html>
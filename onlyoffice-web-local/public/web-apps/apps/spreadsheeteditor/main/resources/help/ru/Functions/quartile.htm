<!DOCTYPE html>
<html>
	<head>
		<title>Функция КВАРТИЛЬ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция КВАРТИЛЬ</h1>
			<p>Функция <b>КВАРТИЛЬ</b> - это одна из статистических функций. Анализирует диапазон данных и возвращает квартиль.</p>
			<p>Синтаксис функции <b>КВАРТИЛЬ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>КВАРТИЛЬ(массив;часть)</em></b></p> 
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>массив</em></b> - выбранный диапазон ячеек для анализа,</p>
				<p style="text-indent: 50px;"><b><em>часть</em></b> - значение квартиля, которое требуется вернуть; числовое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка. Квартиль может иметь одно из следующих значений:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Числовое значение</b></td>
					<td><b>Квартиль</b></td>
				</tr>
				<tr>
					<td>0</td>
					<td>Минимальное значение в массиве данных</td>
				</tr>
				<tr>
					<td>1</td>
					<td>Первый квартиль (25-й процентиль)</td>
				</tr>
				<tr>
					<td>2</td>
					<td>Второй квартиль (50-й процентиль)</td>
				</tr>
				<tr>
					<td>3</td>
					<td>Третий квартиль (75-й процентиль)</td>
				</tr>
				<tr>
					<td>4</td>
					<td>Максимальное значение в массиве данных</td>
				</tr>
				
			</table>
			<p>Чтобы применить функцию <b>КВАРТИЛЬ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>КВАРТИЛЬ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция КВАРТИЛЬ" src="../images/quartile.png" /></p>
		</div>
	</body>
</html>
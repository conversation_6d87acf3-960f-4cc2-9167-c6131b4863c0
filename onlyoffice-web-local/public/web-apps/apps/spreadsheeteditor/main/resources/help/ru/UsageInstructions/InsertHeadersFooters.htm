<!DOCTYPE html>
<html>
	<head>
		<title>Вставка верхнего и нижнего колонтитулов</title>
		<meta charset="utf-8" />
        <meta name="description" content="Вставляйте верхние и нижние колонтитулы, чтобы отобразить дополнительную информацию в печатной версии таблицы." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка верхнего и нижнего колонтитулов</h1>
            <p>Колонтитулы позволяют вставить в печатную версию рабочего листа какую-то дополнительную информацию, такую как дата и время, номер страницы, имя листа и так далее. Колонтитулы отображаются в печатной версии электронной таблицы.</p>
			<p>Чтобы вставить верхний или нижний колонтитул на рабочий лист:</p>
			<ol>
			<li>перейдите на вкладку <b>Вставка</b> или <b>Макет</b>,</li>
			<li>нажмите кнопку <div class = "icon icon-header_footer_icon"></div> <b>Изменить колонтитулы</b> на верхней панели инструментов,</li>
            <li>откроется окно <b>Параметры колонтитулов</b>, в котором можно настроить следующие параметры:
                <ul>
                    <li>поставьте галочку <b>Особый для первой страницы</b>, если надо применить особый верхний или нижний колонтитул для самой первой страницы или вообще не добавлять на нее колонтитулы. Ниже появится вкладка <b>Первая страница</b>.</li>
                    <li>поставьте галочку <b>Разные для четных и нечетных</b>, чтобы добавить разные колонтитулы для четных и нечетных страниц. Ниже появятся вкладки <b>Нечетная страница</b> и <b>Четная страница</b>.</li>
                    <li>опция <b>Изменять масштаб вместе с документом</b> позволяет масштабировать колонтитулы вместе с рабочим листом. Этот параметр включен по умолчанию.</li>
                    <li>опция <b>Выровнять относительно полей страницы</b> позволяет выровнять левый колонтитул по левому полю и правый колонтитул по правому полю. Этот параметр включен по умолчанию.</li>
                </ul>
                <p><img alt="Параметры колонтитулов" src="../images/header_footer_settings.png" /></p>
            </li>
            <li>вставьте нужные данные. В зависимости от выбранных параметров, вы можете изменить настройки для <b>Всех страниц</b> или настроить колонтитулы для первой страницы, а также для нечетных и четных страниц в отдельности. Перейдите на нужную вкладку и настройте доступные параметры. Вы можете использовать один из готовых пресетов или вставить вручную нужные данные в левое, центральное и правое поля верхнего и нижнего колонтитула:
                    <ul>
                        <li>выберите один из доступных пресетов в списке <b>Предустановки</b>: <em>Страница 1</em>; <em>Страница 1 из ?</em>; <em>Лист1</em>; <em>Конфиденциально, дд/мм/гггг, Страница 1</em>; <em>Имя таблицы.xlsx</em>; <em>Лист1, Страница 1</em>; <em>Лист1, Конфиденциально, Страница 1</em>; <em>Имя таблицы.xlsx, Страница 1</em>; <em>Страница 1, Лист</em>; <em>Страница 1, Имя таблицы.xlsx</em>; <em>Автор, Страница 1, дд/мм/гггг</em>; <em>Подготовил: Автор дд/мм/гггг, Страница 1</em>.
                            <p>Будут добавлены соответствующие переменные.</p>
                        </li>
                        <li>установите курсор в левом, центральном или правом поле верхнего или нижнего колонтитула и используйте список <b>Вставить</b>, чтобы добавить <em>Номер страницы</em>, <em>Число страниц</em>, <em>Дату</em>, <em>Время</em>, <em>Имя файла</em>, <em>Имя листа</em>.</li>
                    </ul>
                </li>
            <li>отформатируйте текст, вставленный в верхний или нижний колонтитул, используя соответствующие опции. Можно изменить заданный по умолчанию шрифт, его размер, цвет, применить такие стили шрифта, как жирный, курсив, подчеркнутый, зачеркнутый, использовать подстрочные или надстрочные знаки.</li>
            <li>когда все будет готово, нажмите кнопку <b>OK</b>, чтобы применить изменения.</li>
			</ol>
            <p>Чтобы изменить добавленные колонтитулы, нажмите кнопку <span class="icon icon-header_footer_icon"></span> <b>Редактировать колонтитулы</b> на верхней панели инструментов, внесите нужные изменения в окне <b>Параметры колонтитулов</b>,  и нажмите на кнопку <b>OK</b>, чтобы сохранить изменения.</p>
            <p>Добавленные верхний и/или нижний колонтитул будут отображены в печатной версии электронной таблицы.</p>
		</div>
	</body>
</html>
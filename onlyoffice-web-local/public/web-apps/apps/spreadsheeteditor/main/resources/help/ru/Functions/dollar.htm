<!DOCTYPE html>
<html>
	<head>
		<title>Функция РУБЛЬ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция РУБЛЬ</h1>
			<p>Функция <b>РУБЛЬ</b> - это одна из функций для работы с текстом и данными. Преобразует число в текст, используя денежный формат $#.##.</p>
			<p>Синтаксис функции <b>РУБЛЬ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>РУБЛЬ(число;[число_знаков])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>число</em></b> - любое число, которое требуется преобразовать,</p> 
			<p style="text-indent: 50px;"><b><em>число_знаков</em></b> - число отображаемых десятичных знаков. Если этот аргумент опущен, то он полагается равным 2.</p>
			<p>Эти аргументы можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>РУБЛЬ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>РУБЛЬ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция РУБЛЬ" src="../images/dollar.png" /></p>
		</div>
	</body>
</html>
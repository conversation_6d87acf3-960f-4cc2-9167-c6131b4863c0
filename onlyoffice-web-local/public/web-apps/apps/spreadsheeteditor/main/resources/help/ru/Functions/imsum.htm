<!DOCTYPE html>
<html>
	<head>
		<title>Функция МНИМ.СУММ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция МНИМ.СУММ</h1>
			<p>Функция <b>МНИМ.СУММ</b> - это одна из инженерных функций. Возвращает сумму двух комплексных чисел, представленных в формате x + yi или x + yj.</p>
			<p>Синтаксис функции <b>МНИМ.СУММ</b>:</p>
      <p style="text-indent: 150px;">
        <b>
          <em>МНИМ.СУММ(список_аргументов)</em>
        </b>
      </p>
      <p>
        где<b>
          <em>список_аргументов</em>
        </b> - это до 30 комплексных чисел в формате x + yi или x + yj, введенных вручную или находящихся в ячейках, на которые даются ссылки.
      </p>
      <p>
        Чтобы применить функцию <b>МНИМ.СУММ</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Инженерные</b>,
        </li>
        <li>
          щелкните по функции <b>МНИМ.СУММ</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выделенной ячейке.</p>
      <p style="text-indent: 150px;"><img alt="Функция МНИМ.СУММ" src="../images/imsum.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ТЕКСТРАЗД</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Функция ТЕКСТРАЗД</h1>
			<p>Функция <b>ТЕКСТРАЗД</b> - это одна из функций для работы с текстом и данными. Разбивает текст на строки или столбцы с помощью разделителей.</p>
			<p class="note">Обратите внимание, что это формула массива. Чтобы узнать больше, обратитесь к статье <a href="../UsageInstructions/InsertArrayFormulas.htm" onclick="onhyperlinkclick(this)">Вставка формул массива</a>.</p>
			<p>Синтаксис функции <b>ТЕКСТРАЗД</b>:</p>
			<p style="text-indent: 150px;"><b><em>ТЕКСТРАЗД(текст,разделитель_столбцов,[разделитель_строк],[игнорировать_пустые], [сопоставление], [заполняющее_значение])</em></b></p>
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>текст</em></b> - текст, который нужно разделить.</p>
			<p style="text-indent: 50px;"><b><em>разделитель_столбцов</em></b> - необязательный аргумент. Задает текст, помечающий точку, в которой надо разделить текст по столбцам.</p>
			<p style="text-indent: 50px;"><b><em>разделитель_строк </em></b> - необязательный аргумент. Задает текст, помечающий точку, в которой надо разделить текст по строкам.</p>
			<p style="text-indent: 50px;"><b><em>игнорировать_пустые</em></b> - необязательный аргумент. Позволяет задать значение <b>TRUE</b>, чтобы игнорировать последовательные разделители. По умолчанию имеет значение <b>FALSE</b>, которое создает пустую ячейку.</p>
			<p style="text-indent: 50px;"><b><em>сопоставление</em></b> - необязательный аргумент. Если задано значение 1, выполняет сопоставление без учета регистра. По умолчанию (0) выполняет сопоставление с учетом регистра.</p>
			<p style="text-indent: 50px;"><b><em>заполняющее_значение</em></b> - задает значение, которым нужно дополнить результат. Значение по умолчанию: #N/A.</p>
			<p>Чтобы применить функцию <b>ТЕКСТРАЗД</b>,</p>
			<ol>
				<li>выделите ячейку, в которой требуется отобразить результат,</li>
				<li>
					щелкните по значку <b>Вставить функцию</b> <div class="icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
					<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
					<br />или щелкните по значку <div class="icon icon-function"></div> перед строкой формул,
				</li>
				<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
				<li>щелкните по функции <b>ТЕКСТРАЗД</b>,</li>
				<li>введите требуемые аргументы через точку с запятой,</li>
				<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ТЕКСТРАЗД" src="../images/textsplit.png" /></p>
		</div>
	</body>
</html>
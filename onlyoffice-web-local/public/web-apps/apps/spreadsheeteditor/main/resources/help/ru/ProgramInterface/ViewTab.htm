<!DOCTYPE html>
<html>
<head>
    <title>Вкладка Вид</title>
    <meta charset="utf-8" />
    <meta name="description" content="Знакомство с пользовательским интерфейсом редактора электронных таблиц - Вкладка Вид" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Вкладка Вид</h1>
        <p>
            Вкладка <b>Вид</b> в <a href="https://www.onlyoffice.com/ru/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редакторе электронных таблиц</b></a> позволяет управлять предустановками представления рабочего листа на основе примененных фильтров.
        </p>
        <div class="onlineDocumentFeatures">
            <p>Окно онлайн-редактора электронных таблиц:</p>
            <p><img alt="Вкладка Вид" src="../images/interface/viewtab.png" /></p>
        </div>
        <div class="desktopDocumentFeatures">
            <p>Окно десктопного редактора электронных таблиц:</p>
            <p><img alt="Вкладка Вид" src="../images/interface/desktop_viewtab.png" /></p>
        </div>
        <p>На этой вкладке доступны следующие параметры просмотра:</p>
        <ul>
            <li><b>Представление листа</b> - позволяет управлять предустановками <a href="../UsageInstructions/SheetView.htm" onclick="onhyperlinkclick(this)">представления листа</a>.</li>
            <li><b>Масштаб</b> - позволяет увеличивать и уменьшать масштаб документа.</li>
            <li><b>Тема интерфейса</b> позволяет изменить тему интерфейса на <b>Системную</b>, <b>Светлую</b>, <b>Классическую светлую</b>, <b>Темную</b> или <b>Контрастную темную</b>.</li>
            <li><b>Закрепить области</b>  - позволяет <a target="_blank" href="https://helpcenter.onlyoffice.com/ru/ONLYOFFICE-Editors/Editors-User-Guides/SpreadsheetEditor/Freeze-rows-and-columns.aspx" onclick="onhyperlinkclick(this)">закреплять и откреплять</a> определенные строки и столбцы.</li>
        </ul>
        <p>Следующие параметры позволяют настроить отображение или скрытие элементов. Чтобы сделать их видимыми, отметьте галочкой следующие элементы:</p>
        <ul>
            <li><b>Строка формул</b> - чтобы <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">строка формул</a> всегда отображалась над рабочей областью.</li>
            <li><b>Заголовки</b> - чтобы <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">заголовки</a>, столбцов вверху и заголовки строк слева всегда отображались.</li>
            <li><b>Линии сетки</b> - чтобы <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">линии сетки</a>, контуры ячеек всегда отображались.</li>
            <li><b>Отображать нули</b> - чтобы отображать значение "0" в ячейке.</li>
            <li><b>Всегда показывать панель инструментов</b> - всегда отображать верхнюю панель инструментов.</li>
            <li><b>Объединить строки листов и состояния</b> - отображать все инструменты навигации по листу и строку состояния в одной строке. Если этот флажок не установлен, строка состояния будет отображаться в виде двух строк.</li>
            <li><b>Левая панель</b> - чтобы левая панель отображалась.</li>
            <li><b>Правая панель</b> - чтобы правая панель отображалась.</li>
        </ul>
    </div>
</body>
</html>
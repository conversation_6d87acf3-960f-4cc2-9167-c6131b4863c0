﻿<!DOCTYPE html>
<html>
	<head>
		<title>LOGNORM.DIST Function</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>LOGNORM.DIST Function</h1>
			<p>The <b>LOGNORM.DIST</b> function is one of the statistical functions. It is used to return the lognormal distribution of <b><em>x</em></b>, where ln(<b><em>x</em></b>) is normally distributed with parameters <b><em>mean</em></b> and <b><em>standard-dev</em></b>.</p>
			<p>The <b>LOGNORM.DIST</b> function syntax is:</p> 
			<p style="text-indent: 150px;"><b><em>LOGNORM.DIST(x, mean, standard-dev, cumulative)</em></b></p> 
			<p><em>where</em></p> 
				<p style="text-indent: 50px;"><b><em>x</em></b> is the value at which the function should be calculated. A numeric value greater than 0.</p>
				<p style="text-indent: 50px;"><b><em>mean</em></b> is the mean of ln(<b><em>x</em></b>), a numeric value.</p>
				<p style="text-indent: 50px;"><b><em>standard-dev</em></b> is the standard deviation of ln(<b><em>x</em></b>), a numeric value greater than 0.</p>
                <p style="text-indent: 50px;"><b><em>cumulative</em></b> is a logical value (TRUE or FALSE) that determines the function form. If it is TRUE, the function returns the cumulative distribution function. If it is FALSE, the function returns the probability density function.</p>
				<p>The numeric values can be entered manually or included into the cells you make reference to.</p>
			<p>To apply the <b>LOGNORM.DIST</b> function,</p>
			<ol>
			<li>select the cell where you wish to display the result,</li>
			<li>click the <b>Insert function</b> <div class = "icon icon-insertfunction"></div> icon situated at the top toolbar,
				<br />or right-click within a selected cell and select the <b>Insert Function</b> option from the menu,
				<br />or click the <div class = "icon icon-function"></div> icon situated at the formula bar,
			</li>
			<li>select the <b>Statistical</b> function group from the list,</li>
			<li>click the <b>LOGNORM.DIST</b> function,</li>
			<li>enter the required arguments separating them by commas,</li>
			<li>press the <b>Enter</b> button.</li>
			</ol>
			<p>The result will be displayed in the selected cell.</p>
			<p style="text-indent: 150px;"><img alt="LOGNORM.DIST Function" src="../images/lognorm-dist.png" /></p>
		</div>
	</body>
</html>
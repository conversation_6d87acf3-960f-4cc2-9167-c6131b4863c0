<!DOCTYPE html>
<html>
	<head>
		<title>Функция F.ТЕСТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция F.ТЕСТ</h1>
			<p>Функция <b>F.ТЕСТ</b> - это одна из статистических функций. Возвращает результат F-теста, двустороннюю вероятность того, что разница между дисперсиями аргументов <b><em>массив1</em></b> и <b><em>массив2</em></b> несущественна. Эта функция позволяет определить, имеют ли две выборки различные дисперсии.</p>
			<p>Синтаксис функции <b>F.ТЕСТ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>F.ТЕСТ(массив1;массив2)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>массив1</em></b> - первый диапазон значений.</p>
			<p style="text-indent: 50px;"><b><em>массив2</em></b> - второй диапазон значений.</p>
			<p>Эти аргументы можно ввести вручную или использовать в качестве аргументов ссылки на ячейки. Текст, логические значения или пустые ячейки игнорируются, ячейки с нулевыми значениями учитываются. Если количество значений в диапазоне меньше 2 или если дисперсия любого из аргументов имеет нулевое значение, функция возвращает значение ошибки #ДЕЛ/0!.</p>
 			<p>Чтобы применить функцию <b>F.ТЕСТ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>F.ТЕСТ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция F.ТЕСТ" src="../images/f-test.png" /></p>
		</div>
	</body>
</html>
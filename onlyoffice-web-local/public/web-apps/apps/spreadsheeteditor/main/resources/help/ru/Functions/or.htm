<!DOCTYPE html>
<html>
	<head>
		<title>Функция ИЛИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ИЛИ</h1>
			<p>Функция <b>ИЛИ</b> - это одна из логических функций. Она используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение ЛОЖЬ, если все аргументы имеют значение ЛОЖЬ.</p>
			<p>Синтаксис функции <b>ИЛИ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ИЛИ(логическое_значение1;[логическое значение2]; ...)</em></b></p> 
			<p>где <b><em>логическое_значение1</em></b> - это значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>ИЛИ</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Логические</b>,</li>
			<li>щелкните по функции <b>ИЛИ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,
			<p class="note"><b>Примечание</b>: можно ввести до <b>255</b> логических значений.</p>
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке. Функция возвращает значение ИСТИНА, если хотя бы один аргумент имеет значение ИСТИНА.</p>
			<p><em>Например:</em></p>
			<p>Здесь три аргумента: <em>логическое_значение1</em> = <b>A1&lt;10</b>; <em>логическое_значение2</em> = <b>34&lt;10</b>; <em>логическое_значение3</em> = <b>50&lt;10</b>, где <b>A1</b> имеет значение <b>12</b>. Все эти логические выражения имеют значение <b>ЛОЖЬ</b>. Следовательно, функция возвращает значение <b>ЛОЖЬ</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция ИЛИ: ЛОЖЬ" src="../images/orfalse.png" /></p>
			<p>Если изменить значение <b>A1</b> с <b>12</b> на <b>2</b>, функция возвращает значение <b>ИСТИНА</b>:</p>
			<p style="text-indent: 150px;"><img alt="Функция ИЛИ: ИСТИНА" src="../images/ortrue.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЧАСТНОЕ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЧАСТНОЕ</h1>
			<p>Функция <b>ЧАСТНОЕ</b> - это одна из математических и тригонометрических функций. Возвращает целую часть результата деления с остатком.</p>
			<p>Синтаксис функции <b>ЧАСТНОЕ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЧАСТНОЕ(числитель;знаменатель)</em></b></p> 
			<p>где <b><em>числитель</em></b> и <b><em>знаменатель</em></b> - это числовые значения, введенные вручную или находящиеся в ячейках, на которые даются ссылки.</p>
			<p>Чтобы применить функцию <b>ЧАСТНОЕ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>ЧАСТНОЕ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ЧАСТНОЕ" src="../images/quotient.png" /></p>
		</div>
	</body>
</html>
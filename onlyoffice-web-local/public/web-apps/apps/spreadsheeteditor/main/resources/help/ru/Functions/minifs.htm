<!DOCTYPE html>
<html>
	<head>
		<title>Функция МИНЕСЛИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция МИНЕСЛИ</h1>
            <p>Функция <b>МИНЕСЛИ</b> - это одна из статистических функций. Возвращает минимальное значение из заданных определенными условиями или критериями ячеек.</p>
            <p>Синтаксис функции <b>МИНЕСЛИ</b>:</p>
            <p style="text-indent: 150px;"><b><em>МИНЕСЛИ(мин_диапазон;диапазон_условия1;условие1;[диапазон_условия2;условие2]; ...)</em></b></p>
            <p style="text-indent: 50px;"><b><em>мин_диапазон</em></b> - диапазон ячеек, для которого определяется минимальное значение.</p>
            <p style="text-indent: 50px;"><b><em>диапазон_условия1</em></b> - первый выбранный диапазон ячеек, к которому применяется <em>условие1</em>.</p>
            <p style="text-indent: 50px;"><b><em>условие1</em></b> - первое условие, которое должно выполняться. Оно применяется к <em>диапазону_условия1</em> и определяет, какие ячейки в диапазоне <em>мин_диапазон</em> будут оцениваться как имеющие минимальное значение. Это значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
            <p style="text-indent: 50px;"><b><em>диапазон_условия2, условие2, ...</em></b> - дополнительные диапазоны ячеек и соответствующие условия. Это необязательные аргументы.</p>
            <p class="note"><b>Примечание:</b> при указании условий можно использовать подстановочные знаки. Вопросительный знак "?" может замещать любой отдельный символ, а звездочку "*" можно использовать вместо любого количества символов.</p>
            <p>Чтобы применить функцию <b>МИНЕСЛИ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>МИНЕСЛИ</b>,</li>
                <li>введите требуемые аргументы вручную через точку с запятой или выделите диапазон ячеек мышью,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция МИНЕСЛИ" src="../images/minifs.png" /></p>
        </div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Сохранение / печать / скачивание таблицы</title>
		<meta charset="utf-8" />
		<meta name="description" content="Сохраните, распечатайте и скачайте электронную таблицу в разных форматах" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Сохранение / печать<span class="onlineDocumentFeatures"> / скачивание</span> таблицы</h1>
            <h3>Сохранение</h3>
            <p class="onlineDocumentFeatures">По умолчанию онлайн-редактор электронных таблиц автоматически сохраняет файл каждые 2 секунды, когда вы работаете над ним, чтобы не допустить потери данных в случае непредвиденного закрытия программы. Если вы совместно редактируете файл в <b>Быстром</b> режиме, таймер запрашивает наличие изменений 25 раз в секунду и сохраняет их, если они были внесены. При совместном редактировании файла в <b>Строгом</b> режиме изменения автоматически сохраняются каждые 10 минут. При необходимости можно легко выбрать предпочтительный режим совместного редактирования или отключить функцию автоматического сохранения на странице <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Дополнительные параметры</a>.</p>
            <p>Чтобы сохранить текущую электронную таблицу вручную в текущем формате и местоположении,</p>
            <ul>
                <li>щелкните по значку <b>Сохранить</b> <div class = "icon icon-save"></div> в левой части шапки редактора, или</li>
                <li>используйте сочетание клавиш <b>Ctrl+S</b>, или</li>
                <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов и выберите опцию <b>Сохранить</b>.</li>
            </ul>
            <p class="note desktopDocumentFeatures">Чтобы не допустить потери данных в <em>десктопной версии</em> в случае непредвиденного закрытия программы, вы можете включить опцию <b>Автовосстановление</b> на странице <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Дополнительные параметры</a>. </p>
            <div class="desktopDocumentFeatures">
                <p>Чтобы в <em>десктопной версии</em> сохранить электронную таблицу под другим именем, в другом местоположении или в другом формате,</p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Сохранить как</b>,</li>
                    <li>выберите один из доступных форматов: XLSX, ODS, CSV, PDF, PDF/A. Также можно выбрать вариант <b>Шаблон таблицы</b> XLTX или OTS.</li>
                </ol>
            </div>
            <div class="onlineDocumentFeatures">
                <h3>Скачивание</h3>
                <p>Чтобы в <em>онлайн-версии</em> скачать готовую электронную таблицу и сохранить ее на жестком диске компьютера,</p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Скачать как</b>,</li>
                    <li>
                        выберите один из доступных форматов: XLSX, PDF, ODS, CSV, XLTX, PDF/A, OTS.
                        <p class="note"><b>Примечание</b>: если вы выберете формат CSV, весь функционал (форматирование шрифта, формулы и так далее), кроме обычного текста, не сохранится в файле CSV. Если вы продолжите сохранение, откроется окно <b>Выбрать параметры CSV</b>. По умолчанию в качестве типа <b>Кодировки</b> используется <em>Unicode (UTF-8)</em>. Стандартным <b>Разделителем</b> является <em>запятая</em> (,), но доступны также следующие варианты: <em>точка с запятой</em> (;), <em>двоеточие</em> (:), <em>Табуляция</em>, <em>Пробел</em> и <em>Другое</em> (эта опция позволяет задать пользовательский символ разделителя).</p>
                    </li>
                </ol>
                <h3>Сохранение копии</h3>
                <p>Чтобы в <em>онлайн-версии</em> сохранить копию электронной таблицы на портале,</p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Сохранить копию как</b>,</li>
                    <li>выберите один из доступных форматов: XLSX, PDF, ODS, CSV, XLTX, PDF/A, OTS,</li>
                    <li>выберите местоположение файла на портале и нажмите <b>Сохранить</b>.</li>
                </ol>
            </div>
            <h3 id="print">Печать</h3>
            <p>Чтобы распечатать текущую электронную таблицу:</p>
            <ul>
                <li>щелкните по значку <b>Печать</b> <div class = "icon icon-print"></div> в левой части шапки редактора, или</li>
                <li>используйте сочетание клавиш <b>Ctrl+P</b>, или</li>
                <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов и выберите опцию <b>Печать</b>.</li>
            </ul>
            <div class="note">
                В браузере Firefox возможна печатать таблицы без предварительной загрузки в виде файла .pdf.
            </div>
            <p>Откроется окно <b>Предпросмотра</b>, в котором можно изменить параметры печати, заданные по умолчанию. Нажмите на кнопку <b>Показать детали</b> внизу окна, чтобы отобразить все параметры.</p>
            <p class="note">Параметры печати можно также настроить на странице <b>Дополнительные параметры</b>: нажмите на вкладку <b>Файл</b> на верхней панели инструментов и перейдите в раздел: <b>Дополнительные параметры</b> &gt;&gt; <b>Параметры страницы</b>. <br />На вкладке <b>Макет</b> верхней панели инструментов также доступны некоторые из этих настроек: <b>Поля</b>, <b>Ориентация</b>, <b>Размер</b> страницы, <b>Область печати</b>, <a href="ScaleToFit.htm" onclick="onhyperlinkclick(this)"><b>Вписать</b></a>.</p>
            <p><img alt="Окно Параметры печати" src="../images/printsettingswindow.png" /></p>
            <p>Здесь можно задать следующие параметры:</p>
            <ul>
                <li>
                    <b>Диапазон печати</b> - укажите, что необходимо напечатать: весь <b>Текущий лист</b>, <b>Все листы</b> электронной таблицы или предварительно выделенный диапазон ячеек (<b>Выделенный фрагмент</b>),
                    <p>Если вы ранее задали постоянную область печати, но хотите напечатать весь лист, поставьте галочку рядом с опцией <b>Игнорировать область печати</b>.</p>
                </li>
                <li><b>Параметры листа</b> - укажите индивидуальные параметры печати для каждого отдельного листа, если в выпадающем списке <b>Диапазон печати</b> выбрана опция <b>Все листы</b>,</li>
                <li><b>Размер страницы</b> - выберите из выпадающего списка один из доступных размеров,</li>
                <li><b>Ориентация страницы</b> - выберите опцию <b>Книжная</b>, если при печати требуется расположить таблицу на странице вертикально, или используйте опцию <b>Альбомная</b>, чтобы расположить ее горизонтально,</li>
                <li>
                    <b>Масштаб</b> - если вы не хотите, чтобы некоторые столбцы или строки были напечатаны на второй странице, можно сжать содержимое листа, чтобы оно помещалось на одной странице, выбрав соответствующую опцию: <b>Вписать лист на одну страницу</b>, <b>Вписать все столбцы на одну страницу</b> или <b>Вписать все строки на одну страницу</b>. Оставьте опцию <b>Реальный размер</b>, чтобы распечатать лист без корректировки,
                    <p>При выборе пункта меню <b>Настраиваемые параметры</b> откроется окно <b>Настройки масштаба</b>:</p>
                    <p><img alt="Окно Настройки масштаба" src="../images/scalesettings.png" /></p>
                    <ol>
                        <li><b>Разместить не более чем на</b>: позволяет выбрать нужное количество страниц, на котором должен разместиться напечатанный рабочий лист. Выберите нужное количество страниц из списков <b>Ширина</b> и <b>Высота</b>.</li>
                        <li><b>Установить</b>: позволяет увеличить или уменьшить масштаб рабочего листа, чтобы он поместился на напечатанных страницах, указав вручную значение в процентах от обычного размера.</li>
                    </ol>
                </li>
                <li><b>Печатать заголовки</b> - если вы хотите печатать заголовки строк или столбцов на каждой странице, используйте опцию <b>Повторять строки сверху</b> или <b>Повторять столбцы слева</b> и выберите одну из доступных опций из выпадающего списка: повторять элементы из выбранного диапазона, повторять закрепленные строки, повторять только первую строку/первый столбец.</li>
                <li><b>Поля</b> - укажите расстояние между данными рабочего листа и краями печатной страницы, изменив размеры по умолчанию в полях <b>Сверху</b>, <b>Снизу</b>, <b>Слева</b> и <b>Справа</b>,</li>
                <li><b>Печать</b> - укажите элементы рабочего листа, которые необходимо выводить на печать, установив соответствующие флажки: <b>Печать сетки</b> и <b>Печать заголовков строк и столбцов</b>.</li>
                <li>
                    <a href="../UsageInstructions/InsertHeadersFooters.htm" onclick="onhyperlinkclick(this)"><b>Параметры верхнего и нижнего колонтитулов</b></a> - позволяет добавить некоторую дополнительную информацию к печатному листу, такую как дата и время, номер страницы, имя листа и т.д. Верхние и нижние колонтитулы будут отображаться в печатной версии электронной таблицы.
                </li>
            </ul>
            <p>После настройки параметров печати нажмите кнопку <b>Печать</b>, чтобы сохранить изменения и распечатать электронную таблицу, или кнопку <b>Сохранить</b>, чтобы сохранить изменения, внесенные в параметры печати.</p>
            <p><span class="desktopDocumentFeatures">В <em>десктопной версии</em> документ будет напрямую отправлен на печать.</span> <span class="onlineDocumentFeatures">В <em>онлайн-версии</em> на основе данного документа будет сгенерирован файл PDF. Вы можете открыть и распечатать его, или сохранить его на жестком диске компьютера или съемном носителе, чтобы распечатать позже. В некоторых браузерах, например Хром и Опера, есть встроенная возможность для прямой печати.</span></p>
            <h4 id="printarea">Настройка области печати</h4>
            <p>Если требуется распечатать только выделенный диапазон ячеек вместо всего листа, можно использовать настройку <b>Выделенный фрагмент</b> в выпадающем списке <b>Диапазон печати</b>. Эта настройка не сохраняется при сохранении рабочей книги и подходит для однократного использования. </p>
            <p>Если какой-то диапазон ячеек требуется распечатывать неоднократно, можно задать постоянную область печати на рабочем листе. Область печати будет сохранена при сохранении рабочей книги и может использоваться при последующем открытии электронной таблицы. Можно также задать несколько постоянных областей печати на листе, в этом случае каждая из них будет выводиться на печать на отдельной странице.</p>
            <p>Чтобы задать область печати:</p>
            <ol>
                <li>выделите нужный диапазон ячеек на рабочем листе. Чтобы выделить несколько диапазонов, удерживайте клавишу <em>Ctrl</em>,</li>
                <li>перейдите на вкладку <b>Макет</b> верхней панели инструментов,</li>
                <li>нажмите на стрелку рядом с кнопкой <div class = "icon icon-printareabutton"></div> <b>Область печати</b> и выберите опцию <b>Задать область печати</b>.</li>
            </ol>
            <p>Созданная область печати сохраняется при сохранении рабочей книги. При последующем открытии файла на печать будет выводиться заданная область печати.</p>
            <p class="note">При создании области печати также автоматически создается <a href="UseNamedRanges.htm" onclick="onhyperlinkclick(this)">именованный диапазон</a> <em>Область_печати</em>, отображаемый в <b>Диспетчере имен</b>. Чтобы выделить границы всех областей печати на текущем рабочем листе, можно нажать на стрелку в поле "Имя" слева от строки формул и выбрать из списка имен <em>Область_печати</em>. </p>
            <p>Чтобы добавить ячейки в область печати:</p>
            <ol>
                <li>откройте нужный рабочий лист, на котором добавлена область печати,</li>
                <li>выделите нужный диапазон ячеек на рабочем листе,</li>
                <li>перейдите на вкладку <b>Макет</b> верхней панели инструментов,</li>
                <li>нажмите на стрелку рядом с кнопкой <div class = "icon icon-printareabutton"></div> <b>Область печати</b> и выберите опцию <b>Добавить в область печати</b>.</li>
            </ol>
            <p><!--Если выделенный диапазон ячеек является смежным с существующей областью печати, он будет добавлен к существующей области печати. Если выделенный диапазон ячеек не является смежным,--> Будет добавлена новая область печати. Каждая из областей печати будет выводиться на печать на отдельной странице.</p>
            <p>Чтобы удалить область печати:</p>
            <ol>
                <li>откройте нужный рабочий лист, на котором добавлена область печати,</li>
                <li>перейдите на вкладку <b>Макет</b> верхней панели инструментов,</li>
                <li>нажмите на стрелку рядом с кнопкой <div class = "icon icon-printareabutton"></div> <b>Область печати</b> и выберите опцию <b>Очистить область печати</b>.</li>
            </ol>
            <p>Будут удалены все области печати, существующие на этом листе. После этого на печать будет выводиться весь лист.</p>
        </div>
	</body>
</html>
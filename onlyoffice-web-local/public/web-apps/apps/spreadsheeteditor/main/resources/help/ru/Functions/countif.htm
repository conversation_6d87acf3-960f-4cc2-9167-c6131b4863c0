<!DOCTYPE html>
<html>
	<head>
		<title>Функция СЧЁТЕСЛИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СЧЁТЕСЛИ</h1>
			<p>Функция <b>СЧЁТЕСЛИ</b> - это одна из статистических функций. Она используется для подсчета количества ячеек выделенного диапазона, соответствующих заданному условию.</p>
			<p>Синтаксис функции <b>СЧЁТЕСЛИ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СЧЁТЕСЛИ(диапазон_ячеек;условие)</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>диапазон_ячеек</em></b> - выбранный диапазон ячеек для подсчета с применением заданного условия,</p> 
				<p style="text-indent: 50px;"><b><em>условие</em></b> - условие, которое требуется применить, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
			<p class="note"><b>Примечание</b>: аргумент <b><em>условие</em></b> может содержать подстановочные знаки — вопросительный знак (?), соответствующий одному символу, и звездочку (*), соответствующую любому количеству символов. Если требуется найти вопросительный знак или звездочку, введите перед этим символом тильду (~).</p>
			<p>Чтобы применить функцию <b>СЧЁТЕСЛИ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>СЧЁТЕСЛИ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СЧЁТЕСЛИ" src="../images/countif.png" /></p>
		</div>
	</body>
</html>
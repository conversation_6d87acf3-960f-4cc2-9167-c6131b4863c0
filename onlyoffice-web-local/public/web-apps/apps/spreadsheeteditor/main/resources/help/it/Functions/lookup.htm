﻿<!DOCTYPE html>
<html>
	<head>
		<title>Funzione LOOKUP</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
	</head>
	<body>
		<div class="mainpart">
			<h1>Funzione LOOKUP</h1>
			<p>La funzione <b>LOOKUP</b> è una delle funzioni della categoria Ricerca e riferimento. Si usa per restituire un valore da un intervallo scelto (riga o colonna contenente i dati in ordine crescente).</p>
			<p>La sintassi della funzione <b>LOOKUP</b> è:</p> 
			<p style="text-indent: 150px;"><b><em>LOOKUP(lookup-value, lookup-vector, result-vector)</em></b></p>
			<p><em>dove</em></p> 
				<p style="text-indent: 50px;"><b><em>lookup-value</em></b> è un valore da cercare.</p> 
				<p style="text-indent: 50px;"><b><em>lookup-vector</em></b> è una singola riga o colonna contenente i dati in ordine crescente.</p> 
				<p style="text-indent: 50px;"><b><em>lookup-result</em></b> è una singola riga o colonna della stessa dimensione che <b><em>lookup-vector</em></b>.</p> 
			<p>La funzione cerca <b><em>lookup-value</em></b> in <b><em>lookup-vector</em></b> e restituisce il valore nella stessa posizione in <b><em>lookup-result</em></b>.</p>
			<p class="note"><b>Nota</b>: se <b>lookup-value</b> è più piccolo che tutti i valori in <b>lookup-vector</b>, la funzione restituisce l'errore <b>#N/A</b>. Se nessun valore corrisponde a <b>lookup-value</b>, la funzione trova il valore più grande in <b>lookup-vector</b> minore o uguale al valore.</p>
			<p>Per applicare la funzione <b>LOOKUP</b>,</p>
			<ol>
			<li>selezionate la cella dove desiderate visualizzare il risultato,</li>
			<li>cliccate sull'icona <b>Inserisci funzione</b> <div class = "icon icon-insertfunction"></div> sulla barra degli strumenti superiore,
				<br />o cliccate con il tasto destro del mouse sulla cella scelta e selezionate l'opzione <b>Inserisci funzione</b> dal menu contestuale,
				<br />o cliccate sull'icona <div class = "icon icon-function"></div> prima della barra della formula,
			</li>
			<li>selezionate il gruppo di funzioni <b>Ricerca e riferimento</b> dall'elenco,</li>
			<li>cliccate sulla funzione <b>LOOKUP</b>,</li>
			<li>inserite gli argomenti richiesti separati da virgola,</li>
			<li>premete il tasto <b>Enter</b>.</li>
			</ol>
			<p>Il risultato sarà visualizzato nella cella scelta.</p>
			<p style="text-indent: 150px;"><img alt="Funzione LOOKUP" src="../images/lookup.png" /></p>
		</div>
	</body>
</html>
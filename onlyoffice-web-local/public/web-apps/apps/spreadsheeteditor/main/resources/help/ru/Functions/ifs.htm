<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЕСЛИМН</title>
		<meta charset="utf-8" />
        <meta name="description" content="Логическая функция ЕСЛИМН проверяет соответствие одному или нескольким условиям и возвращает значение для первого условия, принимающего значение ИСТИНА." />
        <meta name="keywords" content="еслимн, функция еслимн, функция еслимн excel, функция еслимн в excel, еслимн эксель">
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЕСЛИМН</h1>
			<p>Функция <b>ЕСЛИМН</b> - это одна из логических функций. Проверяет соответствие одному или нескольким условиям и возвращает значение для первого условия, принимающего значение ИСТИНА.</p>
			<p>Синтаксис функции <b>ЕСЛИМН</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЕСЛИМН(условие1;значение1;[условие2;значение2]; ...)</em></b></p> 
			<p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>условие1</em></b> - первое условие, которое оценивается как имеющие значение ИСТИНА или ЛОЖЬ.</p>
            <p style="text-indent: 50px;"><b><em>значение1</em></b> - значение, возвращаемое, если <b><em>условие1</em></b> принимает значение ИСТИНА.</p>
            <p style="text-indent: 50px;"><b><em>условие2, значение2, ...</em></b> - дополнительные условия и возвращаемые значения. Это необязательные аргументы. Можно проверить до 127 условий.</p>
            <p>Эти аргументы можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
            <h2>Как работает функция ЕСЛИМН</h2>
            <p>Чтобы применить функцию <b>ЕСЛИМН</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Логические</b>,</li>
                <li>щелкните по функции <b>ЕСЛИМН</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p><em>Например:</em></p>
			<p>Здесь используются следующие аргументы: <em>условие1</em> = <b>A1&lt;100</b>, <em>значение1</em> = <b>1</b>, <em>условие2</em> = <b>A1&gt;100</b>, <em>значение2</em> = <b>2</b>, где <b>A1</b> имеет значение <b>120</b>. Второе логическое выражение имеет значение <b>ИСТИНА</b>. Следовательно, функция возвращает значение <b>2</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция ЕСЛИМН" src="../images/ifs.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция КОНМЕСЯЦА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция КОНМЕСЯЦА</h1>
			<p>Функция <b>КОНМЕСЯЦА</b> - это одна из функций даты и времени. Возвращает порядковый номер последнего дня месяца, который идет на заданное число месяцев до или после заданной начальной даты. </p>
			<p>Синтаксис функции <b>КОНМЕСЯЦА</b>:</p> 
			<p style="text-indent: 150px;"><b><em>КОНМЕСЯЦА(нач_дата;число_месяцев)</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>нач_дата</em></b> - число, представляющее первую дату периода, введенное с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени.</p>
				<p style="text-indent: 50px;"><b><em>число_месяцев</em></b> - количество месяцев до или после <b><em>начальной даты</em></b>. Если значение аргумента <b><em>число_месяцев</em></b> имеет отрицательный знак, функция КОНМЕСЯЦА возвращает порядковый номер даты, идущей перед заданной <b>начальной датой</b>. Если значение аргумента <b><em>число_месяцев</em></b> имеет положительный знак, функция КОНМЕСЯЦА возвращает порядковый номер даты, идущей после заданной <b>начальной даты</b>.</p>
			<p>Чтобы применить функцию <b>КОНМЕСЯЦА</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>КОНМЕСЯЦА</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция КОНМЕСЯЦА" src="../images/eomonth.png" /></p>
		</div>
	</body>
</html>
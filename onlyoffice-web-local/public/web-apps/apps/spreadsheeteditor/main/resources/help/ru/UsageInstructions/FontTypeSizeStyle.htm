<!DOCTYPE html>
<html>
	<head>
		<title>Настройка типа, размера, стиля и цветов шрифта</title>
		<meta charset="utf-8" />
		<meta name="description" content="Измените следующие параметры форматирования: тип, размер, стиль и цвета шрифта" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Настройка типа, размера, стиля и цветов шрифта</h1>
			<p>Можно выбрать тип шрифта и его размер, применить один из стилей оформления и изменить цвета шрифта и фона, используя соответствующие значки, расположенные на вкладке <b>Главная</b> верхней панели инструментов.</p>
			<p class="note"><b>Примечание</b>: если необходимо применить форматирование к данным, которые уже есть в электронной таблице, выделите их мышью или <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">с помощью клавиатуры</a>, а затем примените форматирование. Если форматирование требуется применить к нескольким ячейкам или диапазонам ячеек, которые не являются смежными, удерживайте клавишу <b>Ctrl</b> при выделении ячеек и диапазонов с помощью мыши.</p>
			<table>
				<tr>
                    <td width="10%">Шрифт</td>
                    <td width="15%"><div class = "big big-fontfamily"></div></td>
					<td>Используется для выбора шрифта из списка доступных. <span class="desktopDocumentFeatures">Если требуемый шрифт отсутствует в списке, его можно скачать и установить в вашей операционной системе, после чего он будет доступен для использования в <em>десктопной версии</em>.</span></td>
				</tr>
				<tr>
					<td>Размер шрифта</td>
					<td><div class = "icon icon-fontsize"></div></td>
					<td>Используется для выбора предустановленного значения размера шрифта из выпадающего списка (доступны следующие стандартные значения: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 и 96). Также можно вручную ввести произвольное значение до 409 пунктов в поле ввода и нажать клавишу <em>Enter</em>.</td>
				</tr>
				<tr>
					<td>Увеличить размер шрифта</td>
					<td><div class = "icon icon-larger"></div></td>
					<td>Используется для изменения размера шрифта, делая его на один пункт крупнее при каждом нажатии на кнопку.</td>
				</tr>
				<tr>
					<td>Уменьшить размер шрифта</td>
					<td><div class = "icon icon-smaller"></div></td>
					<td>Используется для изменения размера шрифта, делая его на один пункт мельче при каждом нажатии на кнопку.</td>
				</tr>
				<tr>
					<td>Полужирный</td>
					<td><div class = "icon icon-bold"></div></td>
					<td>Используется для придания шрифту большей насыщенности.</td>
				</tr>
				<tr>
					<td>Курсив</td>
					<td><div class = "icon icon-italic"></div></td>
					<td>Используется для придания шрифту наклона вправо.</td>
				</tr>
				<tr>
					<td>Подчеркнутый</td>
					<td><div class = "icon icon-underline"></div></td>
					<td>Используется для подчеркивания текста чертой, проведенной под буквами.</td>
				</tr>
                <tr>
                    <td>Зачёркнутый</td>
                    <td><div class = "icon icon-strike"></div></td>
                    <td>Используется для зачёркивания текста чертой, проведенной по буквам.</td>
                </tr>
                <tr>
                    <td>Подстрочные/надстрочные знаки</td>
                    <td><div class = "icon icon-subscripticon"></div></td>
                    <td>Позволяет выбрать опцию <b>Надстрочные знаки</b> или <b>Подстрочные знаки</b>. Опция <b>Надстрочные знаки</b> используется, чтобы сделать текст мельче и поместить его в верхней части строки, например, как в дробях. Опция <b>Подстрочные знаки</b> используется, чтобы сделать текст мельче и поместить его в нижней части строки, например, как в химических формулах.</td>
                </tr>
				<tr>
					<td>Цвет шрифта</td>
					<td><div class = "icon icon-fontcolor"></div></td>
					<td>Используется для изменения цвета букв/символов в ячейках.</td>
				</tr>
				<tr>
					<td>Цвет заливки</td>
					<td><div class = "icon icon-backgroundcolor"></div></td>
					<td>Используется для изменения цвета заливки ячейки. С помощью этого значка можно применить заливку <em>сплошным цветом</em>. Цвет заливки ячейки также можно изменить с помощью палитры <b>Заливка</b> на вкладке <a href="../UsageInstructions/AddBorders.htm" onclick="onhyperlinkclick(this)">Параметры ячейки</a> правой боковой панели.</td>
				</tr>
				<tr>
					<td id="colorscheme">Изменение цветовой схемы</td>
					<td><div class = "icon icon-changecolorscheme"></div></td>
					<td>Используется для изменения цветовой палитры по умолчанию для элементов рабочего листа (шрифт, фон, диаграммы и их элементы) путем выбора одной из доступных схем: <b>Новая офисная</b>, <b>Стандартная</b>, <b>Оттенки серого</b>, <b>Апекс</b>, <b>Аспект</b>, <b>Официальная</b>, <b>Открытая</b>, <b>Справедливость</b>, <b>Поток</b>, <b>Литейная</b>, <b>Обычная</b>, <b>Метро</b>, <b>Модульная</b>, <b>Изящная</b>, <b>Эркер</b>, <b>Начальная</b>, <b>Бумажная</b>, <b>Солнцестояние</b>, <b>Техническая</b>, <b>Трек</b>, <b>Городская</b> или <b>Яркая</b>.</td>
				</tr>
			</table>
			<p><p class="note"><b>Примечание</b>: можно также применить один из предустановленных стилей форматирования текста. Для этого выделите ячейку, которую требуется отформатировать, и выберите нужную предустановку из списка на вкладке <b>Главная</b> верхней панели инструментов:<br /><br />
				<img alt="Предустановки форматирования текста" src="../images/presets.png" />
			   </p>
			<p>Чтобы изменить цвет шрифта или использовать заливку <em>сплошным цветом</em> в качестве фона ячейки,</p>
			<ol>
			<li>выделите мышью символы/ячейки или весь рабочий лист, используя сочетание клавиш <b>Ctrl+A</b>,</li>
				<li>щелкните по соответствующему значку на верхней панели инструментов,</li>
				<li>выберите любой цвет на доступных палитрах
					<p><img alt="Палитра" src="../images/palette.png" /></p>
					<ul>
						<li><b>Цвета темы</b> - цвета, соответствующие выбранной цветовой схеме электронной таблицы.</li>
						<li><b>Стандартные цвета</b> - набор стандартных цветов.</li>
						<li><b>Пользовательский цвет</b> - щелкните по этой надписи, если в доступных палитрах нет нужного цвета. Выберите нужный цветовой диапазон, перемещая вертикальный ползунок цвета, и определите конкретный цвет, перетаскивая инструмент для выбора цвета внутри большого квадратного цветового поля. Как только Вы выберете какой-то цвет, в полях справа отобразятся соответствующие цветовые значения RGB и sRGB. Также можно задать цвет на базе цветовой модели RGB, введя нужные числовые значения в полях <b>R</b>, <b>G</b>, <b>B</b> (красный, зеленый, синий), или указать шестнадцатеричный код sRGB в поле, отмеченном знаком <b>#</b>. Выбранный цвет появится в окне предпросмотра <b>Новый</b>. Если к объекту был ранее применен какой-то пользовательский цвет, этот цвет отображается в окне <b>Текущий</b>, так что вы можете сравнить исходный и измененный цвета. Когда цвет будет задан, нажмите на кнопку <b>Добавить</b>:
						<p><img alt="Палитра - Пользовательский цвет" src="../../../../../../common/main/resources/help/ru/images/palette_custom.png" /></p>
						<p>Пользовательский цвет будет применен к тексту и добавлен в палитру <b>Пользовательский цвет</b>.</p>
						</li>
						</ul>
					</li>
			</ol>
			<p>Чтобы очистить цвет фона определенной ячейки,</p>
			<ol>
				<li>выделите мышью ячейку или диапазон ячеек, или весь рабочий лист с помощью сочетания клавиш <b>Ctrl+A</b>,</li>
				<li>нажмите на значок <b>Цвет заливки</b> <div class = "icon icon-backgroundcolor"></div> на вкладке <b>Главная</b> верхней панели инструментов,</li>
				<li>выберите значок <div class = "icon icon-nofill"></div>.</li>
			</ol>
		</div>
	</body>
</html>
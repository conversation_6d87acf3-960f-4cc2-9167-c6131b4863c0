<!DOCTYPE html>
<html>
	<head>
		<title>Создание срезов для форматированных таблиц</title>
		<meta charset="utf-8" />
        <meta name="description" content="Создавайте срезы для форматированных таблиц для быстрой фильтрации данных." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Создание срезов для форматированных таблиц</h1>
            <h3>Создание нового среза</h3>
			<p>После создания новой <a href="FormattedTables.htm" onclick="onhyperlinkclick(this)">форматированной таблицы</a> или <a href="PivotTables.htm" onclick="onhyperlinkclick(this)">сводной таблицы</a> вы можете создавать срезы для быстрой фильтрации данных. Для этого:</p>
			<ol>
				<li>выделите мышью хотя бы одну ячейку в форматированной таблице и нажмите значок <b>Параметры таблицы</b> <div class = "icon icon-table_settings_icon"></div> справа.</li>
				<li>нажмите на пункт <div class = "icon icon-insertslicer"></div> <b>Вставить срез</b> на вкладке <b>Параметры таблицы</b> правой боковой панели. Также можно перейти на вкладку <b>Вставка</b> верхней панели инструментов и нажать кнопку <div class = "icon icon-slicer_icon"></div> <b>Срез</b>. Откроется окно <b>Вставка срезов</b>:
                <p><img alt="Вставка срезов" src="../images/insertslicer_window.png" /></p>
                </li>
				<li>отметьте галочками нужные столбцы в окне <b>Вставка срезов</b>.</li>
				<li>нажмите кнопку <b>OK</b>.</li>
			</ol>
            <p>Срез будет добавлен для каждого из выделенных столбцов. Если вы добавили несколько срезов, они будут перекрывать друг друга. После того как срез будет добавлен, можно <a href="ManipulateObjects.htm" onclick="onhyperlinkclick(this)">изменить его размер и местоположение</a> и другие параметры.</p>
            <p><img alt="Срез" src="../images/slicer.png" /></p>
            <p>Срез содержит кнопки, на которые можно нажимать, чтобы отфильтровать форматированную таблицу. Кнопки, соответствующие пустым ячейкам, обозначены меткой <em>(пусто)</em>. При нажатии на кнопку среза будет снято выделение с других кнопок, а соответствующий столбец в исходной таблице будет отфильтрован, чтобы в нем был отображен только выбранный элемент:</p>
            <p><img alt="Срез" src="../images/slicer_filter.png" /></p>
            <p>Если вы добавили несколько срезов, изменения в одном из срезов могут повлиять на элементы другого среза. Когда к срезу применен один или несколько фильтров, в другом срезе могут появиться элементы без данных (более светлого цвета):</p>
            <p><img alt="Срез - элементы без данных" src="../images/slicer_nodata.png" /></p>
            <p>Способ отображения элементов без данных в срезе можно настроить в параметрах среза.</p>
            <p>Чтобы выделить несколько кнопок среза, используйте значок <span class="icon icon-multiselect"></span> <b>Множественное выделение</b> в правом верхнем углу среза или нажмите <em>Alt+S</em>. Выделите нужные кнопки среза, нажимая их по очереди.</p>
            <p>Чтобы очистить фильтр среза, используйте значок <span class="icon icon-slicer_clearfilter"></span> <b>Очистить фильтр</b> в правом верхнем углу среза или нажмите  <em>Alt+C</em>.</p>
            
            <h3>Редактирование срезов</h3>
            <p>Некоторые параметры среза можно изменить с помощью вкладки <b>Параметры среза</b> на правой боковой панели, которая открывается при выделении среза мышью.</p>
            <p>Эту вкладку можно скрыть или показать, нажав на значок <span class="icon icon-slicer_settings"></span> справа.</p>
            <p><img alt="Вкладка Параметры среза" src="../images/right_slicer.png" /></p>
            <h4>Изменение размера и положения среза</h4>
            <p>Опции <b>Ширина</b> и <b>Высота</b> позволяют изменить ширину и/или высоту среза. Если нажата кнопка <b>Сохранять пропорции</b> <span class="icon icon-constantproportions"></span> (в этом случае она выглядит так: <span class="icon icon-constantproportionsactivated"></span>), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон среза.</p>
            <p>В разделе <b>Положение</b> можно изменить положение среза <b>По горизонтали</b> и/или <b>По вертикали</b>.</p>
            <p>Опция <b>Отключить изменение размера или перемещение</b> позволяет запретить перемещение или изменение размера среза. Когда эта опция выбрана, опции <b>Ширина</b>, <b>Высота</b>, <b>Положение</b> и <b>Кнопки</b> неактивны.</p>            
            <h4>Изменение макета и стиля среза</h4>
            <p>В разделе <b>Кнопки</b> можно указать нужное количество <b>Столбцов</b> и задать <b>Ширину</b> и <b>Высоту</b> кнопок. По умолчанию срез содержит один столбец. Если элементы содержат короткий текст, можно изменить число столбцов до 2 и более:</p>
            <p><img alt="Срез - два столбца" src="../images/slicer_columns.png" /></p>
            <p>При увеличении ширины кнопок ширина среза будет меняться соответственно. При увеличении высоты кнопок в срез будет добавлена полоса прокрутки:</p>
            <p><img alt="Срез - полоса прокрутки" src="../images/slicer_scroll.png" /></p>
            <p>В разделе <b>Стиль</b> можно выбрать один из готовых стилей срезов.</p>
                        
            <h4>Применение параметров сортировки и фильтрации</h4>
            <ul>
                <li><b>По возрастанию (от A до Я)</b> - используется для сортировки данных в порядке возрастания - от A до Я по алфавиту или от наименьшего значения к наибольшему для числовых данных.</li>
                <li><b>По убыванию (от Я до A)</b> - используется для сортировки данных в порядке убывания - от Я до A по алфавиту или от наибольшего значения к наименьшему для числовых данных.</li>
            </ul>
            <p>Опция <b>Скрыть элементы без данных</b> позволяет скрыть элементы без данных из среза. Когда эта опция отмечена, опции <b>Визуально выделять пустые элементы</b> и <b>Показывать пустые элементы последними</b> неактивны.</p>
            <p>Когда опция <b>Скрыть элементы без данных</b> не отмечена, можно использовать следующие параметры:</p>
            <ul>
                <li>Опция <b>Визуально выделять пустые элементы</b> позволяет отображать элементы без данных с другим форматированием (более светлого цвета). Если снять галочку с этой опции, все элементы будут отображаться с одинаковым форматированием.</li>
                <li>Опция <b>Показывать пустые элементы последними</b> позволяет отображать элементы без данных в конце списка. Если снять галочку с этой опции, все элементы будут отображаться в том же порядке, что и в исходной таблице.</li>
            </ul>
                        
            <h3>Изменение дополнительных параметров среза</h3>
            <p>Чтобы изменить дополнительные параметры среза, нажмите ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно 'Срез - Дополнительные параметры':</p>
            <p><img alt="Срез - Дополнительные параметры" src="../images/slicer_properties.png" /></p>
            <p>Вкладка <b>Стиль и размер</b>  содержит следующие параметры:</p>
            <ul>
                <li>Опция <b>Заголовок</b> позволяет изменить заголовок среза. Снимите галочку с опции <b>Показывать заголовок</b>, если вы не хотите отображать заголовок среза.</li>
                <li>Опция <b>Стиль</b> позволяет выбрать один из готовых стилей срезов.</li>
                <li>Опции <b>Ширина</b> и <b>Высота</b> позволяют изменить ширину и/или высоту среза. Если нажата кнопка <b>Сохранять пропорции</b> <div class = "icon icon-constantproportions"></div> (в этом случае она выглядит так: <div class = "icon icon-constantproportionsactivated"></div>), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон среза.</li>
                <li>В разделе <b>Кнопки</b> можно указать нужное количество <b>Столбцов</b> и задать <b>Высоту</b> кнопок.</li>
            </ul>
            <p><img alt="Срез - Дополнительные параметры" src="../images/slicer_properties2.png" /></p>
            <p>Вкладка <b>Сортировка и фильтрация</b> содержит следующие параметры:</p>
            <ul>
                <li><b>По возрастанию (от A до Я)</b> - используется для сортировки данных в порядке возрастания - от A до Я по алфавиту или от наименьшего значения к наибольшему для числовых данных.</li>
                <li><b>По убыванию (от Я до A)</b> - используется для сортировки данных в порядке убывания - от Я до A по алфавиту или от наибольшего значения к наименьшему для числовых данных.</li>
            </ul>
            <p>Опция <b>Скрыть элементы без данных</b> позволяет скрыть элементы без данных из среза. Когда эта опция отмечена, опции <b>Визуально выделять пустые элементы</b> и <b>Показывать пустые элементы последними</b> неактивны.</p>
            <p>Когда опция <b>Скрыть элементы без данных</b> не отмечена, можно использовать следующие параметры:</p>
            <ul>
                <li>Опция <b>Визуально выделять пустые элементы</b> позволяет отображать элементы без данных с другим форматированием (более светлого цвета).</li>
                <li>Опция <b>Показывать пустые элементы последними</b> позволяет отображать элементы без данных в конце списка.</li>
            </ul>
            <p><img alt="Срез - Дополнительные параметры" src="../images/slicer_properties3.png" /></p>
            <p>Вкладка <b>Ссылки</b> содержит следующие параметры:</p>
            <ul>
                <li>Опция <b>Имя источника</b> позволяет посмотреть имя поля, соответствующее заголовку столбца из исходного набора данных.</li>
                <li>Опция <b>Имя для использования в формулах</b> позволяет посмотреть имя среза, которое отображается в <a href="UseNamedRanges.htm" onclick="onhyperlinkclick(this)"><b>Диспетчере имен</b></a>. </li>
                <li>Опция <b>Имя</b> позволяет задать произвольное имя среза, тобы сделать его более содержательным и понятным.</li>
            </ul>
            <p><img alt="Срез - Дополнительные параметры" src="../images/slicer_properties4.png" /></p>
            <p>Вкладка <b>Привязка к ячейке</b> содержит следующие параметры:</p>
            <ul>
                <li><b>Перемещать и изменять размеры вместе с ячейками</b> - эта опция позволяет привязать срез к ячейке позади него. Если ячейка перемещается (например, при вставке или удалении нескольких строк/столбцов), срез будет перемещаться вместе с ячейкой. При увеличении или уменьшении ширины или высоты ячейки размер среза также будет изменяться.</li>
                <li><b>Перемещать, но не изменять размеры вместе с ячейками</b> - эта опция позволяет привязать срез к ячейке позади него, не допуская изменения размера среза. Если ячейка перемещается, срез будет перемещаться вместе с ячейкой, но при изменении размера ячейки размеры среза останутся неизменными.</li>
                <li><b>Не перемещать и не изменять размеры вместе с ячейками</b> - эта опция позволяет запретить перемещение или изменение размера среза при изменении положения или размера ячейки.</li>
            </ul>
            <p><img alt="Срез - Дополнительные параметры" src="../images/slicer_properties5.png" /></p>
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит срез.</p>
            
            <h3>Удаление среза</h3>
            <p>Для удаления среза:</p>
            <ol>
                <li>Выделите срез, щелкнув по нему.</li>
                <li>Нажмите клавишу <b>Delete</b>.</li>
            </ol>
		</div>
	</body>
</html>
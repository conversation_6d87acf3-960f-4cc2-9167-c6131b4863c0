<!DOCTYPE html>
<html>
	<head>
		<title>Функция ГИПЕРГЕОМЕТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ГИПЕРГЕОМЕТ</h1>
			<p>Функция <b>ГИПЕРГЕОМЕТ</b> - это одна из статистических функций. Возвращает гипергеометрическое распределение, вероятность заданного количества успехов в выборке, если заданы размер выборки, количество успехов в генеральной совокупности и размер генеральной совокупности.</p>
			<p>Синтаксис функции <b>ГИПЕРГЕОМЕТ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ГИПЕРГЕОМЕТ(число_успехов_в_выборке;размер_выборки;число_успехов_в_совокупности;размер_совокупности)</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>число_успехов_в_выборке</em></b> - количество успешных испытаний в заданной выборке, числовое значение больше 0, но меньше, чем наименьшее значение аргументов <b><em>размер_выборки</em></b> или <b><em>число_успехов_в_совокупности</em></b>.</p>
				<p style="text-indent: 50px;"><b><em>размер_выборки</em></b> - размер выборки, числовое значение больше 0, но меньше, чем значение аргумента <b><em>размер_совокупности</em></b>.</p>
				<p style="text-indent: 50px;"><b><em>число_успехов_в_совокупности</em></b> - количество успешных испытаний в генеральной совокупности, числовое значение больше 0, но меньше, чем значение аргумента <b><em>размер_совокупности</em></b>.</p>
				<p style="text-indent: 50px;"><b><em>размер_совокупности</em></b> - размер генеральной совокупности, числовое значение больше 0.</p>
				<p>Эти числовые значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>ГИПЕРГЕОМЕТ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Статистические</b>,</li>
			<li>щелкните по функции <b>ГИПЕРГЕОМЕТ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ГИПЕРГЕОМЕТ" src="../images/hypgeomdist.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЦЕНАПОСЛНЕРЕГ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ЦЕНАПОСЛНЕРЕГ</h1>
            <p>Функция <b>ЦЕНАПОСЛНЕРЕГ</b> - это одна из финансовых функций. Используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги с периодической выплатой процентов в случае нерегулярной продолжительности последнего периода выплаты процентов (больше или меньше остальных периодов).</p>
          <p>Синтаксис функции <b>ЦЕНАПОСЛНЕРЕГ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ЦЕНАПОСЛНЕРЕГ(дата_согл;дата_вступл_в_силу;последняя_выплата;ставка;доход;погашение;частота;[базис])</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>дата_согл</em></b> - это дата покупки ценной бумаги.</p>
            <p style="text-indent: 50px;"><b><em>дата_вступл_в_силу</em></b> - это дата истечения срока действия ценной бумаги (дата погашения).</p>
            <p style="text-indent: 50px;"><b><em>последняя_выплата</em></b> - это дата последней выплаты по купону. Она должна наступать раньше даты покупки ценной бумаги.</p>
            <p style="text-indent: 50px;"><b><em>ставка</em></b> - это процентная ставка по ценной бумаге.</p>
            <p style="text-indent: 50px;"><b><em>доход</em></b> - это годовой доход по ценной бумаге.</p>
            <p style="text-indent: 50px;"><b><em>погашение</em></b> - это выкупная стоимость ценной бумаги в расчете на 100 рублей номинальной стоимости.</p>
          <p style="text-indent: 50px;">
            <b>
              <em>частота</em>
            </b> - это количество выплат процентов за год. Возможные значения этого аргумента: 1 в случае, если проценты выплачиваются ежегодно, 2 в случае, если проценты выплачиваются каждые полгода, 4 в случае, если проценты выплачиваются ежеквартально.
          </p>
          <p style="text-indent: 50px;">
            <b>
              <em>базис</em>
            </b> - это используемый способ расчета количества дней; числовое значение от 0 до 4. Это необязательный аргумент. Он может принимать одно из следующих значений:
          </p>
          <table style="width: 40%">
            <tr>
              <td>
                <b>Числовое значение</b>
              </td>
              <td>
                <b>Способ расчета</b>
              </td>
            </tr>
            <tr>
              <td>0</td>
              <td>Американский метод (NASD) 30/360</td>
            </tr>
            <tr>
              <td>1</td>
              <td>Фактический метод</td>
            </tr>
            <tr>
              <td>2</td>
              <td>Фактический/360 метод</td>
            </tr>
            <tr>
              <td>3</td>
              <td>Фактический/365 метод</td>
            </tr>
            <tr>
              <td>4</td>
              <td>Европейский метод 30/360</td>
            </tr>
          </table>
          <p class="note">
            <b>Примечание:</b> даты должны быть введены с помощью функции ДАТА.
          </p>
          <p>Значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
          <p>
            Чтобы применить функцию <b>ЦЕНАПОСЛНЕРЕГ</b>,
          </p>
          <ol>
            <li>выделите ячейку, в которой требуется отобразить результат,</li>
            <li>
              щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
              <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
              <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
            </li>
            <li>
              выберите из списка группу функций <b>Финансовые</b>,
            </li>
            <li>
              щелкните по функции <b>ЦЕНАПОСЛНЕРЕГ</b>,
            </li>
            <li>введите требуемые аргументы через точку с запятой,</li>
            <li>
              нажмите клавишу <b>Enter</b>.
            </li>
          </ol>
          <p>Результат будет отображен в выделенной ячейке.</p>  
          <p style="text-indent: 150px;"><img alt="Функция ЦЕНАПОСЛНЕРЕГ" src="../images/oddlprice.png" /></p>
        </div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Вставка текстовых объектов</title>
		<meta charset="utf-8" />
        <meta name="description" content="Вставьте текстовые объекты, такие как надписи и объекты Text Art, чтобы привлечь внимание к определенной части электронной таблицы" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вставка текстовых объектов</h1>
            <p>Чтобы привлечь внимание к определенной части электронной таблицы, можно вставить надпись (прямоугольную рамку, внутри которой вводится текст) или объект Text Art (текстовое поле с предварительно заданным стилем и цветом шрифта, позволяющее применять текстовые эффекты).</p>
            <h3>Добавление текстового объекта</h3>
            <p>Текстовый объект можно добавить в любом месте рабочего листа. Для этого:</p>
            <ol>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>
                    выберите нужный тип текстового объекта:
                    <ul>
                        <li>
                            чтобы добавить текстовое поле, щелкните по значку <div class = "icon icon-inserttexticon"></div> <b>Надпись</b> на верхней панели инструментов, затем щелкните там, где требуется поместить надпись, удерживайте кнопку мыши и перетаскивайте границу текстового поля, чтобы задать его размер. Когда вы отпустите кнопку мыши, в добавленном текстовом поле появится курсор, и вы сможете ввести свой текст.
                            <p class="note"><b>Примечание</b>: надпись можно также вставить, если щелкнуть по значку <span class="icon icon-insertautoshape"></span> <b>Фигура</b> на верхней панели инструментов и выбрать фигуру <span class="icon icon-text_autoshape"></span> из группы <b>Основные фигуры</b>.</p>
                        </li>
                        <li>чтобы добавить объект Text Art, щелкните по значку <div class = "icon icon-inserttextarticon"></div> <b>Text Art</b> на верхней панели инструментов, затем щелкните по нужному шаблону стиля – объект Text Art будет добавлен в центре рабочего листа. Выделите мышью стандартный текст внутри текстового поля и напишите вместо него свой текст.</li>
                    </ul>
                </li>
                <li>щелкните за пределами текстового объекта, чтобы применить изменения и вернуться к рабочему листу.</li>
            </ol>
            <p>Текст внутри текстового объекта является его частью (при перемещении или повороте текстового объекта текст будет перемещаться или поворачиваться вместе с ним).</p>
            <p>Поскольку вставленный текстовый объект представляет собой прямоугольную рамку с текстом внутри (у объектов Text Art по умолчанию невидимые границы), а эта рамка является обычной автофигурой, можно изменять свойства и фигуры, и текста.</p>
            <p>Чтобы удалить добавленный текстовый объект, щелкните по краю текстового поля и нажмите клавишу <b>Delete</b> на клавиатуре. Текст внутри текстового поля тоже будет удален.</p>
            <h3>Форматирование текстового поля</h3>
            <p>Выделите текстовое поле, щелкнув по его границе, чтобы можно было изменить его свойства. Когда текстовое поле выделено, его границы отображаются как сплошные, а не пунктирные линии.</p>
            <p><img alt="Выделенное текстовое поле" src="../images/textbox_boxselected.png" /></p>
            <ul>
                <li>чтобы вручную <a href="ManipulateObjects.htm" onclick="onhyperlinkclick(this)">изменить размер текстового поля, переместить, повернуть</a> его, используйте специальные маркеры по краям фигуры.</li>
                <li>чтобы изменить <a href="InsertAutoshapes.htm#shape_fill" onclick="onhyperlinkclick(this)">заливку</a>, <a href="InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)">контур</a>, <b>заменить</b> прямоугольное поле на какую-то другую фигуру или открыть <a href="InsertAutoshapes.htm#shape_advanced" onclick="onhyperlinkclick(this)">дополнительные параметры фигуры</a>, щелкните по значку <b>Параметры фигуры</b> <div class = "icon icon-shape_settings_icon"></div> на правой боковой панели и используйте соответствующие опции.</li>
                <li>чтобы расположить текстовые поля <b>в определенном порядке</b> относительно других объектов, <b>выровнять несколько текстовых полей</b> относительно друг друга, <b>повернуть или отразить</b> текстовое поле, щелкните правой кнопкой мыши по границе текстового поля и используйте опции контекстного меню. Подробнее о выравнивании и расположении объектов в определенном порядке рассказывается на <a href="../UsageInstructions/ManipulateObjects.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</li>
                <li>чтобы создать <b>колонки текста</b> внутри текстового поля, щелкните правой кнопкой мыши по границе текстового поля, нажмите на пункт меню <b>Дополнительные параметры фигуры</b> и перейдите на вкладку <a href="../UsageInstructions/InsertAutoshapes.htm#columns" onclick="onhyperlinkclick(this)"><b>Колонки</b></a> в окне <b>Фигура - дополнительные параметры</b>.</li>
            </ul>
            <h3 id="textbox_textformatting">Форматирование текста внутри текстового поля</h3>
            <p>Щелкните по тексту внутри текстового поля, чтобы можно было изменить его свойства. Когда текст выделен, границы текстового поля отображаются как пунктирные линии.</p>
            <p><img alt="Выделенный текст" src="../images/textbox_textselected.png" /></p>
            <p class="note"><b>Примечание</b>: форматирование текста можно изменить и в том случае, если выделено текстовое поле, а не сам текст. В этом случае любые изменения будут применены ко всему тексту в текстовом поле. Некоторые параметры форматирования шрифта (тип, размер, цвет и стили оформления шрифта) можно отдельно применить к предварительно выделенному фрагменту текста.</p>
            <ul>
                <li>Настройте <b>параметры форматирования шрифта</b> (измените его тип, размер, цвет и примените стили оформления) с помощью <a href="../UsageInstructions/FontTypeSizeStyle.htm" onclick="onhyperlinkclick(this)">соответствующих значков</a> на вкладке <b>Главная</b> верхней панели инструментов. Некоторые дополнительные параметры шрифта можно также изменить на вкладке <b>Шрифт</b> в окне свойств абзаца. Чтобы его открыть, щелкнуть правой кнопкой мыши по тексту в текстовом поле и выберите опцию <b>Дополнительные параметры текста</b>.</li>
                <li><b>Выровняйте текст внутри текстового поля по горизонтали</b> с помощью <a href="../UsageInstructions/AlignText.htm" onclick="onhyperlinkclick(this)">соответствующих значков</a> на вкладке <b>Главная</b> верхней панели инструментов или в окне <a href="../UsageInstructions/InsertTextObjects.htm#textadvancedsettings" onclick="onhyperlinkclick(this)"><b>Абзац - Дополнительные параметры</b></a>.</li>
                <li><b>Выровняйте текст внутри текстового поля по вертикали</b> с помощью <a href="../UsageInstructions/AlignText.htm" onclick="onhyperlinkclick(this)">соответствующих значков</a> на вкладке <b>Главная</b> верхней панели инструментов. Можно также щелкнуть по тексту правой кнопкой мыши, выбрать опцию <b>Вертикальное выравнивание</b>, а затем - один из доступных вариантов: <b>По верхнему краю</b>, <b>По центру</b> или <b>По нижнему краю</b>.</li>
                <li><b>Поверните</b> текст внутри текстового поля. Для этого щелкните по тексту правой кнопкой мыши, выберите опцию <b>Направление текста</b>, а затем выберите один из доступных вариантов: <b>Горизонтальное</b> (выбран по умолчанию), <b>Повернуть текст вниз</b> (задает вертикальное направление, сверху вниз) или <b>Повернуть текст вверх</b> (задает вертикальное направление, снизу вверх).</li>
                <li>
                    Создайте <b>маркированный или нумерованный список</b>. Для этого щелкните по тексту правой кнопкой мыши, выберите в контекстном меню пункт <b>Маркеры и нумерация</b>, а затем выберите один из доступных знаков маркера или стилей нумерации.
                    <p><img alt="Маркеры и нумерация" src="../images/bulletsandnumbering.png" /></p>
                    <p>Опция <b>Параметры списка</b> позволяет открыть окно <b>Параметры списка</b>, в котором можно настроить параметры для соответствующего типа списка:</p>
                    <p><img alt="Окно настроек маркированного списка" src="../images/bulletedlistsettings.png" /></p>
                    <p><b>Тип (маркированный список)</b> - позволяет выбрать нужный символ, используемый для маркированного списка. При нажатии на поле <b>Новый маркер</b> открывается окно <b>Символ</b>, в котором можно выбрать один из доступных символов. Для получения дополнительной информации о работе с символами вы можете обратиться к <a href="../UsageInstructions/InsertSymbols.htm" onclick="onhyperlinkclick(this)">этой статье</a>.</p>
                    <p>При нажатии на поле <b>Новое изображение</b> появляется новое поле <b>Импорт</b>, в котором можно выбрать новое изображение для маркеров <em>Из файла</em>, <em>По URL</em> или <em>Из хранилища</em>.</p>
                    <p><img alt="Окно настроек нумерованного списка" src="../images/numberedlistsettings.png" /></p>
                    <p><b>Тип (нумерованный список)</b> - позволяет выбрать нужный формат нумерованного списка.</p>
                    <ul>
                        <li><b>Размер</b> - позволяет выбрать нужный размер для каждого из маркеров или нумераций в зависимости от текущего размера текста. Может принимать значение от 25% до 400%.</li>
                        <li><b>Цвет</b> - позволяет выбрать нужный цвет маркеров или нумерации. Вы можете выбрать на палитре один из <em>цветов темы</em> или <em>стандартных цветов</em>, или задать <em>пользовательский</em> цвет.</li>
                        <li><b>Начать с</b> - позволяет задать нужное числовое значение, с которого вы хотите начать нумерацию.</li>
                    </ul>
                </li>
                <li>Вставьте <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">гиперссылку</a>.</li>
                <li>
                    <b>Задайте междустрочный интервал и интервал между абзацами</b> для многострочного текста внутри текстового поля с помощью вкладки <b>Параметры текста</b> на правой боковой панели. Чтобы ее открыть, щелкните по значку <b>Параметры текста</b> <div class = "icon icon-textsettings"></div>. Здесь можно задать высоту строки для строк текста в абзаце, а также поля между текущим и предыдущим или последующим абзацем.
                    <p><img alt="Вкладка Параметры текста" src="../images/textsettingstab.png" /></p>
                    <ul>
                        <li><b>Междустрочный интервал</b> - задайте высоту строки для строк текста в абзаце. Вы можете выбрать одну из двух опций: <b>множитель</b> (устанавливает междустрочный интервал, который может быть выражен в числах больше 1), <b>точно</b> (устанавливает фиксированный междустрочный интервал). Необходимое значение можно указать в поле справа.</li>
                        <li>
                            <b>Интервал между абзацами</b> - задайте величину свободного пространства между абзацами.
                            <ul>
                                <li><b>Перед</b> - задайте величину свободного пространства перед абзацем.</li>
                                <li><b>После</b> - задайте величину свободного пространства после абзаца.</li>
                            </ul>
                            <p class="note"><b>Примечание</b>: эти параметры также можно найти в окне <a href="../UsageInstructions/InsertTextObjects.htm#textadvancedsettings" onclick="onhyperlinkclick(this)"><b>Абзац - Дополнительные параметры</b></a>.</p>
                        </li>
                    </ul>
                </li>
            </ul>

            <h3 id="textadvancedsettings">Изменение дополнительных параметров абзаца</h3>
            <p>Измените <b>дополнительные параметры</b> абзаца (можно настроить <b>отступы абзаца</b> и <b>позиции табуляции</b> для многострочного текста внутри текстового поля и применить некоторые <b>параметры форматирования шрифта</b>). Установите курсор в пределах нужного абзаца - на правой боковой панели будет активирована вкладка <b>Параметры текста</b>. Нажмите на ссылку <b>Дополнительные параметры</b>. Также можно щелкнуть по тексту в текстовом поле правой кнопкой мыши и использовать пункт контекстного меню <b>Дополнительные параметры текста</b>. Откроется окно свойств абзаца:</p>
            <p><img alt="Свойства абзаца - вкладка Отступы и интервалы" src="../images/textadvancedsettings1.png" /></p>
            <p>На вкладке <b>Отступы и интервалы</b> можно выполнить следующие действия:</p>
            <ul>
                <li>изменить тип <b>выравнивания</b> текста внутри абзаца,</li>
                <li>
                    изменить <b>отступы</b> абзаца от <a href="../UsageInstructions/InsertAutoshapes.htm#internalmargins" onclick="onhyperlinkclick(this)">внутренних полей</a> текстового объекта,
                    <ul>
                        <li><b>Слева</b> - задайте смещение всего абзаца от <b>левого</b> внутреннего поля текстового блока, указав нужное числовое значение,</li>
                        <li><b>Справа</b> - задайте смещение всего абзаца от <b>правого</b> внутреннего поля текстового блока, указав нужное числовое значение,</li>
                        <li><b>Первая строка</b> - задайте отступ для <b>первой строки</b> абзаца, выбрав соответствующий пункт меню (<b>(нет)</b>, <b>Отступ</b>, <b>Выступ</b>) и изменив числовое значение для <b>Отступа</b> или <b>Выступа</b>, заданное по умолчанию,</li>
                    </ul>
                </li>
                <li>изменить <b>междустрочный интервал</b> внутри абзаца.</li>
            </ul>
            <p><img alt="Свойства абзаца - вкладка Шрифт" src="../images/textadvancedsettings2.png" /></p>
            <p>Вкладка <b>Шрифт</b> содержит следующие параметры:</p>
            <ul>
                <li><b>Зачёркивание</b> - используется для зачеркивания текста чертой, проведенной по буквам.</li>
                <li><b>Двойное зачёркивание</b> - используется для зачеркивания текста двойной чертой, проведенной по буквам.</li>
                <li><b>Надстрочные</b> - используется, чтобы сделать текст мельче и поместить его в верхней части строки, например, как в дробях.</li>
                <li><b>Подстрочные</b> - используется, чтобы сделать текст мельче и поместить его в нижней части строки, например, как в химических формулах.</li>
                <li><b>Малые прописные</b> - используется, чтобы сделать все буквы строчными.</li>
                <li><b>Все прописные</b> - используется, чтобы сделать все буквы прописными.</li>
                <li>
                    <b>Межзнаковый интервал</b> - используется, чтобы задать расстояние между символами. Увеличьте значение, заданное по умолчанию, чтобы применить <b>Разреженный</b> интервал, или уменьшите значение, заданное по умолчанию, чтобы применить <b>Уплотненный</b> интервал. Используйте кнопки со стрелками или введите нужное значение в поле ввода.
                    <p>Все изменения будут отображены в расположенном ниже поле предварительного просмотра.</p>
                </li>
            </ul>
            <p><img alt="Свойства абзаца - вкладка Табуляция" src="../images/textadvancedsettings3.png" /></p>
            <p>На вкладке <b>Табуляция</b> можно изменить позиции табуляции, то есть те позиции, куда переходит курсор при нажатии клавиши <b>Tab</b> на клавиатуре.</p>
            <ul>
                <li>Позиция табуляции <b>По умолчанию</b> имеет значение 2.54 см. Это значение можно уменьшить или увеличить, используя кнопки со стрелками или введя в поле нужное значение.</li>
                <li><b>Позиция</b> - используется, чтобы задать пользовательские позиции табуляции. Введите в этом поле нужное значение, настройте его более точно, используя кнопки со стрелками, и нажмите на кнопку <b>Задать</b>. Пользовательская позиция табуляции будет добавлена в список в расположенном ниже поле.</li>
                <li>
                    <b>Выравнивание</b> - используется, чтобы задать нужный тип выравнивания для каждой из позиций табуляции в расположенном выше списке. Выделите нужную позицию табуляции в списке, выберите в выпадающем списке <b>Выравнивание</b> опцию <b>По левому краю</b>, <b>По центру</b> или <b>По правому краю</b> и нажмите на кнопку <b>Задать</b>.
                    <ul>
                        <li><b>По левому краю</b> - выравнивает текст по левому краю относительно позиции табуляции; при наборе текст движется вправо от позиции табуляции. </li>
                        <li><b>По центру</b> - центрирует текст относительно позиции табуляции.</li>
                        <li><b>По правому краю</b> - выравнивает текст по правому краю относительно позиции табуляции; при наборе текст движется влево от позиции табуляции.</li>
                    </ul>
                    <p>Для удаления позиций табуляции из списка выделите позицию табуляции и нажмите кнопку <b>Удалить</b> или <b>Удалить все</b>.</p>
                </li>
            </ul>

            <h3>Назначение макроса к текстовому объекту</h3>
            <p>Вы можете обеспечить быстрый и легкий доступ к макросу в электронной таблице, назначив макрос любому текстовому объекту. После назначения макроса, графический объект отображается как кнопка, и вы можете запускать макрос всякий раз, когда нажимаете на объект.</p>
            <p>Чтобы назначить макрос,</p>
            <ul type="circle">
                <li>
                    Щелкните правой кнопкой мыши по текстовому объекту и в контекстном меню выберите пункт <b>Назначить макрос</b>.
                    <p><img alt="Контекстное меню - Назначить макрос" src="../images/assignmacro_rightclickshape.png" /></p>
                </li>
                <li>Откроется окно <b>Назначить макрос</b></li>
                <li>
                    Выберите макрос из списка или вручную введите название макроса и нажмите <b>ОК</b>.
                    <p><img alt="Назначить макрос" src="../images/assignmacro.png" /></p>
                </li>
            </ul>

            <h3>Изменение стиля объекта Text Art</h3>
            <p>Выделите текстовый объект и щелкните по значку <b>Параметры объектов Text Art</b> <span class="icon icon-textart_settings_icon"></span> на правой боковой панели.</p>
            <p><img alt="Вкладка Параметры объектов Text Art" src="../images/right_textart.png" /></p>
            <ul>
                <li>Измените примененный стиль текста, выбрав из галереи новый <b>Шаблон</b>. Можно также дополнительно изменить этот базовый стиль, выбрав другой тип, размер шрифта и т.д.</li>
                <li>Измените <a href="InsertAutoshapes.htm#shape_fill" onclick="onhyperlinkclick(this)">заливку</a> и <a href="InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)">контур</a> шрифта. Доступны точно такие же опции, как и для автофигур.</li>
                <li>Примените текстовый эффект, выбрав нужный тип трансформации текста из галереи <b>Трансформация</b>. Можно скорректировать степень искривления текста, перетаскивая розовый маркер в форме ромба.</li>
            </ul>
            <p><img alt="Трансформация объекта Text Art" src="../images/textart_transformation.png" /></p>
        </div>
	</body>
</html>
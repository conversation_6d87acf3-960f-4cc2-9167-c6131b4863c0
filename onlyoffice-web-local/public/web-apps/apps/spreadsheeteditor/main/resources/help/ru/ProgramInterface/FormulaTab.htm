<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Формула</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора электронных таблиц - Вкладка Формула" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Вкладка Формула</h1>
            <p>Вкладка <b>Формула</b> в <a href="https://www.onlyoffice.com/ru/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редакторе электронных таблиц</b></a> позволяет удобно <a href="../UsageInstructions/InsertFunction.htm" onclick="onhyperlinkclick(this)">работать со всеми функциями</a>.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора электронных таблиц:</p>
                <p><img alt="Вкладка Формула" src="../images/interface/formulatab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора электронных таблиц:</p>
                <p><img alt="Вкладка Формула" src="../images/interface/desktop_formulatab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>вставлять функции, используя диалоговое окно <b>Вставка функций</b>,</li>
                <li>получать быстрый доступ к формулам <b>Автосуммы</b>,</li>
                <li>получать доступ к 10 <b>последним использованным</b> формулам,</li> 
                <li>работать с формулами, распределенными по категориям,</li>
                <li>работать с <a href="../UsageInstructions/UseNamedRanges.htm" onclick="onhyperlinkclick(this)">именованными диапазонами</a>,</li>
                <li>использовать параметры <b>пересчета</b>: выполнять пересчет всей книги или только текущего рабочего листа<!--, а также выбирать предпочтительный режим пересчета: автоматически или вручную-->.</li>               
            </ul>
		</div>
	</body>
</html>
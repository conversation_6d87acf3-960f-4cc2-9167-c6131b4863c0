<!DOCTYPE html>
<html>
	<head>
		<title>Функция CSC</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция CSC</h1>
			<p>Функция <b>CSC</b> - это одна из математических и тригонометрических функций. Она возвращает косеканс заданного угла в радианах.</p>
			<p>Синтаксис функции <b>CSC</b>:</p> 
			<p style="text-indent: 150px;"><b><em>CSC(x)</em></b></p> 
			<p>где <b><em>x</em></b> - это угол в радианах, косеканс которого требуется вычислить. Числовое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка. Его абсолютное значение должно быть меньше 2^27.</p>
			<p>Чтобы применить функцию <b>CSC</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Математические</b>,</li>
                <li>щелкните по функции <b>CSC</b>,</li>
                <li>введите требуемый аргумент,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция CSC" src="../images/csc.png" /></p>
		</div>
	</body>
</html>
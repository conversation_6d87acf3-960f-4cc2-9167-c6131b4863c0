<!DOCTYPE html>
<html>
	<head>
		<title>Функция ОТРБИНОМ.РАСП</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ОТРБИНОМ.РАСП</h1>
            <p>Функция <b>ОТРБИНОМ.РАСП</b> - это одна из статистических функций. Возвращает отрицательное биномиальное распределение - вероятность возникновения определенного числа неудач до указанного количества успехов при заданной вероятности успеха.</p>
            <p>Синтаксис функции <b>ОТРБИНОМ.РАСП</b>:</p>
            <p style="text-indent: 150px;"><b><em>ОТРБИНОМ.РАСП(число_неудач;число_успехов;вероятность_успеха;интегральная)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>число_неудач</em></b> - количество неудачных испытаний; числовое значение, большее или равное 0.</p>
            <p style="text-indent: 50px;"><b><em>число_успехов</em></b> - пороговое значение числа успешных испытаний; числовое значение, большее или равное 1.</p>
            <p style="text-indent: 50px;"><b><em>вероятность_успеха</em></b> - вероятность успеха каждого испытания; числовое значение больше 0, но меньше 1.</p>
            <p style="text-indent: 50px;"><b><em>интегральная</em></b> логическое значение (ИСТИНА или ЛОЖЬ), определяющее форму функции. Если этот аргумент имеет значение ИСТИНА, возвращается интегральная функция распределения. Если этот аргумент имеет значение ЛОЖЬ, возвращается функция плотности распределения.</p>
            <p>Эти числовые значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
            <p>Чтобы применить функцию <b>ОТРБИНОМ.РАСП</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>ОТРБИНОМ.РАСП</b>,</li>
                <li>введите требуемые аргументы вручную через точку с запятой или выделите диапазон ячеек мышью,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ОТРБИНОМ.РАСП" src="../images/negbinom-dist.png" /></p>
        </div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЧИСЛКОМБ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЧИСЛКОМБ</h1>
			<p>Функция <b>ЧИСЛКОМБ</b> - это одна из математических и тригонометрических функций. Она возвращает количество комбинаций для заданного числа элементов.</p>
			<p>Синтаксис функции <b>ЧИСЛКОМБ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЧИСЛКОМБ(число;число_выбранных)</em></b></p> 
			<p><em>где</em></p>
				<p style="text-indent: 50px;"><b><em>число</em></b> - количество элементов; числовое значение, большее или равное 0,</p>
				<p style="text-indent: 50px;"><b><em>число_выбранных</em></b> - количество элементов в комбинации; числовое значение, большее или равное 0, но меньшее, чем значение <b><em>число</em></b>,</p>
				<p>Эти аргументы можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>ЧИСЛКОМБ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>ЧИСЛКОМБ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ЧИСЛКОМБ" src="../images/combin.png" /></p>
		</div>
	</body>
</html>
<!DOCTYPE html>
<html>
	<head>
		<title>Добавление гиперссылок</title>
		<meta charset="utf-8" />
		<meta name="description" content="Преобразуйте слово или текстовый фрагмент в гиперссылку, ведущую на внешний веб-сайт или другой рабочий лист" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Добавление гиперссылок</h1>
			<p>Для добавления гиперссылки:</p>
			<ol>
				<li>выделите ячейку, в которую требуется добавить гиперссылку,</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>нажмите значок <div class = "icon icon-addhyperlink"></div> <b>Гиперссылка</b> на верхней панели инструментов,</li>
				<li>после этого появится окно <b>Параметры гиперссылки</b>, в котором можно указать параметры гиперссылки:
				<ul>
					<li>Bыберите тип ссылки, которую требуется вставить:
                        <p>Используйте опцию <b>Внешняя ссылка</b> и введите URL в формате <em>http://www.example.com</em> в расположенном ниже поле <b>Связать с</b>, если вам требуется добавить гиперссылку, ведущую на <b>внешний</b> сайт. Если надо добавить гиперссылку на <b>локальный</b> файл, введите URL в формате <em>file://path/Spreadsheet.xlsx</em> (для Windows) или <em>file:///path/Spreadsheet.xlsx</em> (для MacOS и Linux) в поле <b>Связать с</b>.</p>
                        <p class="note">Гиперссылки <em>file://path/Spreadsheet.xlsx</em> или <em>file:///path/Spreadsheet.xlsx</em> можно открыть только в десктопной версии редактора. В веб-редакторе можно только добавить такую ссылку без возможности открыть ее.</p>
                        <p><img alt="Окно Параметры гиперссылки" src="../../../../../../common/main/resources/help/ru/images/hyperlinkwindow.png" /></p>
					    <p>Используйте опцию <b>Внутренний диапазон данных</b> и выберите рабочий лист и диапазон ячеек в поле ниже или ранее добавленный <a href="UseNamedRanges.htm" onclick="onhyperlinkclick(this)">Именованный диапазон</a>, если вам требуется добавить гиперссылку, ведущую на определенный диапазон ячеек в той же самой электронной таблице.</p>
                        <p>Вы также можете сгенерировать внешнюю ссылку, ведущую на определенную ячейку или диапазон ячеек, нажав кнопку <b>Получить ссылку</b> или используя опцию <b>Получить ссылку на этот диапазон</b> в контекстном меню, вызываемом правой кнопкой мыши, нужного диапазона ячеек.</p>
                        <p><img alt="Окно Параметры гиперссылки" src="../images/name_hyperlink.png" /></p>
					</li>
					<li><b>Отображать</b> - введите текст, который должен стать ссылкой и будет вести по веб-адресу, указанному в поле выше.
					<p class="note"><b>Примечание</b>: если выбранная ячейка уже содержит данные, они автоматически отобразятся в этом поле.</p>
					</li>
					<li><b>Текст всплывающей подсказки</b> - введите текст краткого примечания к гиперссылке, который будет появляться в маленьком всплывающем окне при наведении на гиперссылку курсора.</li>
				</ul>				
				</li>
				<li>нажмите кнопку <b>OK</b>.</li>
			</ol>
            <p>Для добавления гиперссылки можно также использовать сочетание клавиш <b>Ctrl+K</b> или щелкнуть правой кнопкой мыши там, где требуется добавить гиперссылку, и выбрать в контекстном меню команду <b>Гиперссылка</b>.</p>
			<p>При наведении курсора на добавленную гиперссылку появится подсказка с заданным текстом. Для перехода по ссылке щелкните по ссылке в таблице. Чтобы выделить ячейку со ссылкой, не переходя по этой ссылке, щелкните и удерживайте кнопку мыши.</p>
			<p>Для удаления добавленной гиперссылки активируйте ячейку, которая содержит добавленную гиперссылку, и нажмите клавишу <b>Delete</b>, или щелкните по ячейке правой кнопкой мыши и выберите из выпадающего списка команду <b>Очистить все</b>.</p>
		</div>
	</body>
</html>

<!DOCTYPE html>
<html>
	<head>
		<title>Функция СЧЁТЕСЛИМН</title>
		<meta charset="utf-8" />
        <meta name="description" content="Статистическая функция СЧЁТЕСЛИМН используется для подсчета количества ячеек выделенного диапазона, соответствующих нескольким заданным условиям." />
        <meta name="keywords" content="счетеслимн, счётеслимн excel, excel счетеслимн, функция счетеслимн, функция счетеслимн в excel">
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СЧЁТЕСЛИМН</h1>
			<p>Функция <b>СЧЁТЕСЛИМН</b> - это одна из статистических функций. Она используется для подсчета количества ячеек выделенного диапазона, соответствующих нескольким заданным условиям.</p>
			<p>Синтаксис функции <b>СЧЁТЕСЛИМН</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СЧЁТЕСЛИМН(диапазон_условия1;условие1;[диапазон_условия2;условие2]; ...)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>диапазон_условия1</em></b> - первый выбранный диапазон ячеек, к которому применяется <em>условие1</em>. Это обязательный аргумент.</p>
            <p style="text-indent: 50px;"><b><em>условие1</em></b> - первое условие, которое должно выполняться. Оно применяется к <em>диапазону_условия1</em> и определяет, какие ячейки в <em>диапазоне_условия1</em> необходимо учитывать. Это значение, введенное вручную или находящееся в ячейке, на которую дается ссылка. Это обязательный аргумент.</p>
            <p style="text-indent: 50px;"><b><em>диапазон_условия2, условие2, ...</em></b> - дополнительные диапазоны ячеек и соответствующие условия. Это необязательные аргументы. Можно добавить до 127 диапазонов и соответствующих условий.</p>
			<p class="note"><b>Примечание</b>: при указании условий можно использовать подстановочные знаки. Вопросительный знак "?" может замещать любой отдельный символ, а звездочку "*" можно использовать вместо любого количества символов. Если требуется найти вопросительный знак или звездочку, введите перед этим символом тильду (~).</p>
			<h2>Как работает функция СЧЁТЕСЛИМН</h2>
            <p>Чтобы применить функцию <b>СЧЁТЕСЛИМН</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>СЧЁТЕСЛИМН</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СЧЁТЕСЛИМН" src="../images/countifs.png" /></p>
		</div>
	</body>
</html>
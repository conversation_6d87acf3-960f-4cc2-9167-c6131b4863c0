<!DOCTYPE html>
<html>
<head>
    <title>Функия ЛГРФПРИБЛ</title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Функия ЛГРФПРИБЛ</h1>
        <p>Функция <b>ЛГРФПРИБЛ</b> - это одна из статистических функций. Используется для вычисления экспоненциальной кривой, которая соответствует данным и возвращает массив значений, описывающих кривую.</p>
        <p>Синтаксис функции <b>ЛГРФПРИБЛ</b>:</p>
        <p style="text-indent: 150px;"><b><em>LOGEST(известные_значения_y, [известные_значения_x], [конст], [статистика])</em></b></p>
        <p>где</p>
        <p><b><em>known_y’s</em></b> - это набор значений, которые вы уже знаете в уравнении <em>y = b*m^x</em>.</p>
        <p><b><em>known_x’s</em></b> - это необязательный набор значений x, которые вы можете знать в уравнении <em>y = b*m^x</em>.</p>
        <p><b><em>const</em></b> - это необязательный аргумент. Значение ПРАВДА или ЛОЖЬ, где при значении ПРАВДА или отсутствии аргумента в уравнении <em>y = b*m^x</em>, где m-значения соответствуют уравнению <em>y = m*x</em>, <em>b</em> вычисляется обычным образом, а при значении ЛОЖЬ, константа <em>b</em> равняется 1.</p>
        <p><b><em>stats</em></b> - это необязательный аргумент. Значение ИСТИНА или ЛОЖЬ, которое определяет, должна ли возвращаться дополнительная статистика регрессии.</p>

        <p>Чтобы применить функцию <b>ЛГРФПРИБЛ</b>,</p>
        <ol>
            <li>выделите ячейку, в которой требуется отобразить результат,</li>
            <li>
                щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
            </li>
            <li>выберите из списка группу функций <b>Статистические</b>,</li>
            <li>щелкните по функции <b>ЛГРФПРИБЛ</b>,</li>
            <li>введите требуемые аргументы,</li>
            <li>нажмите клавишу <b>Enter</b>.</li>
        </ol>
        <p>Результат будет отображен в выделенной ячейке.</p>
        <p style="text-indent: 150px;"><img alt="Функция ЛГРФПРИБЛ" src="../images/logest.png" /></p>
    </div>
</body>
</html>